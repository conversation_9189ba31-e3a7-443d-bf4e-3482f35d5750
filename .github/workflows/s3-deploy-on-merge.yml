name: s3-deploy-on-merge

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Build React App
        run: npm install && npm run production
      - name: Deploy app build to S3 bucket
        run: aws s3 sync ./dist/ s3://hootsworth-jett --delete
      - name: Update CloudFront
        run: aws cloudfront create-invalidation --distribution-id E1K9PZ4FQTJ0QI --paths "/*"
