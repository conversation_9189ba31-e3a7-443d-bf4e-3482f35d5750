{"parser": "@babel/eslint-parser", "env": {"es6": true, "browser": true, "jest": true}, "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"jsx": true, "experimentalObjectRestSpread": true}}, "extends": ["airbnb", "plugin:react-hooks/recommended"], "rules": {"arrow-body-style": 0, "class-methods-use-this": 0, "consistent-return": 0, "curly": 0, "func-names": ["error", "never"], "function-paren-newline": 0, "no-underscore-dangle": ["error", {"allow": ["_original", "_error"]}], "object-curly-newline": 0, "operator-linebreak": 0, "prefer-object-spread": 0, "prefer-destructuring": 0, "max-len": [1, 120, 2, {"ignoreComments": true}], "react/destructuring-assignment": 0, "react/require-default-props": 0, "react/no-did-mount-set-state": 0, "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "react/jsx-no-bind": 0, "jsx-a11y/click-events-have-key-events": 0, "jsx-a11y/img-has-alt": [0], "jsx-a11y/no-static-element-interactions": 0, "import/no-extraneous-dependencies": [0], "import/extensions": [0], "import/no-unresolved": [0], "import/prefer-default-export": [0], "react/forbid-prop-types": [0], "react/jsx-one-expression-per-line": 0, "jsx-a11y/anchor-is-valid": [0], "react/prop-types": 0, "react/jsx-props-no-spreading": 0, "no-use-before-define": ["error", {"functions": false}], "jsx-quotes": ["error", "prefer-double"], "jsx-a11y/label-has-for": [2, {"components": ["Label"], "required": {"every": ["id"]}, "allowChildren": false}]}}