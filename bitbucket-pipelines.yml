image: node:20.19.4

definitions:
  scripts:
    - script: &npm-commands
        npm install && npm run production
    - script: &export-test-automation-variables
        export DEVUSER=$DEVUSER &&
        export DEVPASS=$DEVPASS &&
        export EMTADMIN_LOGIN=$EMTADMIN_LOGIN &&
        export EMTADMIN_PASS=$EMTADMIN_PASS &&
        export MANAGER_LOGIN=$MANAGER_LOGIN &&
        export MANAGER_PASS=$MANAGER_PASS &&
        export DEFAULT_PASS=$DEFAULT_PASS    
    - script: &clone-test-automation-repo
        cd /opt/atlassian/pipelines/agent/build && 
        git clone --branch $TEST_AUTOMATION_BRANCH *****************:emtrain/test-automation.git && 
        cd test-automation        
  steps:
    - step: &clear-node-modules-cache
        name: Clear node_modules from cache
        condition:
          changesets:
            includePaths:
              - package.json
              - package-lock.json
        script:
          - pipe: atlassian/bitbucket-clear-cache:3.1.1
            variables:
              BITBUCKET_USERNAME: $BITBUCKET_USER_NAME
              BITBUCKET_APP_PASSWORD: $BITBUCKET_APP_PASSWORD
              CACHES: ['node']
    - step: &build-deps
        name: Install & build node dependencies
        caches:
          - node
        script:
          - *npm-commands
        artifacts:
          - dist/**
    - step: &e2e-smoke-test-suite
        name: E2E testing (Smoke Testsuite)
        clone:
          enabled: false
        image: cypress/browsers:node14.15.0-chrome96-ff94
        script:
          - *clone-test-automation-repo
          - *export-test-automation-variables
          - npm install
          - npm run test-smoke
        artifacts:
          - /opt/atlassian/pipelines/agent/build/test-automation/cypress/temp/**          

    - step: &deploy-to-aws-s3-and-invalidate-cloudfront-cache
        name: Deploy to aws s3 bucket & Invalidate cloudfront cache
        clone:
          enabled: false
        script:
          - pipe: atlassian/aws-s3-deploy:1.1.0
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              S3_BUCKET: $S3_BUCKET
              DELETE_FLAG: 'true'
              LOCAL_PATH: 'dist'
          - pipe: atlassian/aws-cloudfront-invalidate:0.6.0
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              DISTRIBUTION_ID: $DISTRIBUTION_ID
        after-script:
          - BUILD_STATUS="Successful"
          - if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then BUILD_STATUS="Failed" ; fi
          - pipe: atlassian/slack-notify:2.0.0
            variables:
              WEBHOOK_URL: $SLACK_WEBHOOK_URL
              PRETEXT: "*Build information for <https://bitbucket.org/$BITBUCKET_WORKSPACE/$BITBUCKET_REPO_SLUG/addon/pipelines/home#!/results/$BITBUCKET_BUILD_NUMBER|Pipeline #$BITBUCKET_BUILD_NUMBER>*"
              MESSAGE: ">Environment: *$ENVIRONMENT*\n>Repository: *$BITBUCKET_REPO_SLUG*\n>Branch: *$BITBUCKET_BRANCH*\n>Build status: *$BUILD_STATUS*"

pipelines:
  # Runs snyk security scan (triggered from snyk web ui only when changes detected in package.json) and cypress automation tests on every pull request
  # pull-requests:
  #   '**':
  #     - step: 
  #         <<: *e2e-smoke-test-suite
  #         after-script:
  #           - BUILD_STATUS="Successful passed e2e smoke testsuite"
  #           - if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then BUILD_STATUS="Failed e2e smoke testsuite" ; fi
  #           - pipe: atlassian/slack-notify:2.0.0
  #             variables:
  #               WEBHOOK_URL: $SLACK_WEBHOOK_URL
  #               PRETEXT: "*Pull Request - E2E Testing (Smoke Testsuite) information for <https://bitbucket.org/$BITBUCKET_WORKSPACE/$BITBUCKET_REPO_SLUG/addon/pipelines/home#!/results/$BITBUCKET_BUILD_NUMBER|Pipeline #$BITBUCKET_BUILD_NUMBER>*"
  #               MESSAGE: ">Repository: *$BITBUCKET_REPO_SLUG*\n>Branch: *$BITBUCKET_BRANCH*\n>Destination Branch: *$BITBUCKET_PR_DESTINATION_BRANCH*\n>Pull Request Id: *$BITBUCKET_PR_ID*\n>Status: *$BUILD_STATUS*"

  
  # Run this pipelines from Bitbucket UI. Select the branch and pipeline.
  custom:
    # PROD Environment
    prod-deploy:
      - step: *clear-node-modules-cache
      - step: *build-deps
      - step:
          <<: *deploy-to-aws-s3-and-invalidate-cloudfront-cache
          deployment: prod

    # Stage environment
    stage-deploy:
      - step: *clear-node-modules-cache
      - step: *build-deps
      - step:
          <<: *deploy-to-aws-s3-and-invalidate-cloudfront-cache
          deployment: stage

    # Deploy on uat environment.
    uat-deploy:
      - step: *clear-node-modules-cache
      - step: *build-deps
      - step:
          <<: *deploy-to-aws-s3-and-invalidate-cloudfront-cache
          deployment: uat
  
  branches:
    # CI Environment
    develop:
      - step: *clear-node-modules-cache
      - step: *build-deps
      - step:
          <<: *deploy-to-aws-s3-and-invalidate-cloudfront-cache
          deployment: ci
   