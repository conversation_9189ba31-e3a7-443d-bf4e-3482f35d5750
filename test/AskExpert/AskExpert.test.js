import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';

import { AskExpertMutation } from '../../src/features/ask-expert/AskExpertMutation.graphql';
import <PERSON><PERSON>x<PERSON> from '../../src/features/ask-expert/AskExpert';

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str) => str,
    };
  },
}));

test('Tests the AskExpert component', async () => {
  const mocks = [{
    request: {
      query: AskExpertMutation,
      variables: { question: 'test question' },
    },
    result: {
      data: {
        askExpert: {
          id: 1,
          question: 'test question',
        },
      },
    },
  }];

  const result = render(<MockedProvider mocks={mocks} addTypename={false}><AskExpert /></MockedProvider>);

  // Make sure all the elements are on the page
  expect(screen.getByText('sideDrawer.askExpert.sendPrompt')).toBeInTheDocument();
  expect(screen.getByText('sideDrawer.askExpert.responsePrompt')).toBeInTheDocument();
  const questionField = screen.getByLabelText('sideDrawer.askExpert.askPrompt', { selector: 'input' });
  // For some reason I had to query the container to find the button
  const submitButton = result.container.querySelector('button[type="submit"]');
  expect(submitButton).not.toEqual(null);

  // Test updating the form.  This test will fail if the input does not match the mock
  fireEvent.change(questionField, { target: { value: 'test question' } }); // Must match mock
  await act(async () => { submitButton.click(); });
  await new Promise((resolve) => setTimeout(resolve, 0)); // Give the test a moment to complete
  expect(screen.getByLabelText('sideDrawer.askExpert.askAnotherPrompt', { selector: 'input' })).not.toEqual(null);
});
