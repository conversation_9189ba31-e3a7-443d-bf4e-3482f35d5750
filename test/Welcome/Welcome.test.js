import React from 'react';
import { render, screen } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';

import { UpdateUserMutation } from '../../src/features/user-profile/UpdateUserMutation.graphql';
import Welcome from '../../src/features/welcome/Welcome';

jest.mock('react-i18next', () => ({
  Trans: (props) => {
    return (props.i18nKey);
  },
}));

jest.mock('../../src/hooks/useUser', () => ({
  useUser: () => {
    return {
      user: {
        displayedWelcome: false,
      },
    };
  },
}));

describe('Welcome component', () => {
  test('first login with assignments', async () => {
    const mocks = [{
      request: {
        query: UpdateUserMutation,
        variables: { displayedWelcome: true },
      },
      result: {
        data: {
          user: {
            id: 1,
            displayedWelcome: true,
          },
        },
      },
    }];

    render(<MockedProvider mocks={mocks} addTypename={false}><Welcome hasTodo /></MockedProvider>);
    expect(screen.getByText('welcome.topWelcomeTitle')).toBeInTheDocument();
    expect(screen.getByText('welcome.topAssignments')).toBeInTheDocument();
    expect(screen.getByText('welcome.bottomHurry')).toBeInTheDocument();
    expect(screen.getByText('welcome.bottomNoProblem')).toBeInTheDocument();
  });

  test('first login without assignments', async () => {
    const mocks = [{
      request: {
        query: UpdateUserMutation,
        variables: { displayedWelcome: true },
      },
      result: {
        data: {
          user: {
            id: 1,
            displayedWelcome: true,
          },
        },
      },
    }];

    render(<MockedProvider mocks={mocks} addTypename={false}><Welcome hasTodo={false} /></MockedProvider>);
    expect(screen.getByText('welcome.topWelcomeTitle')).toBeInTheDocument();
    expect(screen.getByText('welcome.topNoAssignments')).toBeInTheDocument();
    expect(screen.getByText('welcome.topShortVideo')).toBeInTheDocument();
  });
});
