import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import GetLessonCardsQuery from '../../src/features/lessons/GetLessonCardsQuery.graphql';
import SaveAnswerMutation from '../../src/features/lessons/lessonCards/SaveAnswerMutation.graphql';
import BooleanCard from '../../src/features/lessons/LessonCards/BooleanCard/BooleanCard';
import BooleanResultCard from '../../src/features/lessons/ResultCards/BooleanResultCard/BooleanResultCard';
import MockTheme from '../__mocks__/MockTheme';

const mocks = [
  {
    request: {
      query: GetLessonCardsQuery,
      variables: { lessonId: 1361 },
    },
    result: {
      data: {
        getLessonCards: [
          {
            id: 121,
            title: 'This is a boolean quiz card',
            description: '<p>This describes a boolean quiz card</p>',
            type: 'BOOLEAN',
            booleanLabel: { falseText: 'No', trueText: 'Yes' },
            answer: { booleanAnswer: true },
          },
          {
            id: 120,
            title: 'End of Lesson',
            description: '<p>This is the end of lesson card</p>',
            type: 'END_OF_LESSON',
          },
        ],
      },
    },
  },
  {
    request: {
      query: SaveAnswerMutation,
      variables: { answer: { type: 'BOOLEAN', booleanAnswer: true } },
    },
    result: {
      data: {
        saveAnswer: {
          id: 20,
          booleanAnswer: true,
        },
      },
    },
  },
];

test('Render a boolean card', async () => {
  render(
    <MockedProvider mocks={mocks} addTypename={false}>
      <MockTheme>
        <BooleanCard
          id={mocks[0].result.data.getLessonCards[0].id}
          title={mocks[0].result.data.getLessonCards[0].title}
          description={mocks[0].result.data.getLessonCards[0].description}
          type={mocks[0].result.data.getLessonCards[0].type}
          booleanLabel={mocks[0].result.data.getLessonCards[0].booleanLabel}
        />
      </MockTheme>
    </MockedProvider>,
  );
  await new Promise((resolve) => setTimeout(resolve, 0)); // wait for mock to load.

  // Check that the title is present:
  const cardTitle = document.querySelector('h3').innerHTML;
  expect(cardTitle).toEqual('This is a boolean quiz card');
  // Check that the description is present
  expect(screen.getByText('This describes a boolean quiz card')).toBeInTheDocument();
  const buttonText = document.querySelector('button').innerHTML;
  expect(buttonText).toEqual('Submit');

  const submitButton = document.querySelector('button[type="button"]');
  expect(submitButton).not.toEqual(null);

  await act(async () => { submitButton.click(); });
  await new Promise((resolve) => setTimeout(resolve, 0)); // Give the test a moment to complete
});

test.skip('Render a boolean results card', async () => {
  // render(
  //   <MockedProvider mocks={mocks} addTypename={false}>
  //     <MockTheme>
  //       <BooleanResultCard
  //         id={mocks[0].result.data.getLessonCards[0].id}
  //         title={mocks[0].result.data.getLessonCards[0].title}
  //         description={mocks[0].result.data.getLessonCards[0].description}
  //         booleanLabel={mocks[0].result.data.getLessonCards[0].booleanLabel}
  //         answer={mocks[0].result.data.getLessonCards[0].answer}
  //       />
  //     </MockTheme>
  //   </MockedProvider>,
  // );
  // await new Promise((resolve) => setTimeout(resolve, 0)); // wait for mock to load.
  // screen.debug();
});
