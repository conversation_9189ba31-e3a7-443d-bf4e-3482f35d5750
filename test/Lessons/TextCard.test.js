import React from 'react';
import { render, screen } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import TextCard from '../../src/features/lessons/LessonCards/TextCard/TextCard';
import GetLessonCardsQuery from '../../src/features/lessons/GetLessonCardsQuery.graphql';

// Test rendering a text card:
test('Can render a text card component', async () => {
  render(
    <TextCard
      id="1"
      description="Card Description"
      type="TEXT"
      title="Card Title"
    />,
  );
  // Check that the title is present:
  const cardTitle = document.querySelector('h3').innerHTML;
  expect(cardTitle).toEqual('Card Title');
  // Check that the description is present
  expect(screen.getByText('Card Description')).toBeInTheDocument();
});

// Test rendering text card using mocks:
test('Can render a text card component using mocks', async () => {
  const mocks = [
    {
      request: {
        query: GetLessonCardsQuery,
        variables: { lessonId: 1 },
      },
      result: {
        data: {
          getLessonCards: [
            {
              id: 1,
              description: '<p>Mocked Description</p>',
              type: 'TEXT',
              title: 'Mocked Title',
            },
          ],
        },
      },
    },
  ];
  render(
    <MockedProvider mocks={mocks} addTypename={false}>
      <TextCard
        id={mocks[0].result.data.getLessonCards[0].id}
        description={mocks[0].result.data.getLessonCards[0].description}
        type={mocks[0].result.data.getLessonCards[0].type}
        title={mocks[0].result.data.getLessonCards[0].title}
      />
    </MockedProvider>,
  );
  await new Promise((resolve) => setTimeout(resolve, 0)); // wait for mock to load.
  // Check that the title is present:
  const cardTitle = document.querySelector('h3').innerHTML;
  expect(cardTitle).toEqual('Mocked Title');
  // Check that the description is present
  expect(screen.getByText('Mocked Description')).toBeInTheDocument();
});
