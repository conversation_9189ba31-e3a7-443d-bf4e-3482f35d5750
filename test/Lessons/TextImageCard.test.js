import React from 'react';
import { render, screen } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import GetLessonCardsQuery from '../../src/features/lessons/GetLessonCardsQuery.graphql';
import TextImageCard from '../../src/features/lessons/LessonCards/TextImageCard/TextImageCard';

const mocks = [
  {
    request: {
      query: GetLessonCardsQuery,
      variables: { lessonId: 1361 },
    },
    result: {
      data: {
        getLessonCards: [
          {
            id: 101,
            title: 'This card has text and an image',
            description: '<p>This describes a text with image card</p>',
            type: 'TEXT_WITH_IMAGE',
            imageUrl: 'https://s3.emtrain.amazon.com/1234567.jpg',
            imageAltText: 'Display this text if there is no image',
          },
          {
            id: 100,
            title: 'End of Lesson',
            description: '<p>This is the end of lesson card</p>',
            type: 'END_OF_LESSON',
          },
        ],
      },
    },
  },
];

test('Render a text with image card', async () => {
  render(
    <MockedProvider mocks={mocks} addTypename={false}>
      <TextImageCard
        id={mocks[0].result.data.getLessonCards[0].id}
        title={mocks[0].result.data.getLessonCards[0].title}
        description={mocks[0].result.data.getLessonCards[0].description}
        type={mocks[0].result.data.getLessonCards[0].type}
        imageUrl={mocks[0].result.data.getLessonCards[0].imageUrl}
        imageAltText={mocks[0].result.data.getLessonCards[0].imageAltText}
      />
    </MockedProvider>,
  );
  await new Promise((resolve) => setTimeout(resolve, 0)); // wait for mock to load.

  // Check that the title is present:
  const cardTitle = document.querySelector('h3').innerHTML;
  expect(cardTitle).toEqual('This card has text and an image');
  // Check that the description is present
  expect(screen.getByText('This describes a text with image card')).toBeInTheDocument();

  // find the image and check the src property.
  const image = document.querySelector('img');
  expect(image.src).toEqual(mocks[0].result.data.getLessonCards[0].imageUrl);
});
