import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { DrawerProvider } from '../../src/hooks/useDrawer';

import AskTopicExpertButton from '../../src/features/lessons/AskTopicExpertButton';

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str) => str,
    };
  },
}));

test('Tests the AskTopicExpert component', async () => {
  const openDrawer = jest.fn();
  const result = render(<DrawerProvider value={{ openDrawer }}><AskTopicExpertButton /></DrawerProvider>);

  // Make sure all the elements are on the page
  expect(screen.getByText('lessons.askExpertTitle')).toBeInTheDocument();
  expect(result.container.querySelector('#AskAnExpertIcon')).not.toEqual(undefined);
  const clickableDiv = result.container.querySelector('.MuiPaper-root');
  expect(clickableDiv).not.toEqual(null);

  // Test clicking the button
  await act(async () => { clickableDiv.click(); });
  await new Promise((resolve) => setTimeout(resolve, 0)); // Give the test a moment to complete
  expect(openDrawer).toHaveBeenCalledWith('expert'); // Mock the context function that is called
});
