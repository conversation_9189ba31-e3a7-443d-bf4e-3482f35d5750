import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

import Footer from '../../src/features/footer/Footer';

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str) => str,
    };
  },
}));

test('Tests the Footer component', async () => {
  render(<BrowserRouter><Footer /></BrowserRouter>);

  // Make sure all the elements are on the page
  expect(screen.getByText(/Emtrain/i)).toBeInTheDocument();

  // For translated text, we look for the tags
  expect(screen.getByText(/footer.privacyPolicy/i)).toBeInTheDocument();
  expect(screen.getByText(/footer.serviceTerms/i)).toBeInTheDocument();
  expect(screen.getByText(/footer.accessibility/i)).toBeInTheDocument();
});
