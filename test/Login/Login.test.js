import React from 'react';
import { render, screen } from '@testing-library/react';

import Login from '../../src/features/authentication/Login.js';
import { AuthProvider } from '../../src/hooks/useAuth';

test('Displays login form', async () => {
  render(<AuthProvider><Login /></AuthProvider>);
  expect(screen.getByRole('heading', { name: /Login/i })).toBeInTheDocument();
  expect(screen.getByRole('textbox', { name: /Email/i })).toBeInTheDocument();
  expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
  expect(screen.getByRole('button', { name: /Submit/i })).toBeInTheDocument();
});

test.skip('Logs in and stores the token', () => {

});
