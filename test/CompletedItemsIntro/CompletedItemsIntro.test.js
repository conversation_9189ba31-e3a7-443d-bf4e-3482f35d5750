import React from 'react';
import { render, screen } from '@testing-library/react';

import CompletedItemsIntro from '../../src/features/completed-items/CompletedItemsIntro';

jest.mock('react-i18next', () => ({
  Trans: (props) => {
    return (props.i18nKey);
  },
}));

describe('Completed items intro component', () => {
  test('first test with no completed assignments', async () => {
    render(<CompletedItemsIntro itemCount={0} />);
    expect(screen.getByText('completedItems.completeTodoPrompt')).toBeInTheDocument();
  });

  test('Next test with completed items present', async () => {
    render(<CompletedItemsIntro itemCount={5} />);
    expect(screen.getByText('completedItems.selectCompletedList')).toBeInTheDocument();
  });
});
