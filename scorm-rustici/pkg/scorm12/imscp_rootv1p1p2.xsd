<?xml version="1.0"?>

<!-- edited with XML Spy v3.5 (http://www.xmlspy.com) by <PERSON> (private) -->
<!-- filename=ims_cp_rootv1p1p2.xsd -->
<!-- Copyright (2) 2001 IMS Global Learning Consortium, Inc. -->
<!-- edited by <PERSON>  -->
<!-- Conforms to w3c http://www.w3.org/TR/xmlschema-1/ 2000-10-24-->

<xsd:schema xmlns="http://www.imsproject.org/xsd/imscp_rootv1p1p2" 
            targetNamespace="http://www.imsproject.org/xsd/imscp_rootv1p1p2" 
            xmlns:xml="http://www.w3.org/XML/1998/namespace" 
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
            elementFormDefault="unqualified" version="IMS CP 1.1.2">

   <!-- ******************** -->
   <!-- ** Change History ** -->
   <!-- ******************** -->
   <xsd:annotation>
      <xsd:documentation xml:lang="en">DRAFT XSD for IMS Content Packaging version 1.1 DRAFT</xsd:documentation>
      <xsd:documentation> Copyright (c) 2001 IMS GLC, Inc. </xsd:documentation>
      <xsd:documentation>2000-04-21, Adjustments by T.D. Wason from CP 1.0.</xsd:documentation>
      <xsd:documentation>2001-02-22, T.D.Wason: Modify for 2000-10-24 XML-Schema version.  Modified to support extension.</xsd:documentation>
      <xsd:documentation>2001-03-12, T.D.Wason: Change filename, target and meta-data namespaces and meta-data fielname.  Add meta-data to itemType, fileType and organizationType.</xsd:documentation>
      <xsd:documentation>Do not define namespaces for xml in XML instances generated from this xsd.</xsd:documentation>
      <xsd:documentation>Imports IMS meta-data xsd, lower case element names.         </xsd:documentation>
      <xsd:documentation>This XSD provides a reference to the IMS meta-data root element as imsmd:record</xsd:documentation>
      <xsd:documentation>If the IMS meta-data is to be used in the XML instance then the instance must define an IMS meta-data prefix with a namespace.  The meta-data targetNamespace should be used.  </xsd:documentation>
      <xsd:documentation>2001-03-20, Thor Anderson: Remove manifestref, change resourceref back to identifierref, change manifest back to contained by manifest. --Tom Wason: manifest may contain _none_ or more manifests.</xsd:documentation>
      <xsd:documentation>2001-04-13 Tom Wason: corrected attirbute name structure.  Was misnamed type.  </xsd:documentation>
      <xsd:documentation>2001-05-14 Schawn Thropp: Made all complexType extensible with the group.any</xsd:documentation>
      <xsd:documentation>Added the anyAttribute to all complexTypes. Changed the href attribute on the fileType and resourceType to xsd:string</xsd:documentation>
      <xsd:documentation>Changed the maxLength of the href, identifierref, parameters, structure attributes to match the Information model.</xsd:documentation>
      <xsd:documentation>2001-07-25 Schawn Thropp: Changed the namespace for the Schema of Schemas to the 5/2/2001 W3C XML Schema</xsd:documentation> 
      <xsd:documentation>Recommendation. attributeGroup attr.imsmd deleted, was not used anywhere.  Any attribute declarations that have</xsd:documentation>
      <xsd:documentation>use = "default" changed to use="optional" - attr.structure.req.</xsd:documentation>
      <xsd:documentation>Any attribute declarations that have value="somevalue" changed to default="somevalue",</xsd:documentation>
      <xsd:documentation>attr.structure.req (hierarchical).  Removed references to IMS MD Version 1.1.</xsd:documentation>
      <xsd:documentation>Modified attribute group "attr.resourcetype.req" to change use from optional</xsd:documentation>
      <xsd:documentation>to required to match the information model.  As a result the default value also needed to be removed</xsd:documentation> 
      <xsd:documentation>Name change for XSD.  Changed to match version of CP Spec                                           </xsd:documentation> 
   </xsd:annotation>

   <xsd:annotation>
      <xsd:documentation>Inclusions and Imports</xsd:documentation>
   </xsd:annotation>

   <xsd:import namespace="http://www.w3.org/XML/1998/namespace" schemaLocation="ims_xml.xsd"/>

   <xsd:annotation>
      <xsd:documentation>Attribute Declarations</xsd:documentation>
   </xsd:annotation>

   <!-- **************************** -->
   <!-- ** Attribute Declarations ** -->
   <!-- **************************** -->
   <xsd:attributeGroup name="attr.base">
      <xsd:attribute ref="xml:base" use="optional"/>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.default">
      <xsd:attribute name="default" type="xsd:IDREF" use="optional"/>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.href">
      <xsd:attribute name="href" use="optional">
         <xsd:simpleType>
            <xsd:restriction base="xsd:anyURI">
               <xsd:maxLength value="2000"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.href.req">
      <xsd:attribute name="href" use="required">
         <xsd:simpleType>
            <xsd:restriction base="xsd:anyURI">
               <xsd:maxLength value="2000"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup> 

   <xsd:attributeGroup name="attr.identifier.req">
      <xsd:attribute name="identifier" type="xsd:ID" use="required"/>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.identifier">
      <xsd:attribute name="identifier" type="xsd:ID" use="optional"/>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.isvisible">
      <xsd:attribute name="isvisible" type="xsd:boolean" use="optional"/>
   </xsd:attributeGroup>
   
   <xsd:attributeGroup name="attr.parameters">
      <xsd:attribute name="parameters" use="optional">
         <xsd:simpleType>
            <xsd:restriction base="xsd:string">
               <xsd:maxLength value="1000"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>
   
   <xsd:attributeGroup name="attr.identifierref">
      <xsd:attribute name="identifierref" use="optional">
         <xsd:simpleType>
            <xsd:restriction base="xsd:string">
               <xsd:maxLength value="2000"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>
   
   <xsd:attributeGroup name="attr.identifierref.req">
      <xsd:attribute name="identifierref" use="required">
         <xsd:simpleType>
            <xsd:restriction base="xsd:string">
               <xsd:maxLength value="2000"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>
                
   <xsd:attributeGroup name="attr.resourcetype.req">
      <xsd:attribute name="type" use="required">
         <xsd:simpleType>
            <xsd:restriction base="xsd:string">
               <xsd:maxLength value="1000"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.structure.req">
      <xsd:attribute name="structure" use="optional" default="hierarchical">
         <xsd:simpleType>
            <xsd:restriction base="xsd:string">
               <xsd:maxLength value="200"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>

   <xsd:attributeGroup name="attr.version">
      <xsd:attribute name="version" use="optional">
         <xsd:simpleType>
            <xsd:restriction base="xsd:string">
               <xsd:maxLength value="20"/>
            </xsd:restriction>
         </xsd:simpleType>
      </xsd:attribute>
   </xsd:attributeGroup>

   <xsd:annotation>
       <xsd:documentation>element groups</xsd:documentation>
   </xsd:annotation>

   <xsd:group name="grp.any">
      <xsd:annotation>
         <xsd:documentation>Any namespaced element from any namespace may be included within an &quot;any&quot; element.  The namespace for the imported element must be defined in the instance, and the schema must be imported.  </xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
         <xsd:any namespace="##other" processContents="strict" minOccurs="0" maxOccurs="unbounded"/>
      </xsd:sequence>
   </xsd:group>

   <!-- ************************** -->
   <!-- ** Element Declarations ** -->
   <!-- ************************** -->

   <xsd:element name="dependency" type="dependencyType"/>
   <xsd:element name="file" type="fileType"/>
   <xsd:element name="item" type="itemType"/>
   <xsd:element name="manifest" type="manifestType"/>
   <xsd:element name="metadata" type="metadataType"/>
   <xsd:element name="organization" type="organizationType"/>
   <xsd:element name="organizations" type="organizationsType"/>
   <xsd:element name="resource" type="resourceType"/>
   <xsd:element name="resources" type="resourcesType"/>
   <xsd:element name="schema" type="schemaType"/>
   <xsd:element name="schemaversion" type="schemaversionType"/>
   <xsd:element name="title" type="titleType"/>

   <!-- ******************* -->
   <!-- ** Complex Types ** -->
   <!-- ******************* -->

   <!-- **************** -->
   <!-- ** dependency ** -->
   <!-- **************** -->
   <xsd:complexType name="dependencyType">
      <xsd:sequence>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.identifierref.req"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- ********** -->
   <!-- ** file ** -->
   <!-- ********** -->
   <xsd:complexType name="fileType">
      <xsd:sequence>
         <xsd:element ref="metadata" minOccurs="0"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.href.req"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- ********** -->
   <!-- ** item ** -->
   <!-- ********** -->
   <xsd:complexType name="itemType">
      <xsd:sequence>
         <xsd:element ref="title" minOccurs="0"/>
         <xsd:element ref="item" minOccurs="0" maxOccurs="unbounded"/>
         <xsd:element ref="metadata" minOccurs="0"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.identifier.req"/>
      <xsd:attributeGroup ref="attr.identifierref"/>
      <xsd:attributeGroup ref="attr.isvisible"/>
      <xsd:attributeGroup ref="attr.parameters"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- ************** -->
   <!-- ** manifest ** -->
   <!-- ************** -->
   <xsd:complexType name="manifestType">
      <xsd:sequence>
         <xsd:element ref="metadata" minOccurs="0"/>
         <xsd:element ref="organizations"/>
         <xsd:element ref="resources"/>
         <xsd:element ref="manifest" minOccurs="0" maxOccurs="unbounded"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.identifier.req"/>
      <xsd:attributeGroup ref="attr.version"/>
      <xsd:attribute ref="xml:base"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- ************** -->
   <!-- ** metadata ** -->
   <!-- ************** -->
   <xsd:complexType name="metadataType">
      <xsd:sequence>
         <xsd:element ref="schema" minOccurs="0"/>
         <xsd:element ref="schemaversion" minOccurs="0"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
   </xsd:complexType>
   
   <!-- ******************* -->
   <!-- ** organizations ** -->
   <!-- ******************* -->
   <xsd:complexType name="organizationsType">
      <xsd:sequence>
         <xsd:element ref="organization" minOccurs="0" maxOccurs="unbounded"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.default"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- ****************** -->
   <!-- ** organization ** -->
   <!-- ****************** -->
   <xsd:complexType name="organizationType">
      <xsd:sequence>
         <xsd:element ref="title" minOccurs="0"/>
         <xsd:element ref="item" minOccurs="0" maxOccurs="unbounded"/>
         <xsd:element ref="metadata" minOccurs="0"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.identifier.req"/>
      <xsd:attributeGroup ref="attr.structure.req"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- *************** -->
   <!-- ** resources ** -->
   <!-- *************** -->
   <xsd:complexType name="resourcesType">
      <xsd:sequence>
          <xsd:element ref="resource" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.base"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>
   
   <!-- ************** -->
   <!-- ** resource ** -->
   <!-- ************** -->
   <xsd:complexType name="resourceType">
      <xsd:sequence>
         <xsd:element ref="metadata" minOccurs="0"/>
         <xsd:element ref="file" minOccurs="0" maxOccurs="unbounded"/>
         <xsd:element ref="dependency" minOccurs="0" maxOccurs="unbounded"/>
         <xsd:group ref="grp.any"/>
      </xsd:sequence>
      <xsd:attributeGroup ref="attr.identifier.req"/>
      <xsd:attributeGroup ref="attr.resourcetype.req"/>
      <xsd:attributeGroup ref="attr.base"/>
      <xsd:attributeGroup ref="attr.href"/>
      <xsd:anyAttribute namespace="##other" processContents="strict"/>
   </xsd:complexType>

   <!-- ****************** -->
   <!-- ** Simple Types ** -->
   <!-- ****************** -->

   <!-- ************ -->
   <!-- ** schema ** -->
   <!-- ************ -->
   <xsd:simpleType name="schemaType">
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="100"/>
      </xsd:restriction>
   </xsd:simpleType>
   
   <!-- ******************* -->
   <!-- ** schemaversion ** -->
   <!-- ******************* -->
   <xsd:simpleType name="schemaversionType">
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="20"/>
      </xsd:restriction>
   </xsd:simpleType>
   
   <!-- *********** -->
   <!-- ** title ** -->
   <!-- *********** -->
   <xsd:simpleType name="titleType">
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="200"/>
      </xsd:restriction>
   </xsd:simpleType>

</xsd:schema>
