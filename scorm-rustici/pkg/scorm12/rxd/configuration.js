var RXDConfig = {
  // the entry URL to the content
  contentUrl: "https://emtrain.edge.emtrain.com/scorm/programs/25?API_KEY=177c2718d0524052a5237b36c1a47aab&INT_KEY=6ab6c602fbf541aebe5f44687b53a9d4",
  // contentUrl: "https://emtrain.edge.emtrain.com/scorm/microlessons/66",
  // contentUrl: "https://emtrain.edge.emtrain.com/scorm-rustici/rxd-sample",

  // value to use as the HTML title for the proxy package
  courseTitle: "Preventing Workplace Harassment",

  // URL where the RXD files are located and available via http/s
  rxdHostUrl: "https://emtrain.edge.emtrain.com/scorm-rustici/rxd",

  // the path to the content API wrapper page, appended to the rxdHostUrl
  // unless it is an absolute http/s URL
  contentApi: "contentAPI.html",

  // URL to fetch pre-launch configuration settings from
  preLaunchConfigurationUrl: ""
};
