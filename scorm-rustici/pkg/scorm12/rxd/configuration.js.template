var RXDConfig = {
    // the entry URL to the content
    contentUrl: "https://emtrain.edge.emtrain.com/scorm/lesson1",

    // value to use as the HTML title for the proxy package
    courseTitle: "This is the course title",

    // URL where the RXD files are located and available via http/s
    rxdHostUrl: "https://edge.emtrain.com/scorm-rustici/rxd",

    // the path to the content API wrapper page, appended to the rxdHostUrl
    // unless it is an absolute http/s URL
    contentApi: "contentAPI.html",

    // URL to fetch pre-launch configuration settings from
    preLaunchConfigurationUrl: ""
};
