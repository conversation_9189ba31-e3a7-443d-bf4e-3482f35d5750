<?xml version="1.0" ?>
<manifest identifier="MANIFEST-EMTRAIN-BULLY-AT-WORK-2019-05-06" version="1"
       xmlns="http://www.imsproject.org/xsd/imscp_rootv1p1p2"
       xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_rootv1p2"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.imsproject.org/xsd/imscp_rootv1p1p2 imscp_rootv1p1p2.xsd
                           http://www.imsglobal.org/xsd/imsmd_rootv1p2p1 imsmd_rootv1p2p1.xsd
                           http://www.adlnet.org/xsd/adlcp_rootv1p2 adlcp_rootv1p2.xsd">
  <metadata>
    <schema>ADL SCORM</schema>
    <schemaversion>1.2</schemaversion>
    <adlcp:location>metadata.xml</adlcp:location>
  </metadata>
  <organizations default="B0">
    <organization identifier="B0">

      <!--****** Title ******-->
      <title><![CDATA[Preventing Workplace Harassment]]></title>

      <item identifier="i1" identifierref="r1" isvisible="true">

        <!--****** Title ******-->
        <title><![CDATA[Preventing Workplace Harassment]]></title>

        <metadata>
          <lom xmlns="http://ltsc.ieee.org/xsd/LOM" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://ltsc.ieee.org/xsd/LOM lom.xsd">
            <general>
              <description>
                <string language="en-us"><![CDATA[This is a Rustici Cross Domain package which targets the course Preventing Workplace Harassment]]></string>
              </description>
            </general>
          </lom>
        </metadata>

      </item>

      <metadata>
        <schema>ADL SCORM</schema>
        <schemaversion>1.2</schemaversion>
        <adlcp:location>metadata.xml</adlcp:location>
      </metadata>
    </organization>
  </organizations>
  <resources>
    <resource identifier="r1" type="webcontent" adlcp:scormtype="sco" href="rxd/indexAPI.html">
      <metadata>
        <schema>ADL SCORM</schema>
        <schemaversion>1.2</schemaversion>
        <adlcp:location>metadata.xml</adlcp:location>
      </metadata>

      <!--****** FILES ******-->
      <file href="rxd/indexAPI.html"/>
      <file href="rxd/blank.html"/>
      <file href="rxd/goodbye.html"/>
      <file href="rxd/configuration.js"/>
    </resource>
  </resources>
</manifest>
