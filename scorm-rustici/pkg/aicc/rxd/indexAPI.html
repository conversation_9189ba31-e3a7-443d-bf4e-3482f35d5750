<!DOCTYPE html>
<html lang="en">

<!-- Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c -->
<head>
    <title>Rustici Cross Domain</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <style type="text/css">
        html, body, #scormdriver_content {
            overflow: hidden;
            height: 100%;
            margin: 0;
            border: none;
        }
    </style>
    <script src="configuration.js"></script>
    <script>
        if (typeof RXDConfig === "undefined") {
            alert("Error loading configuration settings (RXDConfig undefined), launch cannot continue");
        }
        else if (typeof RXDConfig.rxdHostUrl === "undefined" || RXDConfig.rxdHostUrl === "") {
            alert("Error loading configuration settings (rxdHostUrl invalid), launch cannot continue");
        }
        else if (typeof RXDConfig.contentUrl === "undefined" || RXDConfig.contentUrl === "") {
            alert("Error loading configuration settings (contentUrl invalid), launch cannot continue");
        }
        else {
            if (RXDConfig.rxdHostUrl.slice(-1) !== "/") {
                RXDConfig.rxdHostUrl += "/";
            }

            if (window.location.protocol === "https:") {
                RXDConfig.rxdHostUrl = RXDConfig.rxdHostUrl.replace("http:", "https:");
            }
            else {
                RXDConfig.rxdHostUrl = RXDConfig.rxdHostUrl.replace("https:", "http:");
            }

            document.title = typeof RXDConfig.courseTitle !== "undefined" ? RXDConfig.courseTitle : "Rustici Cross Domain";

            RXDConfig._includeScript = function (src) {
                var headElement = document.getElementsByTagName("head").item(0),
                    scriptElement = document.createElement("script");

                scriptElement.src = RXDConfig.rxdHostUrl + src;

                headElement.appendChild(scriptElement);
            };

            RXDConfig._includeScript("rustici-driver-aicc.min.js?e51db7282aa82cc09edd3de18d47c90ff579580c");
            RXDConfig._includeScript("rustici-xd-pkg.min.js?e51db7282aa82cc09edd3de18d47c90ff579580c");

            window.IndexApiStart = function () {
                // wait for Rustici Driver and proxy package support script to be available,
                // both loaded asynchronously, Start will call LoadContent
                if (typeof Start === "undefined" || typeof LoadContent === "undefined") {
                    setTimeout(window.IndexApiStart, 200);
                    return;
                }

                Start();
            };

            window.IndexApiUnload = function () {
                Unload();
            };
        }
    </script>
</head>

<body onload="IndexApiStart()" onbeforeunload="IndexApiUnload()" onunload="IndexApiUnload()">
    <!-- Don't change the names of these frames below. Doing so may break functionality -->
    <iframe name="scormdriver_content" id="scormdriver_content" src="blank.html" title="Course content"></iframe>
    <iframe name="AICCComm" src="AICCComm.html" title="Intentionally blank" aria-hidden="true" style="display: none;"></iframe>
    <iframe name="rusticisoftware_aicc_results" src="blank.html" title="Intentionally blank" aria-hidden="true" style="display: none;"></iframe>
</body>
</html>
