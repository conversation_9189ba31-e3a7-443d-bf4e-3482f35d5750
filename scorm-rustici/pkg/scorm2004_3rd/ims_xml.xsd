<?xml version="1.0" encoding="UTF-8"?>
<!-- filename=ims_xml.xsd -->

<xsd:schema xmlns="http://www.w3.org/XML/1998/namespace" 
            targetNamespace="http://www.w3.org/XML/1998/namespace" 
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
            elementFormDefault="qualified">

   <!-- CHANGE HISTORY -->
   <xsd:annotation>
      <xsd:documentation>2001-02-22: <PERSON> initial creation                  </xsd:documentation>
      <xsd:documentation>In namespace-aware XML processors, the &quot;xml&quot;     </xsd:documentation>
      <xsd:documentation>prefix is bound to the namespace name http://www.w3.org/XML/1998/namespace.</xsd:documentation>
      <xsd:documentation>Do not reference this file in XML instances                </xsd:documentation>
      <xsd:documentation><PERSON><PERSON><PERSON> Thropp: Changed the uriReference type to string type</xsd:documentation>
      <xsd:documentation>2001-07-26: S Thropp: Changed the XSD namespace to point to</xsd:documentation>
      <xsd:documentation>Schema of schemas for the 5/2/2001 W3C Recommendation      </xsd:documentation>
      <xsd:documentation>Changed the XSD types for base and link to xsd:anyURI      </xsd:documentation>
   </xsd:annotation>

   <xsd:attribute name="lang" type="xsd:language">
      <xsd:annotation>
         <xsd:documentation>Refers to universal XML 1.0 lang attribute</xsd:documentation>
      </xsd:annotation>
   </xsd:attribute>

   <xsd:attribute name="base" type="xsd:anyURI">
      <xsd:annotation>
         <xsd:documentation>Refers to XML Base: http://www.w3.org/TR/xmlbase</xsd:documentation>
      </xsd:annotation>
   </xsd:attribute>

   <xsd:attribute name="link" type="xsd:anyURI"/>

</xsd:schema>