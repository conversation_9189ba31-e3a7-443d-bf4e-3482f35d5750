<xs:schema targetNamespace="http://ltsc.ieee.org/xsd/LOM"
           xmlns="http://ltsc.ieee.org/xsd/LOM"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           version="IEEE LTSC LOM XML 1.0">

   <xs:annotation>
      <xs:documentation>
         This work is licensed under the Creative Commons Attribution-ShareAlike
         License.  To view a copy of this license, see the file license.txt,
         visit http://creativecommons.org/licenses/by-sa/2.0 or send a letter to
         Creative Commons, 559 Nathan <PERSON>, Stanford, California 94305, USA.
      </xs:documentation>
      <xs:documentation>
         This file represents a composite schema for validating
         LOM XML Instances.  This file is built by default to represent a 
         composite schema for validation of the following:          

         1) The use of LOMv1.0 base schema (i.e., 1484.12.1-2002) vocabulary
            source/value pairs only
         2) Uniqueness constraints defined by LOMv1.0 base schema
         3) No existenace of any defined extensions:
            LOMv1.0 base schema XML element extension,
            LOMv1.0 base schema XML attribute extension and 
            LOMv1.0 base schema vocabulary data type extension

         Alternative composite schemas can be assembled by selecting
         from the various alternative component schema listed below.
      </xs:documentation>
   </xs:annotation>

   <!-- Learning Object Metadata -->

   <xs:include schemaLocation="common/anyElement.xsd"/>

   <!-- LOM data element uniqueness constraints:  use one of the following         -->
   <!-- Use unique/loose.xsd to relax element uniqueness constraints               -->
   <!-- Use unique/strict.xsd to enforce element uniqueness constraints            -->

   <!-- <xs:import namespace="http://ltsc.ieee.org/xsd/LOM/unique"
              schemaLocation="unique/loose.xsd"/> -->

   <xs:import namespace="http://ltsc.ieee.org/xsd/LOM/unique"
              schemaLocation="unique/strict.xsd"/>

   <!-- Vocabulary value validation:  use one of the following                     -->
   <!-- Use vocab/loose.xsd to relax vocabulary value constraints                  -->
   <!-- Use vocab/strict.xsd to enforce the LOMv1.0 base schema vocabulary values  -->
   <!-- Use vocab/custom.xsd to enforce custom vocabulary values                   -->

   <!--<xs:import namespace="http://ltsc.ieee.org/xsd/LOM/vocab"
              schemaLocation="vocab/loose.xsd"/> -->

   <xs:import namespace="http://ltsc.ieee.org/xsd/LOM/vocab"
              schemaLocation="vocab/strict.xsd"/> 

   <!-- <xs:import namespace="http://ltsc.ieee.org/xsd/LOM/vocab"
              schemaLocation="vocab/custom.xsd"/> -->

   <!-- Extension elements/attributes support:  use one of the following           -->
   <!-- Use extend/strict.xsd to enforce no element/attribute extension            -->
   <!-- Use extend/custom.xsd to allow element/attribute extension                 -->

   <xs:import namespace="http://ltsc.ieee.org/xsd/LOM/extend"
             schemaLocation="extend/strict.xsd"/>  

   <!--<xs:import namespace="http://ltsc.ieee.org/xsd/LOM/extend"
              schemaLocation="extend/custom.xsd"/> -->

   <xs:include schemaLocation="common/dataTypes.xsd"/>
   <xs:include schemaLocation="common/elementNames.xsd"/>
   <xs:include schemaLocation="common/elementTypes.xsd"/>
   <xs:include schemaLocation="common/rootElement.xsd"/>
   <xs:include schemaLocation="common/vocabValues.xsd"/>
   <xs:include schemaLocation="common/vocabTypes.xsd"/>

</xs:schema>
