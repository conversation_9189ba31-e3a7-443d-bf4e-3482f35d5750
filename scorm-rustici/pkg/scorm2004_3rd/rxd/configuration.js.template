var RXDConfig = {
    // the entry URL to the content
    contentUrl: "{CONTENT_URL}",

    // value to use as the HTML title for the proxy package
    courseTitle: "{COURSE_TITLE}",

    // URL where the RXD files are located and available via http/s
    rxdHostUrl: "{RXD_HOST_URL}",

    // the path to the content API wrapper page, appended to the rxdHostUrl
    // unless it is an absolute http/s URL
    contentApi: "contentAPI.html",

    // URL to fetch pre-launch configuration settings from
    preLaunchConfigurationUrl: "{PRE_LAUNCH_CONFIGURATION_URL}"
};
