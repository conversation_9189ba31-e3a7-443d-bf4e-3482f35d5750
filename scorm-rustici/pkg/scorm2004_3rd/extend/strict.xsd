<xs:schema targetNamespace="http://ltsc.ieee.org/xsd/LOM/extend"
           xmlns="http://ltsc.ieee.org/xsd/LOM/extend"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           version="IEEE LTSC LOM XML 1.0">

  <xs:annotation>
    <xs:documentation>
       This work is licensed under the Creative Commons Attribution-ShareAlike
       License.  To view a copy of this license, see the file license.txt,
       visit http://creativecommons.org/licenses/by-sa/2.0 or send a letter to
       Creative Commons, 559 Nathan <PERSON> Way, Stanford, California 94305, USA.
    </xs:documentation>

    <xs:documentation> 
       This component schema definition defines the XML Schema content model groups 
       customElements and customAttributes. The content model groups are defined as 
       empty groups and are used in conjunction with other component XSDs to support 
       the validation of strictly conforming LOM data elements. 

       This component XSD should be used if extensions are not to be supported in 
       LOM XML instances.
    </xs:documentation>
  </xs:annotation>

  <!-- Model group declarations -->

  <xs:group name="customElements">
    <xs:choice/>
  </xs:group>

  <xs:attributeGroup name="customAttributes"/>

</xs:schema>
