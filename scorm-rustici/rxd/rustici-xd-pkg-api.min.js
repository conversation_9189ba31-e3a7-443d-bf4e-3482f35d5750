/*! Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c */

!function(){var i,c,r,a,o,e,s,n,d={};!function(){var e,n,t,o=document.location.search.substring(1).split("&");for(e=0;e<o.length;e+=1)n=o[e].split("=",2),d[n[0].toLowerCase()]=decodeURIComponent(n[1].replace(/\+/g,"%20"));if(void 0!==d.startdata)try{t=JSON.parse(d.startdata),i=t[0],c=t[1],r=t[2],a=t[3]}catch(e){}}(),(e=function(e){this.input=window.parent,this.domain=e,this.sequenceNumber=0}).prototype={sendMessage:function(e){e.rusticiSoftwareCrossDomain=!0,e.sequenceNumber=this.sequenceNumber,this.sequenceNumber+=1;try{this.input.postMessage(JSON.stringify(e),this.domain)}catch(e){alert("The communication link for RXD has been broken, this may result in a loss of data. (postMessage returned exception: "+e+")")}}},s=new e(d.pipeurl),n=function(e,n){return{type:e,identifier:n[0],result:n[2]?"correct":"incorrect",learnerResponse:n[1],correctResponse:n[3],description:n[4],weighting:n[5],latency:n[6],learningObjectiveId:n[7]}},window.RXD={Initialize:function(){s.sendMessage({action:"Initialize"})},ConcedeControl:function(){s.sendMessage({action:"ConcedeControl"})},SetPassed:function(){c="passed",s.sendMessage({action:"SetPassed"})},SetFailed:function(){c="failed",s.sendMessage({action:"SetFailed"})},SetReachedEnd:function(){"passed"!==c&&"failed"!==c&&(c="completed"),s.sendMessage({action:"SetReachedEnd"})},ResetStatus:function(){c="unknown",s.sendMessage({action:"ResetStatus"})},GetStatus:function(){return c},SetScore:function(e,n,t){o=e,s.sendMessage({action:"SetScore",cfg:{raw:e,min:t,max:n}})},GetScore:function(){return o},SetBookmark:function(e){r=e.toString(),s.sendMessage({action:"SetBookmark",cfg:{location:r}})},GetBookmark:function(){return r},SetSuspendData:function(e){a=e.toString(),s.sendMessage({action:"SetSuspendData",cfg:{data:a}})},GetSuspendData:function(){return a},GetLessonMode:function(){return i},GetStudentID:function(){return d.studentid},GetStudentName:function(){return d.studentname},RecordInteraction:function(e){s.sendMessage({action:"RecordInteraction",cfg:e})},RecordTrueFalseInteraction:function(){window.RXD.RecordInteraction(n("true-false",arguments))},RecordMultipleChoiceInteraction:function(){window.RXD.RecordInteraction(n("choice",arguments))},RecordFillInInteraction:function(){window.RXD.RecordInteraction(n("fill-in",arguments))},RecordMatchingInteraction:function(){window.RXD.RecordInteraction(n("matching",arguments))},RecordPerformanceInteraction:function(){window.RXD.RecordInteraction(n("performance",arguments))},RecordSequencingInteraction:function(){window.RXD.RecordInteraction(n("sequencing",arguments))},RecordLikertInteraction:function(){window.RXD.RecordInteraction(n("likert",arguments))},RecordNumericInteraction:function(){window.RXD.RecordInteraction(n("numeric",arguments))}}}();