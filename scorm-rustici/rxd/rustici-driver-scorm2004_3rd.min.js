/*! Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c */

var VERSION="7.3.1",PREFERENCE_DEFAULT=0,PREFERENCE_OFF=-1,PREFERENCE_ON=1,LESSON_STATUS_PASSED=1,LESSON_STATUS_COMPLETED=2,LESSON_STATUS_FAILED=3,LESSON_STATUS_INCOMPLETE=4,LESSON_STATUS_BROWSED=5,LESSON_STATUS_NOT_ATTEMPTED=6,ENTRY_REVIEW=1,ENTRY_FIRST_TIME=2,ENTRY_RESUME=3,MODE_NORMAL=1,MODE_BROWSE=2,MODE_REVIEW=3,MAX_CMI_TIME=36002439990,NO_ERROR=0,ERROR_LMS=1,ERROR_INVALID_PREFERENCE=2,ERROR_INVALID_NUMBER=3,ERROR_INVALID_ID=4,ERROR_INVALID_STATUS=5,ERROR_INVALID_RESPONSE=6,ERROR_NOT_LOADED=7,ERROR_INVALID_INTERACTION_RESPONSE=8,EXIT_TYPE_SUSPEND="SUSPEND",EXIT_TYPE_FINISH="FINISH",EXIT_TYPE_TIMEOUT="TIMEOUT",EXIT_TYPE_UNLOAD="UNLOAD",INTERACTION_RESULT_CORRECT="CORRECT",INTERACTION_RESULT_WRONG="WRONG",INTERACTION_RESULT_UNANTICIPATED="UNANTICIPATED",INTERACTION_RESULT_NEUTRAL="NEUTRAL",INTERACTION_TYPE_TRUE_FALSE="true-false",INTERACTION_TYPE_CHOICE="choice",INTERACTION_TYPE_FILL_IN="fill-in",INTERACTION_TYPE_LONG_FILL_IN="long-fill-in",INTERACTION_TYPE_MATCHING="matching",INTERACTION_TYPE_PERFORMANCE="performance",INTERACTION_TYPE_SEQUENCING="sequencing",INTERACTION_TYPE_LIKERT="likert",INTERACTION_TYPE_NUMERIC="numeric",DATA_CHUNK_PAIR_SEPARATOR="###",DATA_CHUNK_VALUE_SEPARATOR="$$",APPID="__APPID__",CLOUDURL="__CLOUDURL__",blnDebug=!0,strLMSStandard="SCORM2004",DEFAULT_EXIT_TYPE=EXIT_TYPE_SUSPEND,AICC_LESSON_ID="1",EXIT_BEHAVIOR="SCORM_RECOMMENDED",EXIT_TARGET="goodbye.html",AICC_COMM_DISABLE_XMLHTTP=!1,AICC_COMM_DISABLE_IFRAME=!1,AICC_COMM_PREPEND_HTTP_IF_MISSING=!0,AICC_REPORT_MIN_MAX_SCORE=!0,SHOW_DEBUG_ON_LAUNCH=!1,DO_NOT_REPORT_INTERACTIONS=!1,SCORE_CAN_ONLY_IMPROVE=!1,REVIEW_MODE_IS_READ_ONLY=!1,TCAPI_DONT_USE_BROKEN_URN_IDS=!0,AICC_RE_CHECK_LOADED_INTERVAL=250,AICC_RE_CHECK_ATTEMPTS_BEFORE_TIMEOUT=240,USE_AICC_KILL_TIME=!0,AICC_ENTRY_FLAG_DEFAULT=ENTRY_REVIEW,AICC_USE_CUSTOM_COMMS=!1,FORCED_COMMIT_TIME="0",ALLOW_NONE_STANDARD=!1,USE_2004_SUSPENDALL_NAVREQ=!1,USE_STRICT_SUSPEND_DATA_LIMITS=!0,EXIT_SUSPEND_IF_COMPLETED=!0,EXIT_NORMAL_IF_PASSED=!1,AICC_ENCODE_PARAMETER_VALUES=!0,PASS_FAIL_SETS_COMPLETION_FOR_2004=!0,ALLOW_INTERACTION_NULL_LEARNER_RESPONSE=!0,PREVENT_STATUS_CHANGE_DURING_INIT=!1;function GetQueryStringValue(e,t){var r;return null===(r=SearchQueryStringPairs((t=t.substring(1)).split("&"),e))&&(r=SearchQueryStringPairs(t.split(/[\?\&]/),e)),null===r?(WriteToDebug("GetQueryStringValue Element '"+e+"' Not Found, Returning: empty string"),""):(WriteToDebug("GetQueryStringValue for '"+e+"' Returning: "+r),r)}function SearchQueryStringPairs(e,t){var r,n,o="";for(t=t.toLowerCase(),r=0;r<e.length;r++)if(-1!=(n=e[r].indexOf("="))&&EqualsIgnoreCase(e[r].substring(0,n),t))return o=e[r].substring(n+1),o=(o=new String(o)).replace(/\+/g,"%20"),o=unescape(o),new String(o);return null}function ConvertStringToBoolean(e){var t;return!(!EqualsIgnoreCase(e,"true")&&!EqualsIgnoreCase(e,"t")&&0!=e.toLowerCase().indexOf("t"))||(1==(t=parseInt(e,10))||-1==t)}function EqualsIgnoreCase(e,t){return e=new String(e),t=new String(t),e.toLowerCase()==t.toLowerCase()}function ValidInteger(e){WriteToDebug("In ValidInteger intNum="+e);var t=new String(e);0==t.indexOf("-",0)&&(t=t.substring(1,t.length-1));var r=new RegExp("[^0-9]");return-1==t.search(r)?(WriteToDebug("Returning true"),!0):(WriteToDebug("Returning false"),!1)}function ConvertDateToIso8601TimeStamp(e){var t,r=(e=new Date(e)).getFullYear(),n=e.getMonth()+1,o=e.getDate(),i=e.getHours(),a=e.getMinutes(),s=e.getSeconds();t=r+"-"+(n=ZeroPad(n,2))+"-"+(o=ZeroPad(o,2))+"T"+(i=ZeroPad(i,2))+":"+(a=ZeroPad(a,2))+":"+(s=ZeroPad(s,2));var u=-e.getTimezoneOffset()/60;if(0!=u)if(t+=".0",0<u)if(-1!=(""+u).indexOf(".")){var l="0"+(""+u).substr((""+u).indexOf("."),(""+u).length);t+="+"+ZeroPad((""+u).substr(0,(""+u).indexOf("."))+"."+(l*=60),2)}else t+="+"+ZeroPad(u,2);else t+=ZeroPad(u,2);return t}function ConvertIso8601TimeStampToDate(e){e=new String(e);var t=new Array,r=(t=e.split(/[\:T+-]/))[0],n=t[1]-1,o=t[2],i=t[3],a=t[4],s=t[5];return new Date(r,n,o,i,a,s,0)}function ConvertDateToCMIDate(e){var t,r,n;return WriteToDebug("In ConvertDateToCMIDate"),t=(e=new Date(e)).getFullYear(),r=e.getMonth()+1,n=e.getDate(),ZeroPad(t,4)+"/"+ZeroPad(r,2)+"/"+ZeroPad(n,2)}function ConvertDateToCMITime(e){var t,r,n;return t=(e=new Date(e)).getHours(),r=e.getMinutes(),n=e.getSeconds(),ZeroPad(t,2)+":"+ZeroPad(r,2)+":"+ZeroPad(n,2)}function ConvertCMITimeSpanToMS(e){var t,r,n,o,i;return WriteToDebug("In ConvertCMITimeSpanToMS, strTime="+e),t=e.split(":"),IsValidCMITimeSpan(e)?(WriteToDebug("intHours="+(r=t[0])+" intMinutes="+(n=t[1])+" intSeconds="+(o=t[2])),i=36e5*r+6e4*n+1e3*o,WriteToDebug("Returning "+(i=Math.round(i))),i):(WriteToDebug("ERROR - Invalid TimeSpan"),SetErrorInfo(SCORM_ERROR_GENERAL,"LMS ERROR - Invalid time span passed to ConvertCMITimeSpanToMS, please contact technical support"),0)}function ConvertScorm2004TimeToMS(e){WriteToDebug("In ConvertScorm2004TimeToMS, strIso8601Time="+e);var t,r,n,o=0,i=0,a=0,s=0,u=0,l=0,c=0;e=new String(e),r=t="",n=!1;for(var S=1;S<e.length;S++)if(IsIso8601SectionDelimiter(r=e.charAt(S))){switch(r.toUpperCase()){case"Y":c=parseInt(t,10);break;case"M":n?a=parseInt(t,10):l=parseInt(t,10);break;case"D":u=parseInt(t,10);break;case"H":s=parseInt(t,10);break;case"S":i=parseFloat(t);break;case"T":n=!0}t=""}else t+=""+r;return WriteToDebug("Years="+c+"\nMonths="+l+"\nDays="+u+"\nHours="+s+"\nMinutes="+a+"\nSeconds="+i+"\n"),o=315576e5*c+26298e5*l+864e5*u+36e5*s+6e4*a+1e3*i,WriteToDebug("returning-"+(o=Math.round(o))),o}function IsIso8601SectionDelimiter(e){return 0<=e.search(/[PYMDTHS]/)}function IsValidCMITimeSpan(e){WriteToDebug("In IsValidCMITimeSpan strValue="+e);return-1<e.search(/^\d?\d?\d?\d:\d?\d:\d?\d(.\d\d?)?$/)?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function IsValidIso8601TimeSpan(e){WriteToDebug("In IsValidIso8601TimeSpan strValue="+e);return-1<e.search(/^P(\d+Y)?(\d+M)?(\d+D)?(T(\d+H)?(\d+M)?(\d+(.\d\d?)?S)?)?$/)?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function ConvertMilliSecondsToTCAPITime(e,t){var r,n,o,i,a,s;return WriteToDebug("In ConvertMilliSecondsToTCAPITime, intTotalMilliseconds = "+e+", blnIncludeFraction = "+t),null!=t&&null!=t||(t=!0),WriteToDebug("Separated Parts, intHours="+(r=(e-(i=e%1e3)-1e3*(o=(e-i)/1e3%60)-6e4*(n=(e-i-1e3*o)/6e4%60))/36e5)+", intMinutes="+n+", intSeconds="+o+", intMilliseconds="+i),1e4==r&&(WriteToDebug("Max intHours detected"),100==(n=(e-36e5*(r=9999))/6e4)&&(n=99),100==(o=(e-36e5*r-6e4*(n=Math.floor(n)))/1e3)&&(o=99),WriteToDebug("Separated Parts, intHours="+r+", intMinutes="+n+", intSeconds="+(o=Math.floor(o))+", intMilliseconds="+(i=e-36e5*r-6e4*n-1e3*o))),a=Math.floor(i/10),s=ZeroPad(r,4)+":"+ZeroPad(n,2)+":"+ZeroPad(o,2),t&&(s+="."+a),WriteToDebug("strCMITimeSpan="+s),9999<r&&(s="9999:99:99",t&&(s+=".99")),WriteToDebug("returning "+s),s}function ConvertMilliSecondsToSCORMTime(e,t){var r,n,o,i,a,s;return WriteToDebug("In ConvertMilliSecondsToSCORMTime, intTotalMilliseconds = "+e+", blnIncludeFraction = "+t),null!=t&&null!=t||(t=!0),WriteToDebug("Separated Parts, intHours="+(r=(e-(i=e%1e3)-1e3*(o=(e-i)/1e3%60)-6e4*(n=(e-i-1e3*o)/6e4%60))/36e5)+", intMinutes="+n+", intSeconds="+o+", intMilliseconds="+i),1e4==r&&(WriteToDebug("Max intHours detected"),100==(n=(e-36e5*(r=9999))/6e4)&&(n=99),100==(o=(e-36e5*r-6e4*(n=Math.floor(n)))/1e3)&&(o=99),WriteToDebug("Separated Parts, intHours="+r+", intMinutes="+n+", intSeconds="+(o=Math.floor(o))+", intMilliseconds="+(i=e-36e5*r-6e4*n-1e3*o))),a=Math.floor(i/10),s=ZeroPad(r,4)+":"+ZeroPad(n,2)+":"+ZeroPad(o,2),t&&(s+="."+a),WriteToDebug("strCMITimeSpan="+s),9999<r&&(s="9999:99:99",t&&(s+=".99")),WriteToDebug("returning "+s),s}function ConvertMilliSecondsIntoSCORM2004Time(e){WriteToDebug("In ConvertMilliSecondsIntoSCORM2004Time intTotalMilliseconds="+e);var t,r,n,o,i,a,s,u="",l=26298e4;return t=Math.floor(e/10),t-=315576e4*(s=Math.floor(t/315576e4)),t-=(a=Math.floor(t/l))*l,t-=864e4*(i=Math.floor(t/864e4)),t-=36e4*(o=Math.floor(t/36e4)),t-=6e3*(n=Math.floor(t/6e3)),0<s&&(u+=s+"Y"),0<a&&(u+=a+"M"),0<i&&(u+=i+"D"),0<(t-=100*(r=Math.floor(t/100)))+r+n+o&&(u+="T",0<o&&(u+=o+"H"),0<n&&(u+=n+"M"),0<t+r&&(u+=r,0<t&&(u+="."+t),u+="S")),""==u&&(u="T0S"),WriteToDebug("Returning-"+(u="P"+u)),u}function ZeroPad(e,t){var r,n,o,i;WriteToDebug("In ZeroPad intNum="+e+" intNumDigits="+t);var a=!1;if(-1!=(r=new String(e)).indexOf("-")&&(a=!0,r=r.substr(1,r.length)),-1!=r.indexOf(".")&&(r.replace(".",""),o=r.substr(r.indexOf(".")+1,r.length),r=r.substr(0,r.indexOf("."))),t<(n=r.length))WriteToDebug("Length of string is greater than num digits, trimming string"),r=r.substr(0,t);else for(i=n;i<t;i++)r="0"+r;return 1==a&&(r="-"+r),null!=o&&""!=o&&(1==o.length?r+=":"+o+"0":r+=":"+o),WriteToDebug("Returning - "+r),r}function IsValidDecimalRange(e){WriteToDebug("In IsDecimalRange, strValue="+e);var t,r,n;return 2===(t=(e=new String(e)).split("[:]")).length?(r=Trim(t[0]),n=Trim(t[1]),0<r.length&&!IsValidDecimal(r)?(WriteToDebug("Returning False - min value supplied range is not a valid decimal, min="+r),!1):0<n.length&&!IsValidDecimal(n)?(WriteToDebug("Returning False - max value supplied for range is not a valid decimal, max="+n),!1):!(0<r.length&&0<n.length&&parseFloat(r)>parseFloat(n))||(WriteToDebug("Returning False - min value supplied for range is greater than the max, min="+r+", max="+n),!1)):(WriteToDebug("Returning false - string supplied for range has incorrect number of parts, parts="+t.length+", strValue="+e),!1)}function ConvertDecimalRangeToDecimalBasedOnLearnerResponse(e,t,r){WriteToDebug("In ConvertDecimalRangeToDecimalBasedOnLearnerResponse strValue="+e+",strLearnerResponse="+t+",blnCorrect="+r);var n,o,i;if(r)return WriteToDebug("Returning strLearnerResponse"),t;if(2===(n=(e=new String(e)).split("[:]")).length){if(o=Trim(n[0]),i=Trim(n[1]),0<o.length)return WriteToDebug("Returning strMin"),o;if(0<i.length)return WriteToDebug("Returning strMax"),i}return WriteToDebug("Returning null"),null}function IsValidDecimal(e){return WriteToDebug("In IsValidDecimal, strValue="+e),-1<(e=new String(e)).search(/[^.\d-]/)?(WriteToDebug("Returning False - character other than a digit, dash or period found"),!1):-1<e.search("-")&&-1<e.indexOf("-",1)?(WriteToDebug("Returning False - dash found in the middle of the string"),!1):e.indexOf(".")!=e.lastIndexOf(".")?(WriteToDebug("Returning False - more than one decimal point found"),!1):e.search(/\d/)<0?(WriteToDebug("Returning False - no digits found"),!1):(WriteToDebug("Returning True"),!0)}function IsAlphaNumeric(e){return WriteToDebug("In IsAlphaNumeric"),e.search(/\w/)<0?(WriteToDebug("Returning false"),!1):(WriteToDebug("Returning true"),!0)}function ReverseNameSequence(e){var t,r,n;return""==e&&(e="Not Found, Learner Name"),n=e.indexOf(","),t=e.slice(n+1),r=e.slice(0,n),(t=Trim(t))+" "+(r=Trim(r))}function LTrim(e){return(e=new String(e)).replace(/^\s+/,"")}function RTrim(e){return(e=new String(e)).replace(/\s+$/,"")}function Trim(e){return LTrim(RTrim(e)).replace(/\s{2,}/g," ")}function GetValueFromDataChunk(e){var t,r=new String(GetDataChunk()),n=new Array,o=new Array;for(n=r.split(parent.DATA_CHUNK_PAIR_SEPARATOR),t=0;t<n.length;t++)if((o=n[t].split(parent.DATA_CHUNK_VALUE_SEPARATOR))[0]==e)return o[1];return""}function SetDataChunkValue(e,t){var r,n=new String(GetDataChunk()),o=new Array,i=new Array,a=new Boolean(!1);for(o=n.split(parent.DATA_CHUNK_PAIR_SEPARATOR),r=0;r<o.length;r++)(i=o[r].split(parent.DATA_CHUNK_VALUE_SEPARATOR))[0]==e&&(i[1]=t,a=!0,o[r]=i[0]+parent.DATA_CHUNK_VALUE_SEPARATOR+i[1]);return 1==a?n=o.join(parent.DATA_CHUNK_PAIR_SEPARATOR):""==n?n=e+parent.DATA_CHUNK_VALUE_SEPARATOR+t:n+=parent.DATA_CHUNK_PAIR_SEPARATOR+e+parent.DATA_CHUNK_VALUE_SEPARATOR+t,SetDataChunk(n),!0}function GetLastDirAndPageName(e){var t=new String(e),r=t.lastIndexOf("/"),n=t.lastIndexOf("/",r-1);return t.substr(n+1)}function RoundToPrecision(e,t){return e=parseFloat(e),Math.round(e*Math.pow(10,t))/Math.pow(10,t)}function IsAbsoluteUrl(e){return null!=e&&(0==e.indexOf("http://")||0==e.indexOf("https://"))}function TouchCloud(){if(null==APPID||""==APPID||"__APPID__"==APPID||null===CLOUDURL||0!==CLOUDURL.indexOf("http"))return!1;var e=document.createElement("form");e.name="cloudform",e.id="cloudform",e.style="display:none;",document.body.appendChild(e);var t=document.createElement("input");t.name="appId",t.value=APPID,t.type="hidden",e.appendChild(t);var r=document.createElement("input");r.name="servingUrl",r.type="hidden",r.value=document.location.href,e.appendChild(r);var n=document.createElement("input");return n.name="version",n.type="hidden",n.value=VERSION,e.appendChild(n),e.target="rusticisoftware_aicc_results",e.action=CLOUDURL,document.getElementById("cloudform").submit(),!0}function IsNumeric(e){return!isNaN(parseFloat(e))&&isFinite(e)}function loadScript(e,t){var r=document.getElementsByTagName("head")[0],n=document.createElement("script");n.type="text/javascript",n.src=e,!n.addEventListener||document.documentMode&&document.documentMode<9?n.onreadystatechange=function(){/loaded|complete/.test(n.readyState)&&(n.onreadystatechange=null,t())}:n.addEventListener("load",t,!1),r.appendChild(n)}!function(e){var t="object"==typeof exports&&exports,r="object"==typeof module&&module&&module.exports==t&&module,n="object"==typeof global&&global;n.global!==n&&n.window!==n||(e=n);var o,i,C=2147483647,E=36,g=1,T=26,a=38,s=700,f=72,D=128,M="-",u=/^xn--/,l=/[^ -~]/,c=/\x2E|\u3002|\uFF0E|\uFF61/g,S={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},R=E-g,b=Math.floor,p=String.fromCharCode;function m(e){throw RangeError(S[e])}function d(e,t){for(var r=e.length;r--;)e[r]=t(e[r]);return e}function I(e,t){return d(e.split(c),t).join(".")}function h(e){for(var t,r,n=[],o=0,i=e.length;o<i;)55296<=(t=e.charCodeAt(o++))&&t<=56319&&o<i?56320==(64512&(r=e.charCodeAt(o++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),o--):n.push(t);return n}function L(e){return d(e,function(e){var t="";return 65535<e&&(t+=p((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=p(e)}).join("")}function A(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function v(e,t,r){var n=0;for(e=r?b(e/s):e>>1,e+=b(e/t);R*T>>1<e;n+=E)e=b(e/R);return b(n+(R+1)*e/(e+a))}function _(e){var t,r,n,o,i,a,s,u,l,c,S,R=[],d=e.length,I=0,_=D,O=f;for((r=e.lastIndexOf(M))<0&&(r=0),n=0;n<r;++n)128<=e.charCodeAt(n)&&m("not-basic"),R.push(e.charCodeAt(n));for(o=0<r?r+1:0;o<d;){for(i=I,a=1,s=E;d<=o&&m("invalid-input"),S=e.charCodeAt(o++),(E<=(u=S-48<10?S-22:S-65<26?S-65:S-97<26?S-97:E)||u>b((C-I)/a))&&m("overflow"),I+=u*a,!(u<(l=s<=O?g:O+T<=s?T:s-O));s+=E)a>b(C/(c=E-l))&&m("overflow"),a*=c;O=v(I-i,t=R.length+1,0==i),b(I/t)>C-_&&m("overflow"),_+=b(I/t),I%=t,R.splice(I++,0,_)}return L(R)}function O(e){var t,r,n,o,i,a,s,u,l,c,S,R,d,I,_,O=[];for(R=(e=h(e)).length,t=D,i=f,a=r=0;a<R;++a)(S=e[a])<128&&O.push(p(S));for(n=o=O.length,o&&O.push(M);n<R;){for(s=C,a=0;a<R;++a)t<=(S=e[a])&&S<s&&(s=S);for(s-t>b((C-r)/(d=n+1))&&m("overflow"),r+=(s-t)*d,t=s,a=0;a<R;++a)if((S=e[a])<t&&++r>C&&m("overflow"),S==t){for(u=r,l=E;!(u<(c=l<=i?g:i+T<=l?T:l-i));l+=E)_=u-c,I=E-c,O.push(p(A(c+_%I,0))),u=b(_/I);O.push(p(A(u,0))),i=v(r,d,n==o),r=0,++n}++r,++t}return O.join("")}if(o={version:"1.2.3",ucs2:{decode:h,encode:L},decode:_,encode:O,toASCII:function(e){return I(e,function(e){return l.test(e)?"xn--"+O(e):e})},toUnicode:function(e){return I(e,function(e){return u.test(e)?_(e.slice(4).toLowerCase()):e})}},"function"==typeof define&&"object"==typeof define.amd&&define.amd)define(function(){return o});else if(t&&!t.nodeType)if(r)r.exports=o;else for(i in o)o.hasOwnProperty(i)&&(t[i]=o[i]);else e.punycode=o}(this),function(e,t){"use strict";"object"==typeof exports?module.exports=t(require("./punycode"),require("./IPv6"),require("./SecondLevelDomains")):"function"==typeof define&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],t):e.URI=t(e.punycode,e.IPv6,e.SecondLevelDomains,e)}(this,function(s,t,l,r){"use strict";var n=r&&r.URI;function d(e,t){if(!(this instanceof d))return new d(e,t);if(void 0===e){if(arguments.length)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}return this.href(e),void 0!==t?this.absoluteTo(t):this}d.version="1.14.2";var e=d.prototype,c=Object.prototype.hasOwnProperty;function u(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function i(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function S(e){return"Array"===i(e)}function a(e,t){var r,n;if(S(t)){for(r=0,n=t.length;r<n;r++)if(!a(e,t[r]))return!1;return!0}var o=i(t);for(r=0,n=e.length;r<n;r++)if("RegExp"===o){if("string"==typeof e[r]&&e[r].match(t))return!0}else if(e[r]===t)return!0;return!1}function R(e,t){if(!S(e)||!S(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}function o(e){return escape(e)}function I(e){return encodeURIComponent(e).replace(/[!'()*]/g,o).replace(/\*/g,"%2A")}function _(e){for(var t,r=s.ucs2.decode(e),n="",o=0;o<r.length;o++){var i=r[o];if(45===(t=i)||46===t||95===t||126===t||48<=t&&t<64||65<=t&&t<91||97<=t&&t<123||160<=t&&t<55296||57344<=t&&t<63743||63744<=t&&t<64976||65008<=t&&t<65520||65536<=t&&t<131070||131072<=t&&t<196606||196608<=t&&t<262142||262144<=t&&t<327678||327680<=t&&t<393214||393216<=t&&t<458750||458752<=t&&t<524286||524288<=t&&t<589822||589824<=t&&t<655358||655360<=t&&t<720894||720896<=t&&t<786430||786432<=t&&t<851966||851968<=t&&t<917502||917504<=t&&t<983038||983040<=t&&t<1048574||1048576<=t&&t<1114110)n+=s.ucs2.encode([i]);else n+=I(s.ucs2.encode([i]))}return n}function O(e){return d.punycode_expression.test(e)&&(e=s.toUnicode(e)),_(e)}d._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,duplicateQueryParameters:d.duplicateQueryParameters,escapeQuerySpace:d.escapeQuerySpace}},d.duplicateQueryParameters=!1,d.escapeQuerySpace=!0,d.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,d.idn_expression=/[^a-z0-9\.-]/i,d.punycode_expression=/(xn--)/i,d.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,d.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,d.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»]))/gi,d.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»]+$/},d.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},d.invalid_hostname_characters=/[^a-zA-Z0-9\.-]/,d.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},d.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return d.domAttributes[t]}},d._defaultRecodeHostname=s?s.toASCII:function(e){return e},d.iso8859=function(){d.recodeHostname=d._defaultRecodeHostname,d.encode=escape,d.decode=unescape},d.unicode=function(){d.recodeHostname=d._defaultRecodeHostname,d.encode=I,d.decode=decodeURIComponent},d.iri=function(){d.recodeHostname=O,d.encode=_,d.decode=decodeURIComponent},d.unicode(),d.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},d.encodeQuery=function(e,t){var r=d.encode(e+"");return void 0===t&&(t=d.escapeQuerySpace),t?r.replace(/%20/g,"+"):r},d.decodeQuery=function(t,e){t+="",void 0===e&&(e=d.escapeQuerySpace);try{return d.decode(e?t.replace(/\+/g,"%20"):t)}catch(e){return t}};var C,E={encode:"encode",decode:"decode"},g=function(r,n){return function(t){try{return d[n](t+"").replace(d.characters[r][n].expression,function(e){return d.characters[r][n].map[e]})}catch(e){return t}}};for(C in E)d[C+"PathSegment"]=g("pathname",E[C]),d[C+"UrnPathSegment"]=g("urnpath",E[C]);var T=function(i,a,s){return function(e){var t;t=s?function(e){return d[a](d[s](e))}:d[a];for(var r=(e+"").split(i),n=0,o=r.length;n<o;n++)r[n]=t(r[n]);return r.join(i)}};function f(r){return function(e,t){return void 0===e?this._parts[r]||"":(this._parts[r]=e||null,this.build(!t),this)}}function D(r,n){return function(e,t){return void 0===e?this._parts[r]||"":(null!==e&&(e+="").charAt(0)===n&&(e=e.substring(1)),this._parts[r]=e,this.build(!t),this)}}d.decodePath=T("/","decodePathSegment"),d.decodeUrnPath=T(":","decodeUrnPathSegment"),d.recodePath=T("/","encodePathSegment","decode"),d.recodeUrnPath=T(":","encodeUrnPathSegment","decode"),d.encodeReserved=g("reserved","encode"),d.parse=function(e,t){var r;return t||(t={}),-1<(r=e.indexOf("#"))&&(t.fragment=e.substring(r+1)||null,e=e.substring(0,r)),-1<(r=e.indexOf("?"))&&(t.query=e.substring(r+1)||null,e=e.substring(0,r)),"//"===e.substring(0,2)?(t.protocol=null,e=e.substring(2),e=d.parseAuthority(e,t)):-1<(r=e.indexOf(":"))&&(t.protocol=e.substring(0,r)||null,t.protocol&&!t.protocol.match(d.protocol_expression)?t.protocol=void 0:"//"===e.substring(r+1,r+3)?(e=e.substring(r+3),e=d.parseAuthority(e,t)):(e=e.substring(r+1),t.urn=!0)),t.path=e,t},d.parseHost=function(e,t){var r,n,o=e.indexOf("/");if(-1===o&&(o=e.length),"["===e.charAt(0))r=e.indexOf("]"),t.hostname=e.substring(1,r)||null,t.port=e.substring(r+2,o)||null,"/"===t.port&&(t.port=null);else{var i=e.indexOf(":"),a=e.indexOf("/"),s=e.indexOf(":",i+1);t.port=-1!==s&&(-1===a||s<a)?(t.hostname=e.substring(0,o)||null,null):(n=e.substring(0,o).split(":"),t.hostname=n[0]||null,n[1]||null)}return t.hostname&&"/"!==e.substring(o).charAt(0)&&(o++,e="/"+e),e.substring(o)||"/"},d.parseAuthority=function(e,t){return e=d.parseUserinfo(e,t),d.parseHost(e,t)},d.parseUserinfo=function(e,t){var r,n=e.indexOf("/"),o=e.lastIndexOf("@",-1<n?n:e.length-1);return-1<o&&(-1===n||o<n)?(r=e.substring(0,o).split(":"),t.username=r[0]?d.decode(r[0]):null,r.shift(),t.password=r[0]?d.decode(r.join(":")):null,e=e.substring(o+1)):(t.username=null,t.password=null),e},d.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,n,o,i={},a=e.split("&"),s=a.length,u=0;u<s;u++)r=a[u].split("="),n=d.decodeQuery(r.shift(),t),o=r.length?d.decodeQuery(r.join("="),t):null,c.call(i,n)?("string"==typeof i[n]&&(i[n]=[i[n]]),i[n].push(o)):i[n]=o;return i},d.build=function(e){var t="";return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//"),t+=d.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&"string"==typeof e.hostname&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},d.buildHost=function(e){var t="";return e.hostname?(d.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},d.buildAuthority=function(e){return d.buildUserinfo(e)+d.buildHost(e)},d.buildUserinfo=function(e){var t="";return e.username&&(t+=d.encode(e.username),e.password&&(t+=":"+d.encode(e.password)),t+="@"),t},d.buildQuery=function(e,t,r){var n,o,i,a,s="";for(o in e)if(c.call(e,o)&&o)if(S(e[o]))for(n={},i=0,a=e[o].length;i<a;i++)void 0!==e[o][i]&&void 0===n[e[o][i]+""]&&(s+="&"+d.buildQueryParameter(o,e[o][i],r),!0!==t&&(n[e[o][i]+""]=!0));else void 0!==e[o]&&(s+="&"+d.buildQueryParameter(o,e[o],r));return s.substring(1)},d.buildQueryParameter=function(e,t,r){return d.encodeQuery(e,r)+(null!==t?"="+d.encodeQuery(t,r):"")},d.addQuery=function(e,t,r){if("object"==typeof t)for(var n in t)c.call(t,n)&&d.addQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=r);"string"==typeof e[t]&&(e[t]=[e[t]]),S(r)||(r=[r]),e[t]=(e[t]||[]).concat(r)}},d.removeQuery=function(e,t,r){var n,o,i;if(S(t))for(n=0,o=t.length;n<o;n++)e[t[n]]=void 0;else if("object"==typeof t)for(i in t)c.call(t,i)&&d.removeQuery(e,i,t[i]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string as the first parameter");void 0!==r?e[t]===r?e[t]=void 0:S(e[t])&&(e[t]=function(e,t){var r,n,o={};if(S(t))for(r=0,n=t.length;r<n;r++)o[t[r]]=!0;else o[t]=!0;for(r=0,n=e.length;r<n;r++)void 0!==o[e[r]]&&(e.splice(r,1),n--,r--);return e}(e[t],r)):e[t]=void 0}},d.hasQuery=function(e,t,r,n){if("object"==typeof t){for(var o in t)if(c.call(t,o)&&!d.hasQuery(e,o,t[o]))return!1;return!0}if("string"!=typeof t)throw new TypeError("URI.hasQuery() accepts an object, string as the name parameter");switch(i(r)){case"Undefined":return t in e;case"Boolean":return r===Boolean(S(e[t])?e[t].length:e[t]);case"Function":return!!r(e[t],t,e);case"Array":return!!S(e[t])&&(n?a:R)(e[t],r);case"RegExp":return S(e[t])?!!n&&a(e[t],r):Boolean(e[t]&&e[t].match(r));case"Number":r=String(r);case"String":return S(e[t])?!!n&&a(e[t],r):e[t]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},d.commonPath=function(e,t){var r,n=Math.min(e.length,t.length);for(r=0;r<n;r++)if(e.charAt(r)!==t.charAt(r)){r--;break}return r<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(r)&&"/"===t.charAt(r)||(r=e.substring(0,r).lastIndexOf("/")),e.substring(0,r+1))},d.withinString=function(e,t,r){r||(r={});var n=r.start||d.findUri.start,o=r.end||d.findUri.end,i=r.trim||d.findUri.trim,a=/[a-z0-9-]=["']?$/i;for(n.lastIndex=0;;){var s=n.exec(e);if(!s)break;var u=s.index;if(r.ignoreHtml){var l=e.slice(Math.max(u-3,0),u);if(l&&a.test(l))continue}var c=u+e.slice(u).search(o),S=e.slice(u,c).replace(i,"");if(!r.ignore||!r.ignore.test(S)){var R=t(S,u,c=u+S.length,e);e=e.slice(0,u)+R+e.slice(c),n.lastIndex=u+R.length}}return n.lastIndex=0,e},d.ensureValidHostname=function(e){if(e.match(d.invalid_hostname_characters)){if(!s)throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-] and Punycode.js is not available');if(s.toASCII(e).match(d.invalid_hostname_characters))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]')}},d.noConflict=function(e){if(e){var t={URI:this.noConflict()};return r.URITemplate&&"function"==typeof r.URITemplate.noConflict&&(t.URITemplate=r.URITemplate.noConflict()),r.IPv6&&"function"==typeof r.IPv6.noConflict&&(t.IPv6=r.IPv6.noConflict()),r.SecondLevelDomains&&"function"==typeof r.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=r.SecondLevelDomains.noConflict()),t}return r.URI===this&&(r.URI=n),this},e.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=d.build(this._parts),this._deferred_build=!1),this},e.clone=function(){return new d(this)},e.valueOf=e.toString=function(){return this.build(!1)._string},e.protocol=f("protocol"),e.username=f("username"),e.password=f("password"),e.hostname=f("hostname"),e.port=f("port"),e.query=D("query","?"),e.fragment=D("fragment","#"),e.search=function(e,t){var r=this.query(e,t);return"string"==typeof r&&r.length?"?"+r:r},e.hash=function(e,t){var r=this.fragment(e,t);return"string"==typeof r&&r.length?"#"+r:r},e.pathname=function(e,t){if(void 0!==e&&!0!==e)return this._parts.urn?this._parts.path=e?d.recodeUrnPath(e):"":this._parts.path=e?d.recodePath(e):"/",this.build(!t),this;var r=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?d.decodeUrnPath:d.decodePath)(r):r},e.path=e.pathname,e.href=function(e,t){var r;if(void 0===e)return this.toString();this._string="",this._parts=d._parts();var n=e instanceof d,o="object"==typeof e&&(e.hostname||e.path||e.pathname);e.nodeName&&(e=e[d.getDomAttribute(e)]||"",o=!1);if(!n&&o&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=d.parse(String(e),this._parts);else{if(!n&&!o)throw new TypeError("invalid input");var i=n?e._parts:e;for(r in i)c.call(this._parts,r)&&(this._parts[r]=i[r])}return this.build(!t),this},e.is=function(e){var t=!1,r=!1,n=!1,o=!1,i=!1,a=!1,s=!1,u=!this._parts.urn;switch(this._parts.hostname&&(u=!1,r=d.ip4_expression.test(this._parts.hostname),n=d.ip6_expression.test(this._parts.hostname),i=(o=!(t=r||n))&&l&&l.has(this._parts.hostname),a=o&&d.idn_expression.test(this._parts.hostname),s=o&&d.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return u;case"absolute":return!u;case"domain":case"name":return o;case"sld":return i;case"ip":return t;case"ip4":case"ipv4":case"inet4":return r;case"ip6":case"ipv6":case"inet6":return n;case"idn":return a;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return s}return null};var M=e.protocol,b=e.port,p=e.hostname;e.protocol=function(e,t){if(void 0!==e&&e&&!(e=e.replace(/:(\/\/)?$/,"")).match(d.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return M.call(this,e,t)},e.scheme=e.protocol,e.port=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),e.match(/[^0-9]/))))throw new TypeError('Port "'+e+'" contains characters other than [0-9]');return b.call(this,e,t)},e.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var r={};d.parseHost(e,r),e=r.hostname}return p.call(this,e,t)},e.host=function(e,t){return this._parts.urn?void 0===e?"":this:void 0===e?this._parts.hostname?d.buildHost(this._parts):"":(d.parseHost(e,this._parts),this.build(!t),this)},e.authority=function(e,t){return this._parts.urn?void 0===e?"":this:void 0===e?this._parts.hostname?d.buildAuthority(this._parts):"":(d.parseAuthority(e,this._parts),this.build(!t),this)},e.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e)return"@"!==e[e.length-1]&&(e+="@"),d.parseUserinfo(e,this._parts),this.build(!t),this;if(!this._parts.username)return"";var r=d.buildUserinfo(this._parts);return r.substring(0,r.length-1)},e.resource=function(e,t){var r;return void 0===e?this.path()+this.search()+this.hash():(r=d.parse(e),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!t),this)},e.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var n=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,n),i=new RegExp("^"+u(o));return e&&"."!==e.charAt(e.length-1)&&(e+="."),e&&d.ensureValidHostname(e),this._parts.hostname=this._parts.hostname.replace(i,e),this.build(!t),this},e.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var n=this._parts.hostname.length-this.tld(t).length-1;return n=this._parts.hostname.lastIndexOf(".",n-1)+1,this._parts.hostname.substring(n)||""}if(!e)throw new TypeError("cannot set domain empty");if(d.ensureValidHostname(e),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var o=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},e.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.lastIndexOf("."),n=this._parts.hostname.substring(r+1);return!0!==t&&l&&l.list[n.toLowerCase()]&&l.get(this._parts.hostname)||n}var o;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!l||!l.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');o=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(o,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");o=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},e.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,n=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return e?d.decodePath(n):n}var o=this._parts.path.length-this.filename().length,i=this._parts.path.substring(0,o),a=new RegExp("^"+u(i));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=d.recodePath(e),this._parts.path=this._parts.path.replace(a,e),this.build(!t),this},e.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),n=this._parts.path.substring(r+1);return e?d.decodePathSegment(n):n}var o=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(o=!0);var i=new RegExp(u(this.filename())+"$");return e=d.recodePath(e),this._parts.path=this._parts.path.replace(i,e),o?this.normalizePath(t):this.build(!t),this},e.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var r,n,o=this.filename(),i=o.lastIndexOf(".");return-1===i?"":(r=o.substring(i+1),n=/^[a-z0-9%]+$/i.test(r)?r:"",e?d.decodePathSegment(n):n)}"."===e.charAt(0)&&(e=e.substring(1));var a,s=this.suffix();if(s)a=e?new RegExp(u(s)+"$"):new RegExp(u("."+s)+"$");else{if(!e)return this;this._parts.path+="."+d.recodePath(e)}return a&&(e=d.recodePath(e),this._parts.path=this._parts.path.replace(a,e)),this.build(!t),this},e.segment=function(e,t,r){var n=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),a=o.split(n);if(void 0!==e&&"number"!=typeof e&&(r=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(i&&a.shift(),e<0&&(e=Math.max(a.length+e,0)),void 0===t)return void 0===e?a:a[e];if(null===e||void 0===a[e])if(S(t)){a=[];for(var s=0,u=t.length;s<u;s++)(t[s].length||a.length&&a[a.length-1].length)&&(a.length&&!a[a.length-1].length&&a.pop(),a.push(t[s]))}else(t||"string"==typeof t)&&(""===a[a.length-1]?a[a.length-1]=t:a.push(t));else t?a[e]=t:a.splice(e,1);return i&&a.unshift(""),this.path(a.join(n),r)},e.segmentCoded=function(e,t,r){var n,o,i;if("number"!=typeof e&&(r=t,t=e,e=void 0),void 0===t){if(S(n=this.segment(e,t,r)))for(o=0,i=n.length;o<i;o++)n[o]=d.decode(n[o]);else n=void 0!==n?d.decode(n):void 0;return n}if(S(t))for(o=0,i=t.length;o<i;o++)t[o]=d.decode(t[o]);else t="string"==typeof t||t instanceof String?d.encode(t):t;return this.segment(e,t,r)};var m=e.query;function h(e,n,o){return function(){var e=d.recodeHostname,t=d.encode,r=d.decode;d.encode=n,d.decode=o;try{this.normalize()}finally{d.recodeHostname=e,d.encode=t,d.decode=r}return this}}return e.query=function(e,t){if(!0===e)return d.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"!=typeof e)return void 0!==e&&"string"!=typeof e?(this._parts.query=d.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):m.call(this,e,t);var r=d.parseQuery(this._parts.query,this._parts.escapeQuerySpace),n=e.call(this,r);return this._parts.query=d.buildQuery(n||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this},e.setQuery=function(e,t,r){var n=d.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)n[e]=void 0!==t?t:null;else{if("object"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in e)c.call(e,o)&&(n[o]=e[o])}return this._parts.query=d.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},e.addQuery=function(e,t,r){var n=d.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return d.addQuery(n,e,void 0===t?null:t),this._parts.query=d.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},e.removeQuery=function(e,t,r){var n=d.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return d.removeQuery(n,e,t),this._parts.query=d.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},e.hasQuery=function(e,t,r){var n=d.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return d.hasQuery(n,e,t,r)},e.setSearch=e.setQuery,e.addSearch=e.addQuery,e.removeSearch=e.removeQuery,e.hasSearch=e.hasQuery,e.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},e.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},e.normalizeHostname=function(e){return this._parts.hostname&&(this.is("IDN")||this.is("punycode")?this._parts.hostname=d.recodeHostname(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!e)),this},e.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===d.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},e.normalizePath=function(e){var t,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=d.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var n,o,i="";for("/"!==r.charAt(0)&&(t=!0,r="/"+r),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),t&&(i=r.substring(1).match(/^(\.\.\/)+/)||"")&&(i=i[0]);-1!==(n=r.indexOf("/.."));)r=0!==n?(-1===(o=r.substring(0,n).lastIndexOf("/"))&&(o=n),r.substring(0,o)+r.substring(n+3)):r.substring(3);return t&&this.is("relative")&&(r=i+r.substring(1)),r=d.recodePath(r),this._parts.path=r,this.build(!e),this},e.normalizePathname=e.normalizePath,e.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(d.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},e.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},e.normalizeSearch=e.normalizeQuery,e.normalizeHash=e.normalizeFragment,e.iso8859=h(0,escape,decodeURIComponent),e.unicode=h(0,I,unescape),e.iri=h(0,_,decodeURIComponent),e.readable=function(){var e=this.clone();e.username("").password("").normalize();var t="";if(e._parts.protocol&&(t+=e._parts.protocol+(e._parts.urn?":":"://")),e._parts.hostname&&(e.is("punycode")&&s?(t+=s.toUnicode(e._parts.hostname),e._parts.port&&(t+=":"+e._parts.port)):t+=e.host()),e._parts.hostname&&e._parts.path&&"/"!==e._parts.path.charAt(0)&&(t+="/"),t+=e.path(!0),e._parts.query){for(var r="",n=0,o=e._parts.query.split("&"),i=o.length;n<i;n++){var a=(o[n]||"").split("=");r+="&"+d.decodeQuery(a[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==a[1]&&(r+="="+d.decodeQuery(a[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}t+="?"+r.substring(1)}return t+=d.decodeQuery(e.hash(),!0)},e.absoluteTo=function(e){var t,r,n,o=this.clone(),i=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof d||(e=new d(e)),o._parts.protocol||(o._parts.protocol=e._parts.protocol),this._parts.hostname)return o;for(r=0;n=i[r];r++)o._parts[n]=e._parts[n];return o._parts.path?".."===o._parts.path.substring(-2)&&(o._parts.path+="/"):(o._parts.path=e._parts.path,o._parts.query||(o._parts.query=e._parts.query)),"/"!==o.path().charAt(0)&&(t=e.directory(),o._parts.path=(t?t+"/":"")+o._parts.path,o.normalizePath()),o.build(),o},e.relativeTo=function(e){var t,r,n,o,i,a=this.clone().normalize();if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new d(e).normalize(),t=a._parts,r=e._parts,o=a.path(),i=e.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==i.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===r.protocol&&(t.protocol=null),t.username!==r.username||t.password!==r.password)return a.build();if(null!==t.protocol||null!==t.username||null!==t.password)return a.build();if(t.hostname!==r.hostname||t.port!==r.port)return a.build();if(t.hostname=null,t.port=null,o===i)return t.path="",a.build();if(!(n=d.commonPath(a.path(),e.path())))return a.build();var s=r.path.substring(n.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=s+t.path.substring(n.length),a.build()},e.equals=function(e){var t,r,n,o,i,a=this.clone(),s=new d(e),u={};if(a.normalize(),s.normalize(),a.toString()===s.toString())return!0;if(n=a.query(),o=s.query(),a.query(""),s.query(""),a.toString()!==s.toString())return!1;if(n.length!==o.length)return!1;for(i in t=d.parseQuery(n,this._parts.escapeQuerySpace),r=d.parseQuery(o,this._parts.escapeQuerySpace),t)if(c.call(t,i)){if(S(t[i])){if(!R(t[i],r[i]))return!1}else if(t[i]!==r[i])return!1;u[i]=!0}for(i in r)if(c.call(r,i)&&!u[i])return!1;return!0},e.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},e.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},d});var STANDARD="SCORM2004",SCORM2004_LOGOUT="logout",SCORM2004_SUSPEND="suspend",SCORM2004_NORMAL_EXIT="normal",SCORM2004_TIMEOUT="time-out",SCORM2004_PASSED="passed",SCORM2004_FAILED="failed",SCORM2004_UNKNOWN="unknown",SCORM2004_COMPLETED="completed",SCORM2004_INCOMPLETE="incomplete",SCORM2004_NOT_ATTEMPTED="not attempted",SCORM2004_CREDIT="credit",SCORM2004_NO_CREDIT="no-credit",SCORM2004_BROWSE="browse",SCORM2004_NORMAL="normal",SCORM2004_REVIEW="review",SCORM2004_ENTRY_ABINITIO="ab-initio",SCORM2004_ENTRY_RESUME="resume",SCORM2004_ENTRY_NORMAL="",SCORM2004_TLA_EXIT_MESSAGE="exit,message",SCORM2004_TLA_EXIT_NO_MESSAGE="exit,no message",SCORM2004_TLA_CONTINUE_MESSAGE="continue,message",SCORM2004_TLA_CONTINUE_NO_MESSAGE="continue,no message",SCORM2004_RESULT_CORRECT="correct",SCORM2004_RESULT_WRONG="incorrect",SCORM2004_RESULT_UNANTICIPATED="unanticipated",SCORM2004_RESULT_NEUTRAL="neutral",SCORM2004_INTERACTION_TYPE_TRUE_FALSE="true-false",SCORM2004_INTERACTION_TYPE_CHOICE="choice",SCORM2004_INTERACTION_TYPE_FILL_IN="fill-in",SCORM2004_INTERACTION_TYPE_LONG_FILL_IN="long-fill-in",SCORM2004_INTERACTION_TYPE_MATCHING="matching",SCORM2004_INTERACTION_TYPE_PERFORMANCE="performance",SCORM2004_INTERACTION_TYPE_SEQUENCING="sequencing",SCORM2004_INTERACTION_TYPE_LIKERT="likert",SCORM2004_INTERACTION_TYPE_NUMERIC="numeric",SCORM2004_NO_ERROR="0",SCORM2004_ERROR_INVALID_PREFERENCE="-1",SCORM2004_ERROR_INVALID_STATUS="-2",SCORM2004_ERROR_INVALID_SPEED="-3",SCORM2004_ERROR_INVALID_TIMESPAN="-4",SCORM2004_ERROR_INVALID_TIME_LIMIT_ACTION="-5",SCORM2004_ERROR_INVALID_DECIMAL="-6",SCORM2004_ERROR_INVALID_CREDIT="-7",SCORM2004_ERROR_INVALID_LESSON_MODE="-8",SCORM2004_ERROR_INVALID_ENTRY="-9",SCORM2004_TRUE="true",SCORM2004_FALSE="false",SCORM2004_EARLIEST_DATE=new Date("1/1/1900"),intSCORM2004Error=SCORM2004_NO_ERROR,strSCORM2004ErrorString="",strSCORM2004ErrorDiagnostic="",SCORM2004_objAPI=null,blnReviewModeSoReadOnly=!1,blnSCORM2004_SSP_Is_Supported=null;function SCORM2004_Initialize(){WriteToDebug("In SCORM2004_Initialize");var e=!0;SCORM2004_ClearErrorInfo(),WriteToDebug("Grabbing API");try{SCORM2004_objAPI=SCORM2004_GrabAPI()}catch(e){WriteToDebug("Error grabbing 1.2 API-"+e.name+":"+e.message)}return void 0===SCORM2004_objAPI||null==SCORM2004_objAPI?(WriteToDebug("Unable to acquire SCORM API:"),WriteToDebug("SCORM2004_objAPI="+typeof SCORM2004_objAPI),InitializeExecuted(!1,"Error - unable to acquire LMS API, content may not play properly and results may not be recorded.  Please contact technical support."),!1):(WriteToDebug("Calling LMSInit"),(e=SCORM2004_CallInitialize())?(PREVENT_STATUS_CHANGE_DURING_INIT||SCORM2004_GetStatus()==LESSON_STATUS_NOT_ATTEMPTED&&(WriteToDebug("Setting Status to Incomplete"),e=SCORM2004_CallSetValue("cmi.completion_status",SCORM2004_INCOMPLETE)),e=SCORM2004_CallSetValue("cmi.exit",SCORM2004_TranslateExitTypeToSCORM(DEFAULT_EXIT_TYPE))&&e,SCORM2004_GetLessonMode()==MODE_REVIEW&&void 0!==REVIEW_MODE_IS_READ_ONLY&&!0===REVIEW_MODE_IS_READ_ONLY&&(blnReviewModeSoReadOnly=!0),WriteToDebug("Calling InitializeExecuted with parameter-"+e),void InitializeExecuted(e,"")):(WriteToDebug("ERROR Initializing LMS"),InitializeExecuted(!1,"Error initializing communications with LMS"),!1))}function SCORM2004_Finish(e,t){var r;WriteToDebug("In SCORM2004_Finish strExitType="+e+", blnStatusWasSet="+t);var n=!0;return SCORM2004_ClearErrorInfo(),e!=EXIT_TYPE_FINISH||t||(WriteToDebug("Getting completion status"),WriteToDebug("Setting completion status to "+(r=SCORM2004_GetCompletionStatus())),n=SCORM2004_CallSetValue("cmi.completion_status",r)&&n),e==EXIT_TYPE_SUSPEND&&USE_2004_SUSPENDALL_NAVREQ&&(WriteToDebug("Setting adl.nav.request to suspendAll"),n=SCORM2004_CallSetValue("adl.nav.request","suspendAll")),WriteToDebug("Setting Exit"),n=SCORM2004_CallSetValue("cmi.exit",SCORM2004_TranslateExitTypeToSCORM(e))&&n,WriteToDebug("Calling Commit"),n=SCORM2004_CallCommit()&&n,WriteToDebug("Calling Finish"),WriteToDebug("Returning "+(n=SCORM2004_CallTerminate()&&n)),n}function SCORM2004_CommitData(){return WriteToDebug("In SCORM2004_CommitData"),SCORM2004_ClearErrorInfo(),SCORM2004_CallCommit()}function SCORM2004_GetStudentID(){return WriteToDebug("In SCORM2004_GetStudentID"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.learner_id")}function SCORM2004_GetStudentName(){return WriteToDebug("In SCORM2004_GetStudentName"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.learner_name")}function SCORM2004_GetBookmark(){return WriteToDebug("In SCORM2004_GetBookmark"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.location")}function SCORM2004_SetBookmark(e){return WriteToDebug("In SCORM2004_SetBookmark strBookmark="+e),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.location",e)}function SCORM2004_GetDataChunk(){return WriteToDebug("In SCORM2004_GetDataChunk"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.suspend_data")}function SCORM2004_SetDataChunk(e){return WriteToDebug("In SCORM2004_SetDataChunk"),SCORM2004_ClearErrorInfo(),1==USE_STRICT_SUSPEND_DATA_LIMITS&&4e3<e.length?(WriteToDebug("SCORM2004_SetDataChunk - suspend_data too large for SCORM 2004 2nd ed (4000 character limit) but will try to persist anyway."),64e3<e.length?(WriteToDebug("SCORM2004_SetDataChunk - suspend_data too large for SCORM 2004 3rd & 4th ed (64000 character limit) so failing to persist."),!1):SCORM2004_CallSetValue("cmi.suspend_data",e)):SCORM2004_CallSetValue("cmi.suspend_data",e)}function SCORM2004_GetLaunchData(){return WriteToDebug("In SCORM2004_GetLaunchData"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.launch_data")}function SCORM2004_GetComments(){var e;WriteToDebug("In SCORM2004_GetComments"),SCORM2004_ClearErrorInfo();var t="";e=SCORM2004_CallGetValue("cmi.comments_from_learner._count");for(var r=0;r<e;r++)0<t.length&&(t+=" | "),t+=SCORM2004_CallGetValue("cmi.comments_from_learner."+r+".comment");return t}function SCORM2004_WriteComment(e){var t,r;return WriteToDebug("In SCORM2004_WriteComment strComment="+e),SCORM2004_ClearErrorInfo(),0==e.search(/ \| /)&&(e=e.substr(3)),e.replace(/\|\|/g,"|"),r=SCORM2004_CallSetValue("cmi.comments_from_learner."+(t=SCORM2004_CallGetValue("cmi.comments_from_learner._count"))+".comment",e),r=SCORM2004_CallSetValue("cmi.comments_from_learner."+t+".timestamp",ConvertDateToIso8601TimeStamp(new Date))&&r}function SCORM2004_GetLMSComments(){var e;WriteToDebug("In SCORM2004_GetLMSComments"),SCORM2004_ClearErrorInfo();var t="";e=SCORM2004_CallGetValue("cmi.comments_from_lms._count");for(var r=0;r<e;r++)0<t.length&&(t+=" \r\n"),t+=SCORM2004_CallGetValue("cmi.comments_from_lms."+r+".comment");return t}function SCORM2004_GetAudioPlayPreference(){var e;return WriteToDebug("In SCORM2004_GetAudioPlayPreference"),SCORM2004_ClearErrorInfo(),""==(e=SCORM2004_CallGetValue("cmi.learner_preference.audio_level"))&&(e=0),WriteToDebug("intTempPreference="+(e=parseInt(e,10))),0<e?(WriteToDebug("Returning On"),PREFERENCE_ON):e<=0?(WriteToDebug("Returning Off"),PREFERENCE_OFF):(WriteToDebug("Error: Invalid preference"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_PREFERENCE,"Invalid audio preference received from LMS","intTempPreference="+e),null)}function SCORM2004_GetAudioVolumePreference(){var e;return WriteToDebug("In SCORM2004_GetAudioVollumePreference"),SCORM2004_ClearErrorInfo(),WriteToDebug("intTempPreference="+(e=SCORM2004_CallGetValue("cmi.learner_preference.audio_level"))),""==e&&(e=100),(e=parseInt(e,10))<=0&&(WriteToDebug("Setting to 100"),e=100),0<e&&e<=100?(WriteToDebug("Returning "+e),e):(WriteToDebug("ERROR: invalid preference"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_PREFERENCE,"Invalid audio preference received from LMS","intTempPreference="+e),null)}function SCORM2004_SetAudioPreference(e,t){return WriteToDebug("In SCORM2004_SetAudioPreference PlayPreference="+e+", intPercentOfMaxVolume="+t),SCORM2004_ClearErrorInfo(),e==PREFERENCE_OFF&&(WriteToDebug("Setting percent to 0"),t=0),SCORM2004_CallSetValue("cmi.learner_preference.audio_level",t)}function SCORM2004_SetLanguagePreference(e){return WriteToDebug("In SCORM2004_SetLanguagePreference strLanguage="+e),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.learner_preference.language",e)}function SCORM2004_GetLanguagePreference(){return WriteToDebug("In SCORM2004_GetLanguagePreference"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.learner_preference.language")}function SCORM2004_SetSpeedPreference(e){return WriteToDebug("In SCORM2004_SetSpeedPreference intPercentOfMax="+e),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.learner_preference.delivery_speed",e)}function SCORM2004_GetSpeedPreference(){var e;return WriteToDebug("In SCORM2004_GetSpeedPreference"),SCORM2004_ClearErrorInfo(),WriteToDebug("intSCORMSpeed="+(e=SCORM2004_CallGetValue("cmi.learner_preference.delivery_speed"))),""==e&&(WriteToDebug("Detected empty string, defaulting to 100"),e=100),(e=parseInt(e,10))<0?(WriteToDebug("ERROR - out of range"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_SPEED,"Invalid speed preference received from LMS - out of range","intSCORMSpeed="+e),null):(WriteToDebug("intSCORMSpeed "+e),e)}function SCORM2004_SetTextPreference(e){return WriteToDebug("In SCORM2004_SetTextPreference intPreference="+e),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.learner_preference.audio_captioning",e)}function SCORM2004_GetTextPreference(){var e;return WriteToDebug("In SCORM2004_GetTextPreference"),SCORM2004_ClearErrorInfo(),e=SCORM2004_CallGetValue("cmi.learner_preference.audio_captioning"),WriteToDebug("intTempPreference="+(e=parseInt(e,10))),0<e?(WriteToDebug("Returning On"),PREFERENCE_ON):0==e||""==e?(WriteToDebug("Returning Default"),PREFERENCE_DEFAULT):e<0?(WriteToDebug("Returning Off"),PREFERENCE_OFF):(WriteToDebug("Error: Invalid preference"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_PREFERENCE,"Invalid text preference received from LMS","intTempPreference="+e),null)}function SCORM2004_GetPreviouslyAccumulatedTime(){var e,t;return WriteToDebug("In SCORM2004_GetPreviouslyAccumulatedTime"),SCORM2004_ClearErrorInfo(),WriteToDebug("strIso8601Time="+(e=SCORM2004_CallGetValue("cmi.total_time"))),IsValidIso8601TimeSpan(e)?(WriteToDebug("Returning "+(t=ConvertScorm2004TimeToMS(e))),t):(WriteToDebug("ERROR - Invalid Iso8601Time"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_TIMESPAN,"Invalid timespan received from LMS","strTime="+e),null)}function SCORM2004_SaveTime(e){var t;return WriteToDebug("In SCORM2004_SaveTime intMilliSeconds="+e),SCORM2004_ClearErrorInfo(),WriteToDebug("strISO8601Time="+(t=ConvertMilliSecondsIntoSCORM2004Time(e))),SCORM2004_CallSetValue("cmi.session_time",t)}function SCORM2004_GetMaxTimeAllowed(){var e,t;return WriteToDebug("In SCORM2004_GetMaxTimeAllowed"),SCORM2004_ClearErrorInfo(),WriteToDebug("strIso8601Time="+(e=SCORM2004_CallGetValue("cmi.max_time_allowed"))),""==e&&(e="20Y"),IsValidIso8601TimeSpan(e)?(WriteToDebug("intMilliseconds="+(t=ConvertScorm2004TimeToMS(ConvertScorm2004TimeToMS))),t):(WriteToDebug("ERROR - Invalid Iso8601Time"),SCORM2004_SetErrorInfoManually(SCORM_ERROR_INVALID_TIMESPAN,"Invalid timespan received from LMS","strIso8601Time="+e),null)}function SCORM2004_DisplayMessageOnTimeout(){var e;return WriteToDebug("In SCORM2004_DisplayMessageOnTimeout"),SCORM2004_ClearErrorInfo(),WriteToDebug("strTLA="+(e=SCORM2004_CallGetValue("cmi.time_limit_action"))),e==SCORM2004_TLA_EXIT_MESSAGE||e==SCORM2004_TLA_CONTINUE_MESSAGE?(WriteToDebug("returning true"),!0):e==SCORM2004_TLA_EXIT_NO_MESSAGE||e==SCORM2004_TLA_CONTINUE_NO_MESSAGE||""==e?(WriteToDebug("returning false"),!1):(WriteToDebug("Error invalid TLA"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_TIME_LIMIT_ACTION,"Invalid time limit action received from LMS","strTLA="+e),null)}function SCORM2004_ExitOnTimeout(){var e;return WriteToDebug("In SCORM2004_ExitOnTimeout"),SCORM2004_ClearErrorInfo(),WriteToDebug("strTLA="+(e=SCORM2004_CallGetValue("cmi.time_limit_action"))),e==SCORM2004_TLA_EXIT_MESSAGE||e==SCORM2004_TLA_EXIT_NO_MESSAGE?(WriteToDebug("returning true"),!0):e==SCORM2004_TLA_CONTINUE_MESSAGE||e==SCORM2004_TLA_CONTINUE_NO_MESSAGE||""==e?(WriteToDebug("returning false"),!1):(WriteToDebug("ERROR invalid TLA"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_TIME_LIMIT_ACTION,"Invalid time limit action received from LMS","strTLA="+e),null)}function SCORM2004_GetPassingScore(){var e;return WriteToDebug("In SCORM2004_GetPassingScore"),SCORM2004_ClearErrorInfo(),WriteToDebug("fltScore="+(e=SCORM2004_CallGetValue("cmi.scaled_passing_score"))),""==e&&(e=0),IsValidDecimal(e)?(e=parseFloat(e),WriteToDebug("returning fltScore-"+(e*=100)),e):(WriteToDebug("Error - score is not a valid decimal"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_DECIMAL,"Invalid mastery score received from LMS","fltScore="+e),null)}function SCORM2004_SetScore(e,t,r){var n,o;return WriteToDebug("In SCORM2004_SetScore intScore="+(e=RoundToPrecision(e,7))+", intMaxScore="+(t=RoundToPrecision(t,7))+", intMinScore="+(r=RoundToPrecision(r,7))),SCORM2004_ClearErrorInfo(),o=RoundToPrecision(e/100,7),n=SCORM2004_CallSetValue("cmi.score.raw",e),n=SCORM2004_CallSetValue("cmi.score.max",t)&&n,n=SCORM2004_CallSetValue("cmi.score.min",r)&&n,WriteToDebug("Returning "+(n=SCORM2004_CallSetValue("cmi.score.scaled",o)&&n)),n}function SCORM2004_GetScore(){return WriteToDebug("In SCORM2004_GetScore"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.score.raw")}function SCORM2004_GetScaledScore(){return WriteToDebug("In SCORM2004_GetScaledScore"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.score.scaled")}function SCORM2004_RecordInteraction(e,t,r,n,o,i,a,s,u,l){var c,S,R;return IsNumeric(r)||(r=new String(r)),SCORM2004_ClearErrorInfo(),WriteToDebug("intInteractionIndex="+(S=SCORM2004_CallGetValue("cmi.interactions._count"))),""==S&&(WriteToDebug("Setting Interaction Index to 0"),S=0),WriteToDebug("strResult="+(R=1==r||"true"==r||r==INTERACTION_RESULT_CORRECT?SCORM2004_RESULT_CORRECT:"false"==String(r)||r==INTERACTION_RESULT_WRONG?SCORM2004_RESULT_WRONG:r==INTERACTION_RESULT_UNANTICIPATED?SCORM2004_RESULT_UNANTICIPATED:r==INTERACTION_RESULT_NEUTRAL?SCORM2004_RESULT_NEUTRAL:IsNumeric(r)?r:"")),c=SCORM2004_CallSetValue("cmi.interactions."+S+".id",e=CreateValidIdentifier(e)),c=SCORM2004_CallSetValue("cmi.interactions."+S+".type",l)&&c,null!==t&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".learner_response",t)&&c),null!=R&&null!=R&&""!=R&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".result",R)&&c),null!=n&&null!=n&&""!=n&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".correct_responses.0.pattern",n)&&c),null!=o&&null!=o&&""!=o&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".description",o)&&c),null!=i&&null!=i&&""!=i&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".weighting",i)&&c),null!=a&&null!=a&&""!=a&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".latency",ConvertMilliSecondsIntoSCORM2004Time(a))&&c),null!=s&&null!=s&&""!=s&&(c=SCORM2004_CallSetValue("cmi.interactions."+S+".objectives.0.id",s)&&c),WriteToDebug("Returning "+(c=SCORM2004_CallSetValue("cmi.interactions."+S+".timestamp",ConvertDateToIso8601TimeStamp(u))&&c)),c}function SCORM2004_RecordTrueFalseInteraction(e,t,r,n,o,i,a,s,u){WriteToDebug("In SCORM2004_RecordTrueFalseInteraction strID="+e+", strResponse="+l+", blnCorrect="+r+", strCorrectResponse="+c+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u);var l=null,c=null;return t?l="true":null!==t&&(l="false"),1==n?c="true":0==n&&(c="false"),SCORM2004_RecordInteraction(e,l,r,c,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_TRUE_FALSE)}function SCORM2004_RecordMultipleChoiceInteraction(e,t,r,n,o,i,a,s,u){WriteToDebug("In SCORM2004_RecordMultipleChoiceInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u);var l=null,c="";if(null!==t){l="";for(var S=0;S<t.length;S++)0<l.length&&(l+="[,]"),l+=t[S].Long}for(S=0;S<n.length;S++)0<c.length&&(c+="[,]"),c+=n[S].Long;return SCORM2004_RecordInteraction(e,l,r,c,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_CHOICE)}function SCORM2004_RecordFillInInteraction(e,t,r,n,o,i,a,s,u){var l;return WriteToDebug("In SCORM2004_RecordFillInInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u),null==n&&(n=""),l=250<(n=new String(n)).length||250<t.length?SCORM2004_INTERACTION_TYPE_LONG_FILL_IN:SCORM2004_INTERACTION_TYPE_FILL_IN,4e3<n.length&&(n=n.substr(0,4e3)),SCORM2004_RecordInteraction(e,t,r,n,o,i,a,s,u,l)}function SCORM2004_RecordMatchingInteraction(e,t,r,n,o,i,a,s,u){WriteToDebug("In SCORM2004_RecordMatchingInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u);var l=null,c="";if(null!==t){l="";for(var S=0;S<t.length;S++)0<l.length&&(l+="[,]"),l+=t[S].Source.Long+"[.]"+t[S].Target.Long}for(S=0;S<n.length;S++)0<c.length&&(c+="[,]"),c+=n[S].Source.Long+"[.]"+n[S].Target.Long;return SCORM2004_RecordInteraction(e,l,r,c,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_MATCHING)}function SCORM2004_RecordPerformanceInteraction(e,t,r,n,o,i,a,s,u){return WriteToDebug("In SCORM2004_RecordPerformanceInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u),null!==t&&(250<(t=new String(t)).length&&(t=t.substr(0,250)),t="[.]"+t),null==n&&(n=""),250<(n=new String(n)).length&&(n=n.substr(0,250)),SCORM2004_RecordInteraction(e,t,r,n="[.]"+n,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_PERFORMANCE)}function SCORM2004_RecordSequencingInteraction(e,t,r,n,o,i,a,s,u){WriteToDebug("In SCORM2004_RecordSequencingInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u);var l=null,c="";if(null!==t){l="";for(var S=0;S<t.length;S++)0<l.length&&(l+="[,]"),l+=t[S].Long}for(S=0;S<n.length;S++)0<c.length&&(c+="[,]"),c+=n[S].Long;return SCORM2004_RecordInteraction(e,l,r,c,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_SEQUENCING)}function SCORM2004_RecordLikertInteraction(e,t,r,n,o,i,a,s,u){WriteToDebug("In RecordLikertInteraction strID="+e+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u);var l=null,c="";return null!==t&&(l=t.Long),null!=n&&(c=n.Long),SCORM2004_RecordInteraction(e,l,r,c,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_LIKERT)}function SCORM2004_RecordNumericInteraction(e,t,r,n,o,i,a,s,u){if(WriteToDebug("In SCORM2004_RecordNumericInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+s+", dtmTime="+u),null!=n&&null!=n){if(IsValidDecimal(n)&&WriteToDebug("SCORM2004_RecordNumericInteraction received decimal correct response and converted to range, strCorrectResponse="+(n=n+"[:]"+n)),!IsValidDecimalRange(n))return WriteToDebug("Returning False - SCORM2004_RecordNumericInteraction received invalid correct response decimal range, strCorrectResponse="+n),!1;if(null===ConvertDecimalRangeToDecimalBasedOnLearnerResponse(n,t,r))return WriteToDebug("Returning False - SCORM2004_RecordNumericInteraction received invalid correct response decimal range, response and correct indicator, strCorrectResponse="+n+", strResponse="+t+", blnCorrect="+r),!1}return SCORM2004_RecordInteraction(e,t,r,n,o,i,a,s,u,SCORM2004_INTERACTION_TYPE_NUMERIC)}function SCORM2004_GetEntryMode(){var e;return WriteToDebug("In SCORM2004_GetEntryMode"),SCORM2004_ClearErrorInfo(),WriteToDebug("strEntry="+(e=SCORM2004_CallGetValue("cmi.entry"))),e==SCORM2004_ENTRY_ABINITIO?(WriteToDebug("Returning first time"),ENTRY_FIRST_TIME):e==SCORM2004_ENTRY_RESUME?(WriteToDebug("Returning resume"),ENTRY_RESUME):e==SCORM2004_ENTRY_NORMAL?(WriteToDebug("returning normal"),ENTRY_REVIEW):(WriteToDebug("ERROR - invalid entry mode"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_ENTRY,"Invalid entry vocab received from LMS","strEntry="+e),null)}function SCORM2004_GetLessonMode(){var e;return WriteToDebug("In SCORM2004_GetLessonMode"),SCORM2004_ClearErrorInfo(),WriteToDebug("strLessonMode="+(e=SCORM2004_CallGetValue("cmi.mode"))),e==SCORM2004_BROWSE?(WriteToDebug("Returning browse"),MODE_BROWSE):e==SCORM2004_NORMAL?(WriteToDebug("returning normal"),MODE_NORMAL):e==SCORM2004_REVIEW?(WriteToDebug("Returning Review"),MODE_REVIEW):(WriteToDebug("ERROR - invalid lesson mode"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_LESSON_MODE,"Invalid lesson_mode vocab received from LMS","strLessonMode="+e),null)}function SCORM2004_GetTakingForCredit(){var e;return WriteToDebug("In SCORM2004_GetTakingForCredit"),SCORM2004_ClearErrorInfo(),WriteToDebug("strCredit="+(e=SCORM2004_CallGetValue("cmi.credit"))),"credit"==e?(WriteToDebug("Returning true"),!0):"no-credit"==e?(WriteToDebug("Returning false"),!1):(WriteToDebug("ERROR - invalid credit"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_CREDIT,"Invalid credit vocab received from LMS","strCredit="+e),null)}function SCORM2004_SetObjectiveScore(e,t,r,n){var o,i,a;return WriteToDebug("In SCORM2004_SetObjectiveScore, strObejctiveID="+e+", intScore="+(t=RoundToPrecision(t,7))+", intMaxScore="+(r=RoundToPrecision(r,7))+", intMinScore="+(n=RoundToPrecision(n,7))),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(o=SCORM2004_FindObjectiveIndexFromID(e))),a=RoundToPrecision(t/100,7),i=SCORM2004_CallSetValue("cmi.objectives."+o+".id",e),i=SCORM2004_CallSetValue("cmi.objectives."+o+".score.raw",t)&&i,i=SCORM2004_CallSetValue("cmi.objectives."+o+".score.max",r)&&i,i=SCORM2004_CallSetValue("cmi.objectives."+o+".score.min",n)&&i,WriteToDebug("Returning "+(i=SCORM2004_CallSetValue("cmi.objectives."+o+".score.scaled",a)&&i)),i}function SCORM2004_SetObjectiveStatus(e,t){var r,n,o="",i="";return WriteToDebug("In SCORM2004_SetObjectiveStatus strObjectiveID="+e+", Lesson_Status="+t),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(r=SCORM2004_FindObjectiveIndexFromID(e))),t==LESSON_STATUS_PASSED?(o=SCORM2004_PASSED,i=SCORM2004_COMPLETED):t==LESSON_STATUS_FAILED?(o=SCORM2004_FAILED,i=SCORM2004_COMPLETED):t==LESSON_STATUS_COMPLETED?(o=SCORM2004_UNKNOWN,i=SCORM2004_COMPLETED):t==LESSON_STATUS_BROWSED?(o=SCORM2004_UNKNOWN,i=SCORM2004_COMPLETED):t==LESSON_STATUS_INCOMPLETE?(o=SCORM2004_UNKNOWN,i=SCORM2004_INCOMPLETE):t==LESSON_STATUS_NOT_ATTEMPTED&&(o=SCORM2004_UNKNOWN,i=SCORM2004_NOT_ATTEMPTED),WriteToDebug("strSCORMSuccessStatus="+o),WriteToDebug("strSCORMCompletionStatus="+i),n=SCORM2004_CallSetValue("cmi.objectives."+r+".id",e),n=SCORM2004_CallSetValue("cmi.objectives."+r+".success_status",o)&&n,WriteToDebug("Returning "+(n=SCORM2004_CallSetValue("cmi.objectives."+r+".completion_status",i)&&n)),n}function SCORM2004_SetObjectiveDescription(e,t){var r;return WriteToDebug("In SCORM2004_SetObjectiveDescription strObjectiveID="+e+", strObjectiveDescription="+t),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(r=SCORM2004_FindObjectiveIndexFromID(e))),blnResult=SCORM2004_CallSetValue("cmi.objectives."+r+".id",e),blnResult=SCORM2004_CallSetValue("cmi.objectives."+r+".description",t)&&blnResult,WriteToDebug("Returning "+blnResult),blnResult}function SCORM2004_GetObjectiveScore(e){var t;return WriteToDebug("In SCORM2004_GetObjectiveScore, strObejctiveID="+e),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(t=SCORM2004_FindObjectiveIndexFromID(e))),SCORM2004_CallGetValue("cmi.objectives."+t+".score.raw")}function SCORM2004_GetObjectiveStatus(e){var t,r,n;return WriteToDebug("In SCORM2004_GetObjectiveStatus, strObejctiveID="+e),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(t=SCORM2004_FindObjectiveIndexFromID(e))),r=SCORM2004_CallGetValue("cmi.objectives."+t+".success_status"),n=SCORM2004_CallGetValue("cmi.objectives."+t+".completion_status"),r==SCORM2004_PASSED?(WriteToDebug("returning Passed"),LESSON_STATUS_PASSED):r==SCORM2004_FAILED?(WriteToDebug("Returning Failed"),LESSON_STATUS_FAILED):n==SCORM2004_COMPLETED?(WriteToDebug("Returning Completed"),LESSON_STATUS_COMPLETED):n==SCORM2004_INCOMPLETE?(WriteToDebug("Returning Incomplete"),LESSON_STATUS_INCOMPLETE):n==SCORM2004_NOT_ATTEMPTED||n==SCORM2004_UNKNOWN||""==n?(WriteToDebug("Returning Not Attempted"),LESSON_STATUS_NOT_ATTEMPTED):(WriteToDebug("ERROR - status not found"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_STATUS,"Invalid objective status received from LMS or initial status not yet recorded for objective","strCompletionStatus="+n),null)}function SCORM2004_GetObjectiveProgressMeasure(e){return SCORM2004_CallGetValue("cmi.objectives."+e+".progress_measure")}function SCORM2004_GetObjectiveDescription(e){var t;return WriteToDebug("In SCORM2004_GetObjectiveDescription, strObejctiveID="+e),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(t=SCORM2004_FindObjectiveIndexFromID(e))),strDescription=SCORM2004_CallGetValue("cmi.objectives."+t+".description"),strDescription}function SCORM2004_FindObjectiveIndexFromID(e){var t,r,n;if(WriteToDebug("In SCORM2004_FindObjectiveIndexFromID"),""==(t=SCORM2004_CallGetValue("cmi.objectives._count")))return WriteToDebug("Setting intCount=0"),0;for(WriteToDebug("intCount="+(t=parseInt(t,10))),r=0;r<t;r++)if(WriteToDebug("Checking index "+r),WriteToDebug("ID="+(n=SCORM2004_CallGetValue("cmi.objectives."+r+".id"))),n==e)return WriteToDebug("Found Matching index"),r;return WriteToDebug("Did not find match, returning count"),t}function SCORM2004_CreateValidIdentifier(e){return CreateUriIdentifier(e)}function SCORM2004_SetFailed(){var e;return WriteToDebug("In SCORM2004_SetFailed"),SCORM2004_ClearErrorInfo(),e=SCORM2004_CallSetValue("cmi.success_status",SCORM2004_FAILED),PASS_FAIL_SETS_COMPLETION_FOR_2004?e=SCORM2004_CallSetValue("cmi.completion_status",SCORM2004_COMPLETED)&&e:SCORM2004_CallGetValue("cmi.completion_status")!==SCORM2004_COMPLETED&&(WriteToDebug("Overriding blnStatusWasSet to false."),blnStatusWasSet=!1),e}function SCORM2004_SetPassed(){var e;return WriteToDebug("In SCORM2004_SetPassed"),SCORM2004_ClearErrorInfo(),e=SCORM2004_CallSetValue("cmi.success_status",SCORM2004_PASSED),PASS_FAIL_SETS_COMPLETION_FOR_2004?e=SCORM2004_CallSetValue("cmi.completion_status",SCORM2004_COMPLETED)&&e:SCORM2004_CallGetValue("cmi.completion_status")!==SCORM2004_COMPLETED&&(WriteToDebug("Overriding blnStatusWasSet to false."),blnStatusWasSet=!1),e}function SCORM2004_SetCompleted(){return WriteToDebug("In SCORM2004_SetCompleted"),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.completion_status",SCORM2004_COMPLETED)}function SCORM2004_ResetStatus(){var e;return WriteToDebug("In SCORM2004_ResetStatus"),SCORM2004_ClearErrorInfo(),e=SCORM2004_CallSetValue("cmi.success_status",SCORM2004_UNKNOWN),e=SCORM2004_CallSetValue("cmi.completion_status",SCORM2004_INCOMPLETE)&&e}function SCORM2004_GetStatus(){var e,t;return WriteToDebug("In SCORM2004_GetStatus"),SCORM2004_ClearErrorInfo(),e=SCORM2004_CallGetValue("cmi.success_status"),t=SCORM2004_CallGetValue("cmi.completion_status"),WriteToDebug("strSuccessStatus="+e),WriteToDebug("strCompletionStatus="+t),e==SCORM2004_PASSED?(WriteToDebug("returning Passed"),LESSON_STATUS_PASSED):e==SCORM2004_FAILED?(WriteToDebug("Returning Failed"),LESSON_STATUS_FAILED):t==SCORM2004_COMPLETED?(WriteToDebug("Returning Completed"),LESSON_STATUS_COMPLETED):t==SCORM2004_INCOMPLETE?(WriteToDebug("Returning Incomplete"),LESSON_STATUS_INCOMPLETE):t==SCORM2004_NOT_ATTEMPTED||t==SCORM2004_UNKNOWN?(WriteToDebug("Returning Not Attempted"),LESSON_STATUS_NOT_ATTEMPTED):(WriteToDebug("ERROR - status not found"),SCORM2004_SetErrorInfoManually(SCORM2004_ERROR_INVALID_STATUS,"Invalid lesson status received from LMS","strCompletionStatus="+t),null)}function SCORM2004_GetProgressMeasure(){return WriteToDebug("In SCORM2004_GetProgressMeasure"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("cmi.progress_measure")}function SCORM2004_SetProgressMeasure(e){return WriteToDebug("In SCORM2004_SetProgressMeasure"),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.progress_measure",e)}function SCORM2004_SetObjectiveProgressMeasure(e,t){var r;return WriteToDebug("In SCORM2004_SetObjectiveProgressMeasure"),WriteToDebug("In SCORM2004_SetObjectiveProgressMeasure, strObejctiveID="+e+", numMeasure="+t),SCORM2004_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(r=SCORM2004_FindObjectiveIndexFromID(e))),SCORM2004_ClearErrorInfo(),SCORM2004_CallSetValue("cmi.objectives."+r+".progress_measure",t)}function SCORM2004_IsContentInBrowseMode(){var e;return WriteToDebug("In SCORM2004_IsContentInBrowseMode"),WriteToDebug("SCORM2004_IsContentInBrowseMode,  strLessonMode="+(e=SCORM2004_CallGetValue("cmi.mode"))),e==SCORM2004_BROWSE?(WriteToDebug("Returning true"),!0):(WriteToDebug("Returning false"),!1)}function SCORM2004_TranslateExitTypeToSCORM(e){return WriteToDebug("In SCORM2004_TranslatgeExitTypeToSCORM strExitType-"+e),e==EXIT_TYPE_SUSPEND?(WriteToDebug("Returning suspend"),SCORM2004_SUSPEND):e==EXIT_TYPE_UNLOAD?(WriteToDebug("Returning Exit"),SCORM2004_NORMAL_EXIT):e==EXIT_TYPE_FINISH?(WriteToDebug("Returning Logout"),SCORM2004_NORMAL_EXIT):e==EXIT_TYPE_TIMEOUT?(WriteToDebug("Returning Timout"),SCORM2004_TIMEOUT):void 0}function SCORM2004_GetCompletionStatus(){return WriteToDebug("In SCORM2004_GetCompletionStatus"),SCORM2004_COMPLETED}function SCORM2004_SetPointBasedScore(e,t,r){var n,o;return WriteToDebug("In SCORM2004_SetPointBasedScore intScore="+e+", intMaxScore="+t+", intMinScore="+r),SCORM2004_ClearErrorInfo(),o=RoundToPrecision(o=r<=e?e/t:(WriteToDebug("intScore is lower than intMinScore. Overriding score with minscore for cmi.score.scaled"),r/t),7),n=SCORM2004_CallSetValue("cmi.score.raw",e),n=SCORM2004_CallSetValue("cmi.score.max",t)&&n,n=SCORM2004_CallSetValue("cmi.score.min",r)&&n,WriteToDebug("Returning "+(n=SCORM2004_CallSetValue("cmi.score.scaled",o)&&n)),n}function SCORM2004_FindInteractionIndexFromID(e){var t,r,n,o,i=new Date,a=new Date("1/1/1900");if(WriteToDebug("In SCORM2004_FindInteractionIndexFromID"),""==(t=SCORM2004_CallGetValue("cmi.interactions._count")))return WriteToDebug("Setting intCount=0"),null;for(WriteToDebug("intCount="+(t=parseInt(t,10))),r=0;r<t;r++)WriteToDebug("Checking index "+r),WriteToDebug("ID="+(n=SCORM2004_CallGetValue("cmi.interactions."+r+".id"))),n==e&&(WriteToDebug("Found Matching index: "+r),WriteToDebug("timestamp for "+r+": "+(i=ConvertIso8601TimeStampToDate(SCORM2004_CallGetValue("cmi.interactions."+r+".timestamp")))),a<i&&(o=r,a=i));return 0<=o?o:(WriteToDebug("Did not find match, returning null"),null)}function SCORM2004_GetInteractionType(e){var t;if(WriteToDebug("In SCORM2004_GetInteractionType, strInteractionID="+e),SCORM2004_ClearErrorInfo(),null==(t=SCORM2004_FindInteractionIndexFromID(e))||null==t)return null;switch(WriteToDebug("intInteractionIndex="+t),SCORM2004_CallGetValue("cmi.interactions."+t+".type")){case SCORM2004_INTERACTION_TYPE_FILL_IN:return INTERACTION_TYPE_FILL_IN;case SCORM2004_INTERACTION_TYPE_LONG_FILL_IN:return INTERACTION_TYPE_LONG_FILL_IN;case SCORM2004_INTERACTION_TYPE_CHOICE:return INTERACTION_TYPE_CHOICE;case SCORM2004_INTERACTION_TYPE_LIKERT:return INTERACTION_TYPE_LIKERT;case SCORM2004_INTERACTION_TYPE_MATCHING:return INTERACTION_TYPE_MATCHING;case SCORM2004_INTERACTION_TYPE_NUMERIC:return INTERACTION_TYPE_NUMERIC;case SCORM2004_INTERACTION_TYPE_PERFORMANCE:return INTERACTION_TYPE_PERFORMANCE;case SCORM2004_INTERACTION_TYPE_SEQUENCING:return INTERACTION_TYPE_SEQUENCING;case SCORM2004_INTERACTION_TYPE_TRUE_FALSE:return INTERACTION_TYPE_TRUE_FALSE;default:return""}}function SCORM2004_GetInteractionTimestamp(e){WriteToDebug("In SCORM2004_GetInteractionTimestamp, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);return WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t?null:SCORM2004_CallGetValue(ConvertIso8601TimeStampToDate("cmi.interactions."+t+".timestamp"))}function SCORM2004_GetInteractionCorrectResponses(e){WriteToDebug("In SCORM2004_GetInteractionCorrectResponses, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);if(WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t)return null;var r=SCORM2004_CallGetValue("cmi.interactions."+t+".type"),n=SCORM2004_CallGetValue("cmi.interactions."+t+".correct_responses._count");if(""==n)return WriteToDebug("Setting intCorrectResponseCount=0"),0;if(WriteToDebug("intCorrectResponseCount="+(n=parseInt(n,10))),0==n)return new Array;1<n&&WriteToDebug("SCORM Driver is not currently implemented to support multiple correct response combinations and will only return the first");var o=new String(SCORM2004_CallGetValue("cmi.interactions."+t+".correct_responses.0.pattern")).split("[,]");return WriteToDebug("aryResponse.length = "+o.length),WriteToDebug("aryResponse.length = "+(o=SCORM2004_ProcessResponseArray(r,o)).length),o}function SCORM2004_GetInteractionWeighting(e){WriteToDebug("In SCORM2004_GetInteractionWeighting, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);return WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t?null:SCORM2004_CallGetValue("cmi.interactions."+t+".weighting")}function SCORM2004_GetInteractionLearnerResponses(e){WriteToDebug("In SCORM2004_GetInteractionLearnerResponses, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);if(WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t)return null;var r=SCORM2004_CallGetValue("cmi.interactions."+t+".type"),n=new String(SCORM2004_CallGetValue("cmi.interactions."+t+".learner_response")).split("[,]");return WriteToDebug("aryResponses.length = "+n.length),n=SCORM2004_ProcessResponseArray(r,n)}function SCORM2004_ProcessResponseArray(e,t){WriteToDebug("Processing Response Array with "+t.length+" pieces");for(var r=0;r<t.length;r++)e==SCORM2004_INTERACTION_TYPE_MATCHING&&(WriteToDebug("processing matching type, i="+r),t[r]=CreateMatchingResponse(t[r]));return t}function SCORM2004_GetInteractionResult(e){WriteToDebug("In SCORM2004_GetInteractionResult, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);return WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t?null:SCORM2004_CallGetValue("cmi.interactions."+t+".result")}function SCORM2004_GetInteractionLatency(e){WriteToDebug("In SCORM2004_GetInteractionLatency, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);if(WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t)return null;var r=SCORM2004_CallGetValue("cmi.interactions."+t+".latency");WriteToDebug("latency returns: "+r);var n=ConvertScorm2004TimeToMS(r);return WriteToDebug("latency in milliseconds: "+n),n}function SCORM2004_GetInteractionDescription(e){WriteToDebug("In SCORM2004_GetInteractionDescription, strInteractionID="+e);var t=SCORM2004_FindInteractionIndexFromID(e);return WriteToDebug("intInteractionIndex="+t),SCORM2004_ClearErrorInfo(),null==t||null==t?null:SCORM2004_CallGetValue("cmi.interactions."+t+".description")}function SCORM2004_CreateDataBucket(e,t,r,n){return WriteToDebug("In SCORM2004_CreateDataBucket, strBucketId="+e+", intMinSize="+t+", intMaxSize="+r+", course="+n),SCORM2004_DetectSSPSupport()?1==SCORM2004_DoesBucketExist(e)?(WriteToDebug("Bucket already exists and can't be re-allocated."),!1):SCORM2004_CallSetValue("ssp.allocate","{bucketID="+e+"}{requested="+r+"}{minimum="+t+"}{reducible=true}{persistence="+n+"}"):(WriteToDebug("SSP is not supported in this LMS, returning false."),!1)}function SCORM2004_GetDataFromBucket(e){return WriteToDebug("In SCORM2004_GetDataFromBucket, strBucketId="+e),SCORM2004_DetectSSPSupport()?SCORM2004_CallGetValue("ssp.data.{bucketID="+e+"}"):(WriteToDebug("SSP is not supported in this LMS, returning empty string."),"")}function SCORM2004_PutDataInBucket(e,t,r){return WriteToDebug("In SCORM2004_PutDataInBucket, strBucketId="+e+", blnAppendToEnd="+r+", strData="+t),SCORM2004_DetectSSPSupport()?SCORM2004_CallSetValue(1==r?"ssp.appendData":"ssp.data","{bucketID="+e+"}"+t):(WriteToDebug("SSP is not supported in this LMS, returning false."),!1)}function SCORM2004_DetectSSPSupport(){if(WriteToDebug("In SCORM2004_DetectSSPSupport"),1==blnSCORM2004_SSP_Is_Supported)return WriteToDebug("Support already detected, returning true"),!0;if(0==blnSCORM2004_SSP_Is_Supported)return WriteToDebug("Support already determined to me missing, returning false"),!1;SCORM2004_CallGetValue("ssp._count");return blnSCORM2004_SSP_Is_Supported=SCORM2004_GetLastError()==NO_ERROR?(WriteToDebug("SSP data model call succeeded, SSP is supported"),!0):(WriteToDebug("SSP data model call failed, SSP is NOT supported"),!1)}function SCORM2004_GetBucketInfo(e){WriteToDebug("In SCORM2004_GetBucketInfo, strBucketId="+e);var t=0,r=0,n=new String(SCORM2004_CallGetValue("ssp.bucket_state.{bucketID="+e+"}"));if(""==n||null==n||null==n)return WriteToDebug("Could not retrieve bucket state, returning 0 total size and 0 used size"),new SSPBucketSize(0,0);var o=n.split("{");for(var i in o)0==(i=(i=new String(o[i])).replace("}","")).indexOf("totalSpace",0)?(WriteToDebug("Found total space"),WriteToDebug("total space="+(t=parseInt(i.substr(11),10)))):0==i.indexOf("used",0)&&(WriteToDebug("Found used space"),WriteToDebug("used="+(r=parseInt(i.substr(5),10))));return new SSPBucketSize(t,r)}function SCORM2004_DoesBucketExist(e){WriteToDebug("In SCORM2004_DoesBucketExist, strBucketId="+e);var t=SCORM2004_CallGetValue("ssp._count");t=parseInt(t,10);for(var r=0;r<t;r++)if(e==SCORM2004_CallGetValue("ssp."+r+".id"))return WriteToDebug("Bucket '"+e+"' Exists"),!0;return WriteToDebug("Bucket '"+e+"' DOES NOT Exist"),!1}function SCORM2004_SetNavigationRequest(e){WriteToDebug("In SCORM2004_SetNavigationRequest, strNavRequest="+e),SCORM2004_ClearErrorInfo();if(e.match(/^\{target=[.A-Za-z0-9_-]+\}choice$/))return SCORM2004_CallSetValue("adl.nav.request",e),!0;switch(e){case"continue":case"previous":case"exit":case"exitAll":case"abandon":case"abandonAll":case"suspendAll":case"_none_":break;default:return WriteToDebug("In SCORM2004_SetNavigationRequest, NavRequest is not valid - strNavRequest="+e),!1}return SCORM2004_CallSetValue("adl.nav.request",e),!0}function SCORM2004_GetNavigationRequest(){return WriteToDebug("In SCORM2004_GetNavigationRequest"),SCORM2004_ClearErrorInfo(),SCORM2004_CallGetValue("adl.nav.request")}function SCORM2004_CallInitialize(){var e;return WriteToDebug("In SCORM2004_CallInitialize"),SCORM2004_objAPI=SCORM2004_GrabAPI(),WriteToDebug("Calling Initialize"),e=SCORM2004_objAPI.Initialize(""),WriteToDebug("strResult="+(e+="")),e==SCORM2004_FALSE?(WriteToDebug("Detected failed call to initialize"),SCORM2004_SetErrorInfo(),WriteToDebug("Error calling Initialize:"),WriteToDebug("              Error Number="+intSCORM2004Error),WriteToDebug("              Error String="+strSCORM2004ErrorString),WriteToDebug("              Diagnostic="+strSCORM2004ErrorDiagnostic),!1):(WriteToDebug("Returning true"),!0)}function SCORM2004_CallSetValue(e,t){var r;return WriteToDebug("SCORM2004_CallSetValue strElement="+e+", strValue="+t),!0===blnReviewModeSoReadOnly?(WriteToDebug("Mode is Review and configuration setting dictates this should be read only so exiting."),!0):(SCORM2004_objAPI=SCORM2004_GrabAPI(),WriteToDebug("Calling SetValue"),e+="",t+="",r=SCORM2004_objAPI.SetValue(e,t),WriteToDebug("strResult="+(r+="")),r==SCORM2004_FALSE?(WriteToDebug("Detected Failed call to SetValue"),SCORM2004_SetErrorInfo(),WriteToDebug("Error calling SetValue:"),WriteToDebug("              strElement="+e),WriteToDebug("              strValue="+t),WriteToDebug("              Error Number="+intSCORM2004Error),WriteToDebug("              Error String="+strSCORM2004ErrorString),WriteToDebug("              Diagnostic="+strSCORM2004ErrorDiagnostic),!1):(WriteToDebug("Returning true"),!0))}function SCORM2004_CallGetValue(e){var t;return WriteToDebug("In SCORM2004_CallGetValue strElement="+e),SCORM2004_objAPI=SCORM2004_GrabAPI(),WriteToDebug("Call GetValue"),e+="",WriteToDebug("strResult="+(t=SCORM2004_objAPI.GetValue(e)+"")),intSCORM2004Error=SCORM2004_objAPI.GetLastError(),WriteToDebug("intSCORM2004Error="+(intSCORM2004Error+="")),intSCORM2004Error!=SCORM2004_NO_ERROR&&(WriteToDebug("Detected failed called to GetValue"),SCORM2004_SetErrorInfo(),WriteToDebug("Error calling LMSGetValue:"),WriteToDebug("              strElement="+e),WriteToDebug("              Error Number="+intSCORM2004Error),WriteToDebug("              Error String="+strSCORM2004ErrorString),WriteToDebug("              Diagnostic="+strSCORM2004ErrorDiagnostic)),WriteToDebug("Returning "+t),t}function SCORM2004_CallCommit(){var e;return WriteToDebug("In SCORM2004_CallCommit"),SCORM2004_objAPI=SCORM2004_GrabAPI(),WriteToDebug("Calling Commit"),e=SCORM2004_objAPI.Commit(""),WriteToDebug("strResult="+(e+="")),e==SCORM2004_FALSE?(WriteToDebug("Detected failed call to Commit"),SCORM2004_SetErrorInfo(),WriteToDebug("Error calling Commit:"),WriteToDebug("              Error Number="+intSCORM2004Error),WriteToDebug("              Error String="+strSCORM2004ErrorString),WriteToDebug("              Diagnostic="+strSCORM2004ErrorDiagnostic),!1):(WriteToDebug("Returning true"),!0)}function SCORM2004_CallTerminate(){var e;return WriteToDebug("In SCORM2004_CallTerminate"),SCORM2004_objAPI=SCORM2004_GrabAPI(),WriteToDebug("Calling Terminate"),e=SCORM2004_objAPI.Terminate(""),WriteToDebug("strResult="+(e+="")),e==SCORM2004_FALSE?(WriteToDebug("Detected failed call to Terminate"),SCORM2004_SetErrorInfo(),WriteToDebug("Error calling Terminate:"),WriteToDebug("              Error Number="+intSCORM2004Error),WriteToDebug("              Error String="+strSCORM2004ErrorString),WriteToDebug("              Diagnostic="+strSCORM2004ErrorDiagnostic),!1):(WriteToDebug("Returning True"),!0)}function SCORM2004_ClearErrorInfo(){WriteToDebug("In SCORM2004_ClearErrorInfo"),intSCORM2004Error=SCORM2004_NO_ERROR,strSCORM2004ErrorDiagnostic=strSCORM2004ErrorString=""}function SCORM2004_SetErrorInfo(){WriteToDebug("In SCORM2004_SetErrorInfo"),intSCORM2004Error=SCORM2004_objAPI.GetLastError(),strSCORM2004ErrorString=SCORM2004_objAPI.GetErrorString(intSCORM2004Error),strSCORM2004ErrorDiagnostic=SCORM2004_objAPI.GetDiagnostic(""),strSCORM2004ErrorString+="",strSCORM2004ErrorDiagnostic+="",WriteToDebug("intSCORM2004Error="+(intSCORM2004Error+="")),WriteToDebug("strSCORM2004ErrorString="+strSCORM2004ErrorString),WriteToDebug("strSCORM2004ErrorDiagnostic="+strSCORM2004ErrorDiagnostic)}function SCORM2004_SetErrorInfoManually(e,t,r){WriteToDebug("In SCORM2004_SetErrorInfoManually"),WriteToDebug("ERROR-Num="+e),WriteToDebug("      String="+t),WriteToDebug("      Diag="+r),intSCORM2004Error=e,strSCORM2004ErrorString=t,strSCORM2004ErrorDiagnostic=r}function SCORM2004_GetLastError(){return WriteToDebug("In SCORM2004_GetLastError"),intSCORM2004Error==SCORM2004_NO_ERROR?(WriteToDebug("Returning No Error"),NO_ERROR):(WriteToDebug("Returning "+intSCORMError),intSCORM2004Error)}function SCORM2004_GetLastErrorDesc(){return WriteToDebug("In SCORM2004_GetLastErrorDesc, "+strSCORM2004ErrorString+"\n"+strSCORM2004ErrorDiagnostic),strSCORM2004ErrorString+"\n"+strSCORM2004ErrorDiagnostic}function SCORM2004_GrabAPI(){return WriteToDebug("In SCORM2004_GrabAPI"),void 0!==SCORM2004_objAPI&&null!=SCORM2004_objAPI||(WriteToDebug("Searching with Rustici Software algorithm"),SCORM2004_objAPI=SCORM2004_GetAPI()),void 0!==SCORM2004_objAPI&&null!=SCORM2004_objAPI&&0!=SCORM2004_objAPI||(WriteToDebug("Searching with SearchForAPI"),SCORM2004_objAPI=SCORM2004_SearchForAPI(window)),WriteToDebug("Grab API, returning, found API = "+(null!=SCORM2004_objAPI)),SCORM2004_objAPI}function SCORM2004_ScanParentsForApi(e){for(var t=0;(null==e.API_1484_11||null==e.API_1484_11)&&null!=e.parent&&e.parent!=e&&t<=500;)t++,e=e.parent;return e.API_1484_11}function SCORM2004_GetAPI(){var e=null;return null!=window.parent&&window.parent!=window&&(e=SCORM2004_ScanParentsForApi(window.parent)),null==e&&null!=window.top.opener&&(e=SCORM2004_ScanParentsForApi(window.top.opener)),e}function SCORM2004_SearchForAPI(e){WriteToDebug("SCORM2004_SearchForAPI");var t,r=null;return t="Name="+e.name+", href="+e.location.href,SCORM2004_APIFound(r=e.API_1484_11)?(WriteToDebug("Found API in this window - "+t),r):(SCORM2004_WindowHasParent(e)&&(WriteToDebug("Searching Parent - "+t),r=SCORM2004_SearchForAPI(e.parent)),SCORM2004_APIFound(r)?(WriteToDebug("Found API in a parent - "+t),r):(SCORM2004_WindowHasOpener(e)&&(WriteToDebug("Searching Opener - "+t),r=SCORM2004_SearchForAPI(e.opener)),SCORM2004_APIFound(r)?(WriteToDebug("Found API in an opener - "+t),r):(WriteToDebug("Looking in children - "+t),SCORM2004_APIFound(r=SCORM2004_LookInChildren(e))?(WriteToDebug("Found API in Children - "+t),r):(WriteToDebug("Didn't find API in this window - "+t),null))))}function SCORM2004_LookInChildren(e){WriteToDebug("SCORM2004_LookInChildren");var t,r=null;t="Name="+e.name+", href="+e.location.href;for(var n=0;n<e.frames.length;n++){if(WriteToDebug("Looking in child frame "+n),SCORM2004_APIFound(r=e.frames[n].API_1484_11))return WriteToDebug("Found API in child frame of "+t),r;if(WriteToDebug("Looking in this child's children "+t),SCORM2004_APIFound(r=SCORM2004_LookInChildren(e.frames[n])))return WriteToDebug("API found in this child's children "+t),r}return null}function SCORM2004_WindowHasOpener(e){return WriteToDebug("In SCORM2004_WindowHasOpener"),null!=e.opener&&e.opener!=e&&void 0!==e.opener?(WriteToDebug("Window Does Have Opener"),!0):(WriteToDebug("Window Does Not Have Opener"),!1)}function SCORM2004_WindowHasParent(e){return WriteToDebug("In SCORM2004_WindowHasParent"),null!=e.parent&&e.parent!=e&&void 0!==e.parent?(WriteToDebug("Window Does Have Parent"),!0):(WriteToDebug("Window Does Not Have Parent"),!1)}function SCORM2004_APIFound(e){return WriteToDebug("In SCORM2004_APIFound"),null==e||void 0===e?(WriteToDebug("API NOT Found"),!1):(WriteToDebug("API Found"),!0)}function LMSStandardAPI(strStandard){WriteToDebug("In LMSStandardAPI strStandard="+strStandard),""==strStandard&&(WriteToDebug("No standard specified, using NONE"),strStandard="NONE"),eval("this.Initialize = "+strStandard+"_Initialize"),eval("this.Finish = "+strStandard+"_Finish"),eval("this.CommitData = "+strStandard+"_CommitData"),eval("this.GetStudentID = "+strStandard+"_GetStudentID"),eval("this.GetStudentName = "+strStandard+"_GetStudentName"),eval("this.GetBookmark = "+strStandard+"_GetBookmark"),eval("this.SetBookmark = "+strStandard+"_SetBookmark"),eval("this.GetDataChunk = "+strStandard+"_GetDataChunk"),eval("this.SetDataChunk = "+strStandard+"_SetDataChunk"),eval("this.GetLaunchData = "+strStandard+"_GetLaunchData"),eval("this.GetComments = "+strStandard+"_GetComments"),eval("this.WriteComment = "+strStandard+"_WriteComment"),eval("this.GetLMSComments = "+strStandard+"_GetLMSComments"),eval("this.GetAudioPlayPreference = "+strStandard+"_GetAudioPlayPreference"),eval("this.GetAudioVolumePreference = "+strStandard+"_GetAudioVolumePreference"),eval("this.SetAudioPreference = "+strStandard+"_SetAudioPreference"),eval("this.SetLanguagePreference = "+strStandard+"_SetLanguagePreference"),eval("this.GetLanguagePreference = "+strStandard+"_GetLanguagePreference"),eval("this.SetSpeedPreference = "+strStandard+"_SetSpeedPreference"),eval("this.GetSpeedPreference = "+strStandard+"_GetSpeedPreference"),eval("this.SetTextPreference = "+strStandard+"_SetTextPreference"),eval("this.GetTextPreference = "+strStandard+"_GetTextPreference"),eval("this.GetPreviouslyAccumulatedTime = "+strStandard+"_GetPreviouslyAccumulatedTime"),eval("this.SaveTime = "+strStandard+"_SaveTime"),eval("this.GetMaxTimeAllowed = "+strStandard+"_GetMaxTimeAllowed"),eval("this.DisplayMessageOnTimeout = "+strStandard+"_DisplayMessageOnTimeout"),eval("this.ExitOnTimeout = "+strStandard+"_ExitOnTimeout"),eval("this.GetPassingScore = "+strStandard+"_GetPassingScore"),eval("this.SetScore = "+strStandard+"_SetScore"),eval("this.GetScore = "+strStandard+"_GetScore"),eval("this.GetScaledScore = "+strStandard+"_GetScaledScore"),eval("this.RecordTrueFalseInteraction = "+strStandard+"_RecordTrueFalseInteraction"),eval("this.RecordMultipleChoiceInteraction = "+strStandard+"_RecordMultipleChoiceInteraction"),eval("this.RecordFillInInteraction = "+strStandard+"_RecordFillInInteraction"),eval("this.RecordMatchingInteraction = "+strStandard+"_RecordMatchingInteraction"),eval("this.RecordPerformanceInteraction = "+strStandard+"_RecordPerformanceInteraction"),eval("this.RecordSequencingInteraction = "+strStandard+"_RecordSequencingInteraction"),eval("this.RecordLikertInteraction = "+strStandard+"_RecordLikertInteraction"),eval("this.RecordNumericInteraction = "+strStandard+"_RecordNumericInteraction"),eval("this.GetEntryMode = "+strStandard+"_GetEntryMode"),eval("this.GetLessonMode = "+strStandard+"_GetLessonMode"),eval("this.GetTakingForCredit = "+strStandard+"_GetTakingForCredit"),eval("this.SetObjectiveScore = "+strStandard+"_SetObjectiveScore"),eval("this.SetObjectiveStatus = "+strStandard+"_SetObjectiveStatus"),eval("this.GetObjectiveScore = "+strStandard+"_GetObjectiveScore"),eval("this.GetObjectiveStatus = "+strStandard+"_GetObjectiveStatus"),eval("this.SetObjectiveDescription = "+strStandard+"_SetObjectiveDescription"),eval("this.GetObjectiveDescription = "+strStandard+"_GetObjectiveDescription"),eval("this.SetFailed = "+strStandard+"_SetFailed"),eval("this.SetPassed = "+strStandard+"_SetPassed"),eval("this.SetCompleted = "+strStandard+"_SetCompleted"),eval("this.ResetStatus = "+strStandard+"_ResetStatus"),eval("this.GetStatus = "+strStandard+"_GetStatus"),eval("this.GetLastError = "+strStandard+"_GetLastError"),eval("this.GetLastErrorDesc = "+strStandard+"_GetLastErrorDesc"),eval("this.GetInteractionType = "+strStandard+"_GetInteractionType"),eval("this.GetInteractionTimestamp = "+strStandard+"_GetInteractionTimestamp"),eval("this.GetInteractionCorrectResponses = "+strStandard+"_GetInteractionCorrectResponses"),eval("this.GetInteractionWeighting = "+strStandard+"_GetInteractionWeighting"),eval("this.GetInteractionLearnerResponses = "+strStandard+"_GetInteractionLearnerResponses"),eval("this.GetInteractionResult = "+strStandard+"_GetInteractionResult"),eval("this.GetInteractionLatency = "+strStandard+"_GetInteractionLatency"),eval("this.GetInteractionDescription = "+strStandard+"_GetInteractionDescription"),eval("this.CreateDataBucket = "+strStandard+"_CreateDataBucket"),eval("this.GetDataFromBucket = "+strStandard+"_GetDataFromBucket"),eval("this.PutDataInBucket = "+strStandard+"_PutDataInBucket"),eval("this.DetectSSPSupport = "+strStandard+"_DetectSSPSupport"),eval("this.GetBucketInfo = "+strStandard+"_GetBucketInfo"),eval("this.GetProgressMeasure = "+strStandard+"_GetProgressMeasure"),eval("this.SetProgressMeasure = "+strStandard+"_SetProgressMeasure"),eval("this.SetPointBasedScore = "+strStandard+"_SetPointBasedScore"),eval("this.SetNavigationRequest = "+strStandard+"_SetNavigationRequest"),eval("this.GetNavigationRequest = "+strStandard+"_GetNavigationRequest"),eval("this.SetObjectiveProgressMeasure = "+strStandard+"_SetObjectiveProgressMeasure"),eval("this.GetObjectiveProgressMeasure = "+strStandard+"_GetObjectiveProgressMeasure"),eval("this.CreateValidIdentifier = "+strStandard+"_CreateValidIdentifier"),void 0!==window[strStandard+"_ConcedeControl"]&&eval("this.ConcedeControl = "+strStandard+"_ConcedeControl"),this.Standard=strStandard}var blnCalledFinish=!1,blnStandAlone=!1,blnLoaded=!1,blnReachedEnd=!1,blnStatusWasSet=!1,blnLmsPresent=!1,dtmStart=null,dtmEnd=null,intAccumulatedMS=0,blnOverrodeTime=!1,intTimeOverrideMS=null,aryDebug=new Array,strDebug="",winDebug,intError=NO_ERROR,strErrorDesc="",objLMS=null;function Start(){var e,t,r=null,n="",o="",i="";if(WriteToDebug("<h1>SCORM Driver starting up</h1>"),WriteToDebug("----------------------------------------"),WriteToDebug("----------------------------------------"),WriteToDebug("In Start - Version: "+VERSION+"  Last Modified="+window.document.lastModified),WriteToDebug("Browser Info ("+navigator.appName+" "+navigator.appVersion+")"),WriteToDebug("URL: "+window.document.location.href),WriteToDebug("----------------------------------------"),WriteToDebug("----------------------------------------"),ClearErrorInfo(),WriteToDebug("strStandAlone="+(e=GetQueryStringValue("StandAlone",window.location.search))+"  strShowInteractiveDebug="+(t=GetQueryStringValue("ShowDebug",window.location.search))),ConvertStringToBoolean(e)&&(WriteToDebug("Entering Stand Alone Mode"),blnStandAlone=!0),blnStandAlone)WriteToDebug("Using NONE Standard"),objLMS=new LMSStandardAPI("NONE");else if(WriteToDebug("Standard From Configuration File - "+strLMSStandard),"AUTO"==strLMSStandard.toUpperCase())if(WriteToDebug("Searching for recognized querystring parameters"),n=GetQueryStringValue("AICC_URL",document.location.search),o=GetQueryStringValue("endpoint",document.location.search),i=GetQueryStringValue("fetch",document.location.search),null!=n&&""!=n)WriteToDebug("Found AICC querystring parameters, using AICC"),objLMS=new LMSStandardAPI("AICC"),blnLmsPresent=!0;else if(null!=o&&""!=o)WriteToDebug("Found endpoint querystring parameter - checking cmi5 or Tin Can"),null!=i&&""!=i?(WriteToDebug("Found fetch querystring parameter, using cmi5"),objLMS=new LMSStandardAPI("CMI5"),blnLmsPresent=!0):(WriteToDebug("Did not find fetch querystring parameter, using Tin Can"),objLMS=new LMSStandardAPI("TCAPI"),blnLmsPresent=!0,strLMSStandard="TCAPI");else{WriteToDebug("Auto-detecting standard - Searching for SCORM 2004 API");try{r=SCORM2004_GrabAPI()}catch(e){WriteToDebug("Error grabbing 2004 API-"+e.name+":"+e.message)}if(void 0!==r&&null!=r)WriteToDebug("Found SCORM 2004 API, using SCORM 2004"),objLMS=new LMSStandardAPI("SCORM2004"),blnLmsPresent=!0;else{WriteToDebug("Searching for SCORM 1.2 API");try{r=SCORM_GrabAPI()}catch(e){WriteToDebug("Error grabbing 1.2 API-"+e.name+":"+e.message)}if(void 0!==r&&null!=r)WriteToDebug("Found SCORM API, using SCORM"),objLMS=new LMSStandardAPI("SCORM"),blnLmsPresent=!0;else{if(!0!==ALLOW_NONE_STANDARD)return WriteToDebug("Could not determine standard, Stand Alone is disabled in configuration"),void DisplayError("Could not determine standard. SCORM, AICC, Tin Can, and CMI5 APIs could not be found");WriteToDebug("Could not determine standard, defaulting to Stand Alone"),objLMS=new LMSStandardAPI("NONE")}}}else WriteToDebug("Using Standard From Configuration File - "+strLMSStandard),objLMS=new LMSStandardAPI(strLMSStandard),blnLmsPresent=!0;(ConvertStringToBoolean(t)||void 0!==SHOW_DEBUG_ON_LAUNCH&&!0===SHOW_DEBUG_ON_LAUNCH)&&(WriteToDebug("Showing Interactive Debug Windows"),ShowDebugWindow()),WriteToDebug("Calling Standard Initialize"),"TCAPI"==strLMSStandard.toUpperCase()?loadScript("../tc-config.js",objLMS.Initialize):objLMS.Initialize(),TouchCloud()}function InitializeExecuted(e,t){if(WriteToDebug("In InitializeExecuted, blnSuccess="+e+", strErrorMessage="+t),!e)return WriteToDebug("ERROR - LMS Initialize Failed"),""==t&&(t="An Error Has Occurred"),blnLmsPresent=!1,void DisplayError(t);"AICC"==objLMS.Standard&&AICC_InitializeExecuted(),blnLoaded=!0,dtmStart=new Date,LoadContent()}function ExecFinish(e){return WriteToDebug("In ExecFinish, ExiType="+e),ClearErrorInfo(),!(blnLoaded&&!blnCalledFinish)||(WriteToDebug("Haven't called finish before, finishing"),blnCalledFinish=!0,blnReachedEnd&&!EXIT_SUSPEND_IF_COMPLETED&&(WriteToDebug("Reached End, overiding exit type to FINISH"),e=EXIT_TYPE_FINISH),1==EXIT_NORMAL_IF_PASSED&&objLMS.GetStatus()==LESSON_STATUS_PASSED&&(WriteToDebug("Passed status and config value set, overiding exit type to FINISH"),e=EXIT_TYPE_FINISH),blnOverrodeTime||(WriteToDebug("Did not override time"),dtmEnd=new Date,AccumulateTime(),objLMS.SaveTime(intAccumulatedMS)),blnLoaded=!1,WriteToDebug("Calling LMS Finish"),objLMS.Finish(e,blnStatusWasSet))}function IsLoaded(){return WriteToDebug("In IsLoaded, returning -"+blnLoaded),blnLoaded}function WriteToDebug(e){if(blnDebug){var t,r=new Date;t=aryDebug.length+":"+r.toString()+" - "+e,aryDebug[aryDebug.length]=t,winDebug&&!winDebug.closed&&(winDebug.document.body.appendChild(winDebug.document.createTextNode(t)),winDebug.document.body.appendChild(winDebug.document.createElement("br")))}}function ShowDebugWindow(){var e=function(){var e,t=aryDebug.length;for(winDebug.document.body.innerHTML="",e=0;e<t;e+=1)winDebug.document.body.appendChild(winDebug.document.createTextNode(aryDebug[e])),winDebug.document.body.appendChild(winDebug.document.createElement("br"))};winDebug&&!winDebug.closed&&winDebug.close(),null===(winDebug=window.open("blank.html","Debug","width=600,height=300,resizable,scrollbars"))?alert("Debug window could not be opened, popup blocker in place?"):((winDebug.addEventListener||winDebug.attachEvent)&&winDebug[winDebug.addEventListener?"addEventListener":"attachEvent"]((winDebug.attachEvent?"on":"")+"load",e,!1),e(),winDebug.document.close(),winDebug.focus())}function DisplayError(e){WriteToDebug("In DisplayError, strMessage="+e),confirm("An error has occurred:\n\n"+e+"\n\nPress 'OK' to view debug information to send to technical support.")&&ShowDebugWindow()}function GetLastError(){return WriteToDebug("In GetLastError, intError="+intError),intError!=NO_ERROR?(WriteToDebug("Returning API Error"),intError):IsLoaded()&&objLMS.GetLastError()!=NO_ERROR?(WriteToDebug("Returning LMS Error"),ERROR_LMS):(WriteToDebug("Returning No Error"),NO_ERROR)}function GetLastLMSErrorCode(){WriteToDebug("In GetLastLMSErrorCode, intError="+intError);var e=objLMS.GetLastError();return IsLoaded()&&e!=NO_ERROR?(WriteToDebug("Returning LMS Error: "+e),e):(WriteToDebug("Returning No Error"),NO_ERROR)}function GetLastErrorDesc(){return WriteToDebug("In GetLastErrorDesc"),intError!=NO_ERROR?(WriteToDebug("Returning API Error - "+strErrorDesc),strErrorDesc):IsLoaded()&&objLMS.GetLastError()!=NO_ERROR?(WriteToDebug("Returning LMS Error"),objLMS.GetLastErrorDesc()):(WriteToDebug("Returning No Error"),"")}function SetErrorInfo(e,t){WriteToDebug("In SetErrorInfo - Num="+e+" Desc="+t),intError=e,strErrorDesc=t}function ClearErrorInfo(){WriteToDebug("In ClearErrorInfo")}function CommitData(){return WriteToDebug("In CommitData"),ClearErrorInfo(),IsLoaded()?(blnOverrodeTime||(WriteToDebug("Did not override time, saving incremental time"),dtmEnd=new Date,AccumulateTime(),dtmStart=new Date,objLMS.SaveTime(intAccumulatedMS)),objLMS.CommitData()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function Suspend(){return WriteToDebug("In Suspend"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_SUSPEND)}function Finish(){return WriteToDebug("In Finish"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_FINISH)}function TimeOut(){return WriteToDebug("In TimeOut"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_TIMEOUT)}function Unload(){return WriteToDebug("In Unload"),ClearErrorInfo(),ExecFinish(DEFAULT_EXIT_TYPE)}function SetReachedEnd(){return WriteToDebug("In SetReachedEnd"),ClearErrorInfo(),IsLoaded()?(0==blnStatusWasSet&&objLMS.SetCompleted(),blnReachedEnd=!0):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function ConcedeControl(){if(WriteToDebug("Conceding control with type: "+EXIT_BEHAVIOR),ClearErrorInfo(),void 0!==objLMS.ConcedeControl)return Suspend(),objLMS.ConcedeControl();var e=null,t=null;switch(EXIT_BEHAVIOR){case"SCORM_RECOMMENDED":(e=SearchParentsForContentRoot())==window.top?(Suspend(),e.window.close()):(Suspend(),null!=e&&(IsAbsoluteUrl(EXIT_TARGET)?e.scormdriver_content.location.href=EXIT_TARGET:(t=GetContentRootUrlBase(e),e.scormdriver_content.location.href=t+EXIT_TARGET)));break;case"ALWAYS_CLOSE":Suspend(),window.close();break;case"ALWAYS_CLOSE_TOP":Suspend(),window.top.close();break;case"ALWAYS_CLOSE_PARENT":Suspend(),window.parent.close();break;case"NOTHING":Suspend();break;case"REDIR_CONTENT_FRAME":Suspend(),null!=(e=SearchParentsForContentRoot())&&(IsAbsoluteUrl(EXIT_TARGET)?e.scormdriver_content.location.href=EXIT_TARGET:(t=GetContentRootUrlBase(e),e.scormdriver_content.location.href=t+EXIT_TARGET))}return!0}function GetContentRootUrlBase(e){var t=e.location.href.split("?")[0].split("/");return delete t[t.length-1],e=t.join("/")}function SearchParentsForContentRoot(){var e=null,t=window,r=0;if(t.scormdriver_content)return e=t;for(;null==e&&t!=window.top&&r++<100;){if(t.scormdriver_content)return e=t;t=t.parent}return WriteToDebug("Unable to locate content root"),null}function GetStudentID(){return WriteToDebug("In GetStudentID"),ClearErrorInfo(),IsLoaded()?objLMS.GetStudentID():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetStudentName(){return WriteToDebug("In GetStudentName"),ClearErrorInfo(),IsLoaded()?objLMS.GetStudentName():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetBookmark(){return WriteToDebug("In GetBookmark"),ClearErrorInfo(),IsLoaded()?objLMS.GetBookmark():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetBookmark(e,t){return WriteToDebug("In SetBookmark - strBookmark="+e+", strDesc="+t),ClearErrorInfo(),IsLoaded()?objLMS.SetBookmark(e,t):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetDataChunk(){return WriteToDebug("In GetDataChunk"),ClearErrorInfo(),IsLoaded()?objLMS.GetDataChunk():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetDataChunk(e){return WriteToDebug("In SetDataChunk strData="+e),ClearErrorInfo(),IsLoaded()?objLMS.SetDataChunk(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLaunchData(){return WriteToDebug("In GetLaunchData"),ClearErrorInfo(),IsLoaded()?objLMS.GetLaunchData():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetComments(){var e,t,r;if(WriteToDebug("In GetComments"),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),null;if(WriteToDebug("strCommentString="+(e=objLMS.GetComments())),""!=(e=new String(e)))for(t=e.split(" | "),r=0;r<t.length;r++)WriteToDebug("Returning Comment #"+r),t[r]=new String(t[r]),t[r]=t[r].replace(/\|\|/g,"|"),WriteToDebug("Comment #"+r+"="+t[r]);else t=new Array(0);return t}function WriteComment(e){var t;return WriteToDebug("In WriteComment strComment="+e),ClearErrorInfo(),e=new String(e),IsLoaded()?(e=e.replace(/\|/g,"||"),""!=(t=objLMS.GetComments())&&"undefined"!=t&&(e=t+" | "+e),objLMS.WriteComment(e)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLMSComments(){return WriteToDebug("In GetLMSComments"),ClearErrorInfo(),IsLoaded()?objLMS.GetLMSComments():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetAudioPlayPreference(){return WriteToDebug("In GetAudioPlayPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetAudioPlayPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),PREFERENCE_DEFAULT)}function GetAudioVolumePreference(){return WriteToDebug("GetAudioVolumePreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetAudioVolumePreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),100)}function SetAudioPreference(e,t){return WriteToDebug("In SetAudioPreference PlayPreference="+e+" intPercentOfMaxVolume="+t),ClearErrorInfo(),IsLoaded()?e!=PREFERENCE_OFF&&e!=PREFERENCE_ON?(WriteToDebug("Error Invalid PlayPreference"),SetErrorInfo(ERROR_INVALID_PREFERENCE,"Invalid PlayPreference passed to SetAudioPreference, PlayPreference="+e),!1):ValidInteger(t)?(t=parseInt(t,10))<1||100<t?(WriteToDebug("Error Invalid PercentOfMaxVolume - out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxVolume passed to SetAudioPreference (must be between 1 and 100), intPercentOfMaxVolume="+t),!1):(WriteToDebug("Calling to LMS"),objLMS.SetAudioPreference(e,t)):(WriteToDebug("Error Invalid PercentOfMaxVolume - not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxVolume passed to SetAudioPreference (not an integer), intPercentOfMaxVolume="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLanguagePreference(){return WriteToDebug("In GetLanguagePreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetLanguagePreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetLanguagePreference(e){return WriteToDebug("In SetLanguagePreference strLanguage="+e),ClearErrorInfo(),IsLoaded()?objLMS.SetLanguagePreference(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetSpeedPreference(){return WriteToDebug("In GetSpeedPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetSpeedPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),100)}function SetSpeedPreference(e){return WriteToDebug("In SetSpeedPreference intPercentOfMax="+e),ClearErrorInfo(),IsLoaded()?ValidInteger(e)?(e=parseInt(e,10))<0||100<e?(WriteToDebug("ERROR Invalid Percent of MaxSpeed, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxSpeed passed to SetSpeedPreference (must be between 1 and 100), intPercentOfMax="+e),!1):(WriteToDebug("Calling to LMS"),objLMS.SetSpeedPreference(e)):(WriteToDebug("ERROR Invalid Percent of MaxSpeed, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxSpeed passed to SetSpeedPreference (not an integer), intPercentOfMax="+e),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetTextPreference(){return WriteToDebug("In GetTextPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetTextPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetTextPreference(e){return WriteToDebug("In SetTextPreference intPreference="+e),ClearErrorInfo(),IsLoaded()?e!=PREFERENCE_DEFAULT&&e!=PREFERENCE_OFF&&e!=PREFERENCE_ON?(WriteToDebug("Error - Invalid Preference"),SetErrorInfo(ERROR_INVALID_PREFERENCE,"Invalid Preference passed to SetTextPreference, intPreference="+e),!1):objLMS.SetTextPreference(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetPreviouslyAccumulatedTime(){return WriteToDebug("In GetPreviouslyAccumulatedTime"),ClearErrorInfo(),IsLoaded()?objLMS.GetPreviouslyAccumulatedTime():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function AccumulateTime(){WriteToDebug("In AccumulateTime dtmStart="+dtmStart+" dtmEnd="+dtmEnd+" intAccumulatedMS="+intAccumulatedMS),null!=dtmEnd&&null!=dtmStart&&(WriteToDebug("Accumulating Time"),WriteToDebug("intAccumulatedMS="+(intAccumulatedMS+=dtmEnd.getTime()-dtmStart.getTime())))}function GetSessionAccumulatedTime(){return WriteToDebug("In GetSessionAccumulatedTime"),ClearErrorInfo(),WriteToDebug("Setting dtmEnd to now"),dtmEnd=new Date,WriteToDebug("Accumulating Time"),AccumulateTime(),null!=dtmStart&&(WriteToDebug("Resetting dtmStart"),dtmStart=new Date),WriteToDebug("Setting dtmEnd to null"),dtmEnd=null,WriteToDebug("Returning "+intAccumulatedMS),intAccumulatedMS}function SetSessionTime(e){return WriteToDebug("In SetSessionTime"),ClearErrorInfo(),ValidInteger(e)?(e=parseInt(e,10))<0?(WriteToDebug("Error, parameter is less than 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMilliseconds passed to SetSessionTime (must be greater than 0), intMilliseconds="+e),!1):(blnOverrodeTime=!0,intTimeOverrideMS=e,objLMS.SaveTime(intTimeOverrideMS),!0):(WriteToDebug("ERROR parameter is not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMilliseconds passed to SetSessionTime (not an integer), intMilliseconds="+e),!1)}function PauseTimeTracking(){return WriteToDebug("In PauseTimeTracking"),ClearErrorInfo(),WriteToDebug("Setting dtmEnd to now"),dtmEnd=new Date,WriteToDebug("Accumulating Time"),AccumulateTime(),WriteToDebug("Setting Start and End times to null"),!(dtmEnd=dtmStart=null)}function ResumeTimeTracking(){return WriteToDebug("In ResumeTimeTracking"),ClearErrorInfo(),WriteToDebug("Setting dtmStart to now"),dtmStart=new Date,!0}function GetMaxTimeAllowed(){return WriteToDebug("In GetMaxTimeAllowed"),ClearErrorInfo(),IsLoaded()?objLMS.GetMaxTimeAllowed():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),MAX_CMI_TIME)}function DisplayMessageOnTimeout(){return WriteToDebug("In DisplayMessageOnTimeOut"),ClearErrorInfo(),IsLoaded()?objLMS.DisplayMessageOnTimeout():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function ExitOnTimeout(){return WriteToDebug("In ExitOnTimeOut"),ClearErrorInfo(),IsLoaded()?objLMS.ExitOnTimeout():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetPassingScore(){return WriteToDebug("In GetPassingScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetPassingScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function GetScore(){return WriteToDebug("In GetScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function GetScaledScore(){return WriteToDebug("In GetScaledScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetScaledScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function SetScore(e,t,r){if(WriteToDebug("In SetScore, intScore="+e+", intMaxScore="+t+", intMinScore="+r),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(!IsValidDecimal(e))return WriteToDebug("ERROR - intScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (not a valid decimal), intScore="+e),!1;if(!IsValidDecimal(t))return WriteToDebug("ERROR - intMaxScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (not a valid decimal), intMaxScore="+t),!1;if(!IsValidDecimal(r))return WriteToDebug("ERROR - intMinScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (not a valid decimal), intMinScore="+r),!1;if(WriteToDebug("Converting SCORES to floats"),e=parseFloat(e),t=parseFloat(t),r=parseFloat(r),"SCORM"==strLMSStandard){if(WriteToDebug("DEBUG - SCORM 1.2 so checking max score length"),e<0||100<e)return WriteToDebug("ERROR - intScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (must be between 0-100), intScore="+e),!1;if(t<0||100<t)return WriteToDebug("ERROR - intMaxScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (must be between 0-100), intMaxScore="+t),!1;if(r<0||100<r)return WriteToDebug("ERROR - intMinScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (must be between 0-100), intMinScore="+r),!1}if(!0===SCORE_CAN_ONLY_IMPROVE){var n=GetScore();if(null!=n&&""!=n&&e<n)return WriteToDebug("Previous score was greater than new score, configuration only allows scores to improve, returning."),!0}return WriteToDebug("Calling to LMS"),objLMS.SetScore(e,t,r)}function SetPointBasedScore(e,t,r){if(WriteToDebug("In SetPointBasedScore, intScore="+e+", intMaxScore="+t+", intMinScore="+r),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(!IsValidDecimal(e))return WriteToDebug("ERROR - intScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (not a valid decimal), intScore="+e),!1;if(!IsValidDecimal(t))return WriteToDebug("ERROR - intMaxScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (not a valid decimal), intMaxScore="+t),!1;if(!IsValidDecimal(r))return WriteToDebug("ERROR - intMinScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (not a valid decimal), intMinScore="+r),!1;if(WriteToDebug("Converting SCORES to floats"),e=parseFloat(e),t=parseFloat(t),r=parseFloat(r),"SCORM"==strLMSStandard){if(e<0||100<e)return WriteToDebug("ERROR - intScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (must be between 0-100), intScore="+e),!1;if(t<0||100<t)return WriteToDebug("ERROR - intMaxScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (must be between 0-100), intMaxScore="+t),!1;if(r<0||100<r)return WriteToDebug("ERROR - intMinScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (must be between 0-100), intMinScore="+r),!1}if(!0===SCORE_CAN_ONLY_IMPROVE){var n=GetScore();if(null!=n&&""!=n&&e<n)return WriteToDebug("Previous score was greater than new score, configuration only allows scores to improve, returning."),!0}return WriteToDebug("Calling to LMS"),objLMS.SetPointBasedScore(e,t,r)}function CreateResponseIdentifier(e,t){return""==e.replace(" ","")?(WriteToDebug("Short Identifier is empty"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1):1!=e.length?(WriteToDebug("ERROR - Short Identifier  not 1 character"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1):IsAlphaNumeric(e)?new ResponseIdentifier(e=e.toLowerCase(),t=CreateValidIdentifier(t)):(WriteToDebug("ERROR - Short Identifier  not alpha numeric"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1)}function ResponseIdentifier(e,t){this.Short=new String(e),this.Long=new String(t),this.toString=function(){return"[Response Identifier "+this.Short+", "+this.Long+"]"}}function MatchingResponse(e,t){e.constructor==String&&(e=CreateResponseIdentifier(e,e)),t.constructor==String&&(t=CreateResponseIdentifier(t,t)),this.Source=e,this.Target=t,this.toString=function(){return"[Matching Response "+this.Source+", "+this.Target+"]"}}function CreateMatchingResponse(e){var t=new Array,r=new Array;t=(e=new String(e)).split("[,]");for(var n=0;n<t.length;n++){WriteToDebug("Matching Response ["+n+"]  source: "+(r=new String(t[n]).split("[.]"))[0]+"  target: "+r[1]),t[n]=new MatchingResponse(r[0],r[1])}return WriteToDebug("pattern: "+e+" becomes "+t[0]),0==t.length?t[0]:t}function CreateValidIdentifier(e){return objLMS.CreateValidIdentifier(e)}function CreateUriIdentifier(e,t){if(null==e||""===e)return"";e=Trim(e);var r=new URI(e);return r.is("absolute")||(e="urn:scormdriver:"+encodeURIComponent(e),r=new URI(e)),r.normalize(),t&&r.iri(),r.toString()}function CreateValidIdentifierLegacy(e){return null!=e||""!=e?(e=new String(e),0==(e=Trim(e)).toLowerCase().indexOf("urn:")&&(e=e.substr(4)),e=e.replace(/[^\w\-\(\)\+\.\:\=\@\;\$\_\!\*\'\%]/g,"_")):""}function Trim(e){return e=(e=(e+="").replace(/^\s*/,"")).replace(/\s*$/,"")}function RecordTrueFalseInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordTrueFalseInteraction strID="+(e=CreateValidIdentifier(e))+", blnResponse="+t+", blnCorrect="+r+", blnCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(1!=t&&0!=t&&null!==t||null===t&&!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The Response parameter must be a valid boolean value."),!1;if(null!=n&&1!=n&&0!=n)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The Correct Response parameter must be a valid boolean value or null."),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordTrueFalseInteraction(e,t,r,n,o,i,a,s,u)}function RecordMultipleChoiceInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordMultipleChoiceInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,l;if(e=new String(e),null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==String){u=new Array;var c=CreateResponseIdentifier(t,t);if(0==c)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u[0]=c}else if(t.constructor==ResponseIdentifier)(u=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))u=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return window.console&&window.console.log("ERROR_INVALID_INTERACTION_RESPONSE :: The response is not in the correct format."),SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null!=n&&null!=n&&""!=n)if(n.constructor==String){if(l=new Array,0==(c=CreateResponseIdentifier(n,n)))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;l[0]=c}else if(n.constructor==ResponseIdentifier)(l=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))l=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;l=n}else l=new Array;var S=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordMultipleChoiceInteraction(e,u,r,l,o,i,a,s,S)}function RecordFillInInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordFillInInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordFillInInteraction(e,t,r,n,o,i,a,s,u)}function RecordMatchingInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordMatchingInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,l;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==MatchingResponse)(u=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))u=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null!=n&&null!=n)if(n.constructor==MatchingResponse)(l=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))l=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;l=n}else l=new Array;var c=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordMatchingInteraction(e,u,r,l,o,i,a,s,c)}function RecordPerformanceInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordPerformanceInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordPerformanceInteraction(e,t,r,n,o,i,a,s,u)}function RecordSequencingInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordSequencingInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,l;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==String){u=new Array;var c=CreateResponseIdentifier(t,t);if(0==c)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u[0]=c}else if(t.constructor==ResponseIdentifier)(u=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))u=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null!=n&&null!=n&&""!=n)if(n.constructor==String){if(l=new Array,0==(c=CreateResponseIdentifier(n,n)))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;l[0]=c}else if(n.constructor==ResponseIdentifier)(l=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))l=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;l=n}else l=new Array;var S=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordSequencingInteraction(e,u,r,l,o,i,a,s,S)}function RecordLikertInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordLikertInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,l;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==String)u=CreateResponseIdentifier(t,t);else{if(t.constructor!=ResponseIdentifier)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null==n||null==n)l=null;else if(n.constructor==ResponseIdentifier)l=n;else{if(n.constructor!=String)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;l=CreateResponseIdentifier(n,n)}var c=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordLikertInteraction(e,u,r,l,o,i,a,s,c)}function RecordNumericInteraction(e,t,r,n,o,i,a,s){if(WriteToDebug("In RecordNumericInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(s=CreateValidIdentifier(s))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(null===t&&!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE||null!==t&&!IsValidDecimal(t))return WriteToDebug("ERROR - Invalid Response, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Response passed to RecordNumericInteraction (not a valid decimal), strResponse="+t),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordNumericInteraction(e,t,r,n,o,i,a,s,u)}function GetStatus(){return WriteToDebug("In GetStatus"),ClearErrorInfo(),IsLoaded()?objLMS.GetStatus():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function ResetStatus(){return WriteToDebug("In ResetStatus"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to false"),blnStatusWasSet=!1,objLMS.ResetStatus()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetProgressMeasure(){return WriteToDebug("In GetProgressMeasure"),ClearErrorInfo(),IsLoaded()?objLMS.GetProgressMeasure():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function SetProgressMeasure(e){return WriteToDebug("In SetProgressMeasure, passing in: "+e),ClearErrorInfo(),IsLoaded()?objLMS.SetProgressMeasure(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function SetPassed(){return WriteToDebug("In SetPassed"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to true"),blnStatusWasSet=!0,objLMS.SetPassed()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetFailed(){return WriteToDebug("In SetFailed"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to true"),blnStatusWasSet=!0,objLMS.SetFailed()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetEntryMode(){return WriteToDebug("In GetEntryMode"),ClearErrorInfo(),IsLoaded()?objLMS.GetEntryMode():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),ENTRY_FIRST_TIME)}function GetLessonMode(){return WriteToDebug("In GetLessonMode"),ClearErrorInfo(),IsLoaded()?objLMS.GetLessonMode():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),MODE_NORMAL)}function GetTakingForCredit(){return WriteToDebug("In GetTakingForCredit"),ClearErrorInfo(),IsLoaded()?objLMS.GetTakingForCredit():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveScore(e,t,r,n){return WriteToDebug("In SetObjectiveScore, intObjectiveID="+e+", intScore="+t+", intMaxScore="+r+", intMinScore="+n),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveScore (must have a value), strObjectiveID="+e),!1):IsValidDecimal(t)?IsValidDecimal(r)?IsValidDecimal(n)?(WriteToDebug("Converting Scores to floats"),t=parseFloat(t),r=parseFloat(r),n=parseFloat(n),t<0||100<t?(WriteToDebug("ERROR - Invalid Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetObjectiveScore (must be between 0-100), intScore="+t),!1):r<0||100<r?(WriteToDebug("ERROR - Invalid Max Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetObjectiveScore (must be between 0-100), intMaxScore="+r),!1):n<0||100<n?(WriteToDebug("ERROR - Invalid Min Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetObjectiveScore (must be between 0-100), intMinScore="+n),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveScore(e,t,r,n))):(WriteToDebug("ERROR - Invalid Min Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetObjectiveScore (not a valid decimal), intMinScore="+n),!1):(WriteToDebug("ERROR - Invalid Max Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetObjectiveScore (not a valid decimal), intMaxScore="+r),!1):(WriteToDebug("ERROR - Invalid Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetObjectiveScore (not a valid decimal), intScore="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveStatus(e,t){return WriteToDebug("In SetObjectiveStatus strObjectiveID="+e+", Lesson_Status="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveStatus (must have a value), strObjectiveID="+e),!1):t!=LESSON_STATUS_PASSED&&t!=LESSON_STATUS_COMPLETED&&t!=LESSON_STATUS_FAILED&&t!=LESSON_STATUS_INCOMPLETE&&t!=LESSON_STATUS_BROWSED&&t!=LESSON_STATUS_NOT_ATTEMPTED?(WriteToDebug("ERROR - Invalid Status"),SetErrorInfo(ERROR_INVALID_STATUS,"Invalid status passed to SetObjectiveStatus, Lesson_Status="+t),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveStatus(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveStatus(e){return WriteToDebug("In GetObjectiveStatus, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveStatus(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveDescription(e,t){return WriteToDebug("In SetObjectiveDescription strObjectiveID="+e+", strObjectiveDescription="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveStatus (must have a value), strObjectiveID="+e),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveDescription(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveDescription(e){return WriteToDebug("In GetObjectiveDescription, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveDescription(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveScore(e){return WriteToDebug("In GetObjectiveScore, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveScore(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function IsLmsPresent(){return blnLmsPresent}function SetObjectiveProgressMeasure(e,t){return WriteToDebug("In SetObjectiveProgressMeasure strObjectiveID="+e+", strObjectiveProgressMeasure="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveProgressMeasure (must have a value), strObjectiveID="+e),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveProgressMeasure(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveProgressMeasure(e){return WriteToDebug("In GetObjectiveProgressMeasure, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveProgressMeasure(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetNavigationRequest(e){return WriteToDebug("In SetNavigationRequest"),ClearErrorInfo(),IsLoaded()?objLMS.SetNavigationRequest(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetNavigationRequest(){return WriteToDebug("In GetNavigationRequest"),ClearErrorInfo(),IsLoaded()?objLMS.GetNavigationRequest():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionType(e){return WriteToDebug("In GetInteractionType, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionType(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionTimestamp(e){return WriteToDebug("In GetInteractionTimestamp, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionTimestamp(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionCorrectResponses(e){return WriteToDebug("In GetInteractionCorrectResponses, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionCorrectResponses(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionWeighting(e){return WriteToDebug("In GetInteractionWeighting, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionWeighting(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionLearnerResponses(e){return WriteToDebug("In GetInteractionLearnerResponses, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionLearnerResponses(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionResult(e){return WriteToDebug("In GetInteractionResult, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionResult(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionLatency(e){return WriteToDebug("In GetInteractionLatency, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionLatency(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionDescription(e){return WriteToDebug("In GetInteractionDescription, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionDescription(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function CreateDataBucket(e,t,r){return WriteToDebug("In CreateDataBucket, strBucketId="+e+", intMinSize="+t+", intMaxSize="+r),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to CreateDataBucket (must have a value), strBucketId="+e),!1):ValidInteger(t)?ValidInteger(r)?(t=parseInt(t,10),r=parseInt(r,10),t<0?(WriteToDebug("ERROR Invalid Min Size, must be greater than or equal to 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Size passed to CreateDataBucket (must be greater than or equal to 0), intMinSize="+t),!1):r<=0?(WriteToDebug("ERROR Invalid Max Size, must be greater than 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Size passed to CreateDataBucket (must be greater than 0), intMaxSize="+r),!1):(t*=2,r*=2,objLMS.CreateDataBucket(e,t,r))):(WriteToDebug("ERROR Invalid Max Size, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMaxSize passed to CreateDataBucket (not an integer), intMaxSize="+r),!1):(WriteToDebug("ERROR Invalid Min Size, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMinSize passed to CreateDataBucket (not an integer), intMinSize="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetDataFromBucket(e){return WriteToDebug("In GetDataFromBucket, strBucketId="+e),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to GetDataFromBucket (must have a value), strBucketId="+e),!1):objLMS.GetDataFromBucket(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function PutDataInBucket(e,t,r){return WriteToDebug("In PutDataInBucket, strBucketId="+e+", blnAppendToEnd="+r+", strData="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to PutDataInBucket (must have a value), strBucketId="+e),!1):(1!=r&&(WriteToDebug("blnAppendToEnd was not explicitly true so setting it to false, blnAppendToEnd="+r),r=!1),objLMS.PutDataInBucket(e,t,r)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function DetectSSPSupport(){return objLMS.DetectSSPSupport()}function GetBucketInfo(e){if(WriteToDebug("In GetBucketInfo, strBucketId="+e),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(""==(e=new String(e)).replace(" ",""))return WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to GetBucketInfo (must have a value), strBucketId="+e),!1;var t=objLMS.GetBucketInfo(e);return t.TotalSpace=t.TotalSpace/2,t.UsedSpace=t.UsedSpace/2,WriteToDebug("GetBucketInfo returning "+t),t}function SSPBucketSize(e,t){this.TotalSpace=e,this.UsedSpace=t,this.toString=function(){return"[SSPBucketSize "+this.TotalSpace+", "+this.UsedSpace+"]"}}