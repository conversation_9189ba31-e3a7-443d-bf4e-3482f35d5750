/*! Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c */

var VERSION="7.3.1",PREFERENCE_DEFAULT=0,PREFERENCE_OFF=-1,PREFERENCE_ON=1,LESSON_STATUS_PASSED=1,LESSON_STATUS_COMPLETED=2,LESSON_STATUS_FAILED=3,LESSON_STATUS_INCOMPLETE=4,LESSON_STATUS_BROWSED=5,LESSON_STATUS_NOT_ATTEMPTED=6,ENTRY_REVIEW=1,ENTRY_FIRST_TIME=2,ENTRY_RESUME=3,MODE_NORMAL=1,MODE_BROWSE=2,MODE_REVIEW=3,MAX_CMI_TIME=36002439990,NO_ERROR=0,ERROR_LMS=1,ERROR_INVALID_PREFERENCE=2,ERROR_INVALID_NUMBER=3,ERROR_INVALID_ID=4,ERROR_INVALID_STATUS=5,ERROR_INVALID_RESPONSE=6,ERROR_NOT_LOADED=7,ERROR_INVALID_INTERACTION_RESPONSE=8,EXIT_TYPE_SUSPEND="SUSPEND",EXIT_TYPE_FINISH="FINISH",EXIT_TYPE_TIMEOUT="TIMEOUT",EXIT_TYPE_UNLOAD="UNLOAD",INTERACTION_RESULT_CORRECT="CORRECT",INTERACTION_RESULT_WRONG="WRONG",INTERACTION_RESULT_UNANTICIPATED="UNANTICIPATED",INTERACTION_RESULT_NEUTRAL="NEUTRAL",INTERACTION_TYPE_TRUE_FALSE="true-false",INTERACTION_TYPE_CHOICE="choice",INTERACTION_TYPE_FILL_IN="fill-in",INTERACTION_TYPE_LONG_FILL_IN="long-fill-in",INTERACTION_TYPE_MATCHING="matching",INTERACTION_TYPE_PERFORMANCE="performance",INTERACTION_TYPE_SEQUENCING="sequencing",INTERACTION_TYPE_LIKERT="likert",INTERACTION_TYPE_NUMERIC="numeric",DATA_CHUNK_PAIR_SEPARATOR="###",DATA_CHUNK_VALUE_SEPARATOR="$$",APPID="__APPID__",CLOUDURL="__CLOUDURL__",blnDebug=!0,strLMSStandard="SCORM",DEFAULT_EXIT_TYPE=EXIT_TYPE_SUSPEND,AICC_LESSON_ID="1",EXIT_BEHAVIOR="SCORM_RECOMMENDED",EXIT_TARGET="goodbye.html",AICC_COMM_DISABLE_XMLHTTP=!1,AICC_COMM_DISABLE_IFRAME=!1,AICC_COMM_PREPEND_HTTP_IF_MISSING=!0,AICC_REPORT_MIN_MAX_SCORE=!0,SHOW_DEBUG_ON_LAUNCH=!1,DO_NOT_REPORT_INTERACTIONS=!1,SCORE_CAN_ONLY_IMPROVE=!1,REVIEW_MODE_IS_READ_ONLY=!1,TCAPI_DONT_USE_BROKEN_URN_IDS=!0,AICC_RE_CHECK_LOADED_INTERVAL=250,AICC_RE_CHECK_ATTEMPTS_BEFORE_TIMEOUT=240,USE_AICC_KILL_TIME=!0,AICC_ENTRY_FLAG_DEFAULT=ENTRY_REVIEW,AICC_USE_CUSTOM_COMMS=!1,FORCED_COMMIT_TIME="0",ALLOW_NONE_STANDARD=!1,USE_2004_SUSPENDALL_NAVREQ=!1,USE_STRICT_SUSPEND_DATA_LIMITS=!0,EXIT_SUSPEND_IF_COMPLETED=!0,EXIT_NORMAL_IF_PASSED=!1,AICC_ENCODE_PARAMETER_VALUES=!0,PASS_FAIL_SETS_COMPLETION_FOR_2004=!0,ALLOW_INTERACTION_NULL_LEARNER_RESPONSE=!0,PREVENT_STATUS_CHANGE_DURING_INIT=!1;function GetQueryStringValue(e,t){var r;return null===(r=SearchQueryStringPairs((t=t.substring(1)).split("&"),e))&&(r=SearchQueryStringPairs(t.split(/[\?\&]/),e)),null===r?(WriteToDebug("GetQueryStringValue Element '"+e+"' Not Found, Returning: empty string"),""):(WriteToDebug("GetQueryStringValue for '"+e+"' Returning: "+r),r)}function SearchQueryStringPairs(e,t){var r,n,o="";for(t=t.toLowerCase(),r=0;r<e.length;r++)if(-1!=(n=e[r].indexOf("="))&&EqualsIgnoreCase(e[r].substring(0,n),t))return o=e[r].substring(n+1),o=(o=new String(o)).replace(/\+/g,"%20"),o=unescape(o),new String(o);return null}function ConvertStringToBoolean(e){var t;return!(!EqualsIgnoreCase(e,"true")&&!EqualsIgnoreCase(e,"t")&&0!=e.toLowerCase().indexOf("t"))||(1==(t=parseInt(e,10))||-1==t)}function EqualsIgnoreCase(e,t){return e=new String(e),t=new String(t),e.toLowerCase()==t.toLowerCase()}function ValidInteger(e){WriteToDebug("In ValidInteger intNum="+e);var t=new String(e);0==t.indexOf("-",0)&&(t=t.substring(1,t.length-1));var r=new RegExp("[^0-9]");return-1==t.search(r)?(WriteToDebug("Returning true"),!0):(WriteToDebug("Returning false"),!1)}function ConvertDateToIso8601TimeStamp(e){var t,r=(e=new Date(e)).getFullYear(),n=e.getMonth()+1,o=e.getDate(),i=e.getHours(),a=e.getMinutes(),S=e.getSeconds();t=r+"-"+(n=ZeroPad(n,2))+"-"+(o=ZeroPad(o,2))+"T"+(i=ZeroPad(i,2))+":"+(a=ZeroPad(a,2))+":"+(S=ZeroPad(S,2));var u=-e.getTimezoneOffset()/60;if(0!=u)if(t+=".0",0<u)if(-1!=(""+u).indexOf(".")){var s="0"+(""+u).substr((""+u).indexOf("."),(""+u).length);t+="+"+ZeroPad((""+u).substr(0,(""+u).indexOf("."))+"."+(s*=60),2)}else t+="+"+ZeroPad(u,2);else t+=ZeroPad(u,2);return t}function ConvertIso8601TimeStampToDate(e){e=new String(e);var t=new Array,r=(t=e.split(/[\:T+-]/))[0],n=t[1]-1,o=t[2],i=t[3],a=t[4],S=t[5];return new Date(r,n,o,i,a,S,0)}function ConvertDateToCMIDate(e){var t,r,n;return WriteToDebug("In ConvertDateToCMIDate"),t=(e=new Date(e)).getFullYear(),r=e.getMonth()+1,n=e.getDate(),ZeroPad(t,4)+"/"+ZeroPad(r,2)+"/"+ZeroPad(n,2)}function ConvertDateToCMITime(e){var t,r,n;return t=(e=new Date(e)).getHours(),r=e.getMinutes(),n=e.getSeconds(),ZeroPad(t,2)+":"+ZeroPad(r,2)+":"+ZeroPad(n,2)}function ConvertCMITimeSpanToMS(e){var t,r,n,o,i;return WriteToDebug("In ConvertCMITimeSpanToMS, strTime="+e),t=e.split(":"),IsValidCMITimeSpan(e)?(WriteToDebug("intHours="+(r=t[0])+" intMinutes="+(n=t[1])+" intSeconds="+(o=t[2])),i=36e5*r+6e4*n+1e3*o,WriteToDebug("Returning "+(i=Math.round(i))),i):(WriteToDebug("ERROR - Invalid TimeSpan"),SetErrorInfo(SCORM_ERROR_GENERAL,"LMS ERROR - Invalid time span passed to ConvertCMITimeSpanToMS, please contact technical support"),0)}function ConvertScorm2004TimeToMS(e){WriteToDebug("In ConvertScorm2004TimeToMS, strIso8601Time="+e);var t,r,n,o=0,i=0,a=0,S=0,u=0,s=0,l=0;e=new String(e),r=t="",n=!1;for(var R=1;R<e.length;R++)if(IsIso8601SectionDelimiter(r=e.charAt(R))){switch(r.toUpperCase()){case"Y":l=parseInt(t,10);break;case"M":n?a=parseInt(t,10):s=parseInt(t,10);break;case"D":u=parseInt(t,10);break;case"H":S=parseInt(t,10);break;case"S":i=parseFloat(t);break;case"T":n=!0}t=""}else t+=""+r;return WriteToDebug("Years="+l+"\nMonths="+s+"\nDays="+u+"\nHours="+S+"\nMinutes="+a+"\nSeconds="+i+"\n"),o=315576e5*l+26298e5*s+864e5*u+36e5*S+6e4*a+1e3*i,WriteToDebug("returning-"+(o=Math.round(o))),o}function IsIso8601SectionDelimiter(e){return 0<=e.search(/[PYMDTHS]/)}function IsValidCMITimeSpan(e){WriteToDebug("In IsValidCMITimeSpan strValue="+e);return-1<e.search(/^\d?\d?\d?\d:\d?\d:\d?\d(.\d\d?)?$/)?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function IsValidIso8601TimeSpan(e){WriteToDebug("In IsValidIso8601TimeSpan strValue="+e);return-1<e.search(/^P(\d+Y)?(\d+M)?(\d+D)?(T(\d+H)?(\d+M)?(\d+(.\d\d?)?S)?)?$/)?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function ConvertMilliSecondsToTCAPITime(e,t){var r,n,o,i,a,S;return WriteToDebug("In ConvertMilliSecondsToTCAPITime, intTotalMilliseconds = "+e+", blnIncludeFraction = "+t),null!=t&&null!=t||(t=!0),WriteToDebug("Separated Parts, intHours="+(r=(e-(i=e%1e3)-1e3*(o=(e-i)/1e3%60)-6e4*(n=(e-i-1e3*o)/6e4%60))/36e5)+", intMinutes="+n+", intSeconds="+o+", intMilliseconds="+i),1e4==r&&(WriteToDebug("Max intHours detected"),100==(n=(e-36e5*(r=9999))/6e4)&&(n=99),100==(o=(e-36e5*r-6e4*(n=Math.floor(n)))/1e3)&&(o=99),WriteToDebug("Separated Parts, intHours="+r+", intMinutes="+n+", intSeconds="+(o=Math.floor(o))+", intMilliseconds="+(i=e-36e5*r-6e4*n-1e3*o))),a=Math.floor(i/10),S=ZeroPad(r,4)+":"+ZeroPad(n,2)+":"+ZeroPad(o,2),t&&(S+="."+a),WriteToDebug("strCMITimeSpan="+S),9999<r&&(S="9999:99:99",t&&(S+=".99")),WriteToDebug("returning "+S),S}function ConvertMilliSecondsToSCORMTime(e,t){var r,n,o,i,a,S;return WriteToDebug("In ConvertMilliSecondsToSCORMTime, intTotalMilliseconds = "+e+", blnIncludeFraction = "+t),null!=t&&null!=t||(t=!0),WriteToDebug("Separated Parts, intHours="+(r=(e-(i=e%1e3)-1e3*(o=(e-i)/1e3%60)-6e4*(n=(e-i-1e3*o)/6e4%60))/36e5)+", intMinutes="+n+", intSeconds="+o+", intMilliseconds="+i),1e4==r&&(WriteToDebug("Max intHours detected"),100==(n=(e-36e5*(r=9999))/6e4)&&(n=99),100==(o=(e-36e5*r-6e4*(n=Math.floor(n)))/1e3)&&(o=99),WriteToDebug("Separated Parts, intHours="+r+", intMinutes="+n+", intSeconds="+(o=Math.floor(o))+", intMilliseconds="+(i=e-36e5*r-6e4*n-1e3*o))),a=Math.floor(i/10),S=ZeroPad(r,4)+":"+ZeroPad(n,2)+":"+ZeroPad(o,2),t&&(S+="."+a),WriteToDebug("strCMITimeSpan="+S),9999<r&&(S="9999:99:99",t&&(S+=".99")),WriteToDebug("returning "+S),S}function ConvertMilliSecondsIntoSCORM2004Time(e){WriteToDebug("In ConvertMilliSecondsIntoSCORM2004Time intTotalMilliseconds="+e);var t,r,n,o,i,a,S,u="",s=26298e4;return t=Math.floor(e/10),t-=315576e4*(S=Math.floor(t/315576e4)),t-=(a=Math.floor(t/s))*s,t-=864e4*(i=Math.floor(t/864e4)),t-=36e4*(o=Math.floor(t/36e4)),t-=6e3*(n=Math.floor(t/6e3)),0<S&&(u+=S+"Y"),0<a&&(u+=a+"M"),0<i&&(u+=i+"D"),0<(t-=100*(r=Math.floor(t/100)))+r+n+o&&(u+="T",0<o&&(u+=o+"H"),0<n&&(u+=n+"M"),0<t+r&&(u+=r,0<t&&(u+="."+t),u+="S")),""==u&&(u="T0S"),WriteToDebug("Returning-"+(u="P"+u)),u}function ZeroPad(e,t){var r,n,o,i;WriteToDebug("In ZeroPad intNum="+e+" intNumDigits="+t);var a=!1;if(-1!=(r=new String(e)).indexOf("-")&&(a=!0,r=r.substr(1,r.length)),-1!=r.indexOf(".")&&(r.replace(".",""),o=r.substr(r.indexOf(".")+1,r.length),r=r.substr(0,r.indexOf("."))),t<(n=r.length))WriteToDebug("Length of string is greater than num digits, trimming string"),r=r.substr(0,t);else for(i=n;i<t;i++)r="0"+r;return 1==a&&(r="-"+r),null!=o&&""!=o&&(1==o.length?r+=":"+o+"0":r+=":"+o),WriteToDebug("Returning - "+r),r}function IsValidDecimalRange(e){WriteToDebug("In IsDecimalRange, strValue="+e);var t,r,n;return 2===(t=(e=new String(e)).split("[:]")).length?(r=Trim(t[0]),n=Trim(t[1]),0<r.length&&!IsValidDecimal(r)?(WriteToDebug("Returning False - min value supplied range is not a valid decimal, min="+r),!1):0<n.length&&!IsValidDecimal(n)?(WriteToDebug("Returning False - max value supplied for range is not a valid decimal, max="+n),!1):!(0<r.length&&0<n.length&&parseFloat(r)>parseFloat(n))||(WriteToDebug("Returning False - min value supplied for range is greater than the max, min="+r+", max="+n),!1)):(WriteToDebug("Returning false - string supplied for range has incorrect number of parts, parts="+t.length+", strValue="+e),!1)}function ConvertDecimalRangeToDecimalBasedOnLearnerResponse(e,t,r){WriteToDebug("In ConvertDecimalRangeToDecimalBasedOnLearnerResponse strValue="+e+",strLearnerResponse="+t+",blnCorrect="+r);var n,o,i;if(r)return WriteToDebug("Returning strLearnerResponse"),t;if(2===(n=(e=new String(e)).split("[:]")).length){if(o=Trim(n[0]),i=Trim(n[1]),0<o.length)return WriteToDebug("Returning strMin"),o;if(0<i.length)return WriteToDebug("Returning strMax"),i}return WriteToDebug("Returning null"),null}function IsValidDecimal(e){return WriteToDebug("In IsValidDecimal, strValue="+e),-1<(e=new String(e)).search(/[^.\d-]/)?(WriteToDebug("Returning False - character other than a digit, dash or period found"),!1):-1<e.search("-")&&-1<e.indexOf("-",1)?(WriteToDebug("Returning False - dash found in the middle of the string"),!1):e.indexOf(".")!=e.lastIndexOf(".")?(WriteToDebug("Returning False - more than one decimal point found"),!1):e.search(/\d/)<0?(WriteToDebug("Returning False - no digits found"),!1):(WriteToDebug("Returning True"),!0)}function IsAlphaNumeric(e){return WriteToDebug("In IsAlphaNumeric"),e.search(/\w/)<0?(WriteToDebug("Returning false"),!1):(WriteToDebug("Returning true"),!0)}function ReverseNameSequence(e){var t,r,n;return""==e&&(e="Not Found, Learner Name"),n=e.indexOf(","),t=e.slice(n+1),r=e.slice(0,n),(t=Trim(t))+" "+(r=Trim(r))}function LTrim(e){return(e=new String(e)).replace(/^\s+/,"")}function RTrim(e){return(e=new String(e)).replace(/\s+$/,"")}function Trim(e){return LTrim(RTrim(e)).replace(/\s{2,}/g," ")}function GetValueFromDataChunk(e){var t,r=new String(GetDataChunk()),n=new Array,o=new Array;for(n=r.split(parent.DATA_CHUNK_PAIR_SEPARATOR),t=0;t<n.length;t++)if((o=n[t].split(parent.DATA_CHUNK_VALUE_SEPARATOR))[0]==e)return o[1];return""}function SetDataChunkValue(e,t){var r,n=new String(GetDataChunk()),o=new Array,i=new Array,a=new Boolean(!1);for(o=n.split(parent.DATA_CHUNK_PAIR_SEPARATOR),r=0;r<o.length;r++)(i=o[r].split(parent.DATA_CHUNK_VALUE_SEPARATOR))[0]==e&&(i[1]=t,a=!0,o[r]=i[0]+parent.DATA_CHUNK_VALUE_SEPARATOR+i[1]);return 1==a?n=o.join(parent.DATA_CHUNK_PAIR_SEPARATOR):""==n?n=e+parent.DATA_CHUNK_VALUE_SEPARATOR+t:n+=parent.DATA_CHUNK_PAIR_SEPARATOR+e+parent.DATA_CHUNK_VALUE_SEPARATOR+t,SetDataChunk(n),!0}function GetLastDirAndPageName(e){var t=new String(e),r=t.lastIndexOf("/"),n=t.lastIndexOf("/",r-1);return t.substr(n+1)}function RoundToPrecision(e,t){return e=parseFloat(e),Math.round(e*Math.pow(10,t))/Math.pow(10,t)}function IsAbsoluteUrl(e){return null!=e&&(0==e.indexOf("http://")||0==e.indexOf("https://"))}function TouchCloud(){if(null==APPID||""==APPID||"__APPID__"==APPID||null===CLOUDURL||0!==CLOUDURL.indexOf("http"))return!1;var e=document.createElement("form");e.name="cloudform",e.id="cloudform",e.style="display:none;",document.body.appendChild(e);var t=document.createElement("input");t.name="appId",t.value=APPID,t.type="hidden",e.appendChild(t);var r=document.createElement("input");r.name="servingUrl",r.type="hidden",r.value=document.location.href,e.appendChild(r);var n=document.createElement("input");return n.name="version",n.type="hidden",n.value=VERSION,e.appendChild(n),e.target="rusticisoftware_aicc_results",e.action=CLOUDURL,document.getElementById("cloudform").submit(),!0}function IsNumeric(e){return!isNaN(parseFloat(e))&&isFinite(e)}function loadScript(e,t){var r=document.getElementsByTagName("head")[0],n=document.createElement("script");n.type="text/javascript",n.src=e,!n.addEventListener||document.documentMode&&document.documentMode<9?n.onreadystatechange=function(){/loaded|complete/.test(n.readyState)&&(n.onreadystatechange=null,t())}:n.addEventListener("load",t,!1),r.appendChild(n)}var STANDARD="SCORM",SCORM_LOGOUT="logout",SCORM_SUSPEND="suspend",SCORM_NORMAL_EXIT="",SCORM_TIMEOUT="time-out",SCORM_PASSED="passed",SCORM_FAILED="failed",SCORM_COMPLETED="completed",SCORM_BROWSED="browsed",SCORM_INCOMPLETE="incomplete",SCORM_NOT_ATTEMPTED="not attempted",SCORM_CREDIT="credit",SCORM_NO_CREDIT="no-credit",SCORM_BROWSE="browse",SCORM_NORMAL="normal",SCORM_REVIEW="review",SCORM_ENTRY_ABINITIO="ab-initio",SCORM_ENTRY_RESUME="resume",SCORM_ENTRY_NORMAL="",SCORM_TLA_EXIT_MESSAGE="exit,message",SCORM_TLA_EXIT_NO_MESSAGE="exit,no message",SCORM_TLA_CONTINUE_MESSAGE="continue,message",SCORM_TLA_CONTINUE_NO_MESSAGE="continue,no message",SCORM_RESULT_CORRECT="correct",SCORM_RESULT_WRONG="wrong",SCORM_RESULT_UNANTICIPATED="unanticipated",SCORM_RESULT_NEUTRAL="neutral",SCORM_INTERACTION_TYPE_TRUE_FALSE="true-false",SCORM_INTERACTION_TYPE_CHOICE="choice",SCORM_INTERACTION_FILL_IN="fill-in",SCORM_INTERACTION_TYPE_MATCHING="matching",SCORM_INTERACTION_TYPE_PERFORMANCE="performance",SCORM_INTERACTION_TYPE_SEQUENCING="sequencing",SCORM_INTERACTION_TYPE_LIKERT="likert",SCORM_INTERACTION_TYPE_NUMERIC="numeric",SCORM_NO_ERROR="0",SCORM_ERROR_INVALID_PREFERENCE="-1",SCORM_ERROR_INVALID_STATUS="-2",SCORM_ERROR_INVALID_SPEED="-3",SCORM_ERROR_INVALID_TIMESPAN="-4",SCORM_ERROR_INVALID_TIME_LIMIT_ACTION="-5",SCORM_ERROR_INVALID_DECIMAL="-6",SCORM_ERROR_INVALID_CREDIT="-7",SCORM_ERROR_INVALID_LESSON_MODE="-8",SCORM_ERROR_INVALID_ENTRY="-9",SCORM_TRUE="true",SCORM_FALSE="false",SCORM_findAPITries=0,SCORM_objAPI=null,intSCORMError=SCORM_NO_ERROR,strSCORMErrorString="",strSCORMErrorDiagnostic="",blnReviewModeSoReadOnly=!1;function SCORM_Initialize(){var e=!0;WriteToDebug("In SCORM_Initialize"),SCORM_ClearErrorInfo(),WriteToDebug("Grabbing API");try{SCORM_objAPI=SCORM_GrabAPI()}catch(e){WriteToDebug("Error grabbing 1.2 API-"+e.name+":"+e.message)}return void 0===SCORM_objAPI||null==SCORM_objAPI?(WriteToDebug("Unable to acquire SCORM API:"),WriteToDebug("SCORM_objAPI="+typeof SCORM_objAPI),InitializeExecuted(!1,"Error - unable to acquire LMS API, content may not play properly and results may not be recorded.  Please contact technical support."),!1):(WriteToDebug("Calling LMSInit"),(e=SCORM_CallLMSInitialize())?(SCORM_GetLessonMode()!=MODE_REVIEW?(SCORM_IsContentInBrowseMode()?(WriteToDebug("Setting Status to Browsed"),e=SCORM_CallLMSSetValue("cmi.core.lesson_status",SCORM_BROWSED)):PREVENT_STATUS_CHANGE_DURING_INIT||SCORM_GetStatus()==LESSON_STATUS_NOT_ATTEMPTED&&(WriteToDebug("Setting Status to Incomplete"),e=SCORM_CallLMSSetValue("cmi.core.lesson_status",SCORM_INCOMPLETE)),e=SCORM_CallLMSSetValue("cmi.core.exit",SCORM_TranslateExitTypeToSCORM(DEFAULT_EXIT_TYPE))&&e):void 0!==REVIEW_MODE_IS_READ_ONLY&&!0===REVIEW_MODE_IS_READ_ONLY&&(blnReviewModeSoReadOnly=!0),WriteToDebug("Calling InitializeExecuted with parameter-"+e),void InitializeExecuted(e,"")):(WriteToDebug("ERROR Initializing LMS"),InitializeExecuted(!1,"Error initializing communications with LMS"),!1))}function SCORM_Finish(e,t){var r,n=!0;return WriteToDebug("In SCORM_Finish strExitType="+e+", blnStatusWasSet="+t),SCORM_ClearErrorInfo(),e!=EXIT_TYPE_FINISH||t||(WriteToDebug("Getting completion status"),WriteToDebug("Setting completion status to "+(r=SCORM_GetCompletionStatus())),n=SCORM_CallLMSSetValue("cmi.core.lesson_status",r)&&n),WriteToDebug("Setting Exit"),n=SCORM_CallLMSSetValue("cmi.core.exit",SCORM_TranslateExitTypeToSCORM(e))&&n,WriteToDebug("Calling Commit"),n=SCORM_CallLMSCommit()&&n,WriteToDebug("Calling Finish"),WriteToDebug("Returning "+(n=SCORM_CallLMSFinish()&&n)),n}function SCORM_CommitData(){return WriteToDebug("In SCORM_CommitData"),SCORM_ClearErrorInfo(),SCORM_CallLMSCommit()}function SCORM_GetStudentID(){return WriteToDebug("In SCORM_GetStudentID"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.core.student_id")}function SCORM_GetStudentName(){return WriteToDebug("In SCORM_GetStudentName"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.core.student_name")}function SCORM_GetBookmark(){return WriteToDebug("In SCORM_GetBookmark"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.core.lesson_location")}function SCORM_SetBookmark(e){return WriteToDebug("In SCORM_SetBookmark strBookmark="+e),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.core.lesson_location",e)}function SCORM_GetDataChunk(){return WriteToDebug("In SCORM_GetDataChunk"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.suspend_data")}function SCORM_SetDataChunk(e){return WriteToDebug("In SCORM_SetDataChunk"),SCORM_ClearErrorInfo(),1==USE_STRICT_SUSPEND_DATA_LIMITS&&4096<e.length?(WriteToDebug("SCORM_SetDataChunk - suspend_data too large (4096 character limit for SCORM 1.2)"),!1):SCORM_CallLMSSetValue("cmi.suspend_data",e)}function SCORM_GetLaunchData(){return WriteToDebug("In SCORM_GetLaunchData"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.launch_data")}function SCORM_GetComments(){return WriteToDebug("In SCORM_GetComments"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.comments")}function SCORM_WriteComment(e){return WriteToDebug("In SCORM_WriteComment strComment="+e),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.comments",e)}function SCORM_GetLMSComments(){return WriteToDebug("In SCORM_GetLMSComments"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.comments_from_lms")}function SCORM_GetAudioPlayPreference(){var e;return WriteToDebug("In SCORM_GetAudioPlayPreference"),SCORM_ClearErrorInfo(),""==(e=SCORM_CallLMSGetValue("cmi.student_preference.audio"))&&(e=0),WriteToDebug("intTempPreference="+(e=parseInt(e,10))),0<e?(WriteToDebug("Returning On"),PREFERENCE_ON):0==e?(WriteToDebug("Returning Default"),PREFERENCE_DEFAULT):e<0?(WriteToDebug("returning Off"),PREFERENCE_OFF):(WriteToDebug("Error: Invalid preference"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_PREFERENCE,"Invalid audio preference received from LMS","intTempPreference="+e),null)}function SCORM_GetAudioVolumePreference(){var e;return WriteToDebug("In SCORM_GetAudioVollumePreference"),SCORM_ClearErrorInfo(),WriteToDebug("intTempPreference="+(e=SCORM_CallLMSGetValue("cmi.student_preference.audio"))),""==e&&(e=100),(e=parseInt(e,10))<=0&&(WriteToDebug("Setting to 100"),e=100),0<e&&e<=100?(WriteToDebug("Returning "+e),e):(WriteToDebug("ERROR: invalid preference"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_PREFERENCE,"Invalid audio preference received from LMS","intTempPreference="+e),null)}function SCORM_SetAudioPreference(e,t){return WriteToDebug("In SCORM_SetAudioPreference PlayPreference="+e+", intPercentOfMaxVolume="+t),SCORM_ClearErrorInfo(),e==PREFERENCE_OFF&&(WriteToDebug("Setting percent to -1 - OFF"),t=-1),SCORM_CallLMSSetValue("cmi.student_preference.audio",t)}function SCORM_SetLanguagePreference(e){return WriteToDebug("In SCORM_SetLanguagePreference strLanguage="+e),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.student_preference.language",e)}function SCORM_GetLanguagePreference(){return WriteToDebug("In SCORM_GetLanguagePreference"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.student_preference.language")}function SCORM_SetSpeedPreference(e){var t;return WriteToDebug("In SCORM_SetSpeedPreference intPercentOfMax="+e),SCORM_ClearErrorInfo(),WriteToDebug("intSCORMSpeed="+(t=2*e-100)),SCORM_CallLMSSetValue("cmi.student_preference.speed",t)}function SCORM_GetSpeedPreference(){var e,t;return WriteToDebug("In SCORM_GetSpeedPreference"),SCORM_ClearErrorInfo(),WriteToDebug("intSCORMSpeed="+(e=SCORM_CallLMSGetValue("cmi.student_preference.speed"))),""==e&&(WriteToDebug("Detected empty string, defaulting to 100"),e=100),ValidInteger(e)?(e=parseInt(e,10))<-100||100<e?(WriteToDebug("ERROR - out of range"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_SPEED,"Invalid speed preference received from LMS - out of range","intSCORMSpeed="+e),null):(t=(e+100)/2,WriteToDebug("Returning "+(t=parseInt(t,10))),t):(WriteToDebug("ERROR - invalid integer"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_SPEED,"Invalid speed preference received from LMS - not an integer","intSCORMSpeed="+e),null)}function SCORM_SetTextPreference(e){return WriteToDebug("In SCORM_SetTextPreference intPreference="+e),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.student_preference.text",e)}function SCORM_GetTextPreference(){var e;return WriteToDebug("In SCORM_GetTextPreference"),SCORM_ClearErrorInfo(),e=SCORM_CallLMSGetValue("cmi.student_preference.text"),WriteToDebug("intTempPreference="+(e=parseInt(e,10))),0<e?(WriteToDebug("Returning On"),PREFERENCE_ON):0==e||""==e?(WriteToDebug("Returning Default"),PREFERENCE_DEFAULT):e<0?(WriteToDebug("returning Off"),PREFERENCE_OFF):(WriteToDebug("Error: Invalid preference"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_PREFERENCE,"Invalid text preference received from LMS","intTempPreference="+e),null)}function SCORM_GetPreviouslyAccumulatedTime(){var e,t;return WriteToDebug("In SCORM_GetPreviouslyAccumulatedTime"),SCORM_ClearErrorInfo(),WriteToDebug("strCMITime="+(e=SCORM_CallLMSGetValue("cmi.core.total_time"))),IsValidCMITimeSpan(e)?(WriteToDebug("Returning "+(t=ConvertCMITimeSpanToMS(e))),t):(WriteToDebug("ERROR - Invalid CMITimeSpan"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_TIMESPAN,"Invalid timespan received from LMS","strTime="+e),null)}function SCORM_SaveTime(e){var t;return WriteToDebug("In SCORM_SaveTime intMilliSeconds="+e),SCORM_ClearErrorInfo(),WriteToDebug("strCMITime="+(t=ConvertMilliSecondsToSCORMTime(e,!0))),SCORM_CallLMSSetValue("cmi.core.session_time",t)}function SCORM_GetMaxTimeAllowed(){var e,t;return WriteToDebug("In SCORM_GetMaxTimeAllowed"),SCORM_ClearErrorInfo(),WriteToDebug("strCMITime="+(e=SCORM_CallLMSGetValue("cmi.student_data.max_time_allowed"))),""==e&&(e="9999:99:99.99"),IsValidCMITimeSpan(e)?(WriteToDebug("intMilliseconds="+(t=ConvertCMITimeSpanToMS(e))),t):(WriteToDebug("ERROR - Invalid CMITimeSpan"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_TIMESPAN,"Invalid timespan received from LMS","strTime="+e),null)}function SCORM_DisplayMessageOnTimeout(){var e;return SCORM_ClearErrorInfo(),WriteToDebug("In SCORM_DisplayMessageOnTimeout"),WriteToDebug("strTLA="+(e=SCORM_CallLMSGetValue("cmi.student_data.time_limit_action"))),e==SCORM_TLA_EXIT_MESSAGE||e==SCORM_TLA_CONTINUE_MESSAGE?(WriteToDebug("returning true"),!0):e==SCORM_TLA_EXIT_NO_MESSAGE||e==SCORM_TLA_CONTINUE_NO_MESSAGE||""==e?(WriteToDebug("returning false"),!1):(WriteToDebug("Error invalid TLA"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_TIME_LIMIT_ACTION,"Invalid time limit action received from LMS","strTLA="+e),null)}function SCORM_ExitOnTimeout(){var e;return WriteToDebug("In SCORM_ExitOnTimeout"),SCORM_ClearErrorInfo(),WriteToDebug("strTLA="+(e=SCORM_CallLMSGetValue("cmi.student_data.time_limit_action"))),e==SCORM_TLA_EXIT_MESSAGE||e==SCORM_TLA_EXIT_NO_MESSAGE?(WriteToDebug("returning true"),!0):e==SCORM_TLA_CONTINUE_MESSAGE||e==SCORM_TLA_CONTINUE_NO_MESSAGE||""==e?(WriteToDebug("returning false"),!1):(WriteToDebug("ERROR invalid TLA"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_TIME_LIMIT_ACTION,"Invalid time limit action received from LMS","strTLA="+e),null)}function SCORM_GetPassingScore(){var e;return WriteToDebug("In SCORM_GetPassingScore"),SCORM_ClearErrorInfo(),WriteToDebug("fltScore="+(e=SCORM_CallLMSGetValue("cmi.student_data.mastery_score"))),""==e&&(e=0),IsValidDecimal(e)?(e=parseFloat(e),WriteToDebug("returning fltScore"),e):(WriteToDebug("Error - score is not a valid decimal"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_DECIMAL,"Invalid mastery score received from LMS","fltScore="+e),null)}function SCORM_SetScore(e,t,r){var n;return WriteToDebug("In SCORM_SetScore intScore="+(e=RoundToPrecision(e,7))+", intMaxScore="+(t=RoundToPrecision(t,7))+", intMinScore="+(r=RoundToPrecision(r,7))),SCORM_ClearErrorInfo(),n=SCORM_CallLMSSetValue("cmi.core.score.raw",e),n=SCORM_CallLMSSetValue("cmi.core.score.max",t)&&n,WriteToDebug("Returning "+(n=SCORM_CallLMSSetValue("cmi.core.score.min",r)&&n)),n}function SCORM_GetScore(){return WriteToDebug("In SCORM_GetScore"),SCORM_ClearErrorInfo(),SCORM_CallLMSGetValue("cmi.core.score.raw")}function SCORM_SetPointBasedScore(e,t,r){return WriteToDebug("SCORM_SetPointBasedScore - SCORM 1.1 and 1.2 do not support SetPointBasedScore, falling back to SetScore"),SCORM_SetScore(e,t,r)}function SCORM_GetScaledScore(e,t,r){return WriteToDebug("SCORM_GetScaledScore - SCORM 1.1 and 1.2 do not support GetScaledScore, returning false"),!1}function SCORM_RecordInteraction(e,t,r,n,o,i,a,S,u,s,l,R){var c,I,d,O;return SCORM_ClearErrorInfo(),WriteToDebug("intInteractionIndex="+(d=SCORM_CallLMSGetValue("cmi.interactions._count"))),""==d&&(WriteToDebug("Setting Interaction Index to 0"),d=0),IsNumeric(r)?O=r:1==r||r==INTERACTION_RESULT_CORRECT?O=SCORM_RESULT_CORRECT:""==r||"false"==r||r==INTERACTION_RESULT_WRONG?O=SCORM_RESULT_WRONG:r==INTERACTION_RESULT_UNANTICIPATED?O=SCORM_RESULT_UNANTICIPATED:r==INTERACTION_RESULT_NEUTRAL&&(O=SCORM_RESULT_NEUTRAL),WriteToDebug("strResult="+O),c=SCORM_CallLMSSetValue("cmi.interactions."+d+".id",e),c=SCORM_CallLMSSetValue("cmi.interactions."+d+".type",s)&&c,null!==t&&0==(I=SCORM_CallLMSSetValue("cmi.interactions."+d+".student_response",t))&&null!==l&&(I=SCORM_CallLMSSetValue("cmi.interactions."+d+".student_response",l)),c=c&&I,null!=n&&null!=n&&""!=n&&(0==(I=SCORM_CallLMSSetValue("cmi.interactions."+d+".correct_responses.0.pattern",n))&&(I=SCORM_CallLMSSetValue("cmi.interactions."+d+".correct_responses.0.pattern",R)),c=c&&I),null!=O&&null!=O&&""!=O&&(c=SCORM_CallLMSSetValue("cmi.interactions."+d+".result",O)&&c),null!=i&&null!=i&&""!=i&&(c=SCORM_CallLMSSetValue("cmi.interactions."+d+".weighting",i)&&c),null!=a&&null!=a&&""!=a&&(c=SCORM_CallLMSSetValue("cmi.interactions."+d+".latency",ConvertMilliSecondsToSCORMTime(a,!0))&&c),null!=S&&null!=S&&""!=S&&(c=SCORM_CallLMSSetValue("cmi.interactions."+d+".objectives.0.id",S)&&c),WriteToDebug("Returning "+(c=SCORM_CallLMSSetValue("cmi.interactions."+d+".time",ConvertDateToCMITime(u))&&c)),c}function SCORM_RecordTrueFalseInteraction(e,t,r,n,o,i,a,S,u){WriteToDebug("In SCORM_RecordTrueFalseInteraction strID="+e+", strResponse="+s+", blnCorrect="+r+", strCorrectResponse="+l+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u);var s=null,l=null;return 1==t?s="t":null!==t&&(s="f"),1==n?l="t":0==n&&(l="f"),SCORM_RecordInteraction(e,s,r,l,o,i,a,S,u,SCORM_INTERACTION_TYPE_TRUE_FALSE,s,l)}function SCORM_RecordMultipleChoiceInteraction(e,t,r,n,o,i,a,S,u){WriteToDebug("In SCORM_RecordMultipleChoiceInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u);var s=null,l=null,R="",c="";if(null!==t){l=s="";for(var I=0;I<t.length;I++)0<s.length&&(s+=","),0<l.length&&(l+=","),s+=t[I].Short,l+=t[I].Long}for(I=0;I<n.length;I++)0<R.length&&(R+=","),0<c.length&&(c+=","),R+=n[I].Short,c+=n[I].Long;return SCORM_RecordInteraction(e,l,r,c,o,i,a,S,u,SCORM_INTERACTION_TYPE_CHOICE,s,R)}function SCORM_RecordFillInInteraction(e,t,r,n,o,i,a,S,u){return WriteToDebug("In SCORM_RecordFillInInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u),255<(t=new String(t)).length&&(t=t.substr(0,255)),null==n&&(n=""),255<(n=new String(n)).length&&(n=n.substr(0,255)),SCORM_RecordInteraction(e,t,r,n,o,i,a,S,u,SCORM_INTERACTION_FILL_IN,t,n)}function SCORM_RecordMatchingInteraction(e,t,r,n,o,i,a,S,u){WriteToDebug("In SCORM_RecordMatchingInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u);var s=null,l=null,R="",c="";if(null!==t){l=s="";for(var I=0;I<t.length;I++)0<s.length&&(s+=","),0<l.length&&(l+=","),s+=t[I].Source.Short+"."+t[I].Target.Short,l+=t[I].Source.Long+"."+t[I].Target.Long}for(I=0;I<n.length;I++)0<R.length&&(R+=","),0<c.length&&(c+=","),R+=n[I].Source.Short+"."+n[I].Target.Short,c+=n[I].Source.Long+"."+n[I].Target.Long;return SCORM_RecordInteraction(e,l,r,c,o,i,a,S,u,SCORM_INTERACTION_TYPE_MATCHING,s,R)}function SCORM_RecordPerformanceInteraction(e,t,r,n,o,i,a,S,u){return WriteToDebug("In SCORM_RecordPerformanceInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u),null!==t&&255<(t=new String(t)).length&&(t=t.substr(0,255)),null==n&&(n=""),255<(n=new String(n)).length&&(n=n.substr(0,255)),SCORM_RecordInteraction(e,t,r,n,o,i,a,S,u,SCORM_INTERACTION_TYPE_PERFORMANCE,t,n)}function SCORM_RecordSequencingInteraction(e,t,r,n,o,i,a,S,u){WriteToDebug("In SCORM_RecordSequencingInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u);var s=null,l=null,R="",c="";if(null!==t){l=s="";for(var I=0;I<t.length;I++)0<s.length&&(s+=","),0<l.length&&(l+=","),s+=t[I].Short,l+=t[I].Long}for(I=0;I<n.length;I++)0<R.length&&(R+=","),0<c.length&&(c+=","),R+=n[I].Short,c+=n[I].Long;return SCORM_RecordInteraction(e,l,r,c,o,i,a,S,u,SCORM_INTERACTION_TYPE_SEQUENCING,s,R)}function SCORM_RecordLikertInteraction(e,t,r,n,o,i,a,S,u){WriteToDebug("In SCORM_RecordLikertInteraction strID="+e+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u);var s=null,l=null,R="",c="";return null!==t&&(s=t.Short,l=t.Long),null!=n&&(R=n.Short,c=n.Long),SCORM_RecordInteraction(e,l,r,c,o,i,a,S,u,SCORM_INTERACTION_TYPE_LIKERT,s,R)}function SCORM_RecordNumericInteraction(e,t,r,n,o,i,a,S,u){return WriteToDebug("In SCORM_RecordNumericInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+S+", dtmTime="+u),null==n||null==n||(IsValidDecimalRange(n)&&(n=ConvertDecimalRangeToDecimalBasedOnLearnerResponse(n,t,r)),IsValidDecimal(n))?SCORM_RecordInteraction(e,t,r,n,o,i,a,S,u,SCORM_INTERACTION_TYPE_NUMERIC,t,n):(WriteToDebug("Returning False - SCORM_RecordNumericInteraction received invalid correct response (not a decimal), strCorrectResponse="+n),!1)}function SCORM_GetEntryMode(){var e;return WriteToDebug("In SCORM_GetEntryMode"),SCORM_ClearErrorInfo(),WriteToDebug("strEntry="+(e=SCORM_CallLMSGetValue("cmi.core.entry"))),e==SCORM_ENTRY_ABINITIO?(WriteToDebug("Returning first time"),ENTRY_FIRST_TIME):e==SCORM_ENTRY_RESUME?(WriteToDebug("Returning resume"),ENTRY_RESUME):e==SCORM_ENTRY_NORMAL?(WriteToDebug("returning normal"),ENTRY_REVIEW):(WriteToDebug("ERROR - invalide entry mode"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_ENTRY,"Invalid entry vocab received from LMS","strEntry="+e),null)}function SCORM_GetLessonMode(){var e;return WriteToDebug("In SCORM_GetLessonMode"),SCORM_ClearErrorInfo(),WriteToDebug("strLessonMode="+(e=SCORM_CallLMSGetValue("cmi.core.lesson_mode"))),e==SCORM_BROWSE?(WriteToDebug("Returning browse"),MODE_BROWSE):e==SCORM_NORMAL?(WriteToDebug("returning normal"),MODE_NORMAL):e==SCORM_REVIEW?(WriteToDebug("Returning Review"),MODE_REVIEW):(WriteToDebug("ERROR - invalid lesson mode"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_LESSON_MODE,"Invalid lesson_mode vocab received from LMS","strLessonMode="+e),null)}function SCORM_GetTakingForCredit(){var e;return WriteToDebug("In SCORM_GetTakingForCredit"),SCORM_ClearErrorInfo(),WriteToDebug("strCredit="+(e=SCORM_CallLMSGetValue("cmi.core.credit"))),"credit"==e?(WriteToDebug("Returning true"),!0):"no-credit"==e?(WriteToDebug("Returning false"),!1):(WriteToDebug("ERROR - invalid credit"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_CREDIT,"Invalid credit vocab received from LMS","strCredit="+e),null)}function SCORM_SetObjectiveScore(e,t,r,n){var o,i;return WriteToDebug("In SCORM_SetObjectiveScore, strObejctiveID="+e+", intScore="+(t=RoundToPrecision(t,7))+", intMaxScore="+(r=RoundToPrecision(r,7))+", intMinScore="+(n=RoundToPrecision(n,7))),SCORM_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(o=SCORM_FindObjectiveIndexFromID(e))),i=SCORM_CallLMSSetValue("cmi.objectives."+o+".id",e),i=SCORM_CallLMSSetValue("cmi.objectives."+o+".score.raw",t)&&i,i=SCORM_CallLMSSetValue("cmi.objectives."+o+".score.max",r)&&i,WriteToDebug("Returning "+(i=SCORM_CallLMSSetValue("cmi.objectives."+o+".score.min",n)&&i)),i}function SCORM_SetObjectiveDescription(e,t){var r;return WriteToDebug("In SCORM_SetObjectiveDescription, strObjectiveDescription="+t),WriteToDebug("Objective Descriptions are not supported prior to SCORM 2004"),SCORM_ClearErrorInfo(),WriteToDebug("Returning "+(r=SCORM_TRUE)),r}function SCORM_SetObjectiveStatus(e,t){var r,n,o="";return WriteToDebug("In SCORM_SetObjectiveStatus strObjectiveID="+e+", Lesson_Status="+t),SCORM_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(r=SCORM_FindObjectiveIndexFromID(e))),t==LESSON_STATUS_PASSED?o=SCORM_PASSED:t==LESSON_STATUS_FAILED?o=SCORM_FAILED:t==LESSON_STATUS_COMPLETED?o=SCORM_COMPLETED:t==LESSON_STATUS_BROWSED?o=SCORM_BROWSED:t==LESSON_STATUS_INCOMPLETE?o=SCORM_INCOMPLETE:t==LESSON_STATUS_NOT_ATTEMPTED&&(o=SCORM_NOT_ATTEMPTED),WriteToDebug("strSCORMStatus="+o),n=SCORM_CallLMSSetValue("cmi.objectives."+r+".id",e),WriteToDebug("Returning "+(n=SCORM_CallLMSSetValue("cmi.objectives."+r+".status",o)&&n)),n}function SCORM_GetObjectiveScore(e){var t;return WriteToDebug("In SCORM_GetObjectiveScore, strObejctiveID="+e),SCORM_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(t=SCORM_FindObjectiveIndexFromID(e))),SCORM_CallLMSGetValue("cmi.objectives."+t+".score.raw")}function SCORM_GetObjectiveDescription(e){return WriteToDebug("In SCORM_GetObjectiveDescription, strObejctiveID="+e),WriteToDebug("ObjectiveDescription is not supported prior to SCORM 2004"),""}function SCORM_GetObjectiveStatus(e){var t,r;return WriteToDebug("In SCORM_GetObjectiveStatus, strObejctiveID="+e),SCORM_ClearErrorInfo(),WriteToDebug("intObjectiveIndex="+(t=SCORM_FindObjectiveIndexFromID(e))),(r=SCORM_CallLMSGetValue("cmi.objectives."+t+".status"))==SCORM_PASSED?(WriteToDebug("returning Passed"),LESSON_STATUS_PASSED):r==SCORM_FAILED?(WriteToDebug("Returning Failed"),LESSON_STATUS_FAILED):r==SCORM_COMPLETED?(WriteToDebug("Returning Completed"),LESSON_STATUS_COMPLETED):r==SCORM_BROWSED?(WriteToDebug("Returning Browsed"),LESSON_STATUS_BROWSED):r==SCORM_INCOMPLETE?(WriteToDebug("Returning Incomplete"),LESSON_STATUS_INCOMPLETE):r==SCORM_NOT_ATTEMPTED||""==r?(WriteToDebug("Returning Not Attempted"),LESSON_STATUS_NOT_ATTEMPTED):(WriteToDebug("ERROR - status not found"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_STATUS,"Invalid objective status received from LMS or initial status not yet recorded for objective","strStatus="+r),null)}function SCORM_FindObjectiveIndexFromID(e){var t,r,n;if(WriteToDebug("In SCORM_FindObjectiveIndexFromID"),""==(t=SCORM_CallLMSGetValue("cmi.objectives._count")))return WriteToDebug("Setting intCount=0"),0;for(WriteToDebug("intCount="+(t=parseInt(t,10))),r=0;r<t;r++)if(WriteToDebug("Checking index "+r),WriteToDebug("ID="+(n=SCORM_CallLMSGetValue("cmi.objectives."+r+".id"))),n==e)return WriteToDebug("Found Matching index"),r;return WriteToDebug("Did not find match, returning count"),t}function SCORM_CreateValidIdentifier(e){return CreateValidIdentifierLegacy(e)}function SCORM_FindInteractionIndexFromID(e){return WriteToDebug("SCORM_FindInteractionIndexFromID - SCORM does not support interaction retrieval, returning null"),null}function SCORM_GetInteractionType(e){return WriteToDebug("SCORM_GetInteractionType - SCORM does not support interaction retrieval, returning empty string"),""}function SCORM_GetInteractionTimestamp(e){return WriteToDebug("SCORM_GetInteractionTimestamp - SCORM does not support interaction retrieval, returning empty string"),""}function SCORM_GetInteractionCorrectResponses(e){return WriteToDebug("SCORM_GetInteractionCorrectResponses - SCORM does not support interaction retrieval, returning empty array"),new Array}function SCORM_GetInteractionWeighting(e){return WriteToDebug("SCORM_GetInteractionWeighting - SCORM does not support interaction retrieval, returning empty string"),""}function SCORM_GetInteractionLearnerResponses(e){return WriteToDebug("SCORM_GetInteractionLearnerResponses - SCORM does not support interaction retrieval, returning empty array"),new Array}function SCORM_GetInteractionResult(e){return WriteToDebug("SCORM_GetInteractionResult - SCORM does not support interaction retrieval, returning empty string"),""}function SCORM_GetInteractionLatency(e){return WriteToDebug("SCORM_GetInteractionDescription - SCORM does not support interaction retrieval, returning empty string"),""}function SCORM_GetInteractionDescription(e){return WriteToDebug("SCORM_GetInteractionDescription - SCORM does not support interaction retrieval, returning empty string"),""}function SCORM_CreateDataBucket(e,t,r){return WriteToDebug("SCORM_CreateDataBucket - SCORM 1.1 and 1.2 do not support SSP, returning false"),!1}function SCORM_GetDataFromBucket(e){return WriteToDebug("SCORM_GetDataFromBucket - SCORM 1.1 and 1.2 do not support SSP, returning empty string"),""}function SCORM_PutDataInBucket(e,t,r){return WriteToDebug("SCORM_PutDataInBucket - SCORM 1.1 and 1.2 do not support SSP, returning false"),!1}function SCORM_DetectSSPSupport(){return WriteToDebug("SCORM_DetectSSPSupport - SCORM 1.1 and 1.2 do not support SSP, returning false"),!1}function SCORM_GetBucketInfo(e){return WriteToDebug("AICC_DetectSSPSupport - SCORM 1.1 and 1.2 do not support SSP, returning empty SSPBucketSize"),new SSPBucketSize(0,0)}function SCORM_SetFailed(){return WriteToDebug("In SCORM_SetFailed"),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.core.lesson_status",SCORM_FAILED)}function SCORM_SetPassed(){return WriteToDebug("In SCORM_SetPassed"),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.core.lesson_status",SCORM_PASSED)}function SCORM_SetCompleted(){return WriteToDebug("In SCORM_SetCompleted"),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.core.lesson_status",SCORM_COMPLETED)}function SCORM_ResetStatus(){return WriteToDebug("In SCORM_ResetStatus"),SCORM_ClearErrorInfo(),SCORM_CallLMSSetValue("cmi.core.lesson_status",SCORM_INCOMPLETE)}function SCORM_GetStatus(){var e;return WriteToDebug("In SCORM_GetStatus"),SCORM_ClearErrorInfo(),WriteToDebug("strStatus="+(e=SCORM_CallLMSGetValue("cmi.core.lesson_status"))),e==SCORM_PASSED?(WriteToDebug("returning Passed"),LESSON_STATUS_PASSED):e==SCORM_FAILED?(WriteToDebug("Returning Failed"),LESSON_STATUS_FAILED):e==SCORM_COMPLETED?(WriteToDebug("Returning Completed"),LESSON_STATUS_COMPLETED):e==SCORM_BROWSED?(WriteToDebug("Returning Browsed"),LESSON_STATUS_BROWSED):e==SCORM_INCOMPLETE?(WriteToDebug("Returning Incomplete"),LESSON_STATUS_INCOMPLETE):e==SCORM_NOT_ATTEMPTED?(WriteToDebug("Returning Not Attempted"),LESSON_STATUS_NOT_ATTEMPTED):(WriteToDebug("ERROR - status not found"),SCORM_SetErrorInfoManually(SCORM_ERROR_INVALID_STATUS,"Invalid lesson status received from LMS","strStatus="+e),null)}function SCORM_GetProgressMeasure(){return WriteToDebug("SCORM_GetProgressMeasure - SCORM 1.1 and 1.2 do not support progress_measure, returning false"),!1}function SCORM_SetProgressMeasure(){return WriteToDebug("SCORM_SetProgressMeasure - SCORM 1.1 and 1.2 do not support progress_measure, returning false"),!1}function SCORM_GetObjectiveProgressMeasure(){return WriteToDebug("SCORM_GetObjectiveProgressMeasure - SCORM 1.1 and 1.2 do not support progress_measure, returning false"),!1}function SCORM_SetObjectiveProgressMeasure(){return WriteToDebug("SCORM_SetObjectiveProgressMeasure - SCORM 1.1 and 1.2 do not support progress_measure, returning false"),!1}function SCORM_IsContentInBrowseMode(){var e;return WriteToDebug("In SCORM_IsContentInBrowseMode"),WriteToDebug("SCORM_IsContentInBrowseMode,  strLessonMode="+(e=SCORM_CallLMSGetValue("cmi.core.lesson_mode"))),e==SCORM_BROWSE?(WriteToDebug("Returning true"),!0):(WriteToDebug("Returning false"),!1)}function SCORM_TranslateExitTypeToSCORM(e){return WriteToDebug("In SCORM_TranslatgeExitTypeToSCORM strExitType-"+e),e==EXIT_TYPE_SUSPEND?(WriteToDebug("Returning suspend"),SCORM_SUSPEND):e==EXIT_TYPE_UNLOAD?(WriteToDebug("Returning Exit"),SCORM_NORMAL_EXIT):e==EXIT_TYPE_FINISH?(WriteToDebug("Returning Logout"),SCORM_NORMAL_EXIT):e==EXIT_TYPE_TIMEOUT?(WriteToDebug("Returning Timout"),SCORM_TIMEOUT):void 0}function SCORM_GetCompletionStatus(){return WriteToDebug("In SCORM_GetCompletionStatus"),SCORM_IsContentInBrowseMode()?(WriteToDebug("Returning browsed"),SCORM_BROWSED):(WriteToDebug("Returning Completed"),SCORM_COMPLETED)}function SCORM_SetNavigationRequest(e){return WriteToDebug("SCORM_GetNavigationRequest - SCORM 1.1 and 1.2 do not support navigation requests, returning false"),!1}function SCORM_GetNavigationRequest(){return WriteToDebug("SCORM_GetNavigationRequest - SCORM 1.1 and 1.2 do not support navigation requests, returning false"),!1}function SCORM_CallLMSInitialize(){var e;return WriteToDebug("In SCORM_CallLMSInitialize"),SCORM_objAPI=SCORM_GrabAPI(),WriteToDebug("Calling LMSInitialize"),e=SCORM_objAPI.LMSInitialize(""),WriteToDebug("strResult="+(e+="")),e==SCORM_FALSE?(WriteToDebug("Detected failed call to initialize"),SCORM_SetErrorInfo(),WriteToDebug("Error calling LMSInitialize:"),WriteToDebug("              intSCORMError="+intSCORMError),WriteToDebug("              SCORMErrorString="+strSCORMErrorString),WriteToDebug("              Diagnostic="+strSCORMErrorDiagnostic),!1):(WriteToDebug("Returning true"),!0)}function SCORM_CallLMSSetValue(e,t){var r;return WriteToDebug("SCORM_CallLMSSetValue strElement="+e+", strValue="+t),!0===blnReviewModeSoReadOnly?(WriteToDebug("Mode is Review and configuration setting dictates this should be read only so exiting."),!0):(SCORM_objAPI=SCORM_GrabAPI(),WriteToDebug("Calling LMSSetValue"),e+="",t+="",r=SCORM_objAPI.LMSSetValue(e,t),WriteToDebug("strResult="+(r+="")),r==SCORM_FALSE?(WriteToDebug("Detected Failed call to LMSSetvalue"),SCORM_SetErrorInfo(),WriteToDebug("Error calling LMSSetValue:"),WriteToDebug("              strElement="+e),WriteToDebug("              strValue="+t),WriteToDebug("              intSCORMError="+intSCORMError),WriteToDebug("              SCORMErrorString="+strSCORMErrorString),WriteToDebug("              Diagnostic="+strSCORMErrorDiagnostic),!1):(WriteToDebug("Returning true"),!0))}function SCORM_CallLMSGetValue(e){var t;return WriteToDebug("In SCORM_CallLMSGetValue strElement="+e),SCORM_objAPI=SCORM_GrabAPI(),WriteToDebug("Call LMSGetValue"),e+="",WriteToDebug("strResult="+(t=SCORM_objAPI.LMSGetValue(e)+"")),intSCORMError=SCORM_objAPI.LMSGetLastError(),WriteToDebug("intSCORMError="+(intSCORMError+="")),intSCORMError!=SCORM_NO_ERROR&&(WriteToDebug("Detected failed called to LMSGetValue"),SCORM_SetErrorInfo(),WriteToDebug("Error calling LMSGetValue:"),WriteToDebug("              strElement="+e),WriteToDebug("              intSCORMError="+intSCORMError),WriteToDebug("              SCORMErrorString="+strSCORMErrorString),WriteToDebug("              Diagnostic="+strSCORMErrorDiagnostic)),WriteToDebug("Returning "+t),t}function SCORM_CallLMSCommit(){var e;return WriteToDebug("In SCORM_CallLMSCommit"),SCORM_objAPI=SCORM_GrabAPI(),WriteToDebug("Calling LMSCommit"),e=SCORM_objAPI.LMSCommit(""),WriteToDebug("strResult="+(e+="")),e==SCORM_FALSE?(WriteToDebug("Detected failed call to LMSCommit"),SCORM_SetErrorInfo(),WriteToDebug("Error calling LMSCommit:"),WriteToDebug("              intSCORMError="+intSCORMError),WriteToDebug("              SCORMErrorString="+strSCORMErrorString),WriteToDebug("              Diagnostic="+strSCORMErrorDiagnostic),!1):(WriteToDebug("Returning true"),!0)}function SCORM_CallLMSFinish(){var e;return WriteToDebug("In SCORM_CallLMSFinish"),SCORM_objAPI=SCORM_GrabAPI(),WriteToDebug("Calling LMS Finish"),e=SCORM_objAPI.LMSFinish(""),WriteToDebug("strResult="+(e+="")),e==SCORM_FALSE?(WriteToDebug("Detected failed call to LMSFinish"),SCORM_SetErrorInfo(),WriteToDebug("Error calling LMSFinish:"),WriteToDebug("              intSCORMError="+intSCORMError),WriteToDebug("              SCORMErrorString="+strSCORMErrorString),WriteToDebug("              Diagnostic="+strSCORMErrorDiagnostic),!1):(WriteToDebug("Returning True"),!0)}function SCORM_ClearErrorInfo(){WriteToDebug("In SCORM_ClearErrorInfo"),intSCORMError=SCORM_NO_ERROR,strSCORMErrorDiagnostic=strSCORMErrorString=""}function SCORM_SetErrorInfo(){WriteToDebug("In SCORM_SetErrorInfo"),intSCORMError=SCORM_objAPI.LMSGetLastError(),strSCORMErrorString=SCORM_objAPI.LMSGetErrorString(intSCORMError),strSCORMErrorDiagnostic=SCORM_objAPI.LMSGetDiagnostic(""),strSCORMErrorString+="",strSCORMErrorDiagnostic+="",WriteToDebug("intSCORMError="+(intSCORMError+="")),WriteToDebug("strSCORMErrorString="+strSCORMErrorString),WriteToDebug("strSCORMErrorDiagnostic="+strSCORMErrorDiagnostic)}function SCORM_SetErrorInfoManually(e,t,r){WriteToDebug("In SCORM_SetErrorInfoManually"),WriteToDebug("ERROR-Num="+e),WriteToDebug("      String="+t),WriteToDebug("      Diag="+r),intSCORMError=e,strSCORMErrorString=t,strSCORMErrorDiagnostic=r}function SCORM_GetLastError(){return WriteToDebug("In SCORM_GetLastError"),intSCORMError==SCORM_NO_ERROR?(WriteToDebug("Returning No Error"),NO_ERROR):(WriteToDebug("Returning "+intSCORMError),intSCORMError)}function SCORM_GetLastErrorDesc(){return WriteToDebug("In SCORM_GetLastErrorDesc, "+strSCORMErrorString+"\n"+strSCORMErrorDiagnostic),strSCORMErrorString+"\n"+strSCORMErrorDiagnostic}function SCORM_GrabAPI(){return WriteToDebug("In SCORM_GrabAPI"),void 0!==SCORM_objAPI&&null!=SCORM_objAPI||(WriteToDebug("Searching with improved ADL algorithm"),SCORM_objAPI=SCORM_GetAPI()),void 0!==SCORM_objAPI&&null!=SCORM_objAPI||(SCORM_objAPI=SCORM_SearchForAPI(window)),WriteToDebug("SCORM_GrabAPI, returning"),SCORM_objAPI}function SCORM_SearchForAPI(e){WriteToDebug("SCORM_SearchForAPI");var t,r=null;return t="Name="+e.name+", href="+e.location.href,SCORM_APIFound(r=e.API)?(WriteToDebug("Found API in this window - "+t),r):(SCORM_WindowHasParent(e)&&(WriteToDebug("Searching Parent - "+t),r=SCORM_SearchForAPI(e.parent)),SCORM_APIFound(r)?(WriteToDebug("Found API in a parent - "+t),r):(SCORM_WindowHasOpener(e)&&(WriteToDebug("Searching Opener - "+t),r=SCORM_SearchForAPI(e.opener)),SCORM_APIFound(r)?(WriteToDebug("Found API in an opener - "+t),r):(WriteToDebug("Looking in children - "+t),SCORM_APIFound(r=SCORM_LookInChildren(e))?(WriteToDebug("Found API in Children - "+t),r):(WriteToDebug("Didn't find API in this window - "+t),null))))}function SCORM_LookInChildren(e){WriteToDebug("SCORM_LookInChildren");var t,r=null;t="Name="+e.name+", href="+e.location.href;for(var n=0;n<e.frames.length;n++){if(WriteToDebug("Looking in child frame "+n),SCORM_APIFound(r=e.frames[n].API))return WriteToDebug("Found API in child frame of "+t),r;if(WriteToDebug("Looking in this child's children "+t),SCORM_APIFound(r=SCORM_LookInChildren(e.frames[n])))return WriteToDebug("API found in this child's children "+t),r}return null}function SCORM_WindowHasOpener(e){return WriteToDebug("In SCORM_WindowHasOpener"),null!=e.opener&&e.opener!=e&&void 0!==e.opener?(WriteToDebug("Window Does Have Opener"),!0):(WriteToDebug("Window Does Not Have Opener"),!1)}function SCORM_WindowHasParent(e){return WriteToDebug("In SCORM_WindowHasParent"),null!=e.parent&&e.parent!=e&&void 0!==e.parent?(WriteToDebug("Window Does Have Parent"),!0):(WriteToDebug("Window Does Not Have Parent"),!1)}function SCORM_APIFound(e){return WriteToDebug("In SCORM_APIFound"),null==e||void 0===e?(WriteToDebug("API NOT Found"),!1):(WriteToDebug("API Found"),!0)}function SCORM_ScanParentsForApi(e){WriteToDebug("In SCORM_ScanParentsForApi, win="+e.location);for(var t=0;(null==e.API||void 0===e.API)&&null!=e.parent&&e.parent!=e&&t<=500;)t++,e=e.parent;return e.API}function SCORM_GetAPI(){WriteToDebug("In SCORM_GetAPI");var e=null;return null!=window.parent&&window.parent!=window&&(WriteToDebug("SCORM_GetAPI, searching parent"),e=SCORM_ScanParentsForApi(window.parent)),null==e&&null!=window.top.opener&&(WriteToDebug("SCORM_GetAPI, searching opener"),e=SCORM_ScanParentsForApi(window.top.opener)),e}function LMSStandardAPI(strStandard){WriteToDebug("In LMSStandardAPI strStandard="+strStandard),""==strStandard&&(WriteToDebug("No standard specified, using NONE"),strStandard="NONE"),eval("this.Initialize = "+strStandard+"_Initialize"),eval("this.Finish = "+strStandard+"_Finish"),eval("this.CommitData = "+strStandard+"_CommitData"),eval("this.GetStudentID = "+strStandard+"_GetStudentID"),eval("this.GetStudentName = "+strStandard+"_GetStudentName"),eval("this.GetBookmark = "+strStandard+"_GetBookmark"),eval("this.SetBookmark = "+strStandard+"_SetBookmark"),eval("this.GetDataChunk = "+strStandard+"_GetDataChunk"),eval("this.SetDataChunk = "+strStandard+"_SetDataChunk"),eval("this.GetLaunchData = "+strStandard+"_GetLaunchData"),eval("this.GetComments = "+strStandard+"_GetComments"),eval("this.WriteComment = "+strStandard+"_WriteComment"),eval("this.GetLMSComments = "+strStandard+"_GetLMSComments"),eval("this.GetAudioPlayPreference = "+strStandard+"_GetAudioPlayPreference"),eval("this.GetAudioVolumePreference = "+strStandard+"_GetAudioVolumePreference"),eval("this.SetAudioPreference = "+strStandard+"_SetAudioPreference"),eval("this.SetLanguagePreference = "+strStandard+"_SetLanguagePreference"),eval("this.GetLanguagePreference = "+strStandard+"_GetLanguagePreference"),eval("this.SetSpeedPreference = "+strStandard+"_SetSpeedPreference"),eval("this.GetSpeedPreference = "+strStandard+"_GetSpeedPreference"),eval("this.SetTextPreference = "+strStandard+"_SetTextPreference"),eval("this.GetTextPreference = "+strStandard+"_GetTextPreference"),eval("this.GetPreviouslyAccumulatedTime = "+strStandard+"_GetPreviouslyAccumulatedTime"),eval("this.SaveTime = "+strStandard+"_SaveTime"),eval("this.GetMaxTimeAllowed = "+strStandard+"_GetMaxTimeAllowed"),eval("this.DisplayMessageOnTimeout = "+strStandard+"_DisplayMessageOnTimeout"),eval("this.ExitOnTimeout = "+strStandard+"_ExitOnTimeout"),eval("this.GetPassingScore = "+strStandard+"_GetPassingScore"),eval("this.SetScore = "+strStandard+"_SetScore"),eval("this.GetScore = "+strStandard+"_GetScore"),eval("this.GetScaledScore = "+strStandard+"_GetScaledScore"),eval("this.RecordTrueFalseInteraction = "+strStandard+"_RecordTrueFalseInteraction"),eval("this.RecordMultipleChoiceInteraction = "+strStandard+"_RecordMultipleChoiceInteraction"),eval("this.RecordFillInInteraction = "+strStandard+"_RecordFillInInteraction"),eval("this.RecordMatchingInteraction = "+strStandard+"_RecordMatchingInteraction"),eval("this.RecordPerformanceInteraction = "+strStandard+"_RecordPerformanceInteraction"),eval("this.RecordSequencingInteraction = "+strStandard+"_RecordSequencingInteraction"),eval("this.RecordLikertInteraction = "+strStandard+"_RecordLikertInteraction"),eval("this.RecordNumericInteraction = "+strStandard+"_RecordNumericInteraction"),eval("this.GetEntryMode = "+strStandard+"_GetEntryMode"),eval("this.GetLessonMode = "+strStandard+"_GetLessonMode"),eval("this.GetTakingForCredit = "+strStandard+"_GetTakingForCredit"),eval("this.SetObjectiveScore = "+strStandard+"_SetObjectiveScore"),eval("this.SetObjectiveStatus = "+strStandard+"_SetObjectiveStatus"),eval("this.GetObjectiveScore = "+strStandard+"_GetObjectiveScore"),eval("this.GetObjectiveStatus = "+strStandard+"_GetObjectiveStatus"),eval("this.SetObjectiveDescription = "+strStandard+"_SetObjectiveDescription"),eval("this.GetObjectiveDescription = "+strStandard+"_GetObjectiveDescription"),eval("this.SetFailed = "+strStandard+"_SetFailed"),eval("this.SetPassed = "+strStandard+"_SetPassed"),eval("this.SetCompleted = "+strStandard+"_SetCompleted"),eval("this.ResetStatus = "+strStandard+"_ResetStatus"),eval("this.GetStatus = "+strStandard+"_GetStatus"),eval("this.GetLastError = "+strStandard+"_GetLastError"),eval("this.GetLastErrorDesc = "+strStandard+"_GetLastErrorDesc"),eval("this.GetInteractionType = "+strStandard+"_GetInteractionType"),eval("this.GetInteractionTimestamp = "+strStandard+"_GetInteractionTimestamp"),eval("this.GetInteractionCorrectResponses = "+strStandard+"_GetInteractionCorrectResponses"),eval("this.GetInteractionWeighting = "+strStandard+"_GetInteractionWeighting"),eval("this.GetInteractionLearnerResponses = "+strStandard+"_GetInteractionLearnerResponses"),eval("this.GetInteractionResult = "+strStandard+"_GetInteractionResult"),eval("this.GetInteractionLatency = "+strStandard+"_GetInteractionLatency"),eval("this.GetInteractionDescription = "+strStandard+"_GetInteractionDescription"),eval("this.CreateDataBucket = "+strStandard+"_CreateDataBucket"),eval("this.GetDataFromBucket = "+strStandard+"_GetDataFromBucket"),eval("this.PutDataInBucket = "+strStandard+"_PutDataInBucket"),eval("this.DetectSSPSupport = "+strStandard+"_DetectSSPSupport"),eval("this.GetBucketInfo = "+strStandard+"_GetBucketInfo"),eval("this.GetProgressMeasure = "+strStandard+"_GetProgressMeasure"),eval("this.SetProgressMeasure = "+strStandard+"_SetProgressMeasure"),eval("this.SetPointBasedScore = "+strStandard+"_SetPointBasedScore"),eval("this.SetNavigationRequest = "+strStandard+"_SetNavigationRequest"),eval("this.GetNavigationRequest = "+strStandard+"_GetNavigationRequest"),eval("this.SetObjectiveProgressMeasure = "+strStandard+"_SetObjectiveProgressMeasure"),eval("this.GetObjectiveProgressMeasure = "+strStandard+"_GetObjectiveProgressMeasure"),eval("this.CreateValidIdentifier = "+strStandard+"_CreateValidIdentifier"),void 0!==window[strStandard+"_ConcedeControl"]&&eval("this.ConcedeControl = "+strStandard+"_ConcedeControl"),this.Standard=strStandard}var blnCalledFinish=!1,blnStandAlone=!1,blnLoaded=!1,blnReachedEnd=!1,blnStatusWasSet=!1,blnLmsPresent=!1,dtmStart=null,dtmEnd=null,intAccumulatedMS=0,blnOverrodeTime=!1,intTimeOverrideMS=null,aryDebug=new Array,strDebug="",winDebug,intError=NO_ERROR,strErrorDesc="",objLMS=null;function Start(){var e,t,r=null,n="",o="",i="";if(WriteToDebug("<h1>SCORM Driver starting up</h1>"),WriteToDebug("----------------------------------------"),WriteToDebug("----------------------------------------"),WriteToDebug("In Start - Version: "+VERSION+"  Last Modified="+window.document.lastModified),WriteToDebug("Browser Info ("+navigator.appName+" "+navigator.appVersion+")"),WriteToDebug("URL: "+window.document.location.href),WriteToDebug("----------------------------------------"),WriteToDebug("----------------------------------------"),ClearErrorInfo(),WriteToDebug("strStandAlone="+(e=GetQueryStringValue("StandAlone",window.location.search))+"  strShowInteractiveDebug="+(t=GetQueryStringValue("ShowDebug",window.location.search))),ConvertStringToBoolean(e)&&(WriteToDebug("Entering Stand Alone Mode"),blnStandAlone=!0),blnStandAlone)WriteToDebug("Using NONE Standard"),objLMS=new LMSStandardAPI("NONE");else if(WriteToDebug("Standard From Configuration File - "+strLMSStandard),"AUTO"==strLMSStandard.toUpperCase())if(WriteToDebug("Searching for recognized querystring parameters"),n=GetQueryStringValue("AICC_URL",document.location.search),o=GetQueryStringValue("endpoint",document.location.search),i=GetQueryStringValue("fetch",document.location.search),null!=n&&""!=n)WriteToDebug("Found AICC querystring parameters, using AICC"),objLMS=new LMSStandardAPI("AICC"),blnLmsPresent=!0;else if(null!=o&&""!=o)WriteToDebug("Found endpoint querystring parameter - checking cmi5 or Tin Can"),null!=i&&""!=i?(WriteToDebug("Found fetch querystring parameter, using cmi5"),objLMS=new LMSStandardAPI("CMI5"),blnLmsPresent=!0):(WriteToDebug("Did not find fetch querystring parameter, using Tin Can"),objLMS=new LMSStandardAPI("TCAPI"),blnLmsPresent=!0,strLMSStandard="TCAPI");else{WriteToDebug("Auto-detecting standard - Searching for SCORM 2004 API");try{r=SCORM2004_GrabAPI()}catch(e){WriteToDebug("Error grabbing 2004 API-"+e.name+":"+e.message)}if(void 0!==r&&null!=r)WriteToDebug("Found SCORM 2004 API, using SCORM 2004"),objLMS=new LMSStandardAPI("SCORM2004"),blnLmsPresent=!0;else{WriteToDebug("Searching for SCORM 1.2 API");try{r=SCORM_GrabAPI()}catch(e){WriteToDebug("Error grabbing 1.2 API-"+e.name+":"+e.message)}if(void 0!==r&&null!=r)WriteToDebug("Found SCORM API, using SCORM"),objLMS=new LMSStandardAPI("SCORM"),blnLmsPresent=!0;else{if(!0!==ALLOW_NONE_STANDARD)return WriteToDebug("Could not determine standard, Stand Alone is disabled in configuration"),void DisplayError("Could not determine standard. SCORM, AICC, Tin Can, and CMI5 APIs could not be found");WriteToDebug("Could not determine standard, defaulting to Stand Alone"),objLMS=new LMSStandardAPI("NONE")}}}else WriteToDebug("Using Standard From Configuration File - "+strLMSStandard),objLMS=new LMSStandardAPI(strLMSStandard),blnLmsPresent=!0;(ConvertStringToBoolean(t)||void 0!==SHOW_DEBUG_ON_LAUNCH&&!0===SHOW_DEBUG_ON_LAUNCH)&&(WriteToDebug("Showing Interactive Debug Windows"),ShowDebugWindow()),WriteToDebug("Calling Standard Initialize"),"TCAPI"==strLMSStandard.toUpperCase()?loadScript("../tc-config.js",objLMS.Initialize):objLMS.Initialize(),TouchCloud()}function InitializeExecuted(e,t){if(WriteToDebug("In InitializeExecuted, blnSuccess="+e+", strErrorMessage="+t),!e)return WriteToDebug("ERROR - LMS Initialize Failed"),""==t&&(t="An Error Has Occurred"),blnLmsPresent=!1,void DisplayError(t);"AICC"==objLMS.Standard&&AICC_InitializeExecuted(),blnLoaded=!0,dtmStart=new Date,LoadContent()}function ExecFinish(e){return WriteToDebug("In ExecFinish, ExiType="+e),ClearErrorInfo(),!(blnLoaded&&!blnCalledFinish)||(WriteToDebug("Haven't called finish before, finishing"),blnCalledFinish=!0,blnReachedEnd&&!EXIT_SUSPEND_IF_COMPLETED&&(WriteToDebug("Reached End, overiding exit type to FINISH"),e=EXIT_TYPE_FINISH),1==EXIT_NORMAL_IF_PASSED&&objLMS.GetStatus()==LESSON_STATUS_PASSED&&(WriteToDebug("Passed status and config value set, overiding exit type to FINISH"),e=EXIT_TYPE_FINISH),blnOverrodeTime||(WriteToDebug("Did not override time"),dtmEnd=new Date,AccumulateTime(),objLMS.SaveTime(intAccumulatedMS)),blnLoaded=!1,WriteToDebug("Calling LMS Finish"),objLMS.Finish(e,blnStatusWasSet))}function IsLoaded(){return WriteToDebug("In IsLoaded, returning -"+blnLoaded),blnLoaded}function WriteToDebug(e){if(blnDebug){var t,r=new Date;t=aryDebug.length+":"+r.toString()+" - "+e,aryDebug[aryDebug.length]=t,winDebug&&!winDebug.closed&&(winDebug.document.body.appendChild(winDebug.document.createTextNode(t)),winDebug.document.body.appendChild(winDebug.document.createElement("br")))}}function ShowDebugWindow(){var e=function(){var e,t=aryDebug.length;for(winDebug.document.body.innerHTML="",e=0;e<t;e+=1)winDebug.document.body.appendChild(winDebug.document.createTextNode(aryDebug[e])),winDebug.document.body.appendChild(winDebug.document.createElement("br"))};winDebug&&!winDebug.closed&&winDebug.close(),null===(winDebug=window.open("blank.html","Debug","width=600,height=300,resizable,scrollbars"))?alert("Debug window could not be opened, popup blocker in place?"):((winDebug.addEventListener||winDebug.attachEvent)&&winDebug[winDebug.addEventListener?"addEventListener":"attachEvent"]((winDebug.attachEvent?"on":"")+"load",e,!1),e(),winDebug.document.close(),winDebug.focus())}function DisplayError(e){WriteToDebug("In DisplayError, strMessage="+e),confirm("An error has occurred:\n\n"+e+"\n\nPress 'OK' to view debug information to send to technical support.")&&ShowDebugWindow()}function GetLastError(){return WriteToDebug("In GetLastError, intError="+intError),intError!=NO_ERROR?(WriteToDebug("Returning API Error"),intError):IsLoaded()&&objLMS.GetLastError()!=NO_ERROR?(WriteToDebug("Returning LMS Error"),ERROR_LMS):(WriteToDebug("Returning No Error"),NO_ERROR)}function GetLastLMSErrorCode(){WriteToDebug("In GetLastLMSErrorCode, intError="+intError);var e=objLMS.GetLastError();return IsLoaded()&&e!=NO_ERROR?(WriteToDebug("Returning LMS Error: "+e),e):(WriteToDebug("Returning No Error"),NO_ERROR)}function GetLastErrorDesc(){return WriteToDebug("In GetLastErrorDesc"),intError!=NO_ERROR?(WriteToDebug("Returning API Error - "+strErrorDesc),strErrorDesc):IsLoaded()&&objLMS.GetLastError()!=NO_ERROR?(WriteToDebug("Returning LMS Error"),objLMS.GetLastErrorDesc()):(WriteToDebug("Returning No Error"),"")}function SetErrorInfo(e,t){WriteToDebug("In SetErrorInfo - Num="+e+" Desc="+t),intError=e,strErrorDesc=t}function ClearErrorInfo(){WriteToDebug("In ClearErrorInfo")}function CommitData(){return WriteToDebug("In CommitData"),ClearErrorInfo(),IsLoaded()?(blnOverrodeTime||(WriteToDebug("Did not override time, saving incremental time"),dtmEnd=new Date,AccumulateTime(),dtmStart=new Date,objLMS.SaveTime(intAccumulatedMS)),objLMS.CommitData()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function Suspend(){return WriteToDebug("In Suspend"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_SUSPEND)}function Finish(){return WriteToDebug("In Finish"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_FINISH)}function TimeOut(){return WriteToDebug("In TimeOut"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_TIMEOUT)}function Unload(){return WriteToDebug("In Unload"),ClearErrorInfo(),ExecFinish(DEFAULT_EXIT_TYPE)}function SetReachedEnd(){return WriteToDebug("In SetReachedEnd"),ClearErrorInfo(),IsLoaded()?(0==blnStatusWasSet&&objLMS.SetCompleted(),blnReachedEnd=!0):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function ConcedeControl(){if(WriteToDebug("Conceding control with type: "+EXIT_BEHAVIOR),ClearErrorInfo(),void 0!==objLMS.ConcedeControl)return Suspend(),objLMS.ConcedeControl();var e=null,t=null;switch(EXIT_BEHAVIOR){case"SCORM_RECOMMENDED":(e=SearchParentsForContentRoot())==window.top?(Suspend(),e.window.close()):(Suspend(),null!=e&&(IsAbsoluteUrl(EXIT_TARGET)?e.scormdriver_content.location.href=EXIT_TARGET:(t=GetContentRootUrlBase(e),e.scormdriver_content.location.href=t+EXIT_TARGET)));break;case"ALWAYS_CLOSE":Suspend(),window.close();break;case"ALWAYS_CLOSE_TOP":Suspend(),window.top.close();break;case"ALWAYS_CLOSE_PARENT":Suspend(),window.parent.close();break;case"NOTHING":Suspend();break;case"REDIR_CONTENT_FRAME":Suspend(),null!=(e=SearchParentsForContentRoot())&&(IsAbsoluteUrl(EXIT_TARGET)?e.scormdriver_content.location.href=EXIT_TARGET:(t=GetContentRootUrlBase(e),e.scormdriver_content.location.href=t+EXIT_TARGET))}return!0}function GetContentRootUrlBase(e){var t=e.location.href.split("?")[0].split("/");return delete t[t.length-1],e=t.join("/")}function SearchParentsForContentRoot(){var e=null,t=window,r=0;if(t.scormdriver_content)return e=t;for(;null==e&&t!=window.top&&r++<100;){if(t.scormdriver_content)return e=t;t=t.parent}return WriteToDebug("Unable to locate content root"),null}function GetStudentID(){return WriteToDebug("In GetStudentID"),ClearErrorInfo(),IsLoaded()?objLMS.GetStudentID():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetStudentName(){return WriteToDebug("In GetStudentName"),ClearErrorInfo(),IsLoaded()?objLMS.GetStudentName():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetBookmark(){return WriteToDebug("In GetBookmark"),ClearErrorInfo(),IsLoaded()?objLMS.GetBookmark():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetBookmark(e,t){return WriteToDebug("In SetBookmark - strBookmark="+e+", strDesc="+t),ClearErrorInfo(),IsLoaded()?objLMS.SetBookmark(e,t):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetDataChunk(){return WriteToDebug("In GetDataChunk"),ClearErrorInfo(),IsLoaded()?objLMS.GetDataChunk():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetDataChunk(e){return WriteToDebug("In SetDataChunk strData="+e),ClearErrorInfo(),IsLoaded()?objLMS.SetDataChunk(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLaunchData(){return WriteToDebug("In GetLaunchData"),ClearErrorInfo(),IsLoaded()?objLMS.GetLaunchData():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetComments(){var e,t,r;if(WriteToDebug("In GetComments"),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),null;if(WriteToDebug("strCommentString="+(e=objLMS.GetComments())),""!=(e=new String(e)))for(t=e.split(" | "),r=0;r<t.length;r++)WriteToDebug("Returning Comment #"+r),t[r]=new String(t[r]),t[r]=t[r].replace(/\|\|/g,"|"),WriteToDebug("Comment #"+r+"="+t[r]);else t=new Array(0);return t}function WriteComment(e){var t;return WriteToDebug("In WriteComment strComment="+e),ClearErrorInfo(),e=new String(e),IsLoaded()?(e=e.replace(/\|/g,"||"),""!=(t=objLMS.GetComments())&&"undefined"!=t&&(e=t+" | "+e),objLMS.WriteComment(e)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLMSComments(){return WriteToDebug("In GetLMSComments"),ClearErrorInfo(),IsLoaded()?objLMS.GetLMSComments():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetAudioPlayPreference(){return WriteToDebug("In GetAudioPlayPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetAudioPlayPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),PREFERENCE_DEFAULT)}function GetAudioVolumePreference(){return WriteToDebug("GetAudioVolumePreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetAudioVolumePreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),100)}function SetAudioPreference(e,t){return WriteToDebug("In SetAudioPreference PlayPreference="+e+" intPercentOfMaxVolume="+t),ClearErrorInfo(),IsLoaded()?e!=PREFERENCE_OFF&&e!=PREFERENCE_ON?(WriteToDebug("Error Invalid PlayPreference"),SetErrorInfo(ERROR_INVALID_PREFERENCE,"Invalid PlayPreference passed to SetAudioPreference, PlayPreference="+e),!1):ValidInteger(t)?(t=parseInt(t,10))<1||100<t?(WriteToDebug("Error Invalid PercentOfMaxVolume - out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxVolume passed to SetAudioPreference (must be between 1 and 100), intPercentOfMaxVolume="+t),!1):(WriteToDebug("Calling to LMS"),objLMS.SetAudioPreference(e,t)):(WriteToDebug("Error Invalid PercentOfMaxVolume - not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxVolume passed to SetAudioPreference (not an integer), intPercentOfMaxVolume="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLanguagePreference(){return WriteToDebug("In GetLanguagePreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetLanguagePreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetLanguagePreference(e){return WriteToDebug("In SetLanguagePreference strLanguage="+e),ClearErrorInfo(),IsLoaded()?objLMS.SetLanguagePreference(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetSpeedPreference(){return WriteToDebug("In GetSpeedPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetSpeedPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),100)}function SetSpeedPreference(e){return WriteToDebug("In SetSpeedPreference intPercentOfMax="+e),ClearErrorInfo(),IsLoaded()?ValidInteger(e)?(e=parseInt(e,10))<0||100<e?(WriteToDebug("ERROR Invalid Percent of MaxSpeed, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxSpeed passed to SetSpeedPreference (must be between 1 and 100), intPercentOfMax="+e),!1):(WriteToDebug("Calling to LMS"),objLMS.SetSpeedPreference(e)):(WriteToDebug("ERROR Invalid Percent of MaxSpeed, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxSpeed passed to SetSpeedPreference (not an integer), intPercentOfMax="+e),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetTextPreference(){return WriteToDebug("In GetTextPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetTextPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetTextPreference(e){return WriteToDebug("In SetTextPreference intPreference="+e),ClearErrorInfo(),IsLoaded()?e!=PREFERENCE_DEFAULT&&e!=PREFERENCE_OFF&&e!=PREFERENCE_ON?(WriteToDebug("Error - Invalid Preference"),SetErrorInfo(ERROR_INVALID_PREFERENCE,"Invalid Preference passed to SetTextPreference, intPreference="+e),!1):objLMS.SetTextPreference(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetPreviouslyAccumulatedTime(){return WriteToDebug("In GetPreviouslyAccumulatedTime"),ClearErrorInfo(),IsLoaded()?objLMS.GetPreviouslyAccumulatedTime():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function AccumulateTime(){WriteToDebug("In AccumulateTime dtmStart="+dtmStart+" dtmEnd="+dtmEnd+" intAccumulatedMS="+intAccumulatedMS),null!=dtmEnd&&null!=dtmStart&&(WriteToDebug("Accumulating Time"),WriteToDebug("intAccumulatedMS="+(intAccumulatedMS+=dtmEnd.getTime()-dtmStart.getTime())))}function GetSessionAccumulatedTime(){return WriteToDebug("In GetSessionAccumulatedTime"),ClearErrorInfo(),WriteToDebug("Setting dtmEnd to now"),dtmEnd=new Date,WriteToDebug("Accumulating Time"),AccumulateTime(),null!=dtmStart&&(WriteToDebug("Resetting dtmStart"),dtmStart=new Date),WriteToDebug("Setting dtmEnd to null"),dtmEnd=null,WriteToDebug("Returning "+intAccumulatedMS),intAccumulatedMS}function SetSessionTime(e){return WriteToDebug("In SetSessionTime"),ClearErrorInfo(),ValidInteger(e)?(e=parseInt(e,10))<0?(WriteToDebug("Error, parameter is less than 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMilliseconds passed to SetSessionTime (must be greater than 0), intMilliseconds="+e),!1):(blnOverrodeTime=!0,intTimeOverrideMS=e,objLMS.SaveTime(intTimeOverrideMS),!0):(WriteToDebug("ERROR parameter is not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMilliseconds passed to SetSessionTime (not an integer), intMilliseconds="+e),!1)}function PauseTimeTracking(){return WriteToDebug("In PauseTimeTracking"),ClearErrorInfo(),WriteToDebug("Setting dtmEnd to now"),dtmEnd=new Date,WriteToDebug("Accumulating Time"),AccumulateTime(),WriteToDebug("Setting Start and End times to null"),!(dtmEnd=dtmStart=null)}function ResumeTimeTracking(){return WriteToDebug("In ResumeTimeTracking"),ClearErrorInfo(),WriteToDebug("Setting dtmStart to now"),dtmStart=new Date,!0}function GetMaxTimeAllowed(){return WriteToDebug("In GetMaxTimeAllowed"),ClearErrorInfo(),IsLoaded()?objLMS.GetMaxTimeAllowed():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),MAX_CMI_TIME)}function DisplayMessageOnTimeout(){return WriteToDebug("In DisplayMessageOnTimeOut"),ClearErrorInfo(),IsLoaded()?objLMS.DisplayMessageOnTimeout():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function ExitOnTimeout(){return WriteToDebug("In ExitOnTimeOut"),ClearErrorInfo(),IsLoaded()?objLMS.ExitOnTimeout():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetPassingScore(){return WriteToDebug("In GetPassingScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetPassingScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function GetScore(){return WriteToDebug("In GetScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function GetScaledScore(){return WriteToDebug("In GetScaledScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetScaledScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function SetScore(e,t,r){if(WriteToDebug("In SetScore, intScore="+e+", intMaxScore="+t+", intMinScore="+r),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(!IsValidDecimal(e))return WriteToDebug("ERROR - intScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (not a valid decimal), intScore="+e),!1;if(!IsValidDecimal(t))return WriteToDebug("ERROR - intMaxScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (not a valid decimal), intMaxScore="+t),!1;if(!IsValidDecimal(r))return WriteToDebug("ERROR - intMinScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (not a valid decimal), intMinScore="+r),!1;if(WriteToDebug("Converting SCORES to floats"),e=parseFloat(e),t=parseFloat(t),r=parseFloat(r),"SCORM"==strLMSStandard){if(WriteToDebug("DEBUG - SCORM 1.2 so checking max score length"),e<0||100<e)return WriteToDebug("ERROR - intScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (must be between 0-100), intScore="+e),!1;if(t<0||100<t)return WriteToDebug("ERROR - intMaxScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (must be between 0-100), intMaxScore="+t),!1;if(r<0||100<r)return WriteToDebug("ERROR - intMinScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (must be between 0-100), intMinScore="+r),!1}if(!0===SCORE_CAN_ONLY_IMPROVE){var n=GetScore();if(null!=n&&""!=n&&e<n)return WriteToDebug("Previous score was greater than new score, configuration only allows scores to improve, returning."),!0}return WriteToDebug("Calling to LMS"),objLMS.SetScore(e,t,r)}function SetPointBasedScore(e,t,r){if(WriteToDebug("In SetPointBasedScore, intScore="+e+", intMaxScore="+t+", intMinScore="+r),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(!IsValidDecimal(e))return WriteToDebug("ERROR - intScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (not a valid decimal), intScore="+e),!1;if(!IsValidDecimal(t))return WriteToDebug("ERROR - intMaxScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (not a valid decimal), intMaxScore="+t),!1;if(!IsValidDecimal(r))return WriteToDebug("ERROR - intMinScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (not a valid decimal), intMinScore="+r),!1;if(WriteToDebug("Converting SCORES to floats"),e=parseFloat(e),t=parseFloat(t),r=parseFloat(r),"SCORM"==strLMSStandard){if(e<0||100<e)return WriteToDebug("ERROR - intScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (must be between 0-100), intScore="+e),!1;if(t<0||100<t)return WriteToDebug("ERROR - intMaxScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (must be between 0-100), intMaxScore="+t),!1;if(r<0||100<r)return WriteToDebug("ERROR - intMinScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (must be between 0-100), intMinScore="+r),!1}if(!0===SCORE_CAN_ONLY_IMPROVE){var n=GetScore();if(null!=n&&""!=n&&e<n)return WriteToDebug("Previous score was greater than new score, configuration only allows scores to improve, returning."),!0}return WriteToDebug("Calling to LMS"),objLMS.SetPointBasedScore(e,t,r)}function CreateResponseIdentifier(e,t){return""==e.replace(" ","")?(WriteToDebug("Short Identifier is empty"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1):1!=e.length?(WriteToDebug("ERROR - Short Identifier  not 1 character"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1):IsAlphaNumeric(e)?new ResponseIdentifier(e=e.toLowerCase(),t=CreateValidIdentifier(t)):(WriteToDebug("ERROR - Short Identifier  not alpha numeric"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1)}function ResponseIdentifier(e,t){this.Short=new String(e),this.Long=new String(t),this.toString=function(){return"[Response Identifier "+this.Short+", "+this.Long+"]"}}function MatchingResponse(e,t){e.constructor==String&&(e=CreateResponseIdentifier(e,e)),t.constructor==String&&(t=CreateResponseIdentifier(t,t)),this.Source=e,this.Target=t,this.toString=function(){return"[Matching Response "+this.Source+", "+this.Target+"]"}}function CreateMatchingResponse(e){var t=new Array,r=new Array;t=(e=new String(e)).split("[,]");for(var n=0;n<t.length;n++){WriteToDebug("Matching Response ["+n+"]  source: "+(r=new String(t[n]).split("[.]"))[0]+"  target: "+r[1]),t[n]=new MatchingResponse(r[0],r[1])}return WriteToDebug("pattern: "+e+" becomes "+t[0]),0==t.length?t[0]:t}function CreateValidIdentifier(e){return objLMS.CreateValidIdentifier(e)}function CreateUriIdentifier(e,t){if(null==e||""===e)return"";e=Trim(e);var r=new URI(e);return r.is("absolute")||(e="urn:scormdriver:"+encodeURIComponent(e),r=new URI(e)),r.normalize(),t&&r.iri(),r.toString()}function CreateValidIdentifierLegacy(e){return null!=e||""!=e?(e=new String(e),0==(e=Trim(e)).toLowerCase().indexOf("urn:")&&(e=e.substr(4)),e=e.replace(/[^\w\-\(\)\+\.\:\=\@\;\$\_\!\*\'\%]/g,"_")):""}function Trim(e){return e=(e=(e+="").replace(/^\s*/,"")).replace(/\s*$/,"")}function RecordTrueFalseInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordTrueFalseInteraction strID="+(e=CreateValidIdentifier(e))+", blnResponse="+t+", blnCorrect="+r+", blnCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(1!=t&&0!=t&&null!==t||null===t&&!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The Response parameter must be a valid boolean value."),!1;if(null!=n&&1!=n&&0!=n)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The Correct Response parameter must be a valid boolean value or null."),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordTrueFalseInteraction(e,t,r,n,o,i,a,S,u)}function RecordMultipleChoiceInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordMultipleChoiceInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,s;if(e=new String(e),null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==String){u=new Array;var l=CreateResponseIdentifier(t,t);if(0==l)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u[0]=l}else if(t.constructor==ResponseIdentifier)(u=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))u=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return window.console&&window.console.log("ERROR_INVALID_INTERACTION_RESPONSE :: The response is not in the correct format."),SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null!=n&&null!=n&&""!=n)if(n.constructor==String){if(s=new Array,0==(l=CreateResponseIdentifier(n,n)))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s[0]=l}else if(n.constructor==ResponseIdentifier)(s=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))s=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s=n}else s=new Array;var R=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordMultipleChoiceInteraction(e,u,r,s,o,i,a,S,R)}function RecordFillInInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordFillInInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordFillInInteraction(e,t,r,n,o,i,a,S,u)}function RecordMatchingInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordMatchingInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,s;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==MatchingResponse)(u=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))u=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null!=n&&null!=n)if(n.constructor==MatchingResponse)(s=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))s=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;s=n}else s=new Array;var l=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordMatchingInteraction(e,u,r,s,o,i,a,S,l)}function RecordPerformanceInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordPerformanceInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordPerformanceInteraction(e,t,r,n,o,i,a,S,u)}function RecordSequencingInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordSequencingInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,s;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==String){u=new Array;var l=CreateResponseIdentifier(t,t);if(0==l)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u[0]=l}else if(t.constructor==ResponseIdentifier)(u=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))u=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null!=n&&null!=n&&""!=n)if(n.constructor==String){if(s=new Array,0==(l=CreateResponseIdentifier(n,n)))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s[0]=l}else if(n.constructor==ResponseIdentifier)(s=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))s=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s=n}else s=new Array;var R=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordSequencingInteraction(e,u,r,s,o,i,a,S,R)}function RecordLikertInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordLikertInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var u,s;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;u=null}else if(t.constructor==String)u=CreateResponseIdentifier(t,t);else{if(t.constructor!=ResponseIdentifier)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;u=t}if(null==n||null==n)s=null;else if(n.constructor==ResponseIdentifier)s=n;else{if(n.constructor!=String)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;s=CreateResponseIdentifier(n,n)}var l=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordLikertInteraction(e,u,r,s,o,i,a,S,l)}function RecordNumericInteraction(e,t,r,n,o,i,a,S){if(WriteToDebug("In RecordNumericInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(S=CreateValidIdentifier(S))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(null===t&&!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE||null!==t&&!IsValidDecimal(t))return WriteToDebug("ERROR - Invalid Response, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Response passed to RecordNumericInteraction (not a valid decimal), strResponse="+t),!1;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordNumericInteraction(e,t,r,n,o,i,a,S,u)}function GetStatus(){return WriteToDebug("In GetStatus"),ClearErrorInfo(),IsLoaded()?objLMS.GetStatus():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function ResetStatus(){return WriteToDebug("In ResetStatus"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to false"),blnStatusWasSet=!1,objLMS.ResetStatus()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetProgressMeasure(){return WriteToDebug("In GetProgressMeasure"),ClearErrorInfo(),IsLoaded()?objLMS.GetProgressMeasure():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function SetProgressMeasure(e){return WriteToDebug("In SetProgressMeasure, passing in: "+e),ClearErrorInfo(),IsLoaded()?objLMS.SetProgressMeasure(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function SetPassed(){return WriteToDebug("In SetPassed"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to true"),blnStatusWasSet=!0,objLMS.SetPassed()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetFailed(){return WriteToDebug("In SetFailed"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to true"),blnStatusWasSet=!0,objLMS.SetFailed()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetEntryMode(){return WriteToDebug("In GetEntryMode"),ClearErrorInfo(),IsLoaded()?objLMS.GetEntryMode():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),ENTRY_FIRST_TIME)}function GetLessonMode(){return WriteToDebug("In GetLessonMode"),ClearErrorInfo(),IsLoaded()?objLMS.GetLessonMode():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),MODE_NORMAL)}function GetTakingForCredit(){return WriteToDebug("In GetTakingForCredit"),ClearErrorInfo(),IsLoaded()?objLMS.GetTakingForCredit():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveScore(e,t,r,n){return WriteToDebug("In SetObjectiveScore, intObjectiveID="+e+", intScore="+t+", intMaxScore="+r+", intMinScore="+n),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveScore (must have a value), strObjectiveID="+e),!1):IsValidDecimal(t)?IsValidDecimal(r)?IsValidDecimal(n)?(WriteToDebug("Converting Scores to floats"),t=parseFloat(t),r=parseFloat(r),n=parseFloat(n),t<0||100<t?(WriteToDebug("ERROR - Invalid Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetObjectiveScore (must be between 0-100), intScore="+t),!1):r<0||100<r?(WriteToDebug("ERROR - Invalid Max Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetObjectiveScore (must be between 0-100), intMaxScore="+r),!1):n<0||100<n?(WriteToDebug("ERROR - Invalid Min Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetObjectiveScore (must be between 0-100), intMinScore="+n),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveScore(e,t,r,n))):(WriteToDebug("ERROR - Invalid Min Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetObjectiveScore (not a valid decimal), intMinScore="+n),!1):(WriteToDebug("ERROR - Invalid Max Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetObjectiveScore (not a valid decimal), intMaxScore="+r),!1):(WriteToDebug("ERROR - Invalid Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetObjectiveScore (not a valid decimal), intScore="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveStatus(e,t){return WriteToDebug("In SetObjectiveStatus strObjectiveID="+e+", Lesson_Status="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveStatus (must have a value), strObjectiveID="+e),!1):t!=LESSON_STATUS_PASSED&&t!=LESSON_STATUS_COMPLETED&&t!=LESSON_STATUS_FAILED&&t!=LESSON_STATUS_INCOMPLETE&&t!=LESSON_STATUS_BROWSED&&t!=LESSON_STATUS_NOT_ATTEMPTED?(WriteToDebug("ERROR - Invalid Status"),SetErrorInfo(ERROR_INVALID_STATUS,"Invalid status passed to SetObjectiveStatus, Lesson_Status="+t),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveStatus(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveStatus(e){return WriteToDebug("In GetObjectiveStatus, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveStatus(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveDescription(e,t){return WriteToDebug("In SetObjectiveDescription strObjectiveID="+e+", strObjectiveDescription="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveStatus (must have a value), strObjectiveID="+e),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveDescription(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveDescription(e){return WriteToDebug("In GetObjectiveDescription, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveDescription(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveScore(e){return WriteToDebug("In GetObjectiveScore, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveScore(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function IsLmsPresent(){return blnLmsPresent}function SetObjectiveProgressMeasure(e,t){return WriteToDebug("In SetObjectiveProgressMeasure strObjectiveID="+e+", strObjectiveProgressMeasure="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveProgressMeasure (must have a value), strObjectiveID="+e),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveProgressMeasure(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveProgressMeasure(e){return WriteToDebug("In GetObjectiveProgressMeasure, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveProgressMeasure(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetNavigationRequest(e){return WriteToDebug("In SetNavigationRequest"),ClearErrorInfo(),IsLoaded()?objLMS.SetNavigationRequest(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetNavigationRequest(){return WriteToDebug("In GetNavigationRequest"),ClearErrorInfo(),IsLoaded()?objLMS.GetNavigationRequest():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionType(e){return WriteToDebug("In GetInteractionType, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionType(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionTimestamp(e){return WriteToDebug("In GetInteractionTimestamp, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionTimestamp(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionCorrectResponses(e){return WriteToDebug("In GetInteractionCorrectResponses, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionCorrectResponses(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionWeighting(e){return WriteToDebug("In GetInteractionWeighting, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionWeighting(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionLearnerResponses(e){return WriteToDebug("In GetInteractionLearnerResponses, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionLearnerResponses(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionResult(e){return WriteToDebug("In GetInteractionResult, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionResult(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionLatency(e){return WriteToDebug("In GetInteractionLatency, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionLatency(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionDescription(e){return WriteToDebug("In GetInteractionDescription, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionDescription(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function CreateDataBucket(e,t,r){return WriteToDebug("In CreateDataBucket, strBucketId="+e+", intMinSize="+t+", intMaxSize="+r),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to CreateDataBucket (must have a value), strBucketId="+e),!1):ValidInteger(t)?ValidInteger(r)?(t=parseInt(t,10),r=parseInt(r,10),t<0?(WriteToDebug("ERROR Invalid Min Size, must be greater than or equal to 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Size passed to CreateDataBucket (must be greater than or equal to 0), intMinSize="+t),!1):r<=0?(WriteToDebug("ERROR Invalid Max Size, must be greater than 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Size passed to CreateDataBucket (must be greater than 0), intMaxSize="+r),!1):(t*=2,r*=2,objLMS.CreateDataBucket(e,t,r))):(WriteToDebug("ERROR Invalid Max Size, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMaxSize passed to CreateDataBucket (not an integer), intMaxSize="+r),!1):(WriteToDebug("ERROR Invalid Min Size, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMinSize passed to CreateDataBucket (not an integer), intMinSize="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetDataFromBucket(e){return WriteToDebug("In GetDataFromBucket, strBucketId="+e),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to GetDataFromBucket (must have a value), strBucketId="+e),!1):objLMS.GetDataFromBucket(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function PutDataInBucket(e,t,r){return WriteToDebug("In PutDataInBucket, strBucketId="+e+", blnAppendToEnd="+r+", strData="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to PutDataInBucket (must have a value), strBucketId="+e),!1):(1!=r&&(WriteToDebug("blnAppendToEnd was not explicitly true so setting it to false, blnAppendToEnd="+r),r=!1),objLMS.PutDataInBucket(e,t,r)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function DetectSSPSupport(){return objLMS.DetectSSPSupport()}function GetBucketInfo(e){if(WriteToDebug("In GetBucketInfo, strBucketId="+e),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(""==(e=new String(e)).replace(" ",""))return WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to GetBucketInfo (must have a value), strBucketId="+e),!1;var t=objLMS.GetBucketInfo(e);return t.TotalSpace=t.TotalSpace/2,t.UsedSpace=t.UsedSpace/2,WriteToDebug("GetBucketInfo returning "+t),t}function SSPBucketSize(e,t){this.TotalSpace=e,this.UsedSpace=t,this.toString=function(){return"[SSPBucketSize "+this.TotalSpace+", "+this.UsedSpace+"]"}}