/*! Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c */

!function(){var r,i,e,a,o=window,c="rxd: ",l=-100,s={"true-false":o.RecordTrueFalseInteraction,choice:o.RecordMultipleChoiceInteraction,"fill-in":o.RecordFillInInteraction,"long-fill-in":o.RecordFillInInteraction,matching:o.RecordMatchingInteraction,performance:o.RecordPerformanceInteraction,sequencing:o.RecordSequencingInteraction,likert:o.RecordLikertInteraction,numeric:o.RecordNumericInteraction},d=function(e){return"[object Array]"===Object.prototype.toString.call(e)},u=function(e){var t,n;return void 0!==e.short||void 0!==e.long?(void 0===(t=e.short)&&(t=e.long.substring(0,1)),n=e.long):1<(t=n=e).length&&(t=e.substring(0,1)),o.CreateResponseIdentifier(t,n)};r=function(e){var t,n,r,i;if(void 0!==s[e.type]){if("correct"===e.result?t=o.INTERACTION_RESULT_CORRECT:"incorrect"===e.result?t=o.INTERACTION_RESULT_WRONG:"unanticipated"===e.result?t=o.INTERACTION_RESULT_UNANTICIPATED:"neutral"===e.result&&(t=o.INTERACTION_RESULT_NEUTRAL),n=e.learnerResponse,r=e.correctResponse,d(r)&&(r=void 0===r[0]?null:r[0]),"true-false"===e.type)null!==n&&!0!==n&&!1!==n&&(n=o.ConvertStringToBoolean(n)),null!==r&&!0!==n&&!1!==n&&(r=o.ConvertStringToBoolean(r));else if("choice"===e.type||"sequencing"===e.type){if(null!==n)if(d(n))for(i=0;i<n.length;i+=1)n[i]=u(n[i]);else n=[u(n)];if(null!==r)if(d(r))for(i=0;i<r.length;i+=1)r[i]=u(r[i]);else r=[u(r)]}else if("likert"===e.type)null!==n&&(n=u(n)),null!==r&&(r=u(r));else if("matching"===e.type){if(null!==n)if(d(n))for(i=0;i<n.length;i+=1)n[i]=new window.MatchingResponse(u(n[i].source),u(n[i].target));else n=[new window.MatchingResponse(u(n.source),u(n.target))];if(null!==r)if(d(r))for(i=0;i<r.length;i+=1)r[i]=new window.MatchingResponse(u(r[i].source),u(r[i].target));else r=[new window.MatchingResponse(u(r.source),u(r.target))]}void 0===e.latency&&(e.latency=null),void 0===e.weighting&&(e.weighting=null),void 0===e.learningObjectiveId&&(e.learningObjectiveId=""),s[e.type](e.identifier,n,t,r,e.description,e.weighting,e.latency,e.learningObjectiveId)}else o.WriteToDebug(c+"unrecognized interaction type will not get recorded: "+e.type)},i=function(e,t){var n=!1;switch(e){case"Initialize":window.clearTimeout(a);break;case"ConcedeControl":o.ConcedeControl();break;case"SetBookmark":o.SetBookmark(t.location),n=!0;break;case"SetSuspendData":o.SetDataChunk(t.data),n=!0;break;case"SetPassed":o.SetPassed(),n=!0;break;case"SetFailed":o.SetFailed(),n=!0;break;case"SetReachedEnd":o.SetReachedEnd(),n=!0;break;case"ResetStatus":o.ResetStatus(),n=!0;break;case"SetScore":o.SetScore(t.raw,t.max,t.min),n=!0;break;case"RecordInteraction":r(t),n=!0;break;default:throw new Error("Unrecognized pipe message action: "+e)}n&&o.CommitData()},e=function(e){var t,n=e.data;o.WriteToDebug(c+"handling message: "+n);try{t=JSON.parse(n)}catch(e){return void o.WriteToDebug(c+"failed to parse message as JSON (likely not an RXD message). Parse Error: "+e+", msg: '"+n+"'")}if(t.rusticiSoftwareCrossDomain){if(!(t.sequenceNumber<=l)){l=t.sequenceNumber,t.cfg=t.cfg||{};try{i(t.action,t.cfg)}catch(e){o.WriteToDebug(c+"error in set call "+t.action+":: Error: "+e),alert("Failed call to 3rd party LMS (data may have been lost): "+n)}}}else o.WriteToDebug(c+"failed to detect 'rusticiSoftwareCrossDomain' property in JSON message (likely not an RXD message), msg: '"+n+"'")};try{window.addEventListener("message",e,!1)}catch(e){alert("Failed to add 'message' event listener (data will be lost) - unsupported browser: "+e)}a=window.setTimeout(function(){alert("Failed to receive initial pipe message from content - data may be lost")},3e4)}();