/*! Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c */

!function(e,t){"object"==typeof exports?module.exports=exports=t():"function"==typeof define&&define.amd?define([],t):e.CryptoJS=t()}(this,function(){var f,n,e,t,r,l,i,o,a,s,c,u,d=d||(f=Math,n=Object.create||function(){function n(){}return function(e){var t;return n.prototype=e,t=new n,n.prototype=null,t}}(),t=(e={}).lib={},r=t.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),(t.init.prototype=t).$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=t.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||o).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,i=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<i;o++){var a=n[o>>>2]>>>24-o%4*8&255;t[r+o>>>2]|=a<<24-(r+o)%4*8}else for(o=0;o<i;o+=4)t[r+o>>>2]=n[o>>>2];return this.sigBytes+=i,this},clamp:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=f.ceil(t/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t,n=[],r=function(t){t=t;var n=987654321,r=4294967295;return function(){var e=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return e/=4294967296,(e+=.5)*(.5<f.random()?1:-1)}},i=0;i<e;i+=4){var o=r(4294967296*(t||f.random()));t=987654071*o(),n.push(4294967296*o()|0)}return new l.init(n,e)}}),i=e.enc={},o=i.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new l.init(n,t/2)}},a=i.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new l.init(n,t)}},s=i.Utf8={stringify:function(e){try{return decodeURIComponent(escape(a.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return a.parse(unescape(encodeURIComponent(e)))}},c=t.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=s.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(e){var t=this._data,n=t.words,r=t.sigBytes,i=this.blockSize,o=r/(4*i),a=(o=e?f.ceil(o):f.max((0|o)-this._minBufferSize,0))*i,s=f.min(4*a,r);if(a){for(var c=0;c<a;c+=i)this._doProcessBlock(n,c);var u=n.splice(0,a);t.sigBytes-=s}return new l.init(u,s)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),t.Hasher=c.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){c.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(n){return function(e,t){return new n.init(t).finalize(e)}},_createHmacHelper:function(n){return function(e,t){return new u.HMAC.init(n,t).finalize(e)}}}),u=e.algo={},e);return d}),function(e,t){"object"==typeof exports?module.exports=exports=t(require("./core")):"function"==typeof define&&define.amd?define(["./core"],t):t(e.CryptoJS)}(this,function(e){var t,n,i,o,r;return n=(t=e).lib,i=n.Base,o=n.WordArray,(r=t.x64={}).Word=i.extend({init:function(e,t){this.high=e,this.low=t}}),r.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,n=[],r=0;r<t;r++){var i=e[r];n.push(i.high),n.push(i.low)}return o.create(n,this.sigBytes)},clone:function(){for(var e=i.clone.call(this),t=e.words=this.words.slice(0),n=t.length,r=0;r<n;r++)t[r]=t[r].clone();return e}}),e}),function(e,t){"object"==typeof exports?module.exports=exports=t(require("./core")):"function"==typeof define&&define.amd?define(["./core"],t):t(e.CryptoJS)}(this,function(t){return function(){if("function"==typeof ArrayBuffer){var e=t.lib.WordArray,i=e.init;(e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,n=[],r=0;r<t;r++)n[r>>>2]|=e[r]<<24-r%4*8;i.call(this,n,t)}else i.apply(this,arguments)}).prototype=e}}(),t.lib.WordArray}),function(e,t){"object"==typeof exports?module.exports=exports=t(require("./core")):"function"==typeof define&&define.amd?define(["./core"],t):t(e.CryptoJS)}(this,function(c){return function(i){var e=c,t=e.lib,n=t.WordArray,r=t.Hasher,o=e.algo,a=[],S=[];!function(){function e(e){for(var t=i.sqrt(e),n=2;n<=t;n++)if(!(e%n))return!1;return!0}function t(e){return 4294967296*(e-(0|e))|0}for(var n=2,r=0;r<64;)e(n)&&(r<8&&(a[r]=t(i.pow(n,.5))),S[r]=t(i.pow(n,1/3)),r++),n++}();var w=[],s=o.SHA256=r.extend({_doReset:function(){this._hash=new n.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],s=n[4],c=n[5],u=n[6],f=n[7],l=0;l<64;l++){if(l<16)w[l]=0|e[t+l];else{var d=w[l-15],h=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,p=w[l-2],y=(p<<15|p>>>17)^(p<<13|p>>>19)^p>>>10;w[l]=h+w[l-7]+y+w[l-16]}var m=r&i^r&o^i&o,g=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),v=f+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&c^~s&u)+S[l]+w[l];f=u,u=c,c=s,s=a+v|0,a=o,o=i,i=r,r=v+(g+m)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+o|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0,n[5]=n[5]+c|0,n[6]=n[6]+u|0,n[7]=n[7]+f|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=i.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});e.SHA256=r._createHelper(s),e.HmacSHA256=r._createHmacHelper(s)}(Math),c.SHA256}),function(){var e={learnerIdTemplate:"{{learnerId}}",learnerFirstNameTemplate:"{{learnerFirstName}}",learnerLastNameTemplate:"{{learnerLastName}}"},v={1:"normal",2:"browse",3:"review"},S={1:"passed",2:"completed",3:"failed",4:"incomplete",5:"browsed",6:"not attempted"},w=function(e,n){return e.replace(/{{([^}]+)}}/g,function(e,t){return void 0!==n[t]?n[t]:e})},r=function(e,t){var n,r,i,o,a,s,c,u,f,l,d,h=RXDConfig.contentUrl,p=objLMS.GetStudentID(),y=(u=document.location,f=u.protocol,l=u.host,(d=u.pathname.split("/")).pop(),[f,"//",l,d.join("/")].join("")),m="",g=[];null!==p&&0!==p.length?(r=(n=function(e){var t,n=[];if(0<=e.indexOf(",")){if(1===(n=e.split(",")).length)return n[0].length<1?["Unknown","Learner"]:[n[0].replace(/\s+/g,""),n[0].replace(/\s+/g,"")];if(0===n.length)return["Unknown","Learner"]}else 0<=e.indexOf(" ")?(t=e.indexOf(" "),n.push(e.substr(t+1)),n.push(e.substr(0,t))):n=[e,"Learner"];return n[0]=n[0].replace(/\s+/g,""),n[1]=n[1].replace(/\s+/g,""),""===n[0]&&(n[0]="Unknown"),""===n[1]&&(n[1]="Learner"),n}(objLMS.GetStudentName()||""))[1],i=n[0],p=w(e.learnerIdTemplate,{learnerId:p,learnerIdHash:CryptoJS.SHA256(p).toString(CryptoJS.enc.Hex)}),r=w(e.learnerFirstNameTemplate,{learnerFirstName:r,learnerFirstNameHash:CryptoJS.SHA256(r).toString(CryptoJS.enc.Hex)}),i=w(e.learnerLastNameTemplate,{learnerLastName:i,learnerLastNameHash:CryptoJS.SHA256(i).toString(CryptoJS.enc.Hex)}),o=v[objLMS.GetLessonMode()]||"unknown",a=S[objLMS.GetStatus()]||"unknown",s=JSON.stringify([o,a,objLMS.GetBookmark(),objLMS.GetDataChunk()]),""!==RXDConfig.contentApi?(0!==RXDConfig.contentApi.indexOf("http")&&(m=RXDConfig.rxdHostUrl),m+=RXDConfig.contentApi,g.push("contentUrl="+encodeURIComponent(h))):m=h,g.push("studentid="+encodeURIComponent(p),"studentname="+encodeURIComponent(i+","+r),"startdata="+encodeURIComponent(s),"pipeurl="+encodeURIComponent(y)),(c=document.createElement("script")).async=!0,c.src=RXDConfig.rxdHostUrl+"rustici-xd-pkg-to-lms.min.js?@@NOCACHE@@",c.onload=function(){c.readyState&&!/loaded|complete/.test(c.readyState)||(clearTimeout(t),c.parentNode.removeChild(c),c=null,document.getElementById("scormdriver_content").src=m+(-1===m.indexOf("?")?"?":"&")+g.join("&"))},document.getElementsByTagName("head")[0].appendChild(c)):alert("The host LMS for this launch returned an empty learner id. The learner id is required for RXD, the launch has been halted.")};window.LoadContent=function(){var n;n=setTimeout(function(){alert("Failed to establish communication with source content (LMS conduit hasn't loaded)")},3e4),""===RXDConfig.preLaunchConfigurationUrl?r(e,n):function(e,t){var n=new XMLHttpRequest;n.onreadystatechange=function(){var e;if(4===n.readyState){if(200===n.status){try{e=JSON.parse(n.responseText)}catch(e){return void t("Failed to parse response from pre-launch configuration URL as JSON, the launch has been halted. ("+e+")")}return void t(null,e)}t("Unrecognized HTTP status returned from pre-launch configuration URL ("+n.status+"), the launch has been halted.")}},n.open("GET",e,!0);try{n.send()}catch(e){}}(RXDConfig.preLaunchConfigurationUrl,function(e,t){null===e?r(t,n):alert("Pre-launch configuration failed: "+e)})}}();