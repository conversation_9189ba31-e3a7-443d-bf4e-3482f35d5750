<!DOCTYPE html>
<html style="width: 100%; height: 100%; overflow: hidden;">

<!-- Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c -->
<head>
    <script src="rustici-xd-pkg-api.min.js?e51db7282aa82cc09edd3de18d47c90ff579580c"></script>
    <script>
        function getQueryStringValue (strElement, strQueryString) {
            var aryPairs,
                i,
                intEqualPos,
                strArg = "",
                strValue = "";

            // get rid of the leading "?"
            strQueryString = strQueryString.substring(1);

            // split into name/value pairs
            aryPairs = strQueryString.split("&");

            strElement = strElement.toLowerCase();

            // search each querystring value and return the first match
            for (i = 0; i < aryPairs.length; i += 1) {
                intEqualPos = aryPairs[i].indexOf("=");

                if (intEqualPos !== -1) {
                    strArg = aryPairs[i].substring(0, intEqualPos);

                    if (strArg.toLowerCase() === strElement.toLowerCase()) {
                        strValue = aryPairs[i].substring(intEqualPos + 1);

                        strValue = strValue.replace(/\+/g, "%20");
                        strValue = decodeURIComponent(strValue);

                        return strValue;
                    }
                }
            }

            // if we didn't find a match, return an empty string
            return "";
        }




        function loadFrames () {
            console.log('In loadFrames...');
            var startPath = getQueryStringValue("contentUrl", document.location.search);

            //
            // This frame goes to the actual real content, that content should access
            // the RXD API via `window.parent`
            //
            document.getElementById("contentRelay").setAttribute("src", startPath);
            RXD.Initialize();
        }
    </script>
</head>

<body onload="loadFrames()" style="width: 100%; height: 100%; margin: 0px;">
    <iframe id="contentRelay" style="width: 100%; height: 100%; border: 0px;" allow="autoplay"></iframe>
</body>
</html>
