/*! Copyright 2003-2018 Rustici Software, LLC All Rights Reserved. Rustici-Cross-Domain 3.0.2 2019-02-20T19:01:06+0000 e51db7282aa82cc09edd3de18d47c90ff579580c */

var VERSION="7.3.1",PREFERENCE_DEFAULT=0,PREFERENCE_OFF=-1,PREFERENCE_ON=1,LESSON_STATUS_PASSED=1,LESSON_STATUS_COMPLETED=2,LESSON_STATUS_FAILED=3,LESSON_STATUS_INCOMPLETE=4,LESSON_STATUS_BROWSED=5,LESSON_STATUS_NOT_ATTEMPTED=6,ENTRY_REVIEW=1,ENTRY_FIRST_TIME=2,ENTRY_RESUME=3,MODE_NORMAL=1,MODE_BROWSE=2,MODE_REVIEW=3,MAX_CMI_TIME=36002439990,NO_ERROR=0,ERROR_LMS=1,ERROR_INVALID_PREFERENCE=2,ERROR_INVALID_NUMBER=3,ERROR_INVALID_ID=4,ERROR_INVALID_STATUS=5,ERROR_INVALID_RESPONSE=6,ERROR_NOT_LOADED=7,ERROR_INVALID_INTERACTION_RESPONSE=8,EXIT_TYPE_SUSPEND="SUSPEND",EXIT_TYPE_FINISH="FINISH",EXIT_TYPE_TIMEOUT="TIMEOUT",EXIT_TYPE_UNLOAD="UNLOAD",INTERACTION_RESULT_CORRECT="CORRECT",INTERACTION_RESULT_WRONG="WRONG",INTERACTION_RESULT_UNANTICIPATED="UNANTICIPATED",INTERACTION_RESULT_NEUTRAL="NEUTRAL",INTERACTION_TYPE_TRUE_FALSE="true-false",INTERACTION_TYPE_CHOICE="choice",INTERACTION_TYPE_FILL_IN="fill-in",INTERACTION_TYPE_LONG_FILL_IN="long-fill-in",INTERACTION_TYPE_MATCHING="matching",INTERACTION_TYPE_PERFORMANCE="performance",INTERACTION_TYPE_SEQUENCING="sequencing",INTERACTION_TYPE_LIKERT="likert",INTERACTION_TYPE_NUMERIC="numeric",DATA_CHUNK_PAIR_SEPARATOR="###",DATA_CHUNK_VALUE_SEPARATOR="$$",APPID="__APPID__",CLOUDURL="__CLOUDURL__",blnDebug=!0,strLMSStandard="AICC",DEFAULT_EXIT_TYPE=EXIT_TYPE_SUSPEND,AICC_LESSON_ID="1",EXIT_BEHAVIOR="SCORM_RECOMMENDED",EXIT_TARGET="goodbye.html",AICC_COMM_DISABLE_XMLHTTP=!1,AICC_COMM_DISABLE_IFRAME=!1,AICC_COMM_PREPEND_HTTP_IF_MISSING=!0,AICC_REPORT_MIN_MAX_SCORE=!0,SHOW_DEBUG_ON_LAUNCH=!1,DO_NOT_REPORT_INTERACTIONS=!1,SCORE_CAN_ONLY_IMPROVE=!1,REVIEW_MODE_IS_READ_ONLY=!1,TCAPI_DONT_USE_BROKEN_URN_IDS=!0,AICC_RE_CHECK_LOADED_INTERVAL=250,AICC_RE_CHECK_ATTEMPTS_BEFORE_TIMEOUT=240,USE_AICC_KILL_TIME=!0,AICC_ENTRY_FLAG_DEFAULT=ENTRY_REVIEW,AICC_USE_CUSTOM_COMMS=!1,FORCED_COMMIT_TIME="0",ALLOW_NONE_STANDARD=!1,USE_2004_SUSPENDALL_NAVREQ=!1,USE_STRICT_SUSPEND_DATA_LIMITS=!0,EXIT_SUSPEND_IF_COMPLETED=!0,EXIT_NORMAL_IF_PASSED=!1,AICC_ENCODE_PARAMETER_VALUES=!0,PASS_FAIL_SETS_COMPLETION_FOR_2004=!0,ALLOW_INTERACTION_NULL_LEARNER_RESPONSE=!0,PREVENT_STATUS_CHANGE_DURING_INIT=!1;function GetQueryStringValue(e,t){var r;return null===(r=SearchQueryStringPairs((t=t.substring(1)).split("&"),e))&&(r=SearchQueryStringPairs(t.split(/[\?\&]/),e)),null===r?(WriteToDebug("GetQueryStringValue Element '"+e+"' Not Found, Returning: empty string"),""):(WriteToDebug("GetQueryStringValue for '"+e+"' Returning: "+r),r)}function SearchQueryStringPairs(e,t){var r,n,o="";for(t=t.toLowerCase(),r=0;r<e.length;r++)if(-1!=(n=e[r].indexOf("="))&&EqualsIgnoreCase(e[r].substring(0,n),t))return o=e[r].substring(n+1),o=(o=new String(o)).replace(/\+/g,"%20"),o=unescape(o),new String(o);return null}function ConvertStringToBoolean(e){var t;return!(!EqualsIgnoreCase(e,"true")&&!EqualsIgnoreCase(e,"t")&&0!=e.toLowerCase().indexOf("t"))||(1==(t=parseInt(e,10))||-1==t)}function EqualsIgnoreCase(e,t){return e=new String(e),t=new String(t),e.toLowerCase()==t.toLowerCase()}function ValidInteger(e){WriteToDebug("In ValidInteger intNum="+e);var t=new String(e);0==t.indexOf("-",0)&&(t=t.substring(1,t.length-1));var r=new RegExp("[^0-9]");return-1==t.search(r)?(WriteToDebug("Returning true"),!0):(WriteToDebug("Returning false"),!1)}function ConvertDateToIso8601TimeStamp(e){var t,r=(e=new Date(e)).getFullYear(),n=e.getMonth()+1,o=e.getDate(),i=e.getHours(),a=e.getMinutes(),I=e.getSeconds();t=r+"-"+(n=ZeroPad(n,2))+"-"+(o=ZeroPad(o,2))+"T"+(i=ZeroPad(i,2))+":"+(a=ZeroPad(a,2))+":"+(I=ZeroPad(I,2));var C=-e.getTimezoneOffset()/60;if(0!=C)if(t+=".0",0<C)if(-1!=(""+C).indexOf(".")){var s="0"+(""+C).substr((""+C).indexOf("."),(""+C).length);t+="+"+ZeroPad((""+C).substr(0,(""+C).indexOf("."))+"."+(s*=60),2)}else t+="+"+ZeroPad(C,2);else t+=ZeroPad(C,2);return t}function ConvertIso8601TimeStampToDate(e){e=new String(e);var t=new Array,r=(t=e.split(/[\:T+-]/))[0],n=t[1]-1,o=t[2],i=t[3],a=t[4],I=t[5];return new Date(r,n,o,i,a,I,0)}function ConvertDateToCMIDate(e){var t,r,n;return WriteToDebug("In ConvertDateToCMIDate"),t=(e=new Date(e)).getFullYear(),r=e.getMonth()+1,n=e.getDate(),ZeroPad(t,4)+"/"+ZeroPad(r,2)+"/"+ZeroPad(n,2)}function ConvertDateToCMITime(e){var t,r,n;return t=(e=new Date(e)).getHours(),r=e.getMinutes(),n=e.getSeconds(),ZeroPad(t,2)+":"+ZeroPad(r,2)+":"+ZeroPad(n,2)}function ConvertCMITimeSpanToMS(e){var t,r,n,o,i;return WriteToDebug("In ConvertCMITimeSpanToMS, strTime="+e),t=e.split(":"),IsValidCMITimeSpan(e)?(WriteToDebug("intHours="+(r=t[0])+" intMinutes="+(n=t[1])+" intSeconds="+(o=t[2])),i=36e5*r+6e4*n+1e3*o,WriteToDebug("Returning "+(i=Math.round(i))),i):(WriteToDebug("ERROR - Invalid TimeSpan"),SetErrorInfo(SCORM_ERROR_GENERAL,"LMS ERROR - Invalid time span passed to ConvertCMITimeSpanToMS, please contact technical support"),0)}function ConvertScorm2004TimeToMS(e){WriteToDebug("In ConvertScorm2004TimeToMS, strIso8601Time="+e);var t,r,n,o=0,i=0,a=0,I=0,C=0,s=0,u=0;e=new String(e),r=t="",n=!1;for(var c=1;c<e.length;c++)if(IsIso8601SectionDelimiter(r=e.charAt(c))){switch(r.toUpperCase()){case"Y":u=parseInt(t,10);break;case"M":n?a=parseInt(t,10):s=parseInt(t,10);break;case"D":C=parseInt(t,10);break;case"H":I=parseInt(t,10);break;case"S":i=parseFloat(t);break;case"T":n=!0}t=""}else t+=""+r;return WriteToDebug("Years="+u+"\nMonths="+s+"\nDays="+C+"\nHours="+I+"\nMinutes="+a+"\nSeconds="+i+"\n"),o=315576e5*u+26298e5*s+864e5*C+36e5*I+6e4*a+1e3*i,WriteToDebug("returning-"+(o=Math.round(o))),o}function IsIso8601SectionDelimiter(e){return 0<=e.search(/[PYMDTHS]/)}function IsValidCMITimeSpan(e){WriteToDebug("In IsValidCMITimeSpan strValue="+e);return-1<e.search(/^\d?\d?\d?\d:\d?\d:\d?\d(.\d\d?)?$/)?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function IsValidIso8601TimeSpan(e){WriteToDebug("In IsValidIso8601TimeSpan strValue="+e);return-1<e.search(/^P(\d+Y)?(\d+M)?(\d+D)?(T(\d+H)?(\d+M)?(\d+(.\d\d?)?S)?)?$/)?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function ConvertMilliSecondsToTCAPITime(e,t){var r,n,o,i,a,I;return WriteToDebug("In ConvertMilliSecondsToTCAPITime, intTotalMilliseconds = "+e+", blnIncludeFraction = "+t),null!=t&&null!=t||(t=!0),WriteToDebug("Separated Parts, intHours="+(r=(e-(i=e%1e3)-1e3*(o=(e-i)/1e3%60)-6e4*(n=(e-i-1e3*o)/6e4%60))/36e5)+", intMinutes="+n+", intSeconds="+o+", intMilliseconds="+i),1e4==r&&(WriteToDebug("Max intHours detected"),100==(n=(e-36e5*(r=9999))/6e4)&&(n=99),100==(o=(e-36e5*r-6e4*(n=Math.floor(n)))/1e3)&&(o=99),WriteToDebug("Separated Parts, intHours="+r+", intMinutes="+n+", intSeconds="+(o=Math.floor(o))+", intMilliseconds="+(i=e-36e5*r-6e4*n-1e3*o))),a=Math.floor(i/10),I=ZeroPad(r,4)+":"+ZeroPad(n,2)+":"+ZeroPad(o,2),t&&(I+="."+a),WriteToDebug("strCMITimeSpan="+I),9999<r&&(I="9999:99:99",t&&(I+=".99")),WriteToDebug("returning "+I),I}function ConvertMilliSecondsToSCORMTime(e,t){var r,n,o,i,a,I;return WriteToDebug("In ConvertMilliSecondsToSCORMTime, intTotalMilliseconds = "+e+", blnIncludeFraction = "+t),null!=t&&null!=t||(t=!0),WriteToDebug("Separated Parts, intHours="+(r=(e-(i=e%1e3)-1e3*(o=(e-i)/1e3%60)-6e4*(n=(e-i-1e3*o)/6e4%60))/36e5)+", intMinutes="+n+", intSeconds="+o+", intMilliseconds="+i),1e4==r&&(WriteToDebug("Max intHours detected"),100==(n=(e-36e5*(r=9999))/6e4)&&(n=99),100==(o=(e-36e5*r-6e4*(n=Math.floor(n)))/1e3)&&(o=99),WriteToDebug("Separated Parts, intHours="+r+", intMinutes="+n+", intSeconds="+(o=Math.floor(o))+", intMilliseconds="+(i=e-36e5*r-6e4*n-1e3*o))),a=Math.floor(i/10),I=ZeroPad(r,4)+":"+ZeroPad(n,2)+":"+ZeroPad(o,2),t&&(I+="."+a),WriteToDebug("strCMITimeSpan="+I),9999<r&&(I="9999:99:99",t&&(I+=".99")),WriteToDebug("returning "+I),I}function ConvertMilliSecondsIntoSCORM2004Time(e){WriteToDebug("In ConvertMilliSecondsIntoSCORM2004Time intTotalMilliseconds="+e);var t,r,n,o,i,a,I,C="",s=26298e4;return t=Math.floor(e/10),t-=315576e4*(I=Math.floor(t/315576e4)),t-=(a=Math.floor(t/s))*s,t-=864e4*(i=Math.floor(t/864e4)),t-=36e4*(o=Math.floor(t/36e4)),t-=6e3*(n=Math.floor(t/6e3)),0<I&&(C+=I+"Y"),0<a&&(C+=a+"M"),0<i&&(C+=i+"D"),0<(t-=100*(r=Math.floor(t/100)))+r+n+o&&(C+="T",0<o&&(C+=o+"H"),0<n&&(C+=n+"M"),0<t+r&&(C+=r,0<t&&(C+="."+t),C+="S")),""==C&&(C="T0S"),WriteToDebug("Returning-"+(C="P"+C)),C}function ZeroPad(e,t){var r,n,o,i;WriteToDebug("In ZeroPad intNum="+e+" intNumDigits="+t);var a=!1;if(-1!=(r=new String(e)).indexOf("-")&&(a=!0,r=r.substr(1,r.length)),-1!=r.indexOf(".")&&(r.replace(".",""),o=r.substr(r.indexOf(".")+1,r.length),r=r.substr(0,r.indexOf("."))),t<(n=r.length))WriteToDebug("Length of string is greater than num digits, trimming string"),r=r.substr(0,t);else for(i=n;i<t;i++)r="0"+r;return 1==a&&(r="-"+r),null!=o&&""!=o&&(1==o.length?r+=":"+o+"0":r+=":"+o),WriteToDebug("Returning - "+r),r}function IsValidDecimalRange(e){WriteToDebug("In IsDecimalRange, strValue="+e);var t,r,n;return 2===(t=(e=new String(e)).split("[:]")).length?(r=Trim(t[0]),n=Trim(t[1]),0<r.length&&!IsValidDecimal(r)?(WriteToDebug("Returning False - min value supplied range is not a valid decimal, min="+r),!1):0<n.length&&!IsValidDecimal(n)?(WriteToDebug("Returning False - max value supplied for range is not a valid decimal, max="+n),!1):!(0<r.length&&0<n.length&&parseFloat(r)>parseFloat(n))||(WriteToDebug("Returning False - min value supplied for range is greater than the max, min="+r+", max="+n),!1)):(WriteToDebug("Returning false - string supplied for range has incorrect number of parts, parts="+t.length+", strValue="+e),!1)}function ConvertDecimalRangeToDecimalBasedOnLearnerResponse(e,t,r){WriteToDebug("In ConvertDecimalRangeToDecimalBasedOnLearnerResponse strValue="+e+",strLearnerResponse="+t+",blnCorrect="+r);var n,o,i;if(r)return WriteToDebug("Returning strLearnerResponse"),t;if(2===(n=(e=new String(e)).split("[:]")).length){if(o=Trim(n[0]),i=Trim(n[1]),0<o.length)return WriteToDebug("Returning strMin"),o;if(0<i.length)return WriteToDebug("Returning strMax"),i}return WriteToDebug("Returning null"),null}function IsValidDecimal(e){return WriteToDebug("In IsValidDecimal, strValue="+e),-1<(e=new String(e)).search(/[^.\d-]/)?(WriteToDebug("Returning False - character other than a digit, dash or period found"),!1):-1<e.search("-")&&-1<e.indexOf("-",1)?(WriteToDebug("Returning False - dash found in the middle of the string"),!1):e.indexOf(".")!=e.lastIndexOf(".")?(WriteToDebug("Returning False - more than one decimal point found"),!1):e.search(/\d/)<0?(WriteToDebug("Returning False - no digits found"),!1):(WriteToDebug("Returning True"),!0)}function IsAlphaNumeric(e){return WriteToDebug("In IsAlphaNumeric"),e.search(/\w/)<0?(WriteToDebug("Returning false"),!1):(WriteToDebug("Returning true"),!0)}function ReverseNameSequence(e){var t,r,n;return""==e&&(e="Not Found, Learner Name"),n=e.indexOf(","),t=e.slice(n+1),r=e.slice(0,n),(t=Trim(t))+" "+(r=Trim(r))}function LTrim(e){return(e=new String(e)).replace(/^\s+/,"")}function RTrim(e){return(e=new String(e)).replace(/\s+$/,"")}function Trim(e){return LTrim(RTrim(e)).replace(/\s{2,}/g," ")}function GetValueFromDataChunk(e){var t,r=new String(GetDataChunk()),n=new Array,o=new Array;for(n=r.split(parent.DATA_CHUNK_PAIR_SEPARATOR),t=0;t<n.length;t++)if((o=n[t].split(parent.DATA_CHUNK_VALUE_SEPARATOR))[0]==e)return o[1];return""}function SetDataChunkValue(e,t){var r,n=new String(GetDataChunk()),o=new Array,i=new Array,a=new Boolean(!1);for(o=n.split(parent.DATA_CHUNK_PAIR_SEPARATOR),r=0;r<o.length;r++)(i=o[r].split(parent.DATA_CHUNK_VALUE_SEPARATOR))[0]==e&&(i[1]=t,a=!0,o[r]=i[0]+parent.DATA_CHUNK_VALUE_SEPARATOR+i[1]);return 1==a?n=o.join(parent.DATA_CHUNK_PAIR_SEPARATOR):""==n?n=e+parent.DATA_CHUNK_VALUE_SEPARATOR+t:n+=parent.DATA_CHUNK_PAIR_SEPARATOR+e+parent.DATA_CHUNK_VALUE_SEPARATOR+t,SetDataChunk(n),!0}function GetLastDirAndPageName(e){var t=new String(e),r=t.lastIndexOf("/"),n=t.lastIndexOf("/",r-1);return t.substr(n+1)}function RoundToPrecision(e,t){return e=parseFloat(e),Math.round(e*Math.pow(10,t))/Math.pow(10,t)}function IsAbsoluteUrl(e){return null!=e&&(0==e.indexOf("http://")||0==e.indexOf("https://"))}function TouchCloud(){if(null==APPID||""==APPID||"__APPID__"==APPID||null===CLOUDURL||0!==CLOUDURL.indexOf("http"))return!1;var e=document.createElement("form");e.name="cloudform",e.id="cloudform",e.style="display:none;",document.body.appendChild(e);var t=document.createElement("input");t.name="appId",t.value=APPID,t.type="hidden",e.appendChild(t);var r=document.createElement("input");r.name="servingUrl",r.type="hidden",r.value=document.location.href,e.appendChild(r);var n=document.createElement("input");return n.name="version",n.type="hidden",n.value=VERSION,e.appendChild(n),e.target="rusticisoftware_aicc_results",e.action=CLOUDURL,document.getElementById("cloudform").submit(),!0}function IsNumeric(e){return!isNaN(parseFloat(e))&&isFinite(e)}function loadScript(e,t){var r=document.getElementsByTagName("head")[0],n=document.createElement("script");n.type="text/javascript",n.src=e,!n.addEventListener||document.documentMode&&document.documentMode<9?n.onreadystatechange=function(){/loaded|complete/.test(n.readyState)&&(n.onreadystatechange=null,t())}:n.addEventListener("load",t,!1),r.appendChild(n)}var STANDARD="AICC",blnDirtyAICCData=!1,blnCommitSavedData=!1,intAICCErrorNum=NO_ERROR,strAICCErrorDesc="",aryAICCFoundItems=new Array,blnUseLongInteractionResultValues=!0,blnReviewModeSoReadOnly=!1,AICC_LMS_Version="",AICC_Student_ID="",AICC_Student_Name="",AICC_Lesson_Location="",AICC_Score="",AICC_Credit="",AICC_Lesson_Status="",AICC_Time="",AICC_Mastery_Score="",AICC_Lesson_Mode="",AICC_Max_Time_Allowed="",AICC_Time_Limit_Action="",AICC_Audio="",AICC_Speed="",AICC_Language="",AICC_Text="",AICC_Launch_Data="",AICC_Data_Chunk="",AICC_Comments="",AICC_Objectives=null,AICC_CourseID="",AICC_fltScoreRaw="",AICC_fltScoreMax="",AICC_fltScoreMin="",AICC_blnCredit=!0,AICC_strLessonMode=MODE_NORMAL,AICC_intPreviouslyAccumulatedMilliseconds=0,AICC_intMaxTimeAllowedMilliseconds=MAX_CMI_TIME,AICC_blnExitOnTimeout=!1,AICC_blnShowMessageOnTimeout=!0,AICC_TextPreference=PREFERENCE_DEFAULT,AICC_Status=LESSON_STATUS_NOT_ATTEMPTED,AICC_Entry=AICC_ENTRY_FLAG_DEFAULT,AICC_AudioPlayPreference=PREFERENCE_DEFAULT,AICC_intAudioVolume=100,AICC_intPercentOfMaxSpeed=100,AICC_intSessionTimeMilliseconds=0,AICC_aryObjectivesRead=new Array,AICC_aryObjectivesWrite=new Array,AICC_aryCommentsFromLearner=new Array,AICC_aryInteractions=new Array,AICC_OBJ_ARRAY_ID=0,AICC_OBJ_ARRAY_SCORE=1,AICC_OBJ_ARRAY_STATUS=2,AICC_INTERACTIONS_ID=0,AICC_INTERACTIONS_RESPONSE=1,AICC_INTERACTIONS_CORRECT=2,AICC_INTERACTIONS_CORRECT_RESPONSE=3,AICC_INTERACTIONS_TIME_STAMP=4,AICC_INTERACTIONS_TYPE=5,AICC_INTERACTIONS_WEIGHTING=6,AICC_INTERACTIONS_LATENCY=7,AICC_INTERACTIONS_RESPONSE_LONG=8,AICC_INTERACTIONS_CORRECT_RESPONSE_LONG=9,AICC_INTERACTION_TYPE_TRUE_FALSE="T",AICC_INTERACTION_TYPE_CHOICE="C",AICC_INTERACTION_TYPE_FILL_IN="F",AICC_INTERACTION_TYPE_MATCHING="M",AICC_INTERACTION_TYPE_PERFORMANCE="P",AICC_INTERACTION_TYPE_SEQUENCING="S",AICC_INTERACTION_TYPE_LIKERT="L",AICC_INTERACTION_TYPE_NUMERIC="N",AICC_RESULT_CORRECT="C",AICC_RESULT_WRONG="W",AICC_RESULT_UNANTICIPATED="U",AICC_RESULT_NEUTRAL="N",AICC_NO_ERROR="0",AICC_ERROR_INVALID_PREFERENCE="-1",AICC_ERROR_INVALID_STATUS="-2",AICC_ERROR_INVALID_SPEED="-3",AICC_ERROR_INVALID_TIMESPAN="-4",AICC_ERROR_INVALID_TIME_LIMIT_ACTION="-5",AICC_ERROR_INVALID_DECIMAL="-6",AICC_ERROR_INVALID_CREDIT="-7",AICC_ERROR_INVALID_LESSON_MODE="-8",AICC_ERROR_INVALID_ENTRY="-9",blnReviewModeSoReadOnly=!1;function AICC_Initialize(){WriteToDebug("In AICC_Initialize"),window.AICCComm.MakeGetParamRequest()}function AICC_InitializeExecuted(){WriteToDebug("In AICC_InitializeExecuted"),AICC_GetLessonMode()!=MODE_REVIEW?AICC_GetStatus()==LESSON_STATUS_NOT_ATTEMPTED&&(WriteToDebug("Setting Status to Incomplete"),AICC_Status=LESSON_STATUS_INCOMPLETE):void 0!==REVIEW_MODE_IS_READ_ONLY&&!0===REVIEW_MODE_IS_READ_ONLY&&(blnReviewModeSoReadOnly=!0)}function AICC_Finish(e,t){return WriteToDebug("In AICC_Finish, strExitType="+e+", blnStatusWasSet="+t),t||(AICC_Status=e==EXIT_TYPE_FINISH?(WriteToDebug("Setting status to complete"),LESSON_STATUS_COMPLETED):(WriteToDebug("Setting status to incomplete"),LESSON_STATUS_INCOMPLETE)),AICC_CommitData(),1==blnCommitSavedData&&KillTime(),window.AICCComm.MakeExitAURequest(),!0}function AICC_CommitData(){var e;return WriteToDebug("In AICC_CommitData"),!0===blnReviewModeSoReadOnly?WriteToDebug("Mode is Review and configuration setting dictates this should be read only so exiting."):(blnCommitSavedData=!1,IsThereDirtyAICCData()&&(blnCommitSavedData=!0,WriteToDebug("Found Dirty Data"),e=FormAICCPostData(),window.AICCComm.MakePutParamRequest(e),0<AICC_aryInteractions.length&&(WriteToDebug("Saving Interactions"),KillTime(),AICC_SendInteractions()),ClearDirtyAICCData())),!0}function KillTime(){if(WriteToDebug("In KillTime"),!1!==USE_AICC_KILL_TIME){var e=new Date;if(0==window.AICCComm.blnCanUseXMLHTTP)if(1==window.AICCComm.blnXMLHTTPIsAvailable)for(var t=0;t<3;t++)window.AICCComm.GetBlankHtmlPage(t);else{window.NothingFrame.document.open();for(t=0;t<1e3;t++)window.NothingFrame.document.write("waiting");window.NothingFrame.document.close()}WriteToDebug("Killed "+(new Date-e)+"milliseconds.")}else WriteToDebug("Configuration disallows use of KillTime, exiting")}function AICC_SendInteractions(){if(WriteToDebug("In AICC_SendInteractions."),!0===blnReviewModeSoReadOnly)return WriteToDebug("Mode is Review and configuration setting dictates this should be read only so exiting."),!0;var e=FormAICCInteractionsData();window.AICCComm.MakePutInteractionsRequest(e),AICC_aryInteractions=new Array}function AICC_GetStudentID(){return WriteToDebug("In AICC_GetStudentID, Returning "+AICC_Student_ID),AICC_Student_ID}function AICC_GetStudentName(){return WriteToDebug("In AICC_GetStudentName, Returning "+AICC_Student_Name),AICC_Student_Name}function AICC_GetBookmark(){return WriteToDebug("In AICC_GetBookmark, Returning "+AICC_Lesson_Location),AICC_Lesson_Location}function AICC_SetBookmark(e){return WriteToDebug("In AICC_SetBookmark, strBookmark="+e),SetDirtyAICCData(),AICC_Lesson_Location=e,!0}function AICC_GetDataChunk(){return WriteToDebug("In AICC_GetDataChunk, Returning "+AICC_Data_Chunk),AICC_Data_Chunk}function AICC_SetDataChunk(e){return WriteToDebug("In AICC_SetDataChunk, strData="+e),1==USE_STRICT_SUSPEND_DATA_LIMITS&&4096<e.length?(WriteToDebug("SCORM_SetDataChunk - suspend_data too large (4096 character limit for AICC)"),!1):(SetDirtyAICCData(),AICC_Data_Chunk=e,!0)}function AICC_GetLaunchData(){return WriteToDebug("In AICC_GetLaunchData, Returning "+AICC_Launch_Data),AICC_Launch_Data}function AICC_GetComments(){return WriteToDebug("In AICC_GetComments, Returning "+AICC_aryCommentsFromLearner.join(" | ")),AICC_aryCommentsFromLearner.join(" | ")}function AICC_WriteComment(e){var t;return WriteToDebug("In AICC_WriteComment, strComment="+e),0==e.search(/ \| /)&&(e=e.substr(3)),e.replace(/\|\|/g,"|"),t=AICC_aryCommentsFromLearner.length,WriteToDebug("Adding comment to array"),AICC_aryCommentsFromLearner[t]=e,SetDirtyAICCData(),!0}function AICC_GetLMSComments(){return WriteToDebug("In AICC_GetLMSComments, Returning "+AICC_Comments),AICC_Comments}function AICC_GetAudioPlayPreference(){return WriteToDebug("In AICC_GetAudioPlayPreference, Returning "+AICC_AudioPlayPreference),AICC_AudioPlayPreference}function AICC_GetAudioVolumePreference(){return WriteToDebug("In AICC_GetAudioVolumePreference, Returning "+AICC_intAudioVolume),AICC_intAudioVolume}function AICC_SetAudioPreference(e,t){return WriteToDebug("In AICC_SetAudioPreference, Returning true"),AICC_AudioPlayPreference=e,AICC_intAudioVolume=t,SetDirtyAICCData(),!0}function AICC_SetLanguagePreference(e){return WriteToDebug("In AICC_SetLanguagePreference, Returning true"),SetDirtyAICCData(),AICC_Language=e,!0}function AICC_GetLanguagePreference(){return WriteToDebug("In AICC_GetLanguagePreference, Returning "+AICC_Language),AICC_Language}function AICC_SetSpeedPreference(e){return WriteToDebug("In AICC_SetSpeedPreference, Returning true"),AICC_intPercentOfMaxSpeed=e,SetDirtyAICCData(),!0}function AICC_GetSpeedPreference(){return WriteToDebug("In AICC_GetSpeedPreference, Returning "+AICC_intPercentOfMaxSpeed),AICC_intPercentOfMaxSpeed}function AICC_SetTextPreference(e){return WriteToDebug("In AICC_SetTextPreference, Returning true"),AICC_TextPreference=e,SetDirtyAICCData(),!0}function AICC_GetTextPreference(){return WriteToDebug("In AICC_GetTextPreference, Returning "+AICC_TextPreference),AICC_TextPreference}function AICC_GetPreviouslyAccumulatedTime(){return WriteToDebug("In AICC_GetPreviouslyAccumulatedTime, Returning "+AICC_intPreviouslyAccumulatedMilliseconds),AICC_intPreviouslyAccumulatedMilliseconds}function AICC_SaveTime(e){return WriteToDebug("In intMilliSeconds, Returning true"),AICC_intSessionTimeMilliseconds=e,SetDirtyAICCData(),!0}function AICC_GetMaxTimeAllowed(){return WriteToDebug("In AICC_GetMaxTimeAllowed, Returning "+AICC_intMaxTimeAllowedMilliseconds),AICC_intMaxTimeAllowedMilliseconds}function AICC_DisplayMessageOnTimeout(){return WriteToDebug("In AICC_DisplayMessageOnTimeout, Returning "+AICC_blnShowMessageOnTimeout),AICC_blnShowMessageOnTimeout}function AICC_ExitOnTimeout(){return WriteToDebug("In AICC_ExitOnTimeout, Returning "+AICC_blnExitOnTimeout),AICC_blnExitOnTimeout}function AICC_GetPassingScore(){return WriteToDebug("In AICC_GetPassingScore, Returning "+AICC_Mastery_Score),AICC_Mastery_Score}function AICC_GetScore(){return WriteToDebug("In AICC_GetScore, Returning "+AICC_fltScoreRaw),AICC_fltScoreRaw}function AICC_SetScore(e,t,r){return WriteToDebug("In AICC_SetScore, fltScore="+e+", fltMaxScore="+t+", fltMinScore="+r),AICC_fltScoreRaw=e,AICC_fltScoreMax=t,AICC_fltScoreMin=r,SetDirtyAICCData(),!0}function AICC_RecordTrueFalseInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordTrueFalseInteraction strID="+e+", blnResponse="+t+", blnCorrect="+r+", blnCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r="");var c="",S="";return null!==t&&(c=t?"t":"f"),1==n?S="t":0==n&&(S="f"),u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=c,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=S,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_TRUE_FALSE,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=c,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=S,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordMultipleChoiceInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordMultipleChoiceInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r="");var c="",S="",l="",_="";if(null!==t)for(var T=0;T<t.length;T++)0<c.length&&(c+=","),0<S.length&&(S+=","),c+=t[T].Short.replace(",",""),S+=t[T].Long.replace(",","");for(T=0;T<n.length;T++)0<l.length&&(l+=","),0<_.length&&(_+=","),l+=n[T].Short.replace(",",""),_+=n[T].Long.replace(",","");return u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=c,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=l,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_CHOICE,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=S,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=_,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordFillInInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordFillInInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);return s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r=""),null===t&&(t=""),null!=n&&null!=n||(n=""),255<(t=new String(t)).length&&(t=t.substr(0,255)),255<(n=new String(n)).length&&(n=n.substr(0,255)),u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=t,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=n,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_FILL_IN,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=t,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=n,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordMatchingInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordMatchingInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r="");var c="",S="",l="",_="";if(null!==t)for(var T=0;T<t.length;T++)0<c.length&&(c+=","),0<S.length&&(S+=","),c+=t[T].Source.Short.replace(",","").replace(".","")+"."+t[T].Target.Short.replace(",","").replace(".",""),S+=t[T].Source.Long.replace(",","").replace(".","")+"."+t[T].Target.Long.replace(",","").replace(".","");for(T=0;T<n.length;T++)0<l.length&&(l+=","),0<_.length&&(_+=","),""!=n[T].Source.Short&&""!=n[T].Source.Long&&(l+=n[T].Source.Short.replace(",","").replace(".","")+"."+n[T].Target.Short.replace(",","").replace(".",""),_+=n[T].Source.Long.replace(",","").replace(".","")+"."+n[T].Target.Long.replace(",","").replace(".",""));return u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=c,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=l,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_MATCHING,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=S,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=_,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordPerformanceInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordPerformanceInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);return s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r=""),null===t&&(t=""),null!=n&&null!=n||(n=""),255<(t=new String(t)).length&&(t=t.substr(0,255)),255<(n=new String(n)).length&&(n=n.substr(0,255)),u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=t,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=n,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_PERFORMANCE,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=t,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=n,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordSequencingInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordSequencingInteraction strID="+e+", aryResponse="+t+", blnCorrect="+r+", aryCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r="");var c="",S="",l="",_="";if(null!==t)for(var T=0;T<t.length;T++)0<c.length&&(c+=","),0<S.length&&(S+=","),c+=t[T].Short.replace(",",""),S+=t[T].Long.replace(",","");for(T=0;T<n.length;T++)0<l.length&&(l+=","),0<_.length&&(_+=","),l+=n[T].Short.replace(",",""),_+=n[T].Long.replace(",","");return u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=c,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=l,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_SEQUENCING,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=S,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=_,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordLikertInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In RecordLikertInteraction strID="+e+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r="");var c="",S="";null!==t&&(c=t.Short,S=t.Long);var l="",_="";return null!=n&&(l=n.Short,_=n.Long),u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=c,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=l,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_LIKERT,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=S,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=_,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_RecordNumericInteraction(e,t,r,n,o,i,a,I,C){var s;WriteToDebug("In AICC_RecordNumericInteraction strID="+e+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+I+", dtmTime="+C);var u=new Array(10);if(s=AICC_aryInteractions.length,null!=i&&null!=i||(i=""),null!=a&&null!=a||(a=""),null!=r&&null!=r||(r=""),null===t&&(t=""),null!=n&&null!=n){if(IsValidDecimalRange(n)&&(n=ConvertDecimalRangeToDecimalBasedOnLearnerResponse(n,t,r)),!IsValidDecimal(n))return WriteToDebug("Returning False - AICC_RecordNumericInteraction received invalid correct response (not a decimal), strCorrectResponse="+n),!1}else n="";return u[AICC_INTERACTIONS_ID]=e,u[AICC_INTERACTIONS_RESPONSE]=t,u[AICC_INTERACTIONS_CORRECT]=r,u[AICC_INTERACTIONS_CORRECT_RESPONSE]=n,u[AICC_INTERACTIONS_TIME_STAMP]=C,u[AICC_INTERACTIONS_TYPE]=AICC_INTERACTION_TYPE_NUMERIC,u[AICC_INTERACTIONS_WEIGHTING]=i,u[AICC_INTERACTIONS_LATENCY]=a,u[AICC_INTERACTIONS_RESPONSE_LONG]=t,u[AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]=n,AICC_aryInteractions[s]=u,WriteToDebug("Added to interactions array, index="+s),SetDirtyAICCData(),!0}function AICC_GetEntryMode(){return WriteToDebug("In AICC_GetEntryMode, Returning "+AICC_Entry),AICC_Entry}function AICC_GetLessonMode(){return WriteToDebug("In AICC_GetLessonMode, Returning "+AICC_strLessonMode),AICC_strLessonMode}function AICC_GetTakingForCredit(){return WriteToDebug("In AICC_GetTakingForCredit, Returning "+AICC_blnCredit),AICC_blnCredit}function AICC_SetObjectiveScore(e,t,r,n){var o,i;WriteToDebug("In AICC_SetObjectiveScore, strObjectiveID="+e+", intScore="+t+", intMaxScore="+r+", intMinScore="+n);var a="";return null!=(i=FindObjectiveById(e,AICC_aryObjectivesRead))?(WriteToDebug("Found read objective"),AICC_aryObjectivesRead[i][AICC_OBJ_ARRAY_SCORE]=t):(WriteToDebug("Adding new read objective"),o=AICC_aryObjectivesRead.length,AICC_aryObjectivesRead[parseInt(o,10)]=new Array(3),AICC_aryObjectivesRead[parseInt(o,10)][AICC_OBJ_ARRAY_ID]=e,AICC_aryObjectivesRead[parseInt(o,10)][AICC_OBJ_ARRAY_SCORE]=t,AICC_aryObjectivesRead[parseInt(o,10)][AICC_OBJ_ARRAY_STATUS]=""),null!=(i=FindObjectiveById(e,AICC_aryObjectivesWrite))?(WriteToDebug("Found write objective"),AICC_aryObjectivesWrite[i][AICC_OBJ_ARRAY_SCORE]=t):(WriteToDebug("Adding new write objective"),o=AICC_aryObjectivesWrite.length,AICC_aryObjectivesWrite[parseInt(o,10)]=new Array(3),a=t,AICC_LMS_Version<3&&""!=a&&(a=parseInt(a,10)),(null==AICC_REPORT_MIN_MAX_SCORE||!0===AICC_REPORT_MIN_MAX_SCORE)&&3<=AICC_LMS_Version&&(""==r&&""==n||(WriteToDebug("Appending Max and Min scores"),a+=","+r+","+n)),AICC_aryObjectivesWrite[parseInt(o,10)][AICC_OBJ_ARRAY_ID]=e,AICC_aryObjectivesWrite[parseInt(o,10)][AICC_OBJ_ARRAY_SCORE]=a,AICC_aryObjectivesWrite[parseInt(o,10)][AICC_OBJ_ARRAY_STATUS]=""),SetDirtyAICCData(),!0}function AICC_SetObjectiveStatus(e,t){var r,n;return WriteToDebug("In AICC_SetObjectiveStatus, strObjectiveID="+e+", Lesson_Status="+t),null!=(n=FindObjectiveById(e,AICC_aryObjectivesRead))?(WriteToDebug("Found read objective"),AICC_aryObjectivesRead[n][AICC_OBJ_ARRAY_STATUS]=t):(WriteToDebug("Adding new read objective"),r=AICC_aryObjectivesRead.length,AICC_aryObjectivesRead[parseInt(r,10)]=new Array(3),AICC_aryObjectivesRead[parseInt(r,10)][AICC_OBJ_ARRAY_ID]=e,AICC_aryObjectivesRead[parseInt(r,10)][AICC_OBJ_ARRAY_STATUS]=t,AICC_aryObjectivesRead[parseInt(r,10)][AICC_OBJ_ARRAY_SCORE]=""),null!=(n=FindObjectiveById(e,AICC_aryObjectivesWrite))?(WriteToDebug("Found write objective"),AICC_aryObjectivesWrite[n][AICC_OBJ_ARRAY_STATUS]=t):(WriteToDebug("Adding new write objective"),r=AICC_aryObjectivesWrite.length,AICC_aryObjectivesWrite[parseInt(r,10)]=new Array(3),AICC_aryObjectivesWrite[parseInt(r,10)][AICC_OBJ_ARRAY_ID]=e,AICC_aryObjectivesWrite[parseInt(r,10)][AICC_OBJ_ARRAY_STATUS]=t,AICC_aryObjectivesWrite[parseInt(r,10)][AICC_OBJ_ARRAY_SCORE]=""),SetDirtyAICCData(),!0}function AICC_SetObjectiveDescription(e,t){return WriteToDebug("In AICC_SetObjectiveDescription, strObjectiveID="+e+", strObjectiveDescription="+t),WriteToDebug("Objective descriptions are not supported prior to SCORM 2004"),!0}function AICC_GetObjectiveScore(e){WriteToDebug("In AICC_SetObjectiveScore, strObjectiveID="+e);var t=FindObjectiveById(e,AICC_aryObjectivesRead);return null!=t?(WriteToDebug("Found objective, returning "+AICC_aryObjectivesRead[t][AICC_OBJ_ARRAY_SCORE]),AICC_aryObjectivesRead[t][AICC_OBJ_ARRAY_SCORE]):(WriteToDebug("Did not find objective, returning ''"),"")}function AICC_GetObjectiveDescription(e){return WriteToDebug("In AICC_GetObjectiveDescription, strObjectiveID="+e),WriteToDebug("Objective descriptions are not supported prior to SCORM 2004"),""}function AICC_GetObjectiveStatus(e){WriteToDebug("In AICC_SetObjectiveStatus, strObjectiveID="+e);var t=FindObjectiveById(e,AICC_aryObjectivesRead);return null!=t?(WriteToDebug("Found objective, returning "+AICC_aryObjectivesRead[t][AICC_OBJ_ARRAY_STATUS]),AICC_aryObjectivesRead[t][AICC_OBJ_ARRAY_STATUS]):(WriteToDebug("Did not find objective, returning "+LESSON_STATUS_NOT_ATTEMPTED),LESSON_STATUS_NOT_ATTEMPTED)}function AICC_SetFailed(){return WriteToDebug("In AICC_SetFailed, Returning true"),AICC_Status=LESSON_STATUS_FAILED,SetDirtyAICCData(),!0}function AICC_SetPassed(){return WriteToDebug("In AICC_SetPassed, Returning true"),AICC_Status=LESSON_STATUS_PASSED,SetDirtyAICCData(),!0}function AICC_SetCompleted(){return WriteToDebug("In AICC_SetCompleted, Returning true"),AICC_Status=LESSON_STATUS_COMPLETED,SetDirtyAICCData(),!0}function AICC_ResetStatus(){return WriteToDebug("In AICC_ResetStatus, Returning true"),AICC_Status=LESSON_STATUS_INCOMPLETE,SetDirtyAICCData(),!0}function AICC_GetStatus(){return WriteToDebug("In AICC_GetStatus, Returning "+AICC_Status),AICC_Status}function AICC_GetProgressMeasure(){return WriteToDebug("AICC_GetProgressMeasure - AICC does not support progress_measure, returning false"),!1}function AICC_SetProgressMeasure(){return WriteToDebug("AICC_SetProgressMeasure - AICC does not support progress_measure, returning false"),!1}function AICC_GetObjectiveProgressMeasure(){return WriteToDebug("AICC_GetObjectiveProgressMeasure - AICC does not support progress_measure, returning false"),!1}function AICC_SetObjectiveProgressMeasure(){return WriteToDebug("AICC_SetObjectiveProgressMeasure - AICC does not support progress_measure, returning false"),!1}function AICC_SetPointBasedScore(e,t,r){return WriteToDebug("AICC_SetPointBasedScore - AICC does not support SetPointBasedScore, falling back to SetScore"),AICC_SetScore(e,t,r)}function AICC_GetScaledScore(e,t,r){return WriteToDebug("AICC_GetScaledScore - AICC does not support GetScaledScore, returning false"),!1}function AICC_GetLastError(){return WriteToDebug("In AICC_GetLastError, Returning "+intAICCErrorNum),intAICCErrorNum}function AICC_GetLastErrorDesc(){return WriteToDebug("In AICC_GetLastErrorDesc, Returning '"+strAICCErrorDesc+"'"),strAICCErrorDesc}function AICC_PutParamFailed(){WriteToDebug("ERROR: In AICC_PutParamFailed"),SetDirtyAICCData()}function AICC_PutInteractionsFailed(){WriteToDebug("ERROR: In AICC_PutInteractionsFailed"),SetDirtyAICCData(),1==parent.blnUseLongInteractionResultValues&&(parent.blnUseLongInteractionResultValues=!1,parent.AICC_CommitData())}function AICC_SetErrorInfo(e,t){WriteToDebug("ERROR: In AICC_SetErrorInfo, strErrorNumLine="+e+", strErrorDescLine="+t),-1==e.toLowerCase().search(/error\s*=\s*0/)?(WriteToDebug("Detected No Error"),intAICCErrorNum=NO_ERROR,strAICCErrorDesc=""):(WriteToDebug("Setting Error Info"),AICC_SetError(GetValueFromAICCLine(strAICCErrorLine),GetValueFromAICCLine(strAICCErrorDesc)))}function AICC_SetError(e,t){WriteToDebug("ERROR: In AICC_SetError, intErrorNum="+e+", strErrorDesc="+t),intAICCErrorNum=e,strAICCErrorDesc=strAICCErrorDesc}function SetDirtyAICCData(){WriteToDebug("In SetDirtyAICCData"),blnDirtyAICCData=!0}function ClearDirtyAICCData(){WriteToDebug("In ClearDirtyAICCData"),blnDirtyAICCData=!1}function IsThereDirtyAICCData(){return WriteToDebug("In IsThereDirtyAICCData, returning "+blnDirtyAICCData),blnDirtyAICCData}function GetValueFromAICCLine(e){var t;WriteToDebug("In GetValueFromAICCLine, strLine="+e);var r,n="";return WriteToDebug("intPos="+(t=(e=new String(e)).indexOf("="))),-1<t&&t+1<e.length&&(WriteToDebug("Grabbing value"),WriteToDebug("strTemp="+(r=e.substring(t+1))),n=r=(r=r.replace(/^\s*/,"")).replace(/\s*$/,"")),WriteToDebug("returning "+n),n}function GetNameFromAICCLine(e){var t,r;WriteToDebug("In GetNameFromAICCLine, strLine="+e);var n="";return WriteToDebug("intPos="+(t=(e=new String(e)).indexOf("="))),-1<t&&t<e.length?(WriteToDebug("Grabbing name from name/value pair"),WriteToDebug("strTemp="+(r=e.substring(0,t))),n=r=(r=r.replace(/^\s*/,"")).replace(/\s*$/,"")):(WriteToDebug("Grabbing name from group / section heading"),WriteToDebug("intPos="+(t=e.indexOf("["))),-1<t&&(WriteToDebug("Replacing []"),WriteToDebug("strTemp="+(r=e.replace(/[\[|\]]/g,""))),n=r=(r=r.replace(/^\s*/,"")).replace(/\s*$/,""))),WriteToDebug("returning "+n),n}function GetIndexFromAICCName(e){var t;WriteToDebug("In GetIndexFromAICCName, strLineName="+e);var r="",n="";return strLine=new String(e),WriteToDebug("intPos="+(t=strLine.indexOf("."))),-1<t&&t+1<strLine.length&&(WriteToDebug("Grabbing index"),WriteToDebug("strTemp="+(n=strLine.substring(t+1))),WriteToDebug("Checking for equal sign"),-1<(t=n.indexOf("="))&&t<n.length&&(WriteToDebug("Found and removing equal sign"),n=strLine.substring(0,t)),WriteToDebug("Removing white space"),r=n=(n=n.replace(/^\s*/,"")).replace(/\s*$/,"")),WriteToDebug("returning "+r),r}function ParseGetParamData(e){var t,r,n,o,i,a;for(WriteToDebug("In ParseGetParamData"),t=(e=new String(e)).split("\n"),WriteToDebug("Split String"),i=0;i<t.length;i++)if(WriteToDebug("Processing Line #"+i+": "+t[i]),o=n="",0<(r=t[i]).length&&(WriteToDebug("Found non-zero length string"),"\r"==r.charAt(0)&&(WriteToDebug("Detected leading \\r"),r=r.substr(1)),"\r"==r.charAt(r.length-1)&&(WriteToDebug("Detected trailing \\r"),r=r.substr(0,r.length-1)),";"!=r.charAt(0)&&(WriteToDebug("Found non-comment line"),WriteToDebug("strLineName="+(n=GetNameFromAICCLine(r))+", strLineValue="+(o=GetValueFromAICCLine(r))))),!AICC_HasItemBeenFound(n=n.toLowerCase()))switch(WriteToDebug("Detected an un-found item"),AICC_FoundItem(n),n){case"version":WriteToDebug("Item is version");var I=parseFloat(o);isNaN(I)&&(I=0),AICC_LMS_Version=I;break;case"student_id":WriteToDebug("Item is student_id"),AICC_Student_ID=o;break;case"student_name":WriteToDebug("Item is student_name"),AICC_Student_Name=o;break;case"lesson_location":WriteToDebug("Item is lesson_location"),AICC_Lesson_Location=o;break;case"score":WriteToDebug("Item is score"),AICC_SeperateScoreValues(AICC_Score=o);break;case"credit":WriteToDebug("Item is credit"),AICC_TranslateCredit(AICC_Credit=o);break;case"lesson_status":WriteToDebug("Item is lesson_status"),AICC_TranslateLessonStatus(AICC_Lesson_Status=o);break;case"time":WriteToDebug("Item is time"),AICC_TranslateTimeToMilliseconds(AICC_Time=o);break;case"mastery_score":WriteToDebug("Item is mastery_score"),AICC_ValidateMasteryScore(AICC_Mastery_Score=o);break;case"lesson_mode":WriteToDebug("Item is lesson_mode"),AICC_TranslateLessonMode(AICC_Lesson_Mode=o);break;case"max_time_allowed":WriteToDebug("Item is max_time_allowed"),AICC_TranslateMaxTimeToMilliseconds(AICC_Max_Time_Allowed=o);break;case"time_limit_action":WriteToDebug("Item is time_limit_action"),AICC_TranslateTimeLimitAction(AICC_Time_Limit_Action=o);break;case"audio":WriteToDebug("Item is audio"),AICC_TranslateAudio(AICC_Audio=o);break;case"speed":WriteToDebug("Item is speed"),AICC_TranslateSpeed(AICC_Speed=o);break;case"language":WriteToDebug("Item is language"),AICC_Language=o;break;case"text":WriteToDebug("Item is text"),AICC_TranslateTextPreference(AICC_Text=o);break;case"course_id":WriteToDebug("Item is course id"),AICC_CourseID=o;break;case"core_vendor":for(WriteToDebug("Item is core_vendor"),r=AICC_Launch_Data="",i+(a=1)<t.length&&(r=t[i+a]);i+a<t.length&&!IsGroupIdentifier(r);)";"!=r.charAt(0)&&(AICC_Launch_Data+=r+"\n"),i+(a+=1)<t.length&&(r=t[i+a]);i=i+a-1,AICC_Launch_Data=AICC_Launch_Data.replace(/\s*$/,"");break;case"core_lesson":for(WriteToDebug("Item is core_lesson"),r=AICC_Data_Chunk="",i+(a=1)<t.length&&(r=t[i+a]);i+a<t.length&&!IsGroupIdentifier(r);)";"!=r.charAt(0)&&(AICC_Data_Chunk+=r+"\n"),i+(a+=1)<t.length&&(r=t[i+a]);i=i+a-1,AICC_Data_Chunk=AICC_Data_Chunk.replace(/\s*$/,"");break;case"comments":for(WriteToDebug("Item is comments"),r=AICC_Comments="",i+(a=1)<t.length&&(r=t[i+a]);i+a<t.length&&!IsGroupIdentifier(r);)";"!=r.charAt(0)&&(AICC_Comments+=r+"\n"),i+(a+=1)<t.length&&(r=t[i+a]);i=i+a-1,AICC_Comments=AICC_Comments.replace(/\s*$/,"");break;case"objectives_status":for(WriteToDebug("Item is objectives_status"),r=AICC_Objectives="",i+(a=1)<t.length&&(r=t[i+a]);i+a<t.length&&!IsGroupIdentifier(r);)";"!=r.charAt(0)&&(AICC_Objectives+=r+"\n"),i+(a+=1)<t.length&&(r=t[i+a]);i=i+a-1,AICC_FormatObjectives(AICC_Objectives=AICC_Objectives.replace(/\s*$/,""));break;default:WriteToDebug("Unknown Item Found")}return!0}function IsGroupIdentifier(e){var t;return WriteToDebug("In IsGroupIdentifier, strLine="+e),WriteToDebug("intPos="+(t=(e=e.replace(/^\s*/,"")).search(/\[[\w]+\]/))),0==t?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function AICC_FoundItem(e){WriteToDebug("In AICC_FoundItem, strItem="+e),aryAICCFoundItems[e]=!0}function AICC_HasItemBeenFound(e){return WriteToDebug("In AICC_HasItemBeenFound, strItem="+e),1==aryAICCFoundItems[e]?(WriteToDebug("Returning True"),!0):(WriteToDebug("Returning False"),!1)}function AICC_SeperateScoreValues(e){WriteToDebug("In AICC_SeperateScoreValues, AICC_Score="+e),aryScore=e.split(","),IsValidDecimal(AICC_fltScoreRaw=aryScore[0])?(WriteToDebug("Found a valid decimal"),AICC_fltScoreRaw=parseFloat(AICC_fltScoreRaw)):(WriteToDebug("ERROR - score from LMS is not a valid decimal"),AICC_SetError(AICC_ERROR_INVALID_DECIMAL,"score is not a valid decimal")),1<aryScore.length&&(WriteToDebug("Max score found"),IsValidDecimal(AICC_fltScoreMax=aryScore[1])?(WriteToDebug("Found a valid decimal"),AICC_fltScoreMax=parseFloat(AICC_fltScoreMax)):(WriteToDebug("ERROR - max score from LMS is not a valid decimal"),AICC_SetError(AICC_ERROR_INVALID_DECIMAL,"max score is not a valid decimal"))),2<aryScore.length&&(WriteToDebug("Max score found"),IsValidDecimal(AICC_fltScoreMin=aryScore[2])?(WriteToDebug("Found a valid decimal"),AICC_fltScoreMin=parseFloat(AICC_fltScoreMin)):(WriteToDebug("ERROR - min score from LMS is not a valid decimal"),AICC_SetError(AICC_ERROR_INVALID_DECIMAL,"min score is not a valid decimal")))}function AICC_ValidateMasteryScore(e){WriteToDebug("In AICC_ValidateMasteryScore, strScore="+e),IsValidDecimal(e)?AICC_Mastery_Score=parseFloat(e):(WriteToDebug("ERROR - mastery score from LMS is not a valid decimal"),AICC_SetError(AICC_ERROR_INVALID_DECIMAL,"mastery score is not a valid decimal"))}function AICC_TranslateCredit(e){var t;WriteToDebug("In AICC_TranslateCredit, strCredit="+e),"c"==(t=e.toLowerCase().charAt(0))?(WriteToDebug("Credit = true"),AICC_blnCredit=!0):"n"==t?(WriteToDebug("Credit = false"),AICC_blnCredit=!1):(WriteToDebug("ERROR - credit value from LMS is not a valid"),AICC_SetError(AICC_ERROR_INVALID_CREDIT,"credit value from LMS is not a valid"))}function AICC_TranslateLessonMode(e){var t;WriteToDebug("In AICC_TranslateLessonMode, strMode="+e),"b"==(t=e.toLowerCase().charAt(0))?(WriteToDebug("Lesson Mode = Browse"),AICC_strLessonMode=MODE_BROWSE):"n"==t?(WriteToDebug("Lesson Mode = normal"),AICC_strLessonMode=MODE_NORMAL):"r"==t?(WriteToDebug("Lesson Mode = review"),AICC_strLessonMode=MODE_REVIEW,void 0!==REVIEW_MODE_IS_READ_ONLY&&!0===REVIEW_MODE_IS_READ_ONLY&&(blnReviewModeSoReadOnly=!0)):(WriteToDebug("ERROR - lesson_mode value from LMS is not a valid"),AICC_SetError(AICC_ERROR_INVALID_LESSON_MODE,"lesson_mode value from LMS is not a valid"))}function AICC_TranslateTimeToMilliseconds(e){WriteToDebug("In AICC_TranslateTimeToMilliseconds, strCMITime="+e),IsValidCMITimeSpan(e)?AICC_intPreviouslyAccumulatedMilliseconds=ConvertCMITimeSpanToMS(e):(WriteToDebug("ERROR - Invalid CMITimeSpan"),AICC_SetError(AICC_ERROR_INVALID_TIMESPAN,"Invalid timespan (previously accumulated time) received from LMS"))}function AICC_TranslateMaxTimeToMilliseconds(e){WriteToDebug("In AICC_TranslateMaxTimeToMilliseconds, strCMITime="+e),IsValidCMITimeSpan(e)?AICC_intMaxTimeAllowedMilliseconds=ConvertCMITimeSpanToMS(e):(WriteToDebug("ERROR - Invalid CMITimeSpan"),AICC_SetError(AICC_ERROR_INVALID_TIMESPAN,"Invalid timespan (max time allowed) received from LMS"))}function AICC_TranslateTimeLimitAction(e){var t;WriteToDebug("In AICC_TranslateTimeLimitAction, strTimeLimitAction="+e);var r=!1,n="",o="";2==(t=e.split(",")).length?(WriteToDebug("Found 2 elements"),WriteToDebug("Got characters, strChar1="+(n=t[0].charAt(0).toLowerCase())+", strChar2="+(o=t[1].charAt(0).toLowerCase())),("e"!=n&&"c"!=n&&"m"!=n&&"n"!=n||"e"!=o&&"c"!=o&&"m"!=o&&"n"!=o||n==o)&&(r=!0,WriteToDebug("Found an invalid character, or 2 identical characters")),"e"!=n&&"e"!=o||(AICC_blnExitOnTimeout=!0),"c"!=n&&"c"!=o||(AICC_blnExitOnTimeout=!1),"n"!=n&&"n"!=o||(AICC_blnShowMessageOnTimeout=!1),"m"!=n&&"m"!=o||(AICC_blnShowMessageOnTimeout=!0),WriteToDebug("AICC_blnExitOnTimeout="+AICC_blnExitOnTimeout+", AICC_blnShowMessageOnTimeout"+AICC_blnShowMessageOnTimeout)):(WriteToDebug("Line does not contain two comma-delimited elements"),r=!0),r&&(WriteToDebug("ERROR - Invalid Time Limit Action"),AICC_SetError(AICC_ERROR_INVALID_TIME_LIMIT_ACTION,"Invalid time limit action received from LMS"))}function AICC_TranslateTextPreference(e){WriteToDebug("In AICC_TranslateTextPreference, strPreference="+e),-1==e?(WriteToDebug("Text Preference = off"),AICC_TextPreference=PREFERENCE_OFF):0==e?(WriteToDebug("Text Preference = default"),AICC_TextPreference=PREFERENCE_DEFAULT):1==e?(WriteToDebug("Text Preference = on"),AICC_TextPreference=PREFERENCE_ON):(WriteToDebug("ERROR - Invalid Text Preference"),AICC_SetError(AICC_ERROR_INVALID_PREFERENCE,"Invalid Text Preference received from LMS"))}function AICC_TranslateLessonStatus(e){var t,r;WriteToDebug("In AICC_TranslateLessonStatus, strStatus="+e),t=e.charAt(0).toLowerCase(),WriteToDebug("AICC_Status="+(AICC_Status=AICC_ConvertAICCStatusIntoLocalStatus(t))),0<(r=e.indexOf(","))&&("a"==(t=e.substr(r).replace(/,/,"").charAt(0).toLowerCase())?(WriteToDebug("Entry is Ab initio"),AICC_Entry=ENTRY_FIRST_TIME):"r"==t?(WriteToDebug("Entry is Resume"),AICC_Entry=ENTRY_RESUME):(WriteToDebug("ERROR - entry not found"),AICC_SetError(AICC_ERROR_INVALID_ENTRY,"Invalid lesson status received from LMS")))}function AICC_ConvertAICCStatusIntoLocalStatus(e){return WriteToDebug("In AICC_ConvertAICCStatusIntoLocalStatus, strFirstCharOfAICCStatus="+e),"p"==e?(WriteToDebug("Status is Passed"),LESSON_STATUS_PASSED):"f"==e?(WriteToDebug("Status is Failed"),LESSON_STATUS_FAILED):"c"==e?(WriteToDebug("Status is Completed"),LESSON_STATUS_COMPLETED):"b"==e?(WriteToDebug("Status is Browsed"),LESSON_STATUS_BROWSED):"i"==e?(WriteToDebug("Status is Incomplete"),LESSON_STATUS_INCOMPLETE):("n"==e?WriteToDebug("Status is Not Attempted"):(WriteToDebug("ERROR - status not found"),AICC_SetError(SCORM_ERROR_INVALID_STATUS,"Invalid status")),LESSON_STATUS_NOT_ATTEMPTED)}function AICC_TranslateAudio(e){WriteToDebug("In AICC_TranslateAudio, strAudio="+e);var t=parseInt(e,10);WriteToDebug("intTempPreference="+t),0<t&&t<=100?(WriteToDebug("Returning On"),AICC_AudioPlayPreference=PREFERENCE_ON,AICC_intAudioVolume=t):0==t?(WriteToDebug("Returning Default"),AICC_AudioPlayPreference=PREFERENCE_DEFAULT):t<0?(WriteToDebug("returning Off"),AICC_AudioPlayPreference=PREFERENCE_OFF):(WriteToDebug("Error: Invalid preference"),AICC_SetError(AICC_ERROR_INVALID_PREFERENCE,"Invalid audio preference received from LMS"))}function AICC_TranslateSpeed(e){var t;return WriteToDebug("In AICC_TranslateSpeed, intAICCSpeed="+e),ValidInteger(e)?(e=parseInt(e,10))<-100||100<e?(WriteToDebug("ERROR - out of range"),void AICC_SetError(AICC_ERROR_INVALID_SPEED,"Invalid speed preference received from LMS - out of range")):(t=((AICC_Speed=e)+100)/2,WriteToDebug("Returning "+(t=parseInt(t,10))),void(AICC_intPercentOfMaxSpeed=t)):(WriteToDebug("ERROR - invalid integer"),void AICC_SetError(AICC_ERROR_INVALID_SPEED,"Invalid speed preference received from LMS - not an integer"))}function AICC_FormatObjectives(e){var t,r,n,o,i,a;for(WriteToDebug("In AICC_FormatObjectives, strObjectivesFromLMS="+e),t=e.split("\n"),r=0;r<t.length;r++)WriteToDebug("Extracting Index From Line: "+t[r]),a=GetIndexFromAICCName(n=GetNameFromAICCLine(t[r])),WriteToDebug("strIndex: "+(a=parseInt(a,10))),AICC_aryObjectivesRead[parseInt(a,10)]=new Array(3);for(r=0;r<t.length;r++)WriteToDebug("Populating Line "+t[r]),n=GetNameFromAICCLine(t[r]),o=GetValueFromAICCLine(t[r]),a=a=GetIndexFromAICCName(n),WriteToDebug("strLineName: "+n),WriteToDebug("strLineValue: "+o),WriteToDebug("strIndex: "+a),"j_id"==(i=n.substr(0,4).toLowerCase())?(WriteToDebug("Found ID"),AICC_aryObjectivesRead[parseInt(a,10)][AICC_OBJ_ARRAY_ID]=o):"j_st"==i?(WriteToDebug("Found Status"),AICC_aryObjectivesRead[parseInt(a,10)][AICC_OBJ_ARRAY_STATUS]=AICC_ConvertAICCStatusIntoLocalStatus(o.charAt(0).toLowerCase())):"j_sc"==i?(WriteToDebug("Found Score"),AICC_aryObjectivesRead[parseInt(a,10)][AICC_OBJ_ARRAY_SCORE]=AICC_ExtractSingleScoreFromObjective(o)):WriteToDebug("WARNING - unidentified objective data found - "+t[r])}function AICC_ExtractSingleScoreFromObjective(e){var t;return WriteToDebug("In AICC_ExtractSingleScoreFromObjective, strLineValue="+e),WriteToDebug("returning "+(t=(t=e.split(";"))[0].split(","))[0]),t[0]}function FindObjectiveById(e,t){WriteToDebug("In FindObjectiveById, strID="+e);for(var r=0;r<=t.length;r++)if(WriteToDebug("Searching element "+r),t[r]&&(WriteToDebug("Element Exists"),t[r][AICC_OBJ_ARRAY_ID].toString()==e.toString()))return WriteToDebug("Element matches"),r;return null}function AICC_CreateValidIdentifier(e){return CreateValidIdentifierLegacy(e)}function AICC_FindInteractionIndexFromID(e){return WriteToDebug("AICC_FindInteractionIndexFromID - AICC does not support interaction retrieval, returning null"),null}function AICC_GetInteractionType(e){return WriteToDebug("AICC_GetInteractionType - AICC does not support interaction retrieval, returning empty string"),""}function AICC_GetInteractionTimestamp(e){return WriteToDebug("AICC_GetInteractionTimestamp - AICC does not support interaction retrieval, returning empty string"),""}function AICC_GetInteractionCorrectResponses(e){return WriteToDebug("AICC_GetInteractionCorrectResponses - AICC does not support interaction retrieval, returning empty array"),new Array}function AICC_GetInteractionWeighting(e){return WriteToDebug("AICC_GetInteractionWeighting - AICC does not support interaction retrieval, returning empty string"),""}function AICC_GetInteractionLearnerResponses(e){return WriteToDebug("AICC_GetInteractionLearnerResponses - AICC does not support interaction retrieval, returning empty array"),new Array}function AICC_GetInteractionResult(e){return WriteToDebug("AICC_GetInteractionResult - AICC does not support interaction retrieval, returning empty string"),""}function AICC_GetInteractionLatency(e){return WriteToDebug("AICC_GetInteractionDescription - AICC does not support interaction retrieval, returning empty string"),""}function AICC_GetInteractionDescription(e){return WriteToDebug("AICC_GetInteractionDescription - AICC does not support interaction retrieval, returning empty string"),""}function AICC_CreateDataBucket(e,t,r){return WriteToDebug("AICC_CreateDataBucket - AICC does not support SSP, returning false"),!1}function AICC_GetDataFromBucket(e){return WriteToDebug("AICC_GetDataFromBucket - AICC does not support SSP, returning empty string"),""}function AICC_PutDataInBucket(e,t,r){return WriteToDebug("AICC_PutDataInBucket - AICC does not support SSP, returning false"),!1}function AICC_DetectSSPSupport(){return WriteToDebug("AICC_DetectSSPSupport - AICC does not support SSP, returning false"),!1}function AICC_GetBucketInfo(e){return WriteToDebug("AICC_DetectSSPSupport - AICC does not support SSP, returning empty SSPBucketSize"),new SSPBucketSize(0,0)}function AICC_SetNavigationRequest(e){return WriteToDebug("AICC_SetNavigationRequest - AICC does not support navigation requests, returning false"),!1}function AICC_GetNavigationRequest(){return WriteToDebug("AICC_SetNavigationRequest - AICC does not support navigation requests, returning false"),!1}function FormAICCPostData(){WriteToDebug("In FormAICCPostData");var e="";return e+="[Core]\r\n",e+="Lesson_Location="+AICC_Lesson_Location+"\r\n",e+="Lesson_Status="+AICC_TranslateLessonStatusToAICC(AICC_Status)+"\r\n",e+="Score="+AICC_TranslateScoreToAICC()+"\r\n",e+="Time="+AICC_TranslateTimeToAICC()+"\r\n",e+="[Comments]\r\n"+AICC_TranslateCommentsToAICC()+"\r\n",e+="[Objectives_Status]\r\n"+AICC_TranslateObjectivesToAICC()+"\r\n",e+="[Student_Preferences]\r\n",e+="Audio="+AICC_TranslateAudioToAICC()+"\r\n",e+="Language="+AICC_Language+"\r\n",e+="Speed="+AICC_TranslateSpeedToAICC()+"\r\n",e+="Text="+AICC_TranslateTextToAICC()+"\r\n",e+="[Core_Lesson]\r\n",WriteToDebug("FormAICCPostData returning: "+(e+=AICC_Data_Chunk)),e}function AICC_TranslateLessonStatusToAICC(e){switch(WriteToDebug("In AICC_TranslateLessonStatusToAICC"),e){case LESSON_STATUS_PASSED:WriteToDebug("Status is passed"),AICC_Lesson_Status="P";break;case LESSON_STATUS_COMPLETED:WriteToDebug("Status is completed"),AICC_Lesson_Status="C";break;case LESSON_STATUS_FAILED:WriteToDebug("Status is failed"),AICC_Lesson_Status="F";break;case LESSON_STATUS_INCOMPLETE:WriteToDebug("Status is incomplete"),AICC_Lesson_Status="I";break;case LESSON_STATUS_BROWSED:WriteToDebug("Status is browsed"),AICC_Lesson_Status="B";break;case LESSON_STATUS_NOT_ATTEMPTED:WriteToDebug("Status is not attempted"),AICC_Lesson_Status="N"}return AICC_Lesson_Status}function AICC_TranslateScoreToAICC(){return WriteToDebug("In AICC_TranslateScoreToAICC"),AICC_Score=AICC_fltScoreRaw,AICC_LMS_Version<3&&""!=AICC_fltScoreRaw&&(AICC_Score=parseInt(AICC_Score,10)),(null==AICC_REPORT_MIN_MAX_SCORE||!0===AICC_REPORT_MIN_MAX_SCORE)&&3<=AICC_LMS_Version&&(WriteToDebug("Using max and min values if available."),""==AICC_fltScoreMax&&""==AICC_fltScoreMin||(WriteToDebug("Appending Max and Min scores"),AICC_Score+=","+AICC_fltScoreMax+","+AICC_fltScoreMin)),WriteToDebug("AICC_Score="+AICC_Score),AICC_Score}function AICC_TranslateTimeToAICC(){return WriteToDebug("In AICC_TranslateTimeToAICC"),ConvertMilliSecondsToSCORMTime(AICC_intSessionTimeMilliseconds,!1)}function AICC_TranslateCommentsToAICC(){WriteToDebug("In AICC_TranslateCommentsToAICC");for(var e="",t=0;t<AICC_aryCommentsFromLearner.length;t++)e+="<"+(t+1)+">"+AICC_aryCommentsFromLearner[t]+"<e."+(t+1)+">";return e}function AICC_TranslateObjectivesToAICC(){WriteToDebug("In AICC_TranslateObjectivesToAICC");for(var e="",t=0;t<AICC_aryObjectivesWrite.length;t++)WriteToDebug("Looking at index: "+t),AICC_aryObjectivesWrite[t]&&(WriteToDebug("Element "+t+" exists, id="+AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_ID]+", score="+AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_SCORE]+", status="+AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_STATUS]),e+="J_ID."+(t+1)+"="+AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_ID]+"\r\n",""!=AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_SCORE]&&(e+="J_Score."+(t+1)+"="+AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_SCORE]+"\r\n"),""!=AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_STATUS]&&(e+="J_Status."+(t+1)+"="+AICC_TranslateLessonStatusToAICC(AICC_aryObjectivesWrite[t][AICC_OBJ_ARRAY_STATUS])+"\r\n"));return e}function AICC_TranslateAudioToAICC(){var e;switch(WriteToDebug("In AICC_TranslateAudioToAICC"),AICC_AudioPlayPreference){case PREFERENCE_ON:WriteToDebug("Preference is ON"),e=AICC_intAudioVolume;break;case PREFERENCE_DEFAULT:WriteToDebug("Preference is DEFAULT"),e=0;break;case PREFERENCE_OFF:WriteToDebug("Preference is OFF"),e=-1}return e}function AICC_TranslateSpeedToAICC(){return WriteToDebug("In AICC_TranslateSpeedToAICC"),2*AICC_intPercentOfMaxSpeed-100}function AICC_TranslateTextToAICC(){WriteToDebug("In AICC_TranslateTextToAICC");var e=0;return AICC_TextPreference==PREFERENCE_OFF?e=-1:AICC_TextPreference==PREFERENCE_DEFAULT?e=0:AICC_TextPreference==PREFERENCE_ON&&(e=1),e}function FormAICCInteractionsData(){var e,t,r;WriteToDebug("In FormAICCInteractionsData");var n="";e='"course_id","student_id","lesson_id","date","time","interaction_id","objective_id","type_interaction","correct_response","student_response","result","weighting","latency"\r\n';for(var o="",i="",a="",I="",C=0;C<AICC_aryInteractions.length;C++){n="",1==(o=AICC_aryInteractions[C][AICC_INTERACTIONS_CORRECT])||o==INTERACTION_RESULT_CORRECT?n=AICC_RESULT_CORRECT:"false"==o||o==INTERACTION_RESULT_WRONG?n=AICC_RESULT_WRONG:o==INTERACTION_RESULT_UNANTICIPATED?n=AICC_RESULT_UNANTICIPATED:o==INTERACTION_RESULT_NEUTRAL&&(n=AICC_RESULT_NEUTRAL),t=ConvertDateToCMIDate(AICC_aryInteractions[C][AICC_INTERACTIONS_TIME_STAMP]),r=ConvertDateToCMITime(AICC_aryInteractions[C][AICC_INTERACTIONS_TIME_STAMP]),a=1==blnUseLongInteractionResultValues?(i=AICC_aryInteractions[C][AICC_INTERACTIONS_RESPONSE_LONG],AICC_aryInteractions[C][AICC_INTERACTIONS_CORRECT_RESPONSE_LONG]):(i=AICC_aryInteractions[C][AICC_INTERACTIONS_RESPONSE],AICC_aryInteractions[C][AICC_INTERACTIONS_CORRECT_RESPONSE]),i=new String(i),a=new String(a);var s=AICC_aryInteractions[C][AICC_INTERACTIONS_LATENCY];null!=s&&""!=s&&(I=ConvertMilliSecondsToSCORMTime(s,!1)),e+='"'+AICC_CourseID.replace('"',"")+'","'+AICC_Student_ID.replace('"',"")+'","'+AICC_LESSON_ID.replace('"',"")+'","'+t+'","'+r+'","'+AICC_aryInteractions[C][AICC_INTERACTIONS_ID].replace('"',"")+'","","'+AICC_aryInteractions[C][AICC_INTERACTIONS_TYPE]+'","'+a.replace('"',"")+'","'+i.replace('"',"")+'","'+n+'","'+AICC_aryInteractions[C][AICC_INTERACTIONS_WEIGHTING]+'","'+I+'"\r\n'}return e}function DisplayAICCVariables(){var e="";e+="AICC_Student_ID = "+AICC_Student_ID+"\n",e+="AICC_Student_Name = "+AICC_Student_Name+"\n",e+="AICC_Lesson_Location = "+AICC_Lesson_Location+"\n",e+="AICC_Score = "+AICC_Score+"\n",e+="AICC_Credit = "+AICC_Credit+"\n",e+="AICC_Lesson_Status = "+AICC_Lesson_Status+"\n",e+="AICC_Time = "+AICC_Time+"\n",e+="AICC_Mastery_Score = "+AICC_Mastery_Score+"\n",e+="AICC_Lesson_Mode = "+AICC_Lesson_Mode+"\n",e+="AICC_Max_Time_Allowed = "+AICC_Max_Time_Allowed+"\n",e+="AICC_Time_Limit_Action = "+AICC_Time_Limit_Action+"\n",e+="AICC_Audio = "+AICC_Audio+"\n",e+="AICC_Speed = "+AICC_Speed+"\n",e+="AICC_Language = "+AICC_Language+"\n",e+="AICC_Text = "+AICC_Text+"\n",e+="AICC_Launch_Data = "+AICC_Launch_Data+"\n",e+="AICC_Data_Chunk = "+AICC_Data_Chunk+"\n",e+="AICC_Comments = "+AICC_Comments+"\n",e+="AICC_Objectives = "+AICC_Objectives+"\n",alert(e)}function LMSStandardAPI(strStandard){WriteToDebug("In LMSStandardAPI strStandard="+strStandard),""==strStandard&&(WriteToDebug("No standard specified, using NONE"),strStandard="NONE"),eval("this.Initialize = "+strStandard+"_Initialize"),eval("this.Finish = "+strStandard+"_Finish"),eval("this.CommitData = "+strStandard+"_CommitData"),eval("this.GetStudentID = "+strStandard+"_GetStudentID"),eval("this.GetStudentName = "+strStandard+"_GetStudentName"),eval("this.GetBookmark = "+strStandard+"_GetBookmark"),eval("this.SetBookmark = "+strStandard+"_SetBookmark"),eval("this.GetDataChunk = "+strStandard+"_GetDataChunk"),eval("this.SetDataChunk = "+strStandard+"_SetDataChunk"),eval("this.GetLaunchData = "+strStandard+"_GetLaunchData"),eval("this.GetComments = "+strStandard+"_GetComments"),eval("this.WriteComment = "+strStandard+"_WriteComment"),eval("this.GetLMSComments = "+strStandard+"_GetLMSComments"),eval("this.GetAudioPlayPreference = "+strStandard+"_GetAudioPlayPreference"),eval("this.GetAudioVolumePreference = "+strStandard+"_GetAudioVolumePreference"),eval("this.SetAudioPreference = "+strStandard+"_SetAudioPreference"),eval("this.SetLanguagePreference = "+strStandard+"_SetLanguagePreference"),eval("this.GetLanguagePreference = "+strStandard+"_GetLanguagePreference"),eval("this.SetSpeedPreference = "+strStandard+"_SetSpeedPreference"),eval("this.GetSpeedPreference = "+strStandard+"_GetSpeedPreference"),eval("this.SetTextPreference = "+strStandard+"_SetTextPreference"),eval("this.GetTextPreference = "+strStandard+"_GetTextPreference"),eval("this.GetPreviouslyAccumulatedTime = "+strStandard+"_GetPreviouslyAccumulatedTime"),eval("this.SaveTime = "+strStandard+"_SaveTime"),eval("this.GetMaxTimeAllowed = "+strStandard+"_GetMaxTimeAllowed"),eval("this.DisplayMessageOnTimeout = "+strStandard+"_DisplayMessageOnTimeout"),eval("this.ExitOnTimeout = "+strStandard+"_ExitOnTimeout"),eval("this.GetPassingScore = "+strStandard+"_GetPassingScore"),eval("this.SetScore = "+strStandard+"_SetScore"),eval("this.GetScore = "+strStandard+"_GetScore"),eval("this.GetScaledScore = "+strStandard+"_GetScaledScore"),eval("this.RecordTrueFalseInteraction = "+strStandard+"_RecordTrueFalseInteraction"),eval("this.RecordMultipleChoiceInteraction = "+strStandard+"_RecordMultipleChoiceInteraction"),eval("this.RecordFillInInteraction = "+strStandard+"_RecordFillInInteraction"),eval("this.RecordMatchingInteraction = "+strStandard+"_RecordMatchingInteraction"),eval("this.RecordPerformanceInteraction = "+strStandard+"_RecordPerformanceInteraction"),eval("this.RecordSequencingInteraction = "+strStandard+"_RecordSequencingInteraction"),eval("this.RecordLikertInteraction = "+strStandard+"_RecordLikertInteraction"),eval("this.RecordNumericInteraction = "+strStandard+"_RecordNumericInteraction"),eval("this.GetEntryMode = "+strStandard+"_GetEntryMode"),eval("this.GetLessonMode = "+strStandard+"_GetLessonMode"),eval("this.GetTakingForCredit = "+strStandard+"_GetTakingForCredit"),eval("this.SetObjectiveScore = "+strStandard+"_SetObjectiveScore"),eval("this.SetObjectiveStatus = "+strStandard+"_SetObjectiveStatus"),eval("this.GetObjectiveScore = "+strStandard+"_GetObjectiveScore"),eval("this.GetObjectiveStatus = "+strStandard+"_GetObjectiveStatus"),eval("this.SetObjectiveDescription = "+strStandard+"_SetObjectiveDescription"),eval("this.GetObjectiveDescription = "+strStandard+"_GetObjectiveDescription"),eval("this.SetFailed = "+strStandard+"_SetFailed"),eval("this.SetPassed = "+strStandard+"_SetPassed"),eval("this.SetCompleted = "+strStandard+"_SetCompleted"),eval("this.ResetStatus = "+strStandard+"_ResetStatus"),eval("this.GetStatus = "+strStandard+"_GetStatus"),eval("this.GetLastError = "+strStandard+"_GetLastError"),eval("this.GetLastErrorDesc = "+strStandard+"_GetLastErrorDesc"),eval("this.GetInteractionType = "+strStandard+"_GetInteractionType"),eval("this.GetInteractionTimestamp = "+strStandard+"_GetInteractionTimestamp"),eval("this.GetInteractionCorrectResponses = "+strStandard+"_GetInteractionCorrectResponses"),eval("this.GetInteractionWeighting = "+strStandard+"_GetInteractionWeighting"),eval("this.GetInteractionLearnerResponses = "+strStandard+"_GetInteractionLearnerResponses"),eval("this.GetInteractionResult = "+strStandard+"_GetInteractionResult"),eval("this.GetInteractionLatency = "+strStandard+"_GetInteractionLatency"),eval("this.GetInteractionDescription = "+strStandard+"_GetInteractionDescription"),eval("this.CreateDataBucket = "+strStandard+"_CreateDataBucket"),eval("this.GetDataFromBucket = "+strStandard+"_GetDataFromBucket"),eval("this.PutDataInBucket = "+strStandard+"_PutDataInBucket"),eval("this.DetectSSPSupport = "+strStandard+"_DetectSSPSupport"),eval("this.GetBucketInfo = "+strStandard+"_GetBucketInfo"),eval("this.GetProgressMeasure = "+strStandard+"_GetProgressMeasure"),eval("this.SetProgressMeasure = "+strStandard+"_SetProgressMeasure"),eval("this.SetPointBasedScore = "+strStandard+"_SetPointBasedScore"),eval("this.SetNavigationRequest = "+strStandard+"_SetNavigationRequest"),eval("this.GetNavigationRequest = "+strStandard+"_GetNavigationRequest"),eval("this.SetObjectiveProgressMeasure = "+strStandard+"_SetObjectiveProgressMeasure"),eval("this.GetObjectiveProgressMeasure = "+strStandard+"_GetObjectiveProgressMeasure"),eval("this.CreateValidIdentifier = "+strStandard+"_CreateValidIdentifier"),void 0!==window[strStandard+"_ConcedeControl"]&&eval("this.ConcedeControl = "+strStandard+"_ConcedeControl"),this.Standard=strStandard}var blnCalledFinish=!1,blnStandAlone=!1,blnLoaded=!1,blnReachedEnd=!1,blnStatusWasSet=!1,blnLmsPresent=!1,dtmStart=null,dtmEnd=null,intAccumulatedMS=0,blnOverrodeTime=!1,intTimeOverrideMS=null,aryDebug=new Array,strDebug="",winDebug,intError=NO_ERROR,strErrorDesc="",objLMS=null;function Start(){var e,t,r=null,n="",o="",i="";if(WriteToDebug("<h1>SCORM Driver starting up</h1>"),WriteToDebug("----------------------------------------"),WriteToDebug("----------------------------------------"),WriteToDebug("In Start - Version: "+VERSION+"  Last Modified="+window.document.lastModified),WriteToDebug("Browser Info ("+navigator.appName+" "+navigator.appVersion+")"),WriteToDebug("URL: "+window.document.location.href),WriteToDebug("----------------------------------------"),WriteToDebug("----------------------------------------"),ClearErrorInfo(),WriteToDebug("strStandAlone="+(e=GetQueryStringValue("StandAlone",window.location.search))+"  strShowInteractiveDebug="+(t=GetQueryStringValue("ShowDebug",window.location.search))),ConvertStringToBoolean(e)&&(WriteToDebug("Entering Stand Alone Mode"),blnStandAlone=!0),blnStandAlone)WriteToDebug("Using NONE Standard"),objLMS=new LMSStandardAPI("NONE");else if(WriteToDebug("Standard From Configuration File - "+strLMSStandard),"AUTO"==strLMSStandard.toUpperCase())if(WriteToDebug("Searching for recognized querystring parameters"),n=GetQueryStringValue("AICC_URL",document.location.search),o=GetQueryStringValue("endpoint",document.location.search),i=GetQueryStringValue("fetch",document.location.search),null!=n&&""!=n)WriteToDebug("Found AICC querystring parameters, using AICC"),objLMS=new LMSStandardAPI("AICC"),blnLmsPresent=!0;else if(null!=o&&""!=o)WriteToDebug("Found endpoint querystring parameter - checking cmi5 or Tin Can"),null!=i&&""!=i?(WriteToDebug("Found fetch querystring parameter, using cmi5"),objLMS=new LMSStandardAPI("CMI5"),blnLmsPresent=!0):(WriteToDebug("Did not find fetch querystring parameter, using Tin Can"),objLMS=new LMSStandardAPI("TCAPI"),blnLmsPresent=!0,strLMSStandard="TCAPI");else{WriteToDebug("Auto-detecting standard - Searching for SCORM 2004 API");try{r=SCORM2004_GrabAPI()}catch(e){WriteToDebug("Error grabbing 2004 API-"+e.name+":"+e.message)}if(void 0!==r&&null!=r)WriteToDebug("Found SCORM 2004 API, using SCORM 2004"),objLMS=new LMSStandardAPI("SCORM2004"),blnLmsPresent=!0;else{WriteToDebug("Searching for SCORM 1.2 API");try{r=SCORM_GrabAPI()}catch(e){WriteToDebug("Error grabbing 1.2 API-"+e.name+":"+e.message)}if(void 0!==r&&null!=r)WriteToDebug("Found SCORM API, using SCORM"),objLMS=new LMSStandardAPI("SCORM"),blnLmsPresent=!0;else{if(!0!==ALLOW_NONE_STANDARD)return WriteToDebug("Could not determine standard, Stand Alone is disabled in configuration"),void DisplayError("Could not determine standard. SCORM, AICC, Tin Can, and CMI5 APIs could not be found");WriteToDebug("Could not determine standard, defaulting to Stand Alone"),objLMS=new LMSStandardAPI("NONE")}}}else WriteToDebug("Using Standard From Configuration File - "+strLMSStandard),objLMS=new LMSStandardAPI(strLMSStandard),blnLmsPresent=!0;(ConvertStringToBoolean(t)||void 0!==SHOW_DEBUG_ON_LAUNCH&&!0===SHOW_DEBUG_ON_LAUNCH)&&(WriteToDebug("Showing Interactive Debug Windows"),ShowDebugWindow()),WriteToDebug("Calling Standard Initialize"),"TCAPI"==strLMSStandard.toUpperCase()?loadScript("../tc-config.js",objLMS.Initialize):objLMS.Initialize(),TouchCloud()}function InitializeExecuted(e,t){if(WriteToDebug("In InitializeExecuted, blnSuccess="+e+", strErrorMessage="+t),!e)return WriteToDebug("ERROR - LMS Initialize Failed"),""==t&&(t="An Error Has Occurred"),blnLmsPresent=!1,void DisplayError(t);"AICC"==objLMS.Standard&&AICC_InitializeExecuted(),blnLoaded=!0,dtmStart=new Date,LoadContent()}function ExecFinish(e){return WriteToDebug("In ExecFinish, ExiType="+e),ClearErrorInfo(),!(blnLoaded&&!blnCalledFinish)||(WriteToDebug("Haven't called finish before, finishing"),blnCalledFinish=!0,blnReachedEnd&&!EXIT_SUSPEND_IF_COMPLETED&&(WriteToDebug("Reached End, overiding exit type to FINISH"),e=EXIT_TYPE_FINISH),1==EXIT_NORMAL_IF_PASSED&&objLMS.GetStatus()==LESSON_STATUS_PASSED&&(WriteToDebug("Passed status and config value set, overiding exit type to FINISH"),e=EXIT_TYPE_FINISH),blnOverrodeTime||(WriteToDebug("Did not override time"),dtmEnd=new Date,AccumulateTime(),objLMS.SaveTime(intAccumulatedMS)),blnLoaded=!1,WriteToDebug("Calling LMS Finish"),objLMS.Finish(e,blnStatusWasSet))}function IsLoaded(){return WriteToDebug("In IsLoaded, returning -"+blnLoaded),blnLoaded}function WriteToDebug(e){if(blnDebug){var t,r=new Date;t=aryDebug.length+":"+r.toString()+" - "+e,aryDebug[aryDebug.length]=t,winDebug&&!winDebug.closed&&(winDebug.document.body.appendChild(winDebug.document.createTextNode(t)),winDebug.document.body.appendChild(winDebug.document.createElement("br")))}}function ShowDebugWindow(){var e=function(){var e,t=aryDebug.length;for(winDebug.document.body.innerHTML="",e=0;e<t;e+=1)winDebug.document.body.appendChild(winDebug.document.createTextNode(aryDebug[e])),winDebug.document.body.appendChild(winDebug.document.createElement("br"))};winDebug&&!winDebug.closed&&winDebug.close(),null===(winDebug=window.open("blank.html","Debug","width=600,height=300,resizable,scrollbars"))?alert("Debug window could not be opened, popup blocker in place?"):((winDebug.addEventListener||winDebug.attachEvent)&&winDebug[winDebug.addEventListener?"addEventListener":"attachEvent"]((winDebug.attachEvent?"on":"")+"load",e,!1),e(),winDebug.document.close(),winDebug.focus())}function DisplayError(e){WriteToDebug("In DisplayError, strMessage="+e),confirm("An error has occurred:\n\n"+e+"\n\nPress 'OK' to view debug information to send to technical support.")&&ShowDebugWindow()}function GetLastError(){return WriteToDebug("In GetLastError, intError="+intError),intError!=NO_ERROR?(WriteToDebug("Returning API Error"),intError):IsLoaded()&&objLMS.GetLastError()!=NO_ERROR?(WriteToDebug("Returning LMS Error"),ERROR_LMS):(WriteToDebug("Returning No Error"),NO_ERROR)}function GetLastLMSErrorCode(){WriteToDebug("In GetLastLMSErrorCode, intError="+intError);var e=objLMS.GetLastError();return IsLoaded()&&e!=NO_ERROR?(WriteToDebug("Returning LMS Error: "+e),e):(WriteToDebug("Returning No Error"),NO_ERROR)}function GetLastErrorDesc(){return WriteToDebug("In GetLastErrorDesc"),intError!=NO_ERROR?(WriteToDebug("Returning API Error - "+strErrorDesc),strErrorDesc):IsLoaded()&&objLMS.GetLastError()!=NO_ERROR?(WriteToDebug("Returning LMS Error"),objLMS.GetLastErrorDesc()):(WriteToDebug("Returning No Error"),"")}function SetErrorInfo(e,t){WriteToDebug("In SetErrorInfo - Num="+e+" Desc="+t),intError=e,strErrorDesc=t}function ClearErrorInfo(){WriteToDebug("In ClearErrorInfo")}function CommitData(){return WriteToDebug("In CommitData"),ClearErrorInfo(),IsLoaded()?(blnOverrodeTime||(WriteToDebug("Did not override time, saving incremental time"),dtmEnd=new Date,AccumulateTime(),dtmStart=new Date,objLMS.SaveTime(intAccumulatedMS)),objLMS.CommitData()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function Suspend(){return WriteToDebug("In Suspend"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_SUSPEND)}function Finish(){return WriteToDebug("In Finish"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_FINISH)}function TimeOut(){return WriteToDebug("In TimeOut"),ClearErrorInfo(),ExecFinish(EXIT_TYPE_TIMEOUT)}function Unload(){return WriteToDebug("In Unload"),ClearErrorInfo(),ExecFinish(DEFAULT_EXIT_TYPE)}function SetReachedEnd(){return WriteToDebug("In SetReachedEnd"),ClearErrorInfo(),IsLoaded()?(0==blnStatusWasSet&&objLMS.SetCompleted(),blnReachedEnd=!0):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function ConcedeControl(){if(WriteToDebug("Conceding control with type: "+EXIT_BEHAVIOR),ClearErrorInfo(),void 0!==objLMS.ConcedeControl)return Suspend(),objLMS.ConcedeControl();var e=null,t=null;switch(EXIT_BEHAVIOR){case"SCORM_RECOMMENDED":(e=SearchParentsForContentRoot())==window.top?(Suspend(),e.window.close()):(Suspend(),null!=e&&(IsAbsoluteUrl(EXIT_TARGET)?e.scormdriver_content.location.href=EXIT_TARGET:(t=GetContentRootUrlBase(e),e.scormdriver_content.location.href=t+EXIT_TARGET)));break;case"ALWAYS_CLOSE":Suspend(),window.close();break;case"ALWAYS_CLOSE_TOP":Suspend(),window.top.close();break;case"ALWAYS_CLOSE_PARENT":Suspend(),window.parent.close();break;case"NOTHING":Suspend();break;case"REDIR_CONTENT_FRAME":Suspend(),null!=(e=SearchParentsForContentRoot())&&(IsAbsoluteUrl(EXIT_TARGET)?e.scormdriver_content.location.href=EXIT_TARGET:(t=GetContentRootUrlBase(e),e.scormdriver_content.location.href=t+EXIT_TARGET))}return!0}function GetContentRootUrlBase(e){var t=e.location.href.split("?")[0].split("/");return delete t[t.length-1],e=t.join("/")}function SearchParentsForContentRoot(){var e=null,t=window,r=0;if(t.scormdriver_content)return e=t;for(;null==e&&t!=window.top&&r++<100;){if(t.scormdriver_content)return e=t;t=t.parent}return WriteToDebug("Unable to locate content root"),null}function GetStudentID(){return WriteToDebug("In GetStudentID"),ClearErrorInfo(),IsLoaded()?objLMS.GetStudentID():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetStudentName(){return WriteToDebug("In GetStudentName"),ClearErrorInfo(),IsLoaded()?objLMS.GetStudentName():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetBookmark(){return WriteToDebug("In GetBookmark"),ClearErrorInfo(),IsLoaded()?objLMS.GetBookmark():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetBookmark(e,t){return WriteToDebug("In SetBookmark - strBookmark="+e+", strDesc="+t),ClearErrorInfo(),IsLoaded()?objLMS.SetBookmark(e,t):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetDataChunk(){return WriteToDebug("In GetDataChunk"),ClearErrorInfo(),IsLoaded()?objLMS.GetDataChunk():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetDataChunk(e){return WriteToDebug("In SetDataChunk strData="+e),ClearErrorInfo(),IsLoaded()?objLMS.SetDataChunk(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLaunchData(){return WriteToDebug("In GetLaunchData"),ClearErrorInfo(),IsLoaded()?objLMS.GetLaunchData():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetComments(){var e,t,r;if(WriteToDebug("In GetComments"),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),null;if(WriteToDebug("strCommentString="+(e=objLMS.GetComments())),""!=(e=new String(e)))for(t=e.split(" | "),r=0;r<t.length;r++)WriteToDebug("Returning Comment #"+r),t[r]=new String(t[r]),t[r]=t[r].replace(/\|\|/g,"|"),WriteToDebug("Comment #"+r+"="+t[r]);else t=new Array(0);return t}function WriteComment(e){var t;return WriteToDebug("In WriteComment strComment="+e),ClearErrorInfo(),e=new String(e),IsLoaded()?(e=e.replace(/\|/g,"||"),""!=(t=objLMS.GetComments())&&"undefined"!=t&&(e=t+" | "+e),objLMS.WriteComment(e)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLMSComments(){return WriteToDebug("In GetLMSComments"),ClearErrorInfo(),IsLoaded()?objLMS.GetLMSComments():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function GetAudioPlayPreference(){return WriteToDebug("In GetAudioPlayPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetAudioPlayPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),PREFERENCE_DEFAULT)}function GetAudioVolumePreference(){return WriteToDebug("GetAudioVolumePreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetAudioVolumePreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),100)}function SetAudioPreference(e,t){return WriteToDebug("In SetAudioPreference PlayPreference="+e+" intPercentOfMaxVolume="+t),ClearErrorInfo(),IsLoaded()?e!=PREFERENCE_OFF&&e!=PREFERENCE_ON?(WriteToDebug("Error Invalid PlayPreference"),SetErrorInfo(ERROR_INVALID_PREFERENCE,"Invalid PlayPreference passed to SetAudioPreference, PlayPreference="+e),!1):ValidInteger(t)?(t=parseInt(t,10))<1||100<t?(WriteToDebug("Error Invalid PercentOfMaxVolume - out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxVolume passed to SetAudioPreference (must be between 1 and 100), intPercentOfMaxVolume="+t),!1):(WriteToDebug("Calling to LMS"),objLMS.SetAudioPreference(e,t)):(WriteToDebug("Error Invalid PercentOfMaxVolume - not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxVolume passed to SetAudioPreference (not an integer), intPercentOfMaxVolume="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetLanguagePreference(){return WriteToDebug("In GetLanguagePreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetLanguagePreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),"")}function SetLanguagePreference(e){return WriteToDebug("In SetLanguagePreference strLanguage="+e),ClearErrorInfo(),IsLoaded()?objLMS.SetLanguagePreference(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetSpeedPreference(){return WriteToDebug("In GetSpeedPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetSpeedPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),100)}function SetSpeedPreference(e){return WriteToDebug("In SetSpeedPreference intPercentOfMax="+e),ClearErrorInfo(),IsLoaded()?ValidInteger(e)?(e=parseInt(e,10))<0||100<e?(WriteToDebug("ERROR Invalid Percent of MaxSpeed, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxSpeed passed to SetSpeedPreference (must be between 1 and 100), intPercentOfMax="+e),!1):(WriteToDebug("Calling to LMS"),objLMS.SetSpeedPreference(e)):(WriteToDebug("ERROR Invalid Percent of MaxSpeed, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid PercentOfMaxSpeed passed to SetSpeedPreference (not an integer), intPercentOfMax="+e),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetTextPreference(){return WriteToDebug("In GetTextPreference"),ClearErrorInfo(),IsLoaded()?objLMS.GetTextPreference():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetTextPreference(e){return WriteToDebug("In SetTextPreference intPreference="+e),ClearErrorInfo(),IsLoaded()?e!=PREFERENCE_DEFAULT&&e!=PREFERENCE_OFF&&e!=PREFERENCE_ON?(WriteToDebug("Error - Invalid Preference"),SetErrorInfo(ERROR_INVALID_PREFERENCE,"Invalid Preference passed to SetTextPreference, intPreference="+e),!1):objLMS.SetTextPreference(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetPreviouslyAccumulatedTime(){return WriteToDebug("In GetPreviouslyAccumulatedTime"),ClearErrorInfo(),IsLoaded()?objLMS.GetPreviouslyAccumulatedTime():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function AccumulateTime(){WriteToDebug("In AccumulateTime dtmStart="+dtmStart+" dtmEnd="+dtmEnd+" intAccumulatedMS="+intAccumulatedMS),null!=dtmEnd&&null!=dtmStart&&(WriteToDebug("Accumulating Time"),WriteToDebug("intAccumulatedMS="+(intAccumulatedMS+=dtmEnd.getTime()-dtmStart.getTime())))}function GetSessionAccumulatedTime(){return WriteToDebug("In GetSessionAccumulatedTime"),ClearErrorInfo(),WriteToDebug("Setting dtmEnd to now"),dtmEnd=new Date,WriteToDebug("Accumulating Time"),AccumulateTime(),null!=dtmStart&&(WriteToDebug("Resetting dtmStart"),dtmStart=new Date),WriteToDebug("Setting dtmEnd to null"),dtmEnd=null,WriteToDebug("Returning "+intAccumulatedMS),intAccumulatedMS}function SetSessionTime(e){return WriteToDebug("In SetSessionTime"),ClearErrorInfo(),ValidInteger(e)?(e=parseInt(e,10))<0?(WriteToDebug("Error, parameter is less than 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMilliseconds passed to SetSessionTime (must be greater than 0), intMilliseconds="+e),!1):(blnOverrodeTime=!0,intTimeOverrideMS=e,objLMS.SaveTime(intTimeOverrideMS),!0):(WriteToDebug("ERROR parameter is not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMilliseconds passed to SetSessionTime (not an integer), intMilliseconds="+e),!1)}function PauseTimeTracking(){return WriteToDebug("In PauseTimeTracking"),ClearErrorInfo(),WriteToDebug("Setting dtmEnd to now"),dtmEnd=new Date,WriteToDebug("Accumulating Time"),AccumulateTime(),WriteToDebug("Setting Start and End times to null"),!(dtmEnd=dtmStart=null)}function ResumeTimeTracking(){return WriteToDebug("In ResumeTimeTracking"),ClearErrorInfo(),WriteToDebug("Setting dtmStart to now"),dtmStart=new Date,!0}function GetMaxTimeAllowed(){return WriteToDebug("In GetMaxTimeAllowed"),ClearErrorInfo(),IsLoaded()?objLMS.GetMaxTimeAllowed():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),MAX_CMI_TIME)}function DisplayMessageOnTimeout(){return WriteToDebug("In DisplayMessageOnTimeOut"),ClearErrorInfo(),IsLoaded()?objLMS.DisplayMessageOnTimeout():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function ExitOnTimeout(){return WriteToDebug("In ExitOnTimeOut"),ClearErrorInfo(),IsLoaded()?objLMS.ExitOnTimeout():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetPassingScore(){return WriteToDebug("In GetPassingScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetPassingScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function GetScore(){return WriteToDebug("In GetScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function GetScaledScore(){return WriteToDebug("In GetScaledScore"),ClearErrorInfo(),IsLoaded()?objLMS.GetScaledScore():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),0)}function SetScore(e,t,r){if(WriteToDebug("In SetScore, intScore="+e+", intMaxScore="+t+", intMinScore="+r),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(!IsValidDecimal(e))return WriteToDebug("ERROR - intScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (not a valid decimal), intScore="+e),!1;if(!IsValidDecimal(t))return WriteToDebug("ERROR - intMaxScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (not a valid decimal), intMaxScore="+t),!1;if(!IsValidDecimal(r))return WriteToDebug("ERROR - intMinScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (not a valid decimal), intMinScore="+r),!1;if(WriteToDebug("Converting SCORES to floats"),e=parseFloat(e),t=parseFloat(t),r=parseFloat(r),"SCORM"==strLMSStandard){if(WriteToDebug("DEBUG - SCORM 1.2 so checking max score length"),e<0||100<e)return WriteToDebug("ERROR - intScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (must be between 0-100), intScore="+e),!1;if(t<0||100<t)return WriteToDebug("ERROR - intMaxScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (must be between 0-100), intMaxScore="+t),!1;if(r<0||100<r)return WriteToDebug("ERROR - intMinScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (must be between 0-100), intMinScore="+r),!1}if(!0===SCORE_CAN_ONLY_IMPROVE){var n=GetScore();if(null!=n&&""!=n&&e<n)return WriteToDebug("Previous score was greater than new score, configuration only allows scores to improve, returning."),!0}return WriteToDebug("Calling to LMS"),objLMS.SetScore(e,t,r)}function SetPointBasedScore(e,t,r){if(WriteToDebug("In SetPointBasedScore, intScore="+e+", intMaxScore="+t+", intMinScore="+r),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(!IsValidDecimal(e))return WriteToDebug("ERROR - intScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (not a valid decimal), intScore="+e),!1;if(!IsValidDecimal(t))return WriteToDebug("ERROR - intMaxScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (not a valid decimal), intMaxScore="+t),!1;if(!IsValidDecimal(r))return WriteToDebug("ERROR - intMinScore not a valid decimal"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (not a valid decimal), intMinScore="+r),!1;if(WriteToDebug("Converting SCORES to floats"),e=parseFloat(e),t=parseFloat(t),r=parseFloat(r),"SCORM"==strLMSStandard){if(e<0||100<e)return WriteToDebug("ERROR - intScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetScore (must be between 0-100), intScore="+e),!1;if(t<0||100<t)return WriteToDebug("ERROR - intMaxScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetScore (must be between 0-100), intMaxScore="+t),!1;if(r<0||100<r)return WriteToDebug("ERROR - intMinScore out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetScore (must be between 0-100), intMinScore="+r),!1}if(!0===SCORE_CAN_ONLY_IMPROVE){var n=GetScore();if(null!=n&&""!=n&&e<n)return WriteToDebug("Previous score was greater than new score, configuration only allows scores to improve, returning."),!0}return WriteToDebug("Calling to LMS"),objLMS.SetPointBasedScore(e,t,r)}function CreateResponseIdentifier(e,t){return""==e.replace(" ","")?(WriteToDebug("Short Identifier is empty"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1):1!=e.length?(WriteToDebug("ERROR - Short Identifier  not 1 character"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1):IsAlphaNumeric(e)?new ResponseIdentifier(e=e.toLowerCase(),t=CreateValidIdentifier(t)):(WriteToDebug("ERROR - Short Identifier  not alpha numeric"),SetErrorInfo(ERROR_INVALID_ID,"Invalid short identifier, strShort="+e),!1)}function ResponseIdentifier(e,t){this.Short=new String(e),this.Long=new String(t),this.toString=function(){return"[Response Identifier "+this.Short+", "+this.Long+"]"}}function MatchingResponse(e,t){e.constructor==String&&(e=CreateResponseIdentifier(e,e)),t.constructor==String&&(t=CreateResponseIdentifier(t,t)),this.Source=e,this.Target=t,this.toString=function(){return"[Matching Response "+this.Source+", "+this.Target+"]"}}function CreateMatchingResponse(e){var t=new Array,r=new Array;t=(e=new String(e)).split("[,]");for(var n=0;n<t.length;n++){WriteToDebug("Matching Response ["+n+"]  source: "+(r=new String(t[n]).split("[.]"))[0]+"  target: "+r[1]),t[n]=new MatchingResponse(r[0],r[1])}return WriteToDebug("pattern: "+e+" becomes "+t[0]),0==t.length?t[0]:t}function CreateValidIdentifier(e){return objLMS.CreateValidIdentifier(e)}function CreateUriIdentifier(e,t){if(null==e||""===e)return"";e=Trim(e);var r=new URI(e);return r.is("absolute")||(e="urn:scormdriver:"+encodeURIComponent(e),r=new URI(e)),r.normalize(),t&&r.iri(),r.toString()}function CreateValidIdentifierLegacy(e){return null!=e||""!=e?(e=new String(e),0==(e=Trim(e)).toLowerCase().indexOf("urn:")&&(e=e.substr(4)),e=e.replace(/[^\w\-\(\)\+\.\:\=\@\;\$\_\!\*\'\%]/g,"_")):""}function Trim(e){return e=(e=(e+="").replace(/^\s*/,"")).replace(/\s*$/,"")}function RecordTrueFalseInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordTrueFalseInteraction strID="+(e=CreateValidIdentifier(e))+", blnResponse="+t+", blnCorrect="+r+", blnCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(1!=t&&0!=t&&null!==t||null===t&&!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The Response parameter must be a valid boolean value."),!1;if(null!=n&&1!=n&&0!=n)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The Correct Response parameter must be a valid boolean value or null."),!1;var C=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordTrueFalseInteraction(e,t,r,n,o,i,a,I,C)}function RecordMultipleChoiceInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordMultipleChoiceInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var C,s;if(e=new String(e),null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;C=null}else if(t.constructor==String){C=new Array;var u=CreateResponseIdentifier(t,t);if(0==u)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;C[0]=u}else if(t.constructor==ResponseIdentifier)(C=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))C=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return window.console&&window.console.log("ERROR_INVALID_INTERACTION_RESPONSE :: The response is not in the correct format."),SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;C=t}if(null!=n&&null!=n&&""!=n)if(n.constructor==String){if(s=new Array,0==(u=CreateResponseIdentifier(n,n)))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s[0]=u}else if(n.constructor==ResponseIdentifier)(s=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))s=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s=n}else s=new Array;var c=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordMultipleChoiceInteraction(e,C,r,s,o,i,a,I,c)}function RecordFillInInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordFillInInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var C=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordFillInInteraction(e,t,r,n,o,i,a,I,C)}function RecordMatchingInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordMatchingInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var C,s;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;C=null}else if(t.constructor==MatchingResponse)(C=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))C=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;C=t}if(null!=n&&null!=n)if(n.constructor==MatchingResponse)(s=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))s=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;s=n}else s=new Array;var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordMatchingInteraction(e,C,r,s,o,i,a,I,u)}function RecordPerformanceInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordPerformanceInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var C=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordPerformanceInteraction(e,t,r,n,o,i,a,I,C)}function RecordSequencingInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordSequencingInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var C,s;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;C=null}else if(t.constructor==String){C=new Array;var u=CreateResponseIdentifier(t,t);if(0==u)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;C[0]=u}else if(t.constructor==ResponseIdentifier)(C=new Array)[0]=t;else if(t.constructor==Array||0<t.constructor.toString().search("Array"))C=t;else{if(!(window.console&&"(Internal Function)"==t.constructor.toString()&&0<t.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;C=t}if(null!=n&&null!=n&&""!=n)if(n.constructor==String){if(s=new Array,0==(u=CreateResponseIdentifier(n,n)))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s[0]=u}else if(n.constructor==ResponseIdentifier)(s=new Array)[0]=n;else if(n.constructor==Array||0<n.constructor.toString().search("Array"))s=n;else{if(!(window.console&&"(Internal Function)"==n.constructor.toString()&&0<n.length))return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The correct response is not in the correct format"),!1;s=n}else s=new Array;var c=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordSequencingInteraction(e,C,r,s,o,i,a,I,c)}function RecordLikertInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordLikertInteraction strID="+(e=CreateValidIdentifier(e))+", response="+t+", blnCorrect="+r+", correctResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;var C,s;if(null===t){if(!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format (null response not allowed)"),!1;C=null}else if(t.constructor==String)C=CreateResponseIdentifier(t,t);else{if(t.constructor!=ResponseIdentifier)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;C=t}if(null==n||null==n)s=null;else if(n.constructor==ResponseIdentifier)s=n;else{if(n.constructor!=String)return SetErrorInfo(ERROR_INVALID_INTERACTION_RESPONSE,"The response is not in the correct format"),!1;s=CreateResponseIdentifier(n,n)}var u=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordLikertInteraction(e,C,r,s,o,i,a,I,u)}function RecordNumericInteraction(e,t,r,n,o,i,a,I){if(WriteToDebug("In RecordNumericInteraction strID="+(e=CreateValidIdentifier(e))+", strResponse="+t+", blnCorrect="+r+", strCorrectResponse="+n+", strDescription="+o+", intWeighting="+i+", intLatency="+a+", strLearningObjectiveID="+(I=CreateValidIdentifier(I))),void 0!==DO_NOT_REPORT_INTERACTIONS&&!0===DO_NOT_REPORT_INTERACTIONS)return WriteToDebug("Configuration specifies interactions should not be reported, exiting."),!0;if(ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(null===t&&!ALLOW_INTERACTION_NULL_LEARNER_RESPONSE||null!==t&&!IsValidDecimal(t))return WriteToDebug("ERROR - Invalid Response, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Response passed to RecordNumericInteraction (not a valid decimal), strResponse="+t),!1;var C=new Date;return WriteToDebug("Calling to LMS"),objLMS.RecordNumericInteraction(e,t,r,n,o,i,a,I,C)}function GetStatus(){return WriteToDebug("In GetStatus"),ClearErrorInfo(),IsLoaded()?objLMS.GetStatus():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function ResetStatus(){return WriteToDebug("In ResetStatus"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to false"),blnStatusWasSet=!1,objLMS.ResetStatus()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetProgressMeasure(){return WriteToDebug("In GetProgressMeasure"),ClearErrorInfo(),IsLoaded()?objLMS.GetProgressMeasure():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function SetProgressMeasure(e){return WriteToDebug("In SetProgressMeasure, passing in: "+e),ClearErrorInfo(),IsLoaded()?objLMS.SetProgressMeasure(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),LESSON_STATUS_INCOMPLETE)}function SetPassed(){return WriteToDebug("In SetPassed"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to true"),blnStatusWasSet=!0,objLMS.SetPassed()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetFailed(){return WriteToDebug("In SetFailed"),ClearErrorInfo(),IsLoaded()?(WriteToDebug("Setting blnStatusWasSet to true"),blnStatusWasSet=!0,objLMS.SetFailed()):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetEntryMode(){return WriteToDebug("In GetEntryMode"),ClearErrorInfo(),IsLoaded()?objLMS.GetEntryMode():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),ENTRY_FIRST_TIME)}function GetLessonMode(){return WriteToDebug("In GetLessonMode"),ClearErrorInfo(),IsLoaded()?objLMS.GetLessonMode():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),MODE_NORMAL)}function GetTakingForCredit(){return WriteToDebug("In GetTakingForCredit"),ClearErrorInfo(),IsLoaded()?objLMS.GetTakingForCredit():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveScore(e,t,r,n){return WriteToDebug("In SetObjectiveScore, intObjectiveID="+e+", intScore="+t+", intMaxScore="+r+", intMinScore="+n),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveScore (must have a value), strObjectiveID="+e),!1):IsValidDecimal(t)?IsValidDecimal(r)?IsValidDecimal(n)?(WriteToDebug("Converting Scores to floats"),t=parseFloat(t),r=parseFloat(r),n=parseFloat(n),t<0||100<t?(WriteToDebug("ERROR - Invalid Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetObjectiveScore (must be between 0-100), intScore="+t),!1):r<0||100<r?(WriteToDebug("ERROR - Invalid Max Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetObjectiveScore (must be between 0-100), intMaxScore="+r),!1):n<0||100<n?(WriteToDebug("ERROR - Invalid Min Score, out of range"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetObjectiveScore (must be between 0-100), intMinScore="+n),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveScore(e,t,r,n))):(WriteToDebug("ERROR - Invalid Min Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Score passed to SetObjectiveScore (not a valid decimal), intMinScore="+n),!1):(WriteToDebug("ERROR - Invalid Max Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Score passed to SetObjectiveScore (not a valid decimal), intMaxScore="+r),!1):(WriteToDebug("ERROR - Invalid Score, not a valid decmial"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Score passed to SetObjectiveScore (not a valid decimal), intScore="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveStatus(e,t){return WriteToDebug("In SetObjectiveStatus strObjectiveID="+e+", Lesson_Status="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveStatus (must have a value), strObjectiveID="+e),!1):t!=LESSON_STATUS_PASSED&&t!=LESSON_STATUS_COMPLETED&&t!=LESSON_STATUS_FAILED&&t!=LESSON_STATUS_INCOMPLETE&&t!=LESSON_STATUS_BROWSED&&t!=LESSON_STATUS_NOT_ATTEMPTED?(WriteToDebug("ERROR - Invalid Status"),SetErrorInfo(ERROR_INVALID_STATUS,"Invalid status passed to SetObjectiveStatus, Lesson_Status="+t),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveStatus(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveStatus(e){return WriteToDebug("In GetObjectiveStatus, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveStatus(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetObjectiveDescription(e,t){return WriteToDebug("In SetObjectiveDescription strObjectiveID="+e+", strObjectiveDescription="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveStatus (must have a value), strObjectiveID="+e),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveDescription(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveDescription(e){return WriteToDebug("In GetObjectiveDescription, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveDescription(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveScore(e){return WriteToDebug("In GetObjectiveScore, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveScore(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function IsLmsPresent(){return blnLmsPresent}function SetObjectiveProgressMeasure(e,t){return WriteToDebug("In SetObjectiveProgressMeasure strObjectiveID="+e+", strObjectiveProgressMeasure="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid ObjectiveID, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid ObjectiveID passed to SetObjectiveProgressMeasure (must have a value), strObjectiveID="+e),!1):(WriteToDebug("Calling To LMS"),objLMS.SetObjectiveProgressMeasure(e,t)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetObjectiveProgressMeasure(e){return WriteToDebug("In GetObjectiveProgressMeasure, strObjectiveID="+e),ClearErrorInfo(),IsLoaded()?objLMS.GetObjectiveProgressMeasure(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function SetNavigationRequest(e){return WriteToDebug("In SetNavigationRequest"),ClearErrorInfo(),IsLoaded()?objLMS.SetNavigationRequest(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetNavigationRequest(){return WriteToDebug("In GetNavigationRequest"),ClearErrorInfo(),IsLoaded()?objLMS.GetNavigationRequest():(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionType(e){return WriteToDebug("In GetInteractionType, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionType(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionTimestamp(e){return WriteToDebug("In GetInteractionTimestamp, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionTimestamp(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionCorrectResponses(e){return WriteToDebug("In GetInteractionCorrectResponses, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionCorrectResponses(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionWeighting(e){return WriteToDebug("In GetInteractionWeighting, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionWeighting(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionLearnerResponses(e){return WriteToDebug("In GetInteractionLearnerResponses, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionLearnerResponses(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionResult(e){return WriteToDebug("In GetInteractionResult, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionResult(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionLatency(e){return WriteToDebug("In GetInteractionLatency, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionLatency(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetInteractionDescription(e){return WriteToDebug("In GetInteractionDescription, strInteractionID="+(e=CreateValidIdentifier(e))),ClearErrorInfo(),IsLoaded()?objLMS.GetInteractionDescription(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function CreateDataBucket(e,t,r){return WriteToDebug("In CreateDataBucket, strBucketId="+e+", intMinSize="+t+", intMaxSize="+r),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to CreateDataBucket (must have a value), strBucketId="+e),!1):ValidInteger(t)?ValidInteger(r)?(t=parseInt(t,10),r=parseInt(r,10),t<0?(WriteToDebug("ERROR Invalid Min Size, must be greater than or equal to 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Min Size passed to CreateDataBucket (must be greater than or equal to 0), intMinSize="+t),!1):r<=0?(WriteToDebug("ERROR Invalid Max Size, must be greater than 0"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid Max Size passed to CreateDataBucket (must be greater than 0), intMaxSize="+r),!1):(t*=2,r*=2,objLMS.CreateDataBucket(e,t,r))):(WriteToDebug("ERROR Invalid Max Size, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMaxSize passed to CreateDataBucket (not an integer), intMaxSize="+r),!1):(WriteToDebug("ERROR Invalid Min Size, not an integer"),SetErrorInfo(ERROR_INVALID_NUMBER,"Invalid intMinSize passed to CreateDataBucket (not an integer), intMinSize="+t),!1):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function GetDataFromBucket(e){return WriteToDebug("In GetDataFromBucket, strBucketId="+e),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to GetDataFromBucket (must have a value), strBucketId="+e),!1):objLMS.GetDataFromBucket(e):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function PutDataInBucket(e,t,r){return WriteToDebug("In PutDataInBucket, strBucketId="+e+", blnAppendToEnd="+r+", strData="+t),ClearErrorInfo(),IsLoaded()?""==(e=new String(e)).replace(" ","")?(WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to PutDataInBucket (must have a value), strBucketId="+e),!1):(1!=r&&(WriteToDebug("blnAppendToEnd was not explicitly true so setting it to false, blnAppendToEnd="+r),r=!1),objLMS.PutDataInBucket(e,t,r)):(SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1)}function DetectSSPSupport(){return objLMS.DetectSSPSupport()}function GetBucketInfo(e){if(WriteToDebug("In GetBucketInfo, strBucketId="+e),ClearErrorInfo(),!IsLoaded())return SetErrorInfo(ERROR_NOT_LOADED,"Cannot make calls to the LMS before calling Start"),!1;if(""==(e=new String(e)).replace(" ",""))return WriteToDebug("ERROR - Invalid BucketId, empty string"),SetErrorInfo(ERROR_INVALID_ID,"Invalid strBucketId passed to GetBucketInfo (must have a value), strBucketId="+e),!1;var t=objLMS.GetBucketInfo(e);return t.TotalSpace=t.TotalSpace/2,t.UsedSpace=t.UsedSpace/2,WriteToDebug("GetBucketInfo returning "+t),t}function SSPBucketSize(e,t){this.TotalSpace=e,this.UsedSpace=t,this.toString=function(){return"[SSPBucketSize "+this.TotalSpace+", "+this.UsedSpace+"]"}}function AICC_Initialize(){WriteToDebug("rxd: overridden AICC_Initialize set AICCComm.blnXMLHTTPIsAvailable to true"),window.AICCComm.blnXMLHTTPIsAvailable=!0,WriteToDebug("rxd: overridden AICC_Initialize set AICCComm.blnCanUseXMLHTTP to true"),window.AICCComm.blnCanUseXMLHTTP=!0,window.AICCComm.MakeGetParamRequest()}