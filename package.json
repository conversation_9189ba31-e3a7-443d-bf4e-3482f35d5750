{"name": "client-learner", "version": "0.1.0", "license": "MIT", "keywords": [], "description": "Emtrain AI client learner application", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/emtrain-dev/client-learner.git"}, "scripts": {"preproduction": "<PERSON><PERSON><PERSON> dist", "production": "webpack", "lint": "eslint src/.; exit 0", "start": "export NODE_OPTIONS=--openssl-legacy-provider && webpack serve", "test": "jest", "test-watch": "jest --watch"}, "dependencies": {"@apollo/client": "3.7.11", "@azure/msal-browser": "^2.36.0", "@azure/msal-react": "^1.5.6", "@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "@fortawesome/fontawesome-svg-core": "^1.3.0-beta3", "@fortawesome/free-regular-svg-icons": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.0.0-beta3", "@fortawesome/react-fontawesome": "^0.1.16", "@mui/icons-material": "^5.0.1", "@mui/material": "^5.0.1", "@mui/styles": "^5.2.3", "mui-nested-menu": "3.2.0", "@react-oauth/google": "^0.9.0", "axios": "^1.9.0", "formik": "^2.2.9", "graphql": "^16.8.2", "i18next": "^21.3.3", "i18next-browser-languagedetector": "^6.1.2", "jwt-decode": "^3.1.2", "launchdarkly-react-client-sdk": "^3.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "prop-types": "^15.7.2", "react": "^17.0.2", "react-country-region-selector": "^3.6.1", "react-dom": "^17.0.2", "react-dropzone": "^14.2.1", "react-i18next": "^11.12.0", "react-jw-player": "^1.19.1", "react-linkedin-login-oauth2": "^2.0.1", "react-router-dom": "^5.3.4", "react-wordcloud": "^1.2.7", "regenerator-runtime": "^0.13.9", "styled-components": "^6.1.18", "victory": "^37.3.6", "yup": "^0.32.9"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/eslint-parser": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.14.5", "@babel/preset-react": "^7.14.5", "@babel/runtime": "^7.27.1", "@babel/traverse": "7.27.1", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@testing-library/user-event": "^13.1.9", "babel-jest": "^27.0.6", "babel-loader": "^8.2.2", "babel-plugin-styled-components": "^1.12.0", "copy-webpack-plugin": "^9.0.0", "css-loader": "^5.2.6", "css-minimizer-webpack-plugin": "^3.0.1", "eslint": "^7.28.0", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "html-loader": "^2.1.2", "html-webpack-plugin": "^5.3.1", "jest": "^27.0.6", "jest-transform-graphql": "^2.1.0", "mini-css-extract-plugin": "^1.6.0", "sass": "^1.35.1", "sass-loader": "^12.1.0", "style-loader": "^2.0.0", "url-loader": "^4.1.1", "webpack": "^5.39.1", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2"}, "overrides": {"d3-color": "^3.1.0"}}