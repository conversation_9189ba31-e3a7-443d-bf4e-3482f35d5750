<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="minimum-scale=1, initial-scale=1, width=device-width" />
    <meta name="description" content="Emtrain AI Application" />
    <link rel="icon" href="/src/images/favicon.ico">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <title>Emtrain | Transforming Workplace Culture </title>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-N9JPDB3');</script>
    <!-- End Google Tag Manager -->
    <!-- Datadog -->
    <script>
      const env = window.location.hostname.split('.').slice(-3)[0]
      console.log('Environment', env)
      if(env === 'app') {
        (function(h,o,u,n,d) {
          h=h[d]=h[d]||{q:[],onReady:function(c){h.q.push(c)}}
          d=o.createElement(u);d.async=1;d.src=n
          n=o.getElementsByTagName(u)[0];n.parentNode.insertBefore(d,n)
        })(window,document,'script','https://www.datadoghq-browser-agent.com/us1/v4/datadog-rum.js','DD_RUM')
        DD_RUM.onReady(function() {
          DD_RUM.init({
            clientToken: 'puba808b18da0f647f43398daf3ff9e7c1c',
            applicationId: 'ab8ca3c6-8959-4509-8f1d-b049f1da412c',
            site: 'datadoghq.com',
            service: 'client-learner',
            env: env === "app" ? "prod": env,
            // Specify a version number to identify the deployed version of your application in Datadog 
            // version: '1.0.0',
            sessionSampleRate: 100,
            sessionReplaySampleRate: 20,
            trackUserInteractions: true,
            trackFrustrations: true,
            trackResources: true,
            trackLongTasks: true,
            defaultPrivacyLevel: 'mask-user-input',
          });
      
          DD_RUM.startSessionReplayRecording();
        })
      }
      </script>
      <script type='module' src='https://public.tableau.com/javascripts/api/tableau.embedding.3.latest.js'></script>
    <!-- Datadog -->
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N9JPDB3"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div id="root"></div>
  </body>
</html>
