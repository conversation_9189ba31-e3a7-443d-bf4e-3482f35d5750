import { createTheme } from '@mui/material';
import { defaultTheme, defaultComponentOverrides } from './themeOverrides.js';
import { DARK_MODE } from './themeNames.js';
import { colors } from './colors.js';

// TODO: Still need to complete this dark theme
export default createTheme({
  ...defaultTheme,
  components: {
    ...defaultComponentOverrides,
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: colors.lightBlack,
        },
        '.ql-align-right': {
          textAlign: 'right',
          width: '100%',
        },
        '.ql-align-center': {
          textAlign: 'center',
          width: '100%',
        },
        '.tippy-box': {
          backgroundColor: colors.tintedWhite,
          color: colors.darkLightPurple,
          padding: '0 0.4rem 0 0.4rem',
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        notchedOutline: {
          borderColor: colors.greyPurple,
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          marginLeft: '5px',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          fontSize: '.8em',
          color: colors.white,
          backgroundColor: colors.lightBlack,
          border: `solid 1px ${colors.white}`,
        },
        arrow: {
          '&:before': {
            border: `solid 1px ${colors.white}`,
          },
          color: colors.lightBlack,
        },
      },
    },
  },
  palette: {
    mode: DARK_MODE,
    background: {
      assignmentList: colors.black,
      highlighted: colors.darkPurple,
      bullet: colors.darkBlue,
      secondary: colors.darkLightPurple,
      progressBar: colors.lightOrange,
      success: colors.lightLightGreen,
      detailsButton: colors.midGrey,
      frost: colors.tintedBlack,
      darkGrey: colors.darkGrey,
      clickExpand: colors.mainPurple,
      clickExpandGradient: colors.greyPurple,
      ribbon: colors.lightOrange,
      pink: colors.pink,
      darkPink: colors.darkPink,
      darkDarkPink: colors.darkDarkPink,
      lighterPink: colors.lighterPink,
      paused: colors.darkYellow,
      eol_ribbon_1: colors.pink,
      eol_ribbon_2: colors.orange,
      eol_transition_background: colors.tintedBlack,
      eol_transition_box: colors.whiteWhite,
      wcs: [colors.green, colors.yellow, colors.orange, colors.red],
      wcsGlobal: [colors.lightPurple, colors.lightGreyPurple, colors.mainPurple, colors.darkPurple],
      slider: [colors.lightOrange, colors.lightLightOrange, colors.lighterOrange,
        colors.lightLightPurple, colors.lightBlue, colors.darkMidBlue, colors.darkerMidBlue],
      noResultsBackground: colors.transparentLightPurple,
      modal: {
        videoPlayer: colors.darkTransparentBlack,
      },
      choiceResultsSection: colors.lightGrey,
      chartsOrange: colors.lightOrange,
      chartsOrangeFaded: colors.lightOrange50,
      chartsBlue: colors.darkerMidBlue,
      chartsBlueFaded: colors.darkerMidBlue50,
      default: colors.white,
      white: colors.whiteWhite,
      lightLightBlue: colors.lightLightBlue,
    },
    border: {
      main: `1px solid ${colors.darkPurple}`,
      slider: `1px solid ${colors.darkLightPurple}`,
      completedAssignmentItems: `1px solid ${colors.darkGrey}`,
      greyShade: `1px solid ${colors.greyShade}`,
      askExpertButton: `1px solid ${colors.lightGreyShade}`,
      dialog: colors.lightBlue,
      dropShadow: `drop-shadow(5px 5px 5px ${colors.shadow})`,
      darkGrey: `1px solid ${colors.darkGrey}`,
      myAnswer: `1px solid ${colors.lightLightGrey}`,
      lightGreenShade: `1px solid ${colors.lightGreenShade}`,
    },
    link: {
      document: colors.midBlue,
      linkColor: colors.blue,
    },
    button: {
      login: {
        color: colors.whiteWhite,
        backgroundColor: colors.darkLightPurple,
        hoverColor: colors.whiteWhite,
        hoverBackgroundColor: colors.darkPurple,
        disabledColor: colors.mainPurple,
        disabledBackgroundColor: colors.lightLightPurple,
      },
      playVideo: {
        color: colors.brightPink,
        backgroundColor: colors.white90percentOpacity,
        backgroundHoverColor: colors.darkMutedPink,
        backgroundFocusColor: colors.whiteWhite,
        focusOutlineColor: colors.blue,
      },
      closeVideoModal: {
        icon: {
          color: colors.white,
        },
        background: colors.black,
        backgroundHoverColor: colors.tintedBlack,
      },
      downloadCertificate: {
        backgroundHoverColor: colors.darkMutedPink,
      },
      buttonGroupButton: {
        background: colors.whiteWhite,
        backgroundSelected: colors.lightLightPurple,
        color: colors.darkLightPurple,
        colorSelected: colors.darkLightPurple,
        borderColor: colors.lightGreyShade,
        borderColorSelected: colors.darkLightPurple,
      },
    },
    resultsTable: {
      darkCellBackground: colors.darkLightPurple,
      darkCellColor: colors.whiteWhite,
      lightCellBackground: colors.lightLightGrey,
      lightCellColor: colors.darkLightPurple,
      borderColor: colors.lightGreyShade,
    },
    markerBox: {
      lightGrey: colors.lightLightPurple,
      darkGrey: colors.darkLightPurple,
    },
    customScrollbar: {
      thumbColor: colors.mainPurple,
      background: colors.darkDarkPurple,
    },
    card: {
      borderColor: colors.transparentLightGrey,
      backgroundColor: colors.blue,
    },
    doughnutGraph: {
      strokeColor: colors.darkPurpleGrey,
    },
  },
});
