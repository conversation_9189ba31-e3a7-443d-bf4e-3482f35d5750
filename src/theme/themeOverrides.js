export const defaultTheme = {
  typography: {
    fontFamily: 'Source Sans Pro, sans-serif',
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
      smallMobile: 460,
      tablet: 600,
      assignmentsDrawerBreak: 940,
      laptop: 1060,
      desktop: 1400,
      loginScreenBreak: 1184,
      largeDesktop: 1800,
      middleA: 1490,
      middleB: 1645,
    },
  },
};

export const defaultComponentOverrides = {
  MuiPaper: {
    defaultProps: {
      elevation: 0,
    },
    styleOverrides: {
      root: {
        borderRadius: '5px',
      },
    },
  },
  MuiDrawer: {
    styleOverrides: {
      paper: {
        borderRadius: '0px',
      },
    },
  },
  MuiDialog: {
    styleOverrides: {
      paper: {
        borderRadius: '15px',
      },
    },
  },
  MuiButton: {
    styleOverrides: {
      root: {
        minWidth: '22px',
      },
    },
  },
};
