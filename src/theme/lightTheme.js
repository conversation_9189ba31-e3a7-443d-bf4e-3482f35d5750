import { createTheme } from '@mui/material';
import { colors } from './colors.js';
import { defaultTheme, defaultComponentOverrides } from './themeOverrides.js';
import { LIGHT_MODE } from './themeNames.js';

export default createTheme({
  ...defaultTheme,
  components: {
    ...defaultComponentOverrides,
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: colors.lightLightPurple,
        },
        '.ql-align-right': {
          textAlign: 'right',
          width: '100%',
        },
        '.ql-align-center': {
          textAlign: 'center',
          width: '100%',
        },
        '.tippy-box': {
          backgroundColor: colors.mainPurple,
          color: colors.white,
          padding: '0 0.4rem 0 0.4rem',
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        notchedOutline: {
          borderColor: colors.greyPurple,
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          marginLeft: '5px',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          fontSize: '.8em',
          color: colors.mainPurple,
          backgroundColor: colors.white,
          border: `solid 1px ${colors.lightLightPurple}`,
        },
        arrow: {
          '&:before': {
            border: `solid 1px ${colors.lightLightPurple}`,
          },
          color: colors.white,
        },
      },
    },
  },
  palette: {
    mode: LIGHT_MODE,
    text: {
      primary: colors.darkLightPurple,
    },
    primary: {
      light: colors.lightGreyPurple,
      main: colors.mainPurple,
      dark: colors.darkPurple,
      contrastText: colors.white,
      white: colors.whiteWhite,
      black: colors.black,
      darkBlackOpacity87: colors.darkBlackOpacity87,
      blackCoral: colors.blackCoral,
      lightBlackOpacity60: colors.lightBlackOpacity60,
      errorText: colors.red,
      greyPurple: colors.greyPurple,
      darkLightPurple: colors.darkLightPurple,
      lightGreyOpacity60: colors.lightGrey60,
      lightGreyOpacity30: colors.lightGrey30,
      greyShade: colors.greyShade,
      darkGrey: colors.darkGrey,
      lightGreyShade: colors.lightGreyShade,
      lightPinkShade: colors.lightPinkShade,
      lightBlue: colors.lightBlue,
      patriotBlue: colors.patriotBlue,
      cyanBlue: colors.cyanBlue,
      darkBlue: colors.brightArcBlue,
      darkGreen: colors.darkGreen,
      darkRed: colors.darkRed,
      lightPictonBlue: colors.lightPictonBlue,
      darkByzantinePurple: colors.darkByzantinePurple,
      blackBorder: colors.blackBorder,
      pureWhite: colors.pureWhite,
      mainBlue: colors.mainBlue,
    },
    background: {
      paper: colors.lightGreyPurple,
      default: colors.white,
      white: colors.whiteWhite,
      lightGrey: colors.lightGrey,
      assignmentList: colors.darkPurple,
      manageMenu: colors.darkPurple,
      assignmentItem: colors.lightGreyPurple,
      highlighted: colors.lightLightPurple,
      bullet: colors.lightBlue,
      secondary: colors.lightPurple,
      progressBar: colors.lightGreen,
      success: colors.lightLightGreen,
      detailsButton: colors.midGrey,
      frost: colors.tintedWhite,
      darkGrey: colors.darkGrey,
      clickExpand: colors.mainPurple,
      clickExpandGradient: colors.greyPurple,
      ribbon: colors.darkLightPurple,
      pink: colors.pink,
      darkPink: colors.darkPink,
      darkDarkPink: colors.darkDarkPink,
      lighterPink: colors.lighterPink,
      paused: colors.darkYellow,
      eol_ribbon_1: colors.pink,
      eol_ribbon_2: colors.orange,
      eol_transition_background: colors.tintedBlack,
      eol_transition_box: colors.whiteWhite,
      wcs: [colors.green, colors.yellow, colors.orange, colors.red],
      wcsGlobal: [colors.green50, colors.yellow50, colors.orange50, colors.red50],
      slider: [colors.lightOrange, colors.lightLightOrange, colors.lighterOrange,
        colors.lightLightPurple, colors.lightBlue, colors.darkMidBlue, colors.darkerMidBlue],
      noResultsBackground: colors.transparentLightPurple,
      modal: {
        videoPlayer: colors.darkTransparentBlack,
      },
      choiceResultsSection: colors.lightGrey,
      chartsOrange: colors.lightOrange,
      chartsOrangeFaded: colors.lightOrange50,
      chartsBlue: colors.darkerMidBlue,
      chartsBlueFaded: colors.darkerMidBlue50,
      ghostWhite: colors.ghostWhite,
      lightBlue: colors.lightMidBlue,
      lightLightBlue: colors.lightLightBlue,
      lightRed: colors.lightRed,
      lightGreyShade: colors.lightGreyShade,
      darkGreyPurple: colors.darkGreyPurple,
      lightGreenBlue: colors.lightGreenBlue,
      wordCloudHeader: colors.mainPurple,
      lightShadeGrey: colors.lightShadeGrey,
      lightShadeBlue: colors.lightShadeBlue,
      lightGrayish: colors.lightGrayish,
    },
    border: {
      main: `1px solid ${colors.lightLightPurple}`,
      slider: `1px solid ${colors.darkLightPurple}`,
      completedAssignmentItems: `1px solid ${colors.darkGrey}`,
      greyShade: `1px solid ${colors.greyShade}`,
      askExpertButton: `1px solid ${colors.lightGreyShade}`,
      dialog: colors.lightBlue,
      dropShadow: `drop-shadow(5px 5px 5px ${colors.shadow})`,
      dropDownShadow: `0px 2px 3px 0px ${colors.dropDownShadow}`,
      darkGrey: `1px solid ${colors.darkGrey}`,
      myAnswer: `1px solid ${colors.lightLightGrey}`,
      lightGreyShade: `1px solid ${colors.lightGreyShade}`,
      bobbyBlue: `1px solid ${colors.bobbyBlue}`,
      lightGrayishShade: `1px solid ${colors.lightGrayishBlue}`,
      altoGrey: `1px solid ${colors.altoGrey}`,
      lightLightPurpleShade: `solid 1px ${colors.lightLightPurple}`,
      lightGreenShade: `1px solid ${colors.lightGreenShade}`,
    },
    link: {
      document: colors.blue,
      linkColor: colors.blue,
    },
    button: {
      login: {
        color: colors.whiteWhite,
        backgroundColor: colors.darkLightPurple,
        hoverColor: colors.whiteWhite,
        hoverBackgroundColor: colors.darkPurple,
        disabledColor: colors.mainPurple,
        disabledBackgroundColor: colors.lightLightPurple,
      },
      playVideo: {
        color: colors.brightPink,
        backgroundColor: colors.white90percentOpacity,
        backgroundHoverColor: colors.darkMutedPink,
        backgroundFocusColor: colors.whiteWhite,
        focusOutlineColor: colors.blue,
      },
      closeVideoModal: {
        icon: {
          color: colors.white,
        },
        background: colors.black,
        backgroundHoverColor: colors.tintedBlack,
      },
      downloadCertificate: {
        backgroundHoverColor: colors.darkMutedPink,
      },
      buttonGroupButton: {
        background: colors.whiteWhite,
        backgroundSelected: colors.lightLightPurple,
        color: colors.darkLightPurple,
        colorSelected: colors.darkLightPurple,
        borderColor: colors.lightGreyShade,
        borderColorSelected: colors.darkLightPurple,
      },
    },
    resultsTable: {
      darkCellBackground: colors.darkLightPurple,
      darkCellColor: colors.whiteWhite,
      lightCellBackground: colors.lightLightGrey,
      lightCellColor: colors.darkLightPurple,
      borderColor: colors.lightGreyShade,
    },
    markerBox: {
      lightGrey: colors.lightLightPurple,
      darkGrey: colors.darkLightPurple,
    },
    customScrollbar: {
      thumbColor: colors.mainPurple,
      background: colors.darkDarkPurple,
    },
    scrollbar: {
      thumbColor: colors.darkGrey,
      background: colors.transparentLightGrey,
    },
    card: {
      borderColor: colors.transparentLightGrey,
      backgroundColor: colors.blue,
    },
    doughnutGraph: {
      strokeColor: colors.darkPurpleGrey,
    },
  },
});
