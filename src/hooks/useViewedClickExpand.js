/* eslint-disable no-param-reassign */
import React, { useState, useContext, createContext } from 'react';

const ClickExpandContext = createContext();

function useProvideViewedClickExpand() {
  const [viewedClickExpand, setViewedClickExpand] =
  useState(JSON.parse(window.localStorage.getItem('expandCardsList')) || {});

  const [showClickExpandTooltip, setShowClickExpandTooltip] = useState(false);
  const [clickExpandBox, setClickExpandBox] = useState(0);

  const updateObject = (clickExpandCards, id, value) => {
    // Check if the object is empty
    if (Object.keys(clickExpandCards).length === 0) {
      clickExpandCards[id] = [value];
    } else if (clickExpandCards[id]) {
      if (!clickExpandCards[id].includes(value)) {
        clickExpandCards[id].push(value);
      }
    } else {
      clickExpandCards[id] = [value];
    }
    return clickExpandCards;
  };

  const addClickExpand = (cardId, buttonIndex) => {
    const clickCards = updateObject(viewedClickExpand, cardId, buttonIndex);
    setViewedClickExpand(clickCards);
    setClickExpandBox(clickExpandBox + 1);
    window.localStorage.setItem('expandCardsList', JSON.stringify(viewedClickExpand));
  };

  const updateClickExpandCardTooltip = (val) => {
    setShowClickExpandTooltip(val);
  };

  return {
    viewedClickExpand,
    addClickExpand,
    showClickExpandTooltip,
    updateClickExpandCardTooltip,
    clickExpandBox,
  };
}

export const useViewedClickExpand = () => useContext(ClickExpandContext);
export const ClickExpandProvider = (props) => (
  <ClickExpandContext.Provider value={useProvideViewedClickExpand()} {...props} />
);
