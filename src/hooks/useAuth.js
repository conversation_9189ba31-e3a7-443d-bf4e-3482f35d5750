import React, { useState, useContext, createContext, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { get } from 'lodash';
import { useApolloClient } from '@apollo/client';
import { googleLogout } from '@react-oauth/google';
import jwtDecode from 'jwt-decode';
import { getCurrentAccount } from '../services/api/currentAccount';
import { pushToHome } from '../features/navigation/Routes/AppRoutes';

import {
  getLoginToken,
  loginById,
  removeLoginToken,
  setLoginToken,
  validateJwt,
  authenticateScorm,
  authenticateSaml,
  authenticateJettSSO,
  setChangePasswordToken,
  removeChangePasswordToken,
  getJettSSOToken,
  resetPassword,
  googleOauthEvent,
  linkedinAuthorizationCode,
  microsoftOauthEvent,
  getByPassSSOSetting,
  setByPassSSOSetting,
  getUserById,
  removeIsPreviewModeEnable,
  removeIsCompletedItemView,
  removeByPassSSOSetting,
  verifyAuth,
  refreshToken,
  checkTokenValid,
  tableauToken,
} from '../services/api/authentication';
import { setGtmUserRole } from '../utils/gtmDataLayer';
import {
  setJWPlayerBandwidth, setJWPlayerLanguage,
} from '../services/jwPlayerSettings';
import { setLanguage } from '../i18next';
import { SAML_SESSION_TIME_OUT, LOGIN_SESSION_TIME_OUT, aiAppURL } from '../config/constants';
import { getCookie, setAuthSession, subdomainName, deleteCookie } from './useCookies';

const authContext = createContext();

export const useAuth = () => {
  return useContext(authContext);
};

function useProvideAuth() {
  let tokenTimer = null;
  let tokenIntervalTimer = null;
  const history = useHistory();
  const [data, setData] = useState(null);
  const [validatingJwt, setValidatingJwt] = useState(true);
  const client = useApolloClient();

  async function handleTokenValidation({ accessToken, scorm, scormProgramId, sso, enforceSequence }) {
    try {
      const res = await validateJwt({ accessToken });
      const user = res.user;
      const isJettClient = get(user, 'accounts[0].isJettClient', true);
      const response = await getUserById(user.id);
      setJWPlayerBandwidth();
      if (user) {
        const videoClosedCaption = get(user, 'accounts[0].videoClosedCaption', false);
        const language = user.language ? user.language.toLowerCase() : 'en';
        setJWPlayerLanguage(language, videoClosedCaption);
      }
      const token = jwtDecode(res.accessToken);
      const accountRoleId = get(user, 'accounts[0].accountUsers.roleId', 0);
      const accountType = get(user, 'accounts[0].accountType');
      const canAccessAdminApp = accountType === 'admin' && (accountRoleId === 6 || accountRoleId === 5);
      const canAccessManageApp = checkManageAccess(accountRoleId);
      const sessionTimeout = get(user, 'accounts[0].sessionTimeout');
      if (sso) {
        const dataSSOUser = { ...res, user: { ...res.user, sso: true } };
        setData(dataSSOUser);
        setLoginToken(accessToken);
        const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : SAML_SESSION_TIME_OUT;
        setAuthSession(hours);
        // eslint-disable-next-line max-len
        const jettForwardTo = localStorage.getItem('answersForwardTo') || localStorage.getItem('forwardToRoute') || null;
        const aiForwardTo = localStorage.getItem('aiForwardTo') || null;
        localStorage.removeItem('aiForwardTo');
        localStorage.removeItem('answersForwardTo');
        setData(dataSSOUser);
        if (!isJettClient && aiForwardTo) {
          authedNavigateToAI(user, aiForwardTo);
          return false;
        }
        if (isJettClient && jettForwardTo) {
          return window.location.replace(jettForwardTo);
        }
        if (canAccessManageApp) {
          if (canAccessAdminApp) {
            return authedNavigateToAI(user, 'superadmin');
          }
          return authedNavigateToAI(user, 'manage');
        // eslint-disable-next-line no-else-return
        } else if (!isJettClient) {
          setValidatingJwt(false);
          return authedNavigateToAI(user);
        } else {
          return window.location.replace(pushToHome());
        }
      } else if (scorm) {
        const dataScormUser = { ...res, user: { ...res.user, scorm: true, scormProgramId, enforceSequence } };
        setData(dataScormUser);
      } else {
        // adminSSO: add SSO admin target account to user data
        if (token.adminAccountId) {
          response.accounts = res.user.accounts;
          response.adminAccountId = token.adminAccountId;
          const ssoAdminAccount = res.user.accounts[1];
          // for adminSSO - to remove buttons when on scorm accounts in CustomerAdminNavigation
          response.adminSSOaccountIsScorm = ssoAdminAccount.adminSSOaccountIsScorm;
        }
        const userData = { ...res, user: { ...res.user, ...response } };
        setData(userData);

        // Initialize GTM dataLayer with user data
        try {
          setGtmUserRole(userData?.user);
        } catch (error) {
          console.error('Error setting GTM user role:', error);
        }

        const isAuthSession = getCookie(`${subdomainName}_isAuthSession`);
        if (!isAuthSession) {
          const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : LOGIN_SESSION_TIME_OUT;
          setAuthSession(hours);
        }
        if (!isJettClient) {
          authedNavigateToAI(user);
        }
      }
      setTimerOnExpiration(token.exp, res.isScormToken);
      setValidatingJwt(false);
    } catch (e) {
      removeLoginToken();
      setValidatingJwt(false);
    }
  }

  useEffect(() => {
    const accessToken = getLoginToken();
    if (accessToken) {
      handleTokenValidation({ accessToken });
    } else {
      setValidatingJwt(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const language = get(data, 'user.language');
    if (language) {
      setLanguage(language.toLowerCase());
    }
  }, [data]);

  // clear the token timer after token expire
  const clearTokenTimers = () => {
    if (tokenTimer) {
      clearTimeout(tokenTimer);
      tokenTimer = null;
    }
    if (tokenIntervalTimer) {
      clearInterval(tokenIntervalTimer);
      tokenIntervalTimer = null;
    }
  };

  async function login({ email, password, forwardTo = '' }) {
    await client.clearStore();
    const res = await loginById({ loginId: email, password });
    const skipSSO = getByPassSSOSetting();
    const user = res.user;
    const accountOwnerId = get(user, 'accounts[0].accountOwnerId');
    const sessionTimeout = get(user, 'accounts[0].sessionTimeout');
    const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : LOGIN_SESSION_TIME_OUT;
    setAuthSession(hours);
    if (skipSSO && user.id !== accountOwnerId) {
      await logout();
      setByPassSSOSetting(false);
      return window.location.assign(accountOwnerId);
    }
    setLoginToken(res.accessToken);
    const response = await getUserById(user.id);
    const token = jwtDecode(res.accessToken);
    setTimerOnExpiration(token.exp);
    if (res.changePasswordToken) {
      setChangePasswordToken(res.changePasswordToken);
    }
    setJWPlayerBandwidth();

    if (user) {
      const videoClosedCaption = get(user, 'accounts[0].videoClosedCaption', false);
      const language = user.language ? user.language.toLowerCase() : 'en';
      setJWPlayerLanguage(language, videoClosedCaption);
    }
    const finalUserData = { ...res, user: { ...res.user, ...response } };
    setData(finalUserData);

    // Initialize GTM dataLayer with user data
    try {
      setGtmUserRole(finalUserData?.user);
    } catch (error) {
      console.error('Error setting GTM user role:', error);
    }

    const accountRoleId = get(user, 'accounts[0].accountUsers.roleId', 0);
    const accountType = get(user, 'accounts[0].accountType');
    const canAccessManageApp = checkManageAccess(accountRoleId);
    const canAccessAdminApp = accountType === 'admin' && (accountRoleId === 6 || accountRoleId === 5);
    const isJettClient = get(user, 'accounts[0].isJettClient', true);
    const jettForwardTo = localStorage.getItem('forwardToRoute') || null;
    if (!isJettClient && forwardTo) {
      authedNavigateToAI(user, forwardTo);
      return false;
    }
    if (isJettClient && forwardTo) {
      history.push(forwardTo);
      return false;
    }

    // for forwardToRoute access for preview
    if (isJettClient && jettForwardTo) {
      return true;
    }

    if (canAccessManageApp && !res.changePasswordToken) {
      removeByPassSSOSetting();
      if (canAccessAdminApp) {
        authedNavigateToAI(user, 'superadmin');
      } else {
        authedNavigateToAI(user, 'manage');
      }
      return false;
    }

    if (!isJettClient && !res.changePasswordToken) {
      authedNavigateToAI(user);
      return false;
    }
    return true;
  }

  async function logout() {
    const account = await getCurrentAccount();
    clearTokenTimers();
    removeLoginToken();
    removeChangePasswordToken();
    googleLogout();
    removeIsPreviewModeEnable();
    removeIsCompletedItemView();
    await client.clearStore();
    if (account.sso) {
      if (account.ssoLogoutUrl) {
        return window.location.replace(account.ssoLogoutUrl);
      }
      return window.location.replace('/logged-out');
    }
    setData(null);
    return null;
  }

  async function clearTokens() {
    setData(null);
    clearTokenTimers();
    removeLoginToken();
    removeChangePasswordToken();
    googleLogout();
    await client.clearStore();
    return null;
  }

  async function updateUserData(updates) {
    const updatedData = { ...data, user: { ...data.user, ...updates } };
    setData(updatedData);
  }

  async function verifyScorm(params) {
    await client.clearStore();
    const res = await authenticateScorm(params);
    setLoginToken(res.accessToken);
    await handleTokenValidation({
      accessToken: res.accessToken,
      scorm: true,
      scormProgramId: res.scormProgramId || res.scormProgram.id,
      enforceSequence: params?.enforceSequence,
    });
    return res.scormProgramId || res.scormProgram.id;
  }

  async function verifySaml(token) {
    await client.clearStore();
    const res = await authenticateSaml(token);
    setLoginToken(res.accessToken);
    await handleTokenValidation({ accessToken: res.accessToken, sso: true });
  }

  async function fetchJettSSOToken() {
    const token = await getJettSSOToken();
    return token;
  }

  async function verifyJettSSO(token, email, employeeId) {
    await client.clearStore();
    const res = await authenticateJettSSO(token, email, employeeId);
    setLoginToken(res.accessToken);
    await handleTokenValidation({ accessToken: res.accessToken });
  }

  async function verifyContentPreview(authKey, authToken) {
    await client.clearStore();
    const res = await verifyAuth(authKey, authToken);
    setLoginToken(res.accessToken);
    await handleTokenValidation({ accessToken: res.accessToken });
  }

  async function verifyAndResetPassword(token, password, emailOrId = null, setup = false) {
    const result = await resetPassword(token, password, emailOrId, setup);
    if (result && result.accessToken) {
      setLoginToken(result.accessToken);
      setData(result);
    }
    const user = result.user;
    const newToken = jwtDecode(result.accessToken);
    const sessionTimeout = get(user, 'accounts[0].sessionTimeout');
    const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : LOGIN_SESSION_TIME_OUT;
    setAuthSession(hours);
    const accountRoleId = get(user, 'accounts[0].accountUsers.roleId', 0);
    const accountType = get(user, 'accounts[0].accountType');
    const canAccessManageApp = checkManageAccess(accountRoleId);
    const canAccessAdminApp = accountType === 'admin' && (accountRoleId === 6 || accountRoleId === 5);
    const isJettClient = get(user, 'accounts[0].isJettClient', true);
    setTimerOnExpiration(newToken.exp);
    if (canAccessManageApp) {
      if (canAccessAdminApp) {
        await authedNavigateToAI(user, 'superadmin');
      } else {
        await authedNavigateToAI(user, 'manage');
      }
      return false;
    }
    if (!isJettClient) {
      await authedNavigateToAI(user);
      return false;
    }
    return window.location.replace(pushToHome());
  }
  async function authedNavigateToAI(user, forwardTo) {
    const token = await fetchJettSSOToken();
    const aiURL = aiAppURL();
    const link = document.createElement('a');
    document.body.appendChild(link);
    const forwardToParam = forwardTo ? `forwardTo=${forwardTo}` : '';
    // eslint-disable-next-line max-len
    const identityParam = user.email ? `&email=${encodeURIComponent(user.email)}` : `&employeeId=${encodeURIComponent(user.employeeId)}`;
    link.href =
      // eslint-disable-next-line max-len
      `${aiURL}/jett-sso/${encodeURIComponent(token)}/?${forwardToParam}${forwardToParam === '' ? '' : '&'}${identityParam}`;
    link.setAttribute('type', 'hidden');
    removeLoginToken(); // remove token while switch to AI manage/admin account
    link.click();
  }

  async function googleLogin(name, email) {
    await client.clearStore();
    const res = await googleOauthEvent({ name, email });
    setLoginToken(res.accessToken);
    const token = jwtDecode(res.accessToken);
    setJWPlayerBandwidth();
    const user = res.user;
    if (user) {
      const videoClosedCaption = get(user, 'accounts[0].videoClosedCaption', false);
      const language = user.language ? user.language.toLowerCase() : 'en';
      setJWPlayerLanguage(language, videoClosedCaption);
    }
    setData(res);
    const sessionTimeout = get(user, 'accounts[0].sessionTimeout');
    const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : LOGIN_SESSION_TIME_OUT;
    setAuthSession(hours);
    const isJettClient = get(user, 'accounts[0].isJettClient', true);
    if (!isJettClient) {
      authedNavigateToAI(user);
      return false;
    }
    setTimerOnExpiration(token.exp);
    return res;
  }

  async function linkedinLogin(code, redirectUri) {
    await client.clearStore();
    const res = await linkedinAuthorizationCode(code, redirectUri);
    setLoginToken(res.accessToken);
    const token = jwtDecode(res.accessToken);
    setJWPlayerBandwidth();
    const user = res.user;
    if (user) {
      const videoClosedCaption = get(user, 'accounts[0].videoClosedCaption', false);
      const language = user.language ? user.language.toLowerCase() : 'en';
      setJWPlayerLanguage(language, videoClosedCaption);
    }
    setData(res);
    const sessionTimeout = get(user, 'accounts[0].sessionTimeout');
    const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : LOGIN_SESSION_TIME_OUT;
    setAuthSession(hours);
    const isJettClient = get(user, 'accounts[0].isJettClient', true);
    if (!isJettClient) {
      authedNavigateToAI(user);
      return false;
    }
    setTimerOnExpiration(token.exp);
    return res;
  }

  async function microsoftLogin(msToken) {
    await client.clearStore();
    const res = await microsoftOauthEvent(msToken);
    setLoginToken(res.accessToken);
    const token = jwtDecode(res.accessToken);
    setJWPlayerBandwidth();
    const user = res.user;
    if (user) {
      const videoClosedCaption = get(user, 'accounts[0].videoClosedCaption', false);
      const language = user.language ? user.language.toLowerCase() : 'en';
      setJWPlayerLanguage(language, videoClosedCaption);
    }
    setData(res);
    const sessionTimeout = get(user, 'accounts[0].sessionTimeout');
    const hours = sessionTimeout ? (sessionTimeout / (60 * 60)) : LOGIN_SESSION_TIME_OUT;
    setAuthSession(hours);
    const isJettClient = get(user, 'accounts[0].isJettClient', true);
    if (!isJettClient) {
      authedNavigateToAI(user);
      return false;
    }
    setTimerOnExpiration(token.exp);
    return res;
  }

  async function setTimerOnExpiration(exp, isScorm = false) {
    const getIsAuthSession = getCookie(`${subdomainName}_isAuthSession`);
    const timestamp = Math.floor(Date.now() / 1000); // in seconds
    const expiry = getIsAuthSession || exp;
    const secondsFromNow = expiry - timestamp;
    clearTokenTimers();
    tokenTimer = setTimeout(() => {
      tokenTimer = null;
      logout();
      deleteCookie();
    }, secondsFromNow * 1000);
    // Another option is to have an interval timer check for the validity of the token.
    tokenIntervalTimer = setInterval(() => {
      const validToken = checkTokenValid();
      const getRefreshSession = getCookie(`${subdomainName}_refreshSession`);
      const isAuthSession = getCookie(`${subdomainName}_isAuthSession`);
      const currentTimestamp = Math.floor(Date.now() / 1000); // in seconds
      const refreshTokenExp = getRefreshSession || currentTimestamp;
      if (!isScorm && (!isAuthSession || !validToken)) {
        logout();
        deleteCookie();
      } else if (!isScorm && (!getRefreshSession || refreshTokenExp < currentTimestamp)) {
        refreshToken();
      }
    }, 15000); // Every 15 seconds -check if token expired or not, but shouldn't hurt anything.
  }

  function checkManageAccess(accountRoleId) {
    return accountRoleId === 3 || accountRoleId === 4 || accountRoleId === 5 ||
      accountRoleId === 6 || accountRoleId === 8 || accountRoleId >= 10;
  }

  const getTableauJwtToken = async () => {
    const token = await tableauToken();
    return token;
  };

  return {
    data,
    login,
    logout,
    clearTokens,
    validatingJwt,
    updateUserData,
    verifyScorm,
    verifySaml,
    fetchJettSSOToken,
    verifyJettSSO,
    verifyContentPreview,
    verifyAndResetPassword,
    authedNavigateToAI,
    googleLogin,
    linkedinLogin,
    microsoftLogin,
    getTableauJwtToken,
  };
}

export function AuthProvider({ children }) {
  const auth = useProvideAuth();
  return <authContext.Provider value={auth}>{children}</authContext.Provider>;
}
