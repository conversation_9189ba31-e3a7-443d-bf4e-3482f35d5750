export function useAccessibilityHelper() {
  function stripHTMLForReader(html) {
    let strippedHTML = (html && html.trim() && html.trim().replace(/(<([^>]+)>)/gi, '')) || '';
    const lastChar = strippedHTML && strippedHTML.slice(-1);
    // punctuation for screen reader
    if (strippedHTML && lastChar !== '.' && lastChar !== '?') {
      strippedHTML += '.';
    }
    return strippedHTML;
  }

  return { stripHTMLForReader };
}
