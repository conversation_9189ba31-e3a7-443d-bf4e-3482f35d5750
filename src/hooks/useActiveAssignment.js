import React, { useState, useContext, createContext } from 'react';
import { isEqual } from 'lodash';

const ActiveAssignmentContext = createContext();

export const useActiveAssignment = () => {
  return useContext(ActiveAssignmentContext);
};

function useProvideActiveAssignment() {
  const [activeAssignmentInfo, setActiveAssignmentInfo] = useState(null);

  async function setActiveAssignmentInfoFromCacheItem(currentAssignmentItem) {
    if (currentAssignmentItem.type === 'program') {
      const { uid, elapsedTimeInSeconds, minTimeInMinutes, status, percentComplete } = currentAssignmentItem.content;
      const timeRequirementMet = Math.min(minTimeInMinutes, Math.floor(elapsedTimeInSeconds / 60)) >= minTimeInMinutes;
      const programIsComplete = status === 'completed';
      const activeProgram = {
        assignmentId: currentAssignmentItem.id,
        programId: uid,
        minTimeInMinutes,
        elapsedTime: elapsedTimeInSeconds,
        status: status,
        programIsComplete,
        timeRequirementMet,
        percentComplete,
      };
      if (!isEqual(activeAssignmentInfo, activeProgram)) {
        setActiveAssignmentInfo(activeProgram);
      }
    }

    if (currentAssignmentItem.type === 'lesson') {
      const { uid, percentComplete } = currentAssignmentItem.content;
      const activeLesson = {
        assignmentId: currentAssignmentItem.id,
        lessonId: uid,
        percentComplete,
      };
      if (!isEqual(activeAssignmentInfo, activeLesson)) {
        setActiveAssignmentInfo(activeLesson);
      }
    }
  }

  return {
    activeAssignmentInfo,
    setActiveAssignmentInfoFromCacheItem,
  };
}

export function ActiveAssignmentProvider({ children }) {
  const ActiveAssignment = useProvideActiveAssignment();
  return (
    <ActiveAssignmentContext.Provider value={ActiveAssignment}>
      {children}
    </ActiveAssignmentContext.Provider>
  );
}
