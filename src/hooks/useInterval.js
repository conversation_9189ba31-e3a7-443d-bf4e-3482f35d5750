import { useEffect, useRef } from 'react';

export function useInterval(callBack, delay) {
  const savedCallback = useRef(null);
  const savedIntervalId = useRef(null);

  useEffect(() => {
    savedCallback.current = callBack;
  }, [callBack]);

  useEffect(() => {
    function tick() {
      savedCallback.current();
    }
    if (delay !== null) {
      const id = setInterval(tick, delay);
      savedIntervalId.current = id;
      return () => clearInterval(id);
    }
    // if the delay is set to null, we always need
    // to clear the existing interval timer if one exists.
    if (delay === null && savedIntervalId.current) {
      clearInterval(savedIntervalId.current);
    }
  }, [delay]);
}
