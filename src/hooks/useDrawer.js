import React, { createContext, useContext, useState } from 'react';

const MenuDrawerContext = createContext();
const AssignmentsDrawerContext = createContext();

function useProvideDrawer() {
  const [drawerState, setDrawerState] = useState('closed'); // closed, open (no subs open), expert
  // const [drawerView, setDrawerStateView] = useState('primary'); // primary, secondary

  function isOpenDrawer() {
    return drawerState !== 'closed';
  }

  function openDrawer(section = undefined) {
    const newState = section || 'open';
    setDrawerState(newState);
  }

  function toggleDrawer() {
    setDrawerState(drawerState === 'closed' ? 'open' : 'closed');
  }

  function closeDrawer() {
    setDrawerState('closed');
  }

  /* function isPrimaryView() {
    return drawerView === 'primary';
  }

  function setPrimaryView() {
    setDrawerStateView('primary');
  }

  function setSecondaryView() {
    setDrawerStateView('secondary');
  } */

  return { drawerState,
    isOpenDrawer,
    openDrawer,
    toggleDrawer,
    closeDrawer,
    // setPrimaryView,
    // setSecondaryView,
    // isPrimaryView,
  };
}

export const useMenuDrawer = () => useContext(MenuDrawerContext);
export const useAssignmentsDrawer = () => useContext(AssignmentsDrawerContext);

export const MenuDrawerProvider = (props) => (
  <MenuDrawerContext.Provider value={useProvideDrawer()} {...props} />
);

export const AssignmentsDrawerProvider = (props) => (
  <AssignmentsDrawerContext.Provider value={useProvideDrawer()} {...props} />
);
