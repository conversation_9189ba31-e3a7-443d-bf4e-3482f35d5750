/* eslint-disable max-len */
/* eslint-disable no-plusplus */
const REFRESH_TOKEN_TIME = 55; // minutes

const currentHostname = window.location.hostname;
const subdomain = currentHostname.split('.');
const domainName = '.emtrain.com';
const isCookieDomain = currentHostname.includes(domainName);

export const subdomainName = subdomain[0];

export function getCookie(name) {
  const sessionName = `${name}=`;
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i];
    while (cookie.charAt(0) === ' ') {
      cookie = cookie.substring(1, cookie.length);
    }
    if (cookie.indexOf(sessionName) === 0) {
      return cookie.substring(sessionName.length, cookie.length);
    }
  }
  return null;
}

export function deleteCookie(path = null) {
  const expiresAt = 'Thu, 01 Jan 1970 00:00:00 GMT';
  document.cookie = `${encodeURIComponent(`${subdomainName}_refreshSession`)}=; expires=${expiresAt};${isCookieDomain ? ` domain=${domainName};` : ''}${path ? ` path=${path};` : ' path=/;'} SameSite=Strict; Secure`;
  document.cookie = `${encodeURIComponent(`${subdomainName}_isAuthSession`)}=; expires=${expiresAt};${isCookieDomain ? ` domain=${domainName};` : ''}${path ? ` path=${path};` : ' path=/;'} SameSite=Strict; Secure`;
}

export function setCookie(name, options = {}) {
  // Default cookie attributes
  const timestamp = Math.floor(Date.now() / 1000) + (options.expires * 60); // in seconds
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(timestamp)}`;
  // Expiry date
  if (options.expires instanceof Date) {
    cookieString += `; expires=${options.expires.toUTCString()}`;
  } else if (typeof options.expires === 'number') {
    const expires = new Date();
    expires.setTime(expires.getTime() + options.expires * 60 * 1000); // Convert seconds to milliseconds
    cookieString += `; expires=${expires.toUTCString()}`;
  }

  // Domain
  if (options.domain) {
    cookieString += `; domain=${options.domain}`;
  }

  // Path
  if (options.path) {
    cookieString += `; path=${options.path}`;
  } else {
    cookieString += '; path=/';
  }

  // Secure
  if (options.secure) {
    cookieString += '; secure';
  }

  // SameSite
  if (options.sameSite) {
    cookieString += `; SameSite=${options.sameSite}`;
  }

  document.cookie = cookieString;
}

export function setAuthSession(hours) {
  setCookie(`${subdomainName}_isAuthSession`, {
    expires: (hours * 60), // minutes
    secure: true,
    domain: isCookieDomain ? domainName : false,
    sameSite: 'Strict',
  });
  setCookie(`${subdomainName}_refreshSession`, {
    expires: REFRESH_TOKEN_TIME,
    secure: true,
    domain: isCookieDomain ? domainName : false,
    sameSite: 'Strict',
  });
}

export function refreshCookie() {
  setCookie(`${subdomainName}_refreshSession`, {
    expires: REFRESH_TOKEN_TIME,
    secure: true,
    domain: isCookieDomain ? domainName : false,
    sameSite: 'Strict',
  });
}
