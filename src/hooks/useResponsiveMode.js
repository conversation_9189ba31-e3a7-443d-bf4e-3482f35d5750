import { useTheme, useMediaQuery } from '@mui/material';

export function useResponsiveMode() {
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('desktop'));
  const isLaptop = useMediaQuery(theme.breakpoints.between('laptop', 'desktop'));
  const isTablet = useMediaQuery(theme.breakpoints.between('tablet', 'laptop'));
  const isMobile = useMediaQuery(theme.breakpoints.down('tablet'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('smallMobile'));
  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('largeDesktop'));
  const isXLLargeDesktop = useMediaQuery(theme.breakpoints.between('xl', 'largeDesktop'));
  const middleScreen = useMediaQuery(theme.breakpoints.between('middleA', 'middleB'));

  const isLargeLoginView = useMediaQuery(theme.breakpoints.up('loginScreenBreak'));
  const isAssignmentsDrawerView = useMediaQuery(theme.breakpoints.down('assignmentsDrawerBreak'));
  const isBtwDesktopAndAssignmentDrawer = useMediaQuery(theme.breakpoints.between('assignmentsDrawerBreak', 'desktop'));

  const getDevice = () => {
    let device = 'smallMobile';
    if (isDesktop) {
      device = 'desktop';
    } if (isLaptop) {
      device = 'laptop';
    } else if (isTablet) {
      device = 'tablet';
    } else if (!isSmallMobile && isMobile) {
      device = 'mobile';
    }
    return device;
  };

  return {
    getDevice,
    isSmallMobile,
    isMobile,
    isTablet,
    isLaptop,
    isDesktop,
    isLargeLoginView,
    isAssignmentsDrawerView,
    isBtwDesktopAndAssignmentDrawer,
    isLargeDesktop,
    isXLLargeDesktop,
    middleScreen,
  };
}
