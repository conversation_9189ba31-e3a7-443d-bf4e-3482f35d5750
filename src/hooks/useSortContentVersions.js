const audienceType = {
  all: 'All',
  employee: 'Non-Manager',
  manager: 'Manager',
};

export function sortVersions(versionsData, hasCustomizedMappedItems) {
  return [...versionsData].sort((a, b) => {
    if (hasCustomizedMappedItems) {
      // Customized items at the top, ordered by id (desc)
      if (a.isCustomized !== b.isCustomized) {
        return b.isCustomized - a.isCustomized;
      }

      if (a.isCustomized === 1) {
        return b.id - a.id;
      }
    }

    // Standard items: Sort by stateCode (ALL first, then alpha ascending)
    if (a.stateCode === 'ALL' && b.stateCode !== 'ALL') return -1;
    if (b.stateCode === 'ALL' && a.stateCode !== 'ALL') return 1;
    if (a.stateCode !== b.stateCode) return a.stateCode.localeCompare(b.stateCode);
    return b.audience.localeCompare(a.audience);
  });
}

export const sortedVersionsData = (allVersions = [], hasCustomizedMappedItems = false, isPriorVersions = false) => {
  const sortedVersions = sortVersions(allVersions, hasCustomizedMappedItems) || [];
  return sortedVersions.map((item) => {
    const {
      id,
      stateCode,
      audience,
      duration,
      isCustomized,
      requiredMinutes,
      minTimeInMinutes,
      type,
    } = item;

    const getDuration = () => {
      if (type === 'program') {
        // eslint-disable-next-line no-nested-ternary
        return minTimeInMinutes
          ? `(${minTimeInMinutes} min timed)`
          : duration
            ? `(${duration} min)`
            : '';
      }
      return requiredMinutes ? `(${requiredMinutes} min)` : '';
    };

    const durationString = getDuration();
    const state = stateCode && stateCode !== 'ALL' ? stateCode : '';
    const capitalizedAudience = (audienceType[audience] || '');
    const baseVersionName = `${state} ${capitalizedAudience} ${durationString} - #${id}`;

    const versionName = isCustomized === 1
      ? `Custom - ${baseVersionName}`
      : baseVersionName;
    return { ...item, listingTitle: item.title, title: isPriorVersions ? `Edited ${versionName}` : versionName };
  });
};

export const getListingLessonsORPrograms = (catalogItems = [], isScormAccount = false, isPriorVersions = false) => {
  return catalogItems.flatMap((item) => {
    const { programs: catalogPrograms, lessons: catalogLessons, ...rest } = item;
    const programItems = (catalogPrograms || []).map((program) => ({
      ...program,
      type: 'program',
      title: program.name,
      accountTitle: program.accountProgramName,
      dateAdded: program.programMappedDate,
      instructionalType: rest.instructionalType,
      edition: program.edition,
      audience: rest.audience,
      part: rest.part,
      stateCode: rest.stateCode,
      stateName: rest.stateName,
    }));

    const lessonItems = (catalogLessons || []).map((lesson) => ({
      ...lesson,
      type: 'lesson',
      accountTitle: lesson.accountLessonTitle,
      dateAdded: lesson.lessonMappedDate,
      instructionalType: rest.instructionalType,
      edition: lesson.edition,
      audience: rest.audience,
      part: rest.part,
      stateCode: rest.stateCode,
      stateName: rest.stateName,
    }));
    let contentItems = [...programItems, ...lessonItems];
    if (isPriorVersions) {
      contentItems = contentItems.filter((obj) => (isScormAccount ? obj.downloadedAt : obj.deployedDate));
    }
    return contentItems;
  });
};
