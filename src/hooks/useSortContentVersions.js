const audienceType = {
  all: 'All',
  employee: 'Non-Manager',
  manager: 'Manager',
};

export function sortVersions(versionsData, hasCustomizedMappedItems) {
  return [...versionsData].sort((a, b) => {
    if (hasCustomizedMappedItems) {
      // Customized items at the top, ordered by id (desc)
      if (a.isCustomized !== b.isCustomized) {
        return b.isCustomized - a.isCustomized;
      }

      if (a.isCustomized === 1) {
        return b.id - a.id;
      }
    }

    // Standard items: Sort by stateCode (ALL first, then alpha ascending)
    if (a.stateCode === 'ALL' && b.stateCode !== 'ALL') return -1;
    if (b.stateCode === 'ALL' && a.stateCode !== 'ALL') return 1;
    if (a.stateCode !== b.stateCode) return a.stateCode?.localeCompare(b.stateCode);
    return b.audience?.localeCompare(a.audience);
  });
}

export function pwhSortVersion(data, isListPage) {
  let filterData = data;
  if (isListPage) {
    filterData = data.filter(item => item.isTimed);
  }
  const partOrder = { a: 1, b: 2, ab: 3 };
  const cleanPart = (p) => p?.toLowerCase()?.trim() || '';
  return filterData.sort((a, b) => {

    // prioritize customized programs
    if (a.isCustomized !== b.isCustomized) {
      return b.isCustomized - a.isCustomized; // customized (1) comes first
    }
    // Sort by part
    const partA = partOrder[cleanPart(a.part)] ?? 99;
    const partB = partOrder[cleanPart(b.part)] ?? 99;
    if (partA !== partB) {
      return partA - partB;
    }
    // sort by pwhUsOrder ascending
    if (a.pwhUsOrder !== b.pwhUsOrder) {
      return a.pwhUsOrder - b.pwhUsOrder;
    }

    // if same pwhUsOrder, sort by id descending
    return b.id - a.id;
  });
}

export const sortedVersionsData = (allVersions = [], hasCustomizedMappedItems = false, isPriorVersions = false, isPWHUS = false, isListPage = true) => {
  let sortedVersions = sortVersions(allVersions, hasCustomizedMappedItems) || [];
  if (isPWHUS) {
    sortedVersions = pwhSortVersion(sortedVersions, isListPage);
  }
  return sortedVersions.map((item) => {
    const {
      id,
      stateCode,
      audience,
      duration,
      isCustomized,
      requiredMinutes,
      minTimeInMinutes,
      type,
      catalogEdition,
      part,
    } = item;

    const getDuration = () => {
      if (type === 'program') {
        // eslint-disable-next-line no-nested-ternary
        return minTimeInMinutes !== null
          ? `(${minTimeInMinutes} min timed)`
          : duration !== null
            ? `(${duration} min)`
            : '';
      }
      return requiredMinutes ? `(${requiredMinutes} min)` : '';
    };

    const durationString = getDuration();
    const state = stateCode && stateCode !== 'ALL' && !isPWHUS ? stateCode : '';
    const capitalizedAudience = !isPWHUS ? (audienceType[audience] || '') : '';
    let baseVersionName = `${state} ${capitalizedAudience} ${durationString}`;
    if (isPWHUS && part && part !== undefined && part !== null) {
      baseVersionName = `${baseVersionName} Part ${part.toUpperCase()}`;
    }
    baseVersionName = `${baseVersionName} - #${id}`;
    const catalogItemEdition = catalogEdition ? `Edition ${catalogEdition} ` : '';
    const versionName = (isCustomized === 1 || isCustomized === true)
      ? `Custom - ${baseVersionName}`
      : baseVersionName;

    let versionTitle = isPriorVersions ? `Edition ${catalogEdition} ${versionName.trim()}` : versionName.trim();
    if (isPWHUS) {
      versionTitle = (isCustomized === 1 || isCustomized === true)
        ? `Custom - ${catalogItemEdition}${baseVersionName}`
        : `${catalogItemEdition}${baseVersionName}`;
    }
    const subMenu = isCustomized === 1 || isCustomized === true ? 'custom' : 'edition';

    return { ...item, listingTitle: item.title, title: versionTitle, subMenu };
  });
};

export const getListingLessonsORPrograms = (catalogItems = [], isScormAccount = false, isPriorVersions = false) => {
  return catalogItems.flatMap((item) => {
    const { programs: catalogPrograms, lessons: catalogLessons, ...rest } = item;
    const programItems = (catalogPrograms || []).map((program) => ({
      ...program,
      type: 'program',
      title: program.name,
      accountTitle: program.accountProgramName,
      dateAdded: program.programMappedDate,
      instructionalType: rest.instructionalType,
      edition: program.edition,
      audience: rest.audience,
      part: rest.part,
      stateCode: rest.stateCode,
      stateName: rest.stateName,
      pwhUsOrder: rest.pwhUsOrder,
      catalogEdition: rest.edition,
    }));
    const lessonItems = (catalogLessons || []).map((lesson) => ({
      ...lesson,
      type: 'lesson',
      accountTitle: lesson.accountLessonTitle,
      dateAdded: lesson.lessonMappedDate,
      instructionalType: rest.instructionalType,
      edition: lesson.edition,
      audience: rest.audience,
      part: rest.part,
      stateCode: rest.stateCode,
      stateName: rest.stateName,
      pwhUsOrder: rest.pwhUsOrder,
      catalogEdition: rest.edition,
    }));
    let contentItems = [...programItems, ...lessonItems];
    if (isPriorVersions) {
      contentItems = contentItems.filter((obj) => (isScormAccount ? obj.downloadedAt : obj.deployedDate));
    }
    return contentItems;
  });
};

export const getCatalogPrograms = (catalogItems = []) => {
  return catalogItems.flatMap((item) => {
    const { catalogItem, ...rest } = item;
    const programItems = {
      ...catalogItem,
      type: 'program',
      catalogId: catalogItem.id,
      catalogName: catalogItem.title,
      id: rest.id,
      isCustomized: rest.isCustomized,
      duration: rest.duration,
      minTimeInMinutes: rest.minTimeInMinutes,
      stateCode: catalogItem?.state?.stateCode || null,
    };
    const contentItems = programItems ? [programItems] : [];
    return contentItems;
  });
};

export const getListingPrograms = (listing = []) => {
  return listing.flatMap((item) => {
    const { programs, ...rest } = item;
    const programItems = (programs || []).map((program) => ({
      listingId: rest.id,
      listingName: rest.title,
      type: 'program',
      title: program.name,
      id: program.id,
      edition: program.edition,
      build: program.build,
      isCustomized: program.isCustomized,
      minTimeInMinutes: program.minTimeInMinutes,
      duration: program.duration,
      audience: program?.catalogItem?.audience,
      part: program?.catalogItem?.part,
      stateCode: program?.catalogItem?.state?.stateCode || null,
      isClientSpecific: program?.catalogItem?.isClientSpecific,
    }));

    const contentItems = [...programItems];
    return contentItems;
  });
};

export const groupedListingORCatalogPrograms = (data) => {
  return Object.values(
    data.reduce((acc, curr) => {
      const key = curr.listingId || curr.catalogId;
      const mclType = curr.listingId ? 'listing' : 'catalog';
      if (!acc[key]) {
        acc[key] = {
          id: curr.listingId || curr.catalogId,
          title: curr.listingName || curr.catalogName,
          mclType,
          isClientSpecific: curr.isClientSpecific,
          programs: [],
        };
      }
      acc[key].programs.push({
        name: curr.title,
        id: curr.id,
      });
      return acc;
    }, {}),
  );
};

export const sortByClientSpecific = (data) => {
  return [...data].sort((a, b) => (b.clientSpecific === true) - (a.clientSpecific === true));
};

// count only timed irrespective of condensed-list or custom
export const getSpecialPWHVersionCount = (catalogData) => {
  let versionCount = 0;
  catalogData.forEach(obj => {
    obj.programs?.forEach(p => {
      if (p.isTimed) {
        versionCount += 1; // total number of timed programs
      }
    });
  });
  return versionCount;
};
