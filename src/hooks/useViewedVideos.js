import React, { useState, useContext, createContext } from 'react';

const ViewedVideosContext = createContext();

function useProvideViewedVideos() {
  const [viewedVideos, setViewedVideos] = useState(JSON.parse(window.localStorage.getItem('viewedVideos')) || []);
  // eslint-disable-next-line max-len
  const [viewedFullVideos, setViewedFullVideos] = useState(JSON.parse(window.localStorage.getItem('viewedFullVideos')) || []);

  const addViewedVideo = (videoId, showFullVideo = false) => {
    const newVids = viewedVideos;
    if (!newVids.includes(videoId)) {
      newVids.push(videoId);
    }
    setViewedVideos(newVids);
    window.localStorage.setItem('viewedVideos', JSON.stringify(newVids));
    if (showFullVideo) {
      const fullViewedVideos = viewedFullVideos;
      if (!fullViewedVideos.includes(videoId)) {
        fullViewedVideos.push(videoId);
      }
      setViewedFullVideos(fullViewedVideos);
      window.localStorage.setItem('viewedFullVideos', JSON.stringify(fullViewedVideos));
    }
  };

  return {
    viewedVideos,
    viewedFullVideos,
    addViewedVideo,
  };
}

export const useViewedVideos = () => useContext(ViewedVideosContext);

export const ViewedVideosProvider = (props) => (
  <ViewedVideosContext.Provider value={useProvideViewedVideos()} {...props} />
);
