import React, { createContext, useContext } from 'react';
import { get, set } from 'lodash';
import { useAuth } from './useAuth';

const UserContext = createContext();

export const UserProvider = (props) => {
  const user = get(useAuth(), 'data.user');
  const customLogoSrc = get(useAuth(), 'data.customLogoSrc');
  const customDarkLogoSrc = get(useAuth(), 'data.customDarkLogoSrc');
  const demographicFields = get(useAuth(), 'data.demographicFields');
  if (user) {
    set(user, 'accounts[0].customLogoSrc', customLogoSrc);
    set(user, 'accounts[0].customDarkLogoSrc', customDarkLogoSrc);
    set(user, 'accounts[0].demographicFields', demographicFields);
    // add isWorkdayCCL flag
    const isWorkdayCCL = user?.accounts?.[0]?.integrations?.some((o) => o.integrationType === 'workdayCCL') || false;
    const isScorm = user?.accounts?.[0]?.scorm || false;
    set(user, 'isWorkdayCCL', isWorkdayCCL);
    set(user, 'isScormAccount', isScorm);
  }
  return (<UserContext.Provider value={user} {...props} />);
};

export const useUser = () => useContext(UserContext);
