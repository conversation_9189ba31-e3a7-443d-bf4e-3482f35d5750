import React from 'react';

const stripTags = (text) => {
  return (text && text.replace(/(<([^>]+)>)/gi, '')) || '';
};
const formatAsNumber = (num) => {
  return Number(parseFloat(num).toFixed(0)) || 0;
};

export const formatScreenReaderText = (text) => {
  let newText = text && stripTags(text.trim());
  if (newText && !['.', '?', '!'].includes[newText.slice(-1)]) {
    newText += '.'; // punctuation for screen reader
  }
  return newText;
};

export const WCSResultsText = (props) => {
  const { t, hideOrg, organizationData, globalData, legendBarData } = props;
  const selected = t('general.selected');
  const text = [];
  // Possible answers are Green, Yellow, Orange and Red
  text.push(`${t('lessonCards.wcs_possible_answers')} `);

  const responseItem = organizationData && organizationData.filter((item) => item.myAnswer === true);
  const response = responseItem && responseItem[0] && responseItem[0].x;

  const correctItem = globalData.filter((item) => item.correctAnswer === true);
  const correctResponse = correctItem && correctItem[0] && correctItem[0].x;

  const userAnswer = response && legendBarData[response - 1].color;
  const correctAnswer = correctResponse && legendBarData[correctResponse - 1].color;

  // You selected Green.
  let userText = `${t('lessonCards.wcs_user_answer', { userAnswer })} `;
  if (correctAnswer) {
    // The Correct Answer is Green.
    userText += `${t('lessonCards.wcs_correct_answer', { correctAnswer })} `;
  }
  text.push(userText);

  // Responses from the people in your company are as follows:
  const orgResonsesText = t('lessonCards.org_responses');
  // Responses from all Emtrain customers are as follows:
  const globalResonsesText = t('lessonCards.global_responses');

  if (!hideOrg && organizationData) {
    text.push(orgResonsesText);
    if (organizationData) {
      organizationData.forEach((item) => {
        const perc = formatAsNumber(item.y);
        const ans = formatAsNumber(item.x);
        if (perc > 0) {
          // 50% selected Green. 50% selected Red...
          const itemText = ` ${perc}% ${selected} ${legendBarData[ans - 1].color}.`;
          text.push(itemText);
        }
      });
    }
  }

  if (globalData) {
    text.push(globalResonsesText);
    globalData.forEach((item) => {
      const perc = formatAsNumber(item.y);
      const ans = formatAsNumber(item.x);
      if (perc > 0) {
        // 50% selected Green. 50% selected Red...
        const itemText = ` ${perc}% ${selected} ${legendBarData[ans - 1].color}.`;
        text.push(itemText);
      }
    });
  }

  const screenReaderText = `<p>${text.join('</p><p>')}</p>`;

  return (
    // eslint-disable-next-line react/no-danger
    <div className="srOnly" dangerouslySetInnerHTML={{ __html: screenReaderText }} />
  );
};

export const SliderResultsText = (props) => {
  const { t, sliderMax, labelText, answer, hideOrg, organizationData, globalData } = props;
  const text = [];

  const possibleAnswers
    // eslint-disable-next-line max-len
    = t('lessonCards.slider_possible_answers', { sliderMax, notAtAllText: labelText[0], veryText: labelText[sliderMax - 1] });
  // Posible anssers are between...
  text.push(`${possibleAnswers} `);

  const youSelected =
  t('lessonCards.slider_you_selected', { answer: answer.sliderAnswer, answerText: labelText[answer.sliderAnswer - 1] });
  // You selelected...
  text.push(youSelected);

  const answered = t('qa.answered');
  // Responses from the people in your company are as follows:
  const orgResonsesText = t('lessonCards.org_responses');
  // Responses from all Emtrain customers are as follows:
  const globalResonsesText = t('lessonCards.global_responses');

  if (!hideOrg && organizationData && organizationData.percentages && organizationData.percentages.length > 0) {
    text.push(orgResonsesText);
    organizationData.percentages.forEach((item) => {
      const perc = formatAsNumber(item.percentage);
      const res = formatAsNumber(item.response);
      if (perc > 0) {
        // 10% Answered Not At All Important, 10% Answered Not Important...
        const itemText = `${perc}% ${answered} ${labelText[res - 1]}. `;
        text.push(itemText);
      }
    });
  }

  if (globalData && globalData.percentages && globalData.percentages.length > 0) {
    text.push(globalResonsesText);
    globalData.percentages.forEach((item) => {
      const perc = formatAsNumber(item.percentage);
      const res = formatAsNumber(item.response);
      if (perc > 0) {
        // 10% Answered Not At All Important... 10% Answered Not Important...
        const itemText = `${perc}% ${answered} ${labelText[res - 1]}. `;
        text.push(itemText);
      }
    });
  }

  const screenReaderText = `<p>${text.join('</p><p>')}</p>`;

  return (
    // eslint-disable-next-line react/no-danger
    <div className="srOnly" dangerouslySetInnerHTML={{ __html: screenReaderText }} />
  );
};
