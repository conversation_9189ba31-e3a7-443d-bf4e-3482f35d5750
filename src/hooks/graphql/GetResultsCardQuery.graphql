query GetResultsCardQuery($lessonId: ID!, $lessonCardId: ID!, $campaignId: ID, $lessonLifecycle: String) {
  getResultsCard(
    lessonId: $lessonId,
    lessonCardId: $lessonCardId,
    campaignId: $campaignId
    lessonLifecycle: $lessonLifecycle
  ) {
    type
    organization {
      ... on BooleanQuizReport {
        booleanReportData {
          total
          percentage
        }
      }
      ... on SingleChoiceQuizReport {
        singleChoiceReportData {
          total
          percentages
        }
      }
      ... on MultiChoiceQuizReport {
        multiChoiceReportData {
          total
          percentages
        }
      }
      ... on WordCloudReport {
        freeFormReportData {
          text
          value
        }
      }
      ... on ColorSpectrumQuizReport {
        colorSpectrumReportData {
          total
          percentages {
            response
            percentage
          }
        }
      }
      ... on SliderQuizReport {
        sliderReportData {
          total
          percentages {
            response
            percentage
          }
        }
      }
    }
    global {
      ... on BooleanQuizReport {
        booleanReportData {
          total
          percentage
        }
      }
      ... on SingleChoiceQuizReport {
        singleChoiceReportData {
          total
          percentages
        }
      }
      ... on MultiChoiceQuizReport {
        multiChoiceReportData {
          total
          percentages
        }
      }
      ... on WordCloudReport {
        freeFormReportData {
          text
          value
        }
      }
      ... on ColorSpectrumQuizReport {
        colorSpectrumReportData {
          total
          percentages {
            response
            percentage
          }
        }
      }
      ... on SliderQuizReport {
        sliderReportData {
          total
          percentages {
            response
            percentage
          }
        }
      }
    }
  }
}
