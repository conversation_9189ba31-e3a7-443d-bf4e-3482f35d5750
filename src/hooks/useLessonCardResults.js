import { useApolloClient, useQuery } from '@apollo/client';
import { useRouteMatch } from 'react-router';

import AssignmentFragment from '../features/assignment/AssignmentFragment.graphql';
import GetResultsCardQuery from './graphql/GetResultsCardQuery.graphql';

export function useLessonCardResults({ lessonCardId, lessonLifecycle }) {
  const client = useApolloClient();
  const { params: { lessonId, assignmentId } } = useRouteMatch();

  const { campaignId } = client.readFragment({
    id: `AssignmentItem:${assignmentId}`,
    fragment: AssignmentFragment,
  });

  const { loading, error, data } = useQuery(GetResultsCardQuery, {
    variables: {
      lessonId,
      lessonCardId,
      campaignId,
      lessonLifecycle,
    },
  });

  return {
    loading,
    error,
    data,
  };
}
