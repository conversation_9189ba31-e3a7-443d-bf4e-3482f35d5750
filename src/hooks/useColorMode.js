import React, { useCallback, useState, useContext, createContext } from 'react';
import { LIGHT_MODE, DARK_MODE } from '../theme/themeNames';

const ColorModeContext = createContext();

function useProvideColorMode() {
  const [mode, setMode] = useState(LIGHT_MODE);

  const toggleColorMode = useCallback(() => {
    setMode((prevMode) => (prevMode === LIGHT_MODE ? DARK_MODE : LIGHT_MODE));
  }, []);

  return { mode, toggleColorMode };
}

export const useColorMode = () => useContext(ColorModeContext);

export const ColorModeProvider = (props) => (
  <ColorModeContext.Provider value={useProvideColorMode()} {...props} />
);
