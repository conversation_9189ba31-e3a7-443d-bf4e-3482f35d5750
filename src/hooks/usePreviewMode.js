import React, { useState, useContext, createContext } from 'react';
import { getIsPreviewModeEnable } from '../services/api/authentication';

const PreviewModeContext = createContext();

function useProvidePreviewMode() {
  const previewModeEnable = getIsPreviewModeEnable();
  const previewFlag = previewModeEnable ? true : false;
  const previewData = previewModeEnable ? JSON.parse(previewModeEnable) : null;
  const [previewMode, setPreviewMode] = useState(previewFlag);
  const [previewAssignmentData, setPreviewAssignmentData] = useState(previewData);

  return { previewMode, setPreviewMode, previewAssignmentData, setPreviewAssignmentData };
}

export const usePreviewMode = () => useContext(PreviewModeContext);

export const PreviewModeProvider = (props) => (
  <PreviewModeContext.Provider value={useProvidePreviewMode()} {...props} />
);
