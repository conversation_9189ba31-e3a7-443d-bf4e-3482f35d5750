import React, { useState, useContext, createContext } from 'react';

const SpeechAudioContext = createContext();

function useProvideSpeechAudio() {
  const [audioObject, setAudioObject] = useState(null);
  const [popupAudioObject, setPopupAudioObject] = useState(null);
  const [audioPlaying, setAudioPlaying] = useState(false);
  const [popupAudioPlaying, setPopupAudioPlaying] = useState(false);
  const [popupOpen, setPopupOpen] = useState(false);

  /*
  * .play() returns a promise
  * .pause() does not return a promise
  * https://developer.chrome.com/blog/play-request-was-interrupted/
  */
  const playAudio = (audio) => {
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise.then(() => {
        setAudioPlaying(true);
      })
        // eslint-disable-next-line no-unused-vars
        .catch((e) => {
          // just capturing the Chrome error - it serves no purpose
          // Uncaught (in promise) DOMException: The play() request was interrupted by a call to pause()
        });
    }
  };

  const playCardAudio = () => {
    if (popupAudioObject && !popupAudioObject.paused) {
      popupAudioObject.pause();
    }
    if (audioObject && audioObject.paused) {
      playAudio(audioObject);
    }
  };

  const pauseCardAudio = () => {
    if (audioObject && !audioObject.paused) {
      audioObject.pause();
      setAudioPlaying(false);
    }
  };

  const playPopupAudio = () => {
    if (audioObject && !audioObject.paused) {
      audioObject.pause();
    }
    if (popupAudioObject && popupAudioObject.paused) {
      playAudio(popupAudioObject);
    }
  };

  const pausePopupAudio = () => {
    if (popupAudioObject && !popupAudioObject.paused) {
      popupAudioObject.pause();
      setAudioPlaying(false);
    }
  };

  // eslint-disable-next-line max-len
  return {
    playAudio,
    playCardAudio,
    pauseCardAudio,
    playPopupAudio,
    pausePopupAudio,
    audioObject,
    setAudioObject,
    popupAudioObject,
    setPopupAudioObject,
    audioPlaying,
    setAudioPlaying,
    popupAudioPlaying,
    setPopupAudioPlaying,
    popupOpen,
    setPopupOpen,
  };
}

export const useSpeechAudio = () => useContext(SpeechAudioContext);

export const SpeechAudioProvider = (props) => (
  <SpeechAudioContext.Provider value={useProvideSpeechAudio()} {...props} />
);
