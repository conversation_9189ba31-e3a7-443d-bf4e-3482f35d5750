/* eslint-disable max-len */
import React, { useState, useContext, createContext } from 'react';
import { trainingTiles } from '../services/api/contentLibrary';

const contentLibraryContext = createContext();

export const useContentLibrary = () => {
  return useContext(contentLibraryContext);
};

function useProvideContentLibrary() {
  const initialFilters = {
    pillarId: null,
    searchType: 'Keyword',
    contentId: null,
    title: null,
    concepts: [],
    indicators: [],
    trainingType: [],
    location: [],
    audience: [],
    sortBy: 'popular',
    isCustomized: false,
    selectedFilterPills: [],
  };
  const [tilesLoading, setTilesLoading] = useState(false);
  const initialTrainingTiles = { total: 0, skip: 0, limit: 15, data: [] };
  const [filters, setFilters] = useState(initialFilters);
  const [tiles, setTiles] = useState(initialTrainingTiles);

  async function clearTrainingTiles() {
    return new Promise((resolve) => {
      setTiles(initialTrainingTiles);
      resolve();
    });
  }

  async function contentFilters(action, value, selectedPillObject) {
    const searchFilter = { title: null, contentId: null };
    const sidebarFilter = { concepts: [],
      indicators: [],
      trainingType: [],
      location: [],
      audience: [],
      isCustomized: false,
      selectedFilterPills: [] };
    let selectedFilters = [];
    if (filters?.selectedFilterPills.some((item) => (item.value === selectedPillObject?.value && item.type === action))) {
      selectedFilters = filters?.selectedFilterPills.filter((item) => (item.value !== selectedPillObject.value || item.type !== selectedPillObject.type));
    } else {
      selectedFilters = selectedPillObject ? [...filters?.selectedFilterPills, selectedPillObject] : filters?.selectedFilterPills;
    }
    setFilters((prevFilters) => {
      switch (action) {
        case 'search':
          return { ...prevFilters, searchType: value, ...searchFilter, ...sidebarFilter };
        case 'Keyword':
          return { ...prevFilters, title: value, contentId: null, pillarId: null, ...sidebarFilter };
        case 'Content ID':
          return { ...prevFilters, title: null, contentId: Number(value), pillarId: null, ...sidebarFilter };
        case 'clearSearch':
          return { ...prevFilters, ...searchFilter, ...sidebarFilter };
        case 'clearAll':
          return { ...prevFilters, ...sidebarFilter };
        case 'pillar':
          return { ...prevFilters, ...searchFilter, pillarId: value, ...sidebarFilter };
        case 'sortBy':
          return { ...prevFilters, sortBy: value };
        case 'trainingType':
        case 'location':
        case 'audience':
        case 'concepts':
        case 'indicators':
        case 'isCustomized':
          return { ...prevFilters, [action]: value, selectedFilterPills: selectedFilters };
        default:
          return { ...prevFilters, ...searchFilter };
      }
    });
  }

  async function contentTrainingTiles(params) {
    setTilesLoading(true);
    try {
      const response = await trainingTiles(params);
      if (params.contentId) { // contentId search
        const data = [];
        if (response.lesson) {
          data.push({ type: response.lesson?.isStandalone ? 'microlesson' : 'lesson',
            ...response.lesson,
            isCustom: response.lesson?.isCustomized,
            filePath: response.lesson.files?.path });
        }
        if (response.program) {
          data.push({ type: 'course',
            ...response.program,
            title: response.program?.name,
            isCustom: response.program?.isCustomized,
            filePath: response.program.file?.path });
        }
        setTiles({ total: data.length, limit: 0, skip: 0, data, isCustomTrainings: response.isCustomTraining });
      } else {
        setTiles((prevTiles) => ({
          total: response.total,
          limit: response.limit,
          skip: response.skip,
          isCustomTrainings: response.isCustomTraining,
          data: response.skip === 0 ? (response.data || []) : [...prevTiles.data, ...response.data],
        }));
      }
      setTilesLoading(false);
    } catch (e) {
      setTilesLoading(false);
      console.error(e);
    }
  }

  return {
    tilesLoading,
    tiles,
    filters,
    contentTrainingTiles,
    clearTrainingTiles,
    contentFilters,
  };
}

export function ContentLibraryProvider({ children }) {
  const contentLibraryData = useProvideContentLibrary();
  return (
    <contentLibraryContext.Provider value={contentLibraryData}>
      {children}
    </contentLibraryContext.Provider>
  );
}
