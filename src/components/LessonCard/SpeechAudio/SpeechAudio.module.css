.playerContainer {
  position: absolute;
  top: 0.17rem;
  right: -3.07rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: space-between;
  z-index: 10;
  width: 3rem;
  background: #FCFCFC;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border: 1px solid #cbccd9;
  border-left: none;
  padding: 0.2rem 0 0.25rem 0;
  margin: 0;
}

.playerContainerPopup {
  right: -3.31rem;
  top: 0.5rem;
}

.audioLinesImage {
  height: 2.1rem;
  width: 2.1rem; 
  margin: 0.2rem 0 0.19rem 0.45rem;
}

.playButtonImage {
  height: 2.1rem;
  width: 2.1rem;
}

.volumeButtonImage {
  height: 2.1rem;
  width: 2.1rem; 
}

.rateButtonImage {
  position: absolute;
  z-index: -1;
  height: 2.1rem;
  width: 2.1rem; 
}

.rateText {
  position: absolute;
  left: -0.2rem;
  width: 3rem;
  z-index: 1;
  text-align: center;
}

.icon {
  height: 1rem;
  width: 1rem;;
}

.playbackSpeedContainer {
  position: absolute;
  z-index: 20;
  background: #FCFCFC;
  border-radius: 5px;
  border: 1px solid #cbccd9;
  padding: 0.5rem;
  width: 8.6rem;
  left: -8.6rem;
  top: -0.34rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.volumeContainer {
  position: absolute;
  z-index: 20;
  background: #FCFCFC;
  border-radius: 5px;
  border: 1px solid #cbccd9;
  padding: 0 0.5rem;
  width: 8.6rem;
  left: -8.6rem;
  top: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 0.6em;
}

