import React, { useEffect, useState, useRef } from 'react';
import { useTheme, Button, List, ListItem, FormControl, InputLabel, Select, MenuItem, Slider } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useSpeechAudio } from '../../../hooks/useSpeechAudio';

import { setLocalStorageVolume, getLocalStorageVolume,
  setLocalStorageRate, getLocalStorageRate,
  setLocalStorageAudioNarration } from '../../../services/speechAudioSettings';

import pauseBtn from '../../../images/pause.png';
import pauseBtnHover from '../../../images/pause-hover.png';
import playBtn from '../../../images/play.png';
import playBtnHover from '../../../images/play-hover.png';
import volumeImg from '../../../images/volume.png';
import playbackOutline from '../../../images/playback-outline.png';
import movingAudioLines from '../../../images/moving-audio-lines.gif';
import staticAudioLines from '../../../images/static-audio-lines.gif';

import styles from './SpeechAudio.module.css';

export default function SpeechAudio({ audioURL, popupAudioURL, open }) {
  const { palette } = useTheme();
  const { t } = useTranslation();

  const { audioObject, setAudioObject, setPopupAudioObject, audioPlaying, setAudioPlaying, playAudio,
    playCardAudio, pauseCardAudio, pausePopupAudio, popupOpen } = useSpeechAudio();

  const [volume, setVolume] = useState(getLocalStorageVolume() || 50);
  const [showVolume, setShowVolume] = useState(false);
  const [showPlaybackRate, setShowPlaybackRate] = useState(false);
  const [playBtnImg, setPlayBtnImg] = useState(playBtn);
  const [pauseBtnImg, setPauseBtnImg] = useState(pauseBtn);
  const [audioReadCompleted, setAudioReadCompleted] = useState(false);

  const [isAudioPause, setIsAudioPause] = useState(JSON.parse(window.localStorage.getItem('audioPause')) || false);
  const savedAudioRef = useRef(null);
  const volumeButtonRef = useRef(null);
  const playbackRateButtonRef = useRef(null);

  // this effect is equivalent to an unmount function to stop audio when moving to a new card.
  useEffect(() => {
    return () => {
      if (savedAudioRef && savedAudioRef.current) {
        savedAudioRef.current.pause();
        setAudioPlaying(false);
        savedAudioRef.current.removeEventListener('ended', () => setAudioPlaying(false));
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [savedAudioRef]);

  // card audio
  useEffect(() => {
    if (!open && audioURL && !popupOpen) {
      if (audioObject && audioObject.src === audioURL && !audioReadCompleted) {
        playAudio(audioObject);
        savedAudioRef.current = audioObject;
      }
      if (!audioObject || audioObject.src !== audioURL) {
        if (audioObject) {
          audioObject.pause();
        }
        if (savedAudioRef.current) {
          savedAudioRef.current.pause();
        }
        const newAudioObject = new Audio(audioURL);
        const rate = getLocalStorageRate();
        newAudioObject.volume = volume / 100;
        newAudioObject.playbackRate = rate / 100;
        playAudio(newAudioObject);
        newAudioObject.addEventListener('ended', () => {
          setAudioPlaying(false);
          setAudioReadCompleted(true);
        });
        savedAudioRef.current = newAudioObject;
        setAudioObject(newAudioObject);
      }
      if (isAudioPause) {
        pauseCardAudio();
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioURL, audioObject, open, popupOpen]);

  // popup audio
  useEffect(() => {
    if (open && popupAudioURL) {
      pauseCardAudio();

      const popAudio = new Audio(popupAudioURL);
      const rate = getLocalStorageRate();
      popAudio.volume = volume / 100;
      popAudio.playbackRate = rate / 100;
      popAudio.play();
      setAudioPlaying(true);
      popAudio.addEventListener('ended', () => setAudioPlaying(false));
      setPopupAudioObject(popAudio);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [popupAudioURL, open]);

  // stop popup audio playing on close
  useEffect(() => {
    if (popupAudioURL && !open) {
      pausePopupAudio();
    }
    if (isAudioPause) {
      setAudioPlaying(false);
    }
  }, [popupAudioURL, open, pausePopupAudio, isAudioPause, setAudioPlaying]);

  const toggleAudio = () => {
    setShowVolume(false);
    setShowPlaybackRate(false);
    if (audioPlaying) {
      pauseCardAudio();
      setLocalStorageAudioNarration(true);
      setIsAudioPause(true);
    } else {
      playCardAudio();
      setLocalStorageAudioNarration(false);
      setIsAudioPause(false);
    }
  };

  const handleSetVolume = (e) => {
    e.preventDefault();
    setVolume(e.target.value);
    setLocalStorageVolume(e.target.value);
    audioObject.volume = e.target.value / 100;
  };

  const handleSetPlaybackRate = (e) => {
    e.preventDefault();
    setLocalStorageRate(e.target.value);
    audioObject.playbackRate = e.target.value / 100;
  };

  const handleRefFocus = (ref) => {
    setTimeout(() => {
      if (ref && ref.current) {
        ref.current.focus();
      }
    }, 100);
  };

  const rate = getLocalStorageRate();
  const rates = { 25: '.25x', 50: '.5x', 75: '.75x', 100: '1x', 125: '1.25x', 150: '1.5x', 175: '1.75x', 200: '2x' };

  return (
    <>
      {audioURL && audioObject && (
        <div className={`${styles.playerContainer} ${popupAudioURL ? styles.playerContainerPopup : ''}`}>
          <List
            dense
            disablePadding
            id="audioNarrationControls"
          >
            {/* audio playing lines */}
            <ListItem
              dense
              disablePadding
              disableGutters
            >
              <img
                src={movingAudioLines}
                style={{ display: audioPlaying ? '' : 'none' }}
                className={styles.audioLinesImage}
                alt={t('speech_audio.playing')}
                tabIndex="-1"
              />
              <img
                src={staticAudioLines}
                style={{ display: audioPlaying ? 'none' : '' }}
                className={styles.audioLinesImage}
                alt={t('speech_audio.not_playing')}
                tabIndex="-1"
              />
            </ListItem>
            {/* play/pause */}
            <ListItem
              dense
              disablePadding
              disableGutters
            >
              <Button
                tabIndex={0}
                aria-describedby="play_pause_button" // TODO
                onClick={() => toggleAudio()}
                onMouseEnter={() => (audioPlaying ? setPauseBtnImg(pauseBtnHover) : setPlayBtnImg(playBtnHover))}
                onMouseLeave={() => (audioPlaying ? setPauseBtnImg(pauseBtn) : setPlayBtnImg(playBtn))}
                disableRipple
                sx={{
                  width: '3rem !important',
                  height: '3rem !important',
                  border: '3px solid transparent',
                  marginTop: '-0.3rem !important',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                  '&:focus': {
                    border: `solid ${palette.card.backgroundColor} 3px`,
                  },
                }}
              >
                <img
                  id="play_pause_button"
                  src={audioPlaying ? pauseBtnImg : playBtnImg}
                  className={styles.playButtonImage}
                  alt={t('speech_audio.play_pause')} // TODO
                />
              </Button>
            </ListItem>
            {/* volume */}
            <ListItem
              dense
              disablePadding
              disableGutters
            >
              <Button
                ref={volumeButtonRef}
                tabIndex={0}
                onClick={() => { setShowVolume(!showVolume); setShowPlaybackRate(false); }}
                disableRipple
                sx={{
                  width: '3rem !important',
                  height: '3rem !important',
                  border: '3px solid transparent',
                  marginTop: '-0.3rem !important',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                  '&:focus': {
                    border: `solid ${palette.card.backgroundColor} 3px`,
                  },
                }}
                aria-haspopup="true"
                aria-controls="volume-slider"
                aria-expanded={showVolume}
              >
                <img
                  src={volumeImg}
                  className={styles.volumeButtonImage}
                  alt={t('speech_audio.volume')}
                />
                <div className="srOnly">{volume}</div>
              </Button>
              {showVolume && (
                <div className={styles.volumeContainer}>
                  <Slider
                    size="small"
                    value={volume}
                    step={5}
                    aria-label={t('speech_audio.volume_slider')} // TODO
                    id="volume-slider"
                    data-cy="volume-slider"
                    valueLabelDisplay="auto"
                    onChange={(e) => handleSetVolume(e)}
                    onClick={() => { setShowVolume(false); handleRefFocus(volumeButtonRef); }}
                    onKeyDown={(e) => {
                      if (e.code === 'Enter' || e.code === 'Space') {
                        e.preventDefault();
                        setShowVolume(false);
                        handleRefFocus(volumeButtonRef);
                      }
                    }}
                  />
                </div>
              )}
            </ListItem>
            {/* playback speed */}
            <ListItem
              dense
              disablePadding
              disableGutters
            >
              <Button
                ref={playbackRateButtonRef}
                id="playback-rate-button"
                tabIndex={0}
                onClick={() => { setShowPlaybackRate(!showPlaybackRate); setShowVolume(false); }}
                disableRipple
                sx={{
                  width: '3rem !important',
                  height: '3rem !important',
                  border: '3px solid transparent',
                  marginTop: '-0.3rem !important',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                  '&:focus': {
                    border: `solid ${palette.card.backgroundColor} 3px`,
                  },
                  textTransform: 'none !important',
                  fontSize: '0.75rem !important',
                  color: palette.primary.greyPurple,
                }}
                aria-haspopup="true"
                aria-controls="playback-rate-menu"
                aria-expanded={showPlaybackRate}
              >
                <img
                  src={playbackOutline}
                  className={styles.rateButtonImage}
                  alt={t('speech_audio.playback_rate')}
                />
                <div className={styles.rateText}>
                  {rate ? rates[rate] : ''}
                </div>
              </Button>
              {showPlaybackRate && (
                <div className={styles.playbackSpeedContainer}>
                  <FormControl sx={{ m: '0.2rem', minWidth: '7rem' }} size="small">
                    <InputLabel id="playback-rate-label">{t('speech_audio.playback_rate')}</InputLabel>
                    <Select
                      labelId="playback-rate-label"
                      id="playback-rate-menu"
                      inputProps={{ 'data-cy': 'playback-speed-select' }}
                      value={rate}
                      label={t('lesson_cards.playback_rate')}
                      onChange={(e) => {
                        handleSetPlaybackRate(e);
                        setShowPlaybackRate(false);
                        handleRefFocus(playbackRateButtonRef);
                      }}
                    >
                      <MenuItem tabIndex={0} dense key=".25x" value="25" data-cy=".25x">.25x</MenuItem>
                      <MenuItem tabIndex={0} dense key=".5x" value="50" data-cy=".50x">.5x</MenuItem>
                      <MenuItem tabIndex={0} dense key=".75x" value="75" data-cy=".75x">.75x</MenuItem>
                      <MenuItem tabIndex={0} dense key="1x" value="100" data-cy="1x">1x</MenuItem>
                      <MenuItem tabIndex={0} dense key="1.25x" value="125" data-cy="1.25x">1.25x</MenuItem>
                      <MenuItem tabIndex={0} dense key="1.5x" value="150" data-cy="1.50x">1.5x</MenuItem>
                      <MenuItem tabIndex={0} dense key="1.75x" value="175" data-cy="1.75x">1.75x</MenuItem>
                      <MenuItem tabIndex={0} dense key="2x" value="200" data-cy="1.75x">2x</MenuItem>
                    </Select>
                  </FormControl>
                </div>
              )}
            </ListItem>
          </List>
        </div>
      )}
    </>
  );
}
