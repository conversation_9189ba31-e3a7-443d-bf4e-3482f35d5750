.container {
  border: solid 1px #cbccd9;
  border-radius: 5px;
  padding: 1.75rem;
  padding-bottom: 3.5rem;
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: 1.5rem;
  z-index: 1;
  /* aspect-ratio: auto 1 / 1.228; */
  /* overflow-x: clip; */
}

.containerAspectRatio {
  aspect-ratio: auto 1 / 1.228;
}

.container h3 {
  margin: 0;
  font-size: 1.75rem;
}

.footer {
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0;
  display: flex;
  justify-content: space-between;
  padding: 0.35rem 1rem;
  align-items: center;
  font-size: 1rem;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  overflow: hidden;
}

.footerNumber {
  font-size: 0.75rem;
  align-self: flex-end;
}