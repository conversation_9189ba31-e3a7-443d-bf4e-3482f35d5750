import React, { useRef, useEffect, useState } from 'react';
import { Paper, useTheme } from '@mui/material';
import { get } from 'lodash';
import { useRouteMatch } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../hooks/useUser';
import { useActiveAssignment } from '../../hooks/useActiveAssignment';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import { cardRequiresUserResponse, isEndOfLessonCard, isPolicyCard } from '../../features/lessons/lessonCardUtils';
import HiddenAlert from '../../features/hidden-alert/HiddenAlert';

import styles from './LessonCard.module.css';
import QuizCardRibbon from './QuizCardRibbon/QuizCardRibbon';
import EndOfLessonCardRibbon from './EndOfLessonCardRibbon/EndOfLessonCardRibbon';
import SpeechAudio from './SpeechAudio/SpeechAudio';
import AudioButton from '../Button/AudioButton';

export default function LessonCard(props) {
  const { children, type, isResultCard, currentCard, lessonLength,
    showCompletionCertificate, speechAudioURL, gatedFeedbackSpeechAudioURL,
    insights, lessonCardId, microLessonId, translationLanguage } = props;

  const user = useUser();
  const { palette } = useTheme();
  const { activeAssignmentInfo } = useActiveAssignment();
  const { isSmallMobile } = useResponsiveMode();
  const screenReaderFocusRef = useRef(null);
  const { params: { programId, lessonId, cardId } } = useRouteMatch();
  const { t } = useTranslation();

  const [screenReaderLessonCardText, setScreenReaderLessonCardText] = useState(null);

  const hideRibbon = !!get(activeAssignmentInfo, 'minTimeInMinutes') &&
    !get(activeAssignmentInfo, 'timeRequirementMet');

  /* TEXT_OVERLAY_WITH_IMAGE */
  let bgcolor = showCompletionCertificate ? 'primary.darkLightPurple' : 'background.default';
  if (!showCompletionCertificate && get(children, 'props.backgroundColor') &&
    get(children, 'props.type') === 'TEXT_OVERLAY_WITH_IMAGE') {
    bgcolor = get(children, 'props.backgroundColor');
  }
  const containerAspectRatio = !insights ? styles.containerAspectRatio : '';

  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (!insights && !showCompletionCertificate && screenReaderFocusRef && screenReaderFocusRef.current && contentContainerElement) {
      const text = currentCard > lessonLength - 1 ? '' : `Lesson Card ${currentCard} of ${lessonLength - 1}`;
      setScreenReaderLessonCardText(text);
      screenReaderFocusRef.current.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [insights, cardId, showCompletionCertificate, currentCard, lessonLength, contentContainerElement]);

  let audioUrl;
  if (!user.audio || isEndOfLessonCard(type)) {
    audioUrl = undefined;
  } else if (speechAudioURL && !isResultCard) {
    audioUrl = speechAudioURL;
  } else if (gatedFeedbackSpeechAudioURL && isResultCard) {
    audioUrl = gatedFeedbackSpeechAudioURL;
  }

  return (
    <Paper
      tabIndex={0}
      ref={screenReaderFocusRef}
      id="lesson-card-focus-element"
      style={{ display: 'block', padding: `${isSmallMobile ? '1.5rem' : ''}` }}
      className={`${styles.container} ${containerAspectRatio}`}
      sx={{
        bgcolor,
        '&:focus': {
          border: `2px solid ${palette.card.backgroundColor}`,
        },
      }}
      role="group"
      aria-roledescription="slider"
      aria-label={screenReaderLessonCardText}
    >
      <HiddenAlert text={screenReaderLessonCardText} />
      {cardRequiresUserResponse(type) && !showCompletionCertificate && !insights ?
        <QuizCardRibbon isResultCard={isResultCard} isPolicyCard={isPolicyCard(type)} /> : null}
      {((isEndOfLessonCard(type) || showCompletionCertificate) && !hideRibbon) ? <EndOfLessonCardRibbon /> : null}
      {(speechAudioURL || gatedFeedbackSpeechAudioURL)
        && !isEndOfLessonCard(type)
        && (!isResultCard || gatedFeedbackSpeechAudioURL) && (
        <AudioButton inQuizCard={cardRequiresUserResponse(type)} />
      )}
      {audioUrl && (
        <SpeechAudio audioURL={audioUrl} />
      )}
      {children}
      {!showCompletionCertificate && !isEndOfLessonCard(type) && (
      <div
        aria-hidden
        className={styles.footer}
        style={{ background: palette.background.frost, color: palette.primary.main }}
      >
        <p className={styles.footerNumber}>
          {`${programId || '0'}.${lessonId || microLessonId}.${cardId || lessonCardId}`}
          {translationLanguage}
        </p>
        {!insights && (<p data-cy="cards_count">{`${currentCard} ${t('platform.of')} ${lessonLength - 1}`}</p>)}
      </div>
      )}
    </Paper>
  );
}
