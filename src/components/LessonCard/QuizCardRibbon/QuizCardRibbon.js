import React from 'react';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import styles from './QuizCardRibbon.module.css';

export default function QuizCardRibbon({ isResultCard, isPolicyCard }) {
  const { palette } = useTheme();
  const { t } = useTranslation();
  return (
    <div
      className={styles.container}
      style={{ background: palette.background.ribbon }}
    >
      <div
        className={styles.text}
        style={{
          background: palette.background.ribbon,
          color: palette.primary.contrastText,
        }}
      >
        {isPolicyCard && (
          <p data-cy={`${isResultCard ? 'results' : 'question'}`}>
            {t('lessonCards.acknowledgement')}
          </p>
        )}
        {!isPolicyCard && (
          <p data-cy={`${isResultCard ? 'results' : 'question'}`}>
            {`${isResultCard ? t('lessonCards.results') : t('lessonCards.question')}`}
          </p>
        )}
      </div>
    </div>
  );
}
