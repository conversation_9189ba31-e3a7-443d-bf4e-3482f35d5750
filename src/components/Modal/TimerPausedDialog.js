import React from 'react';
import { Dialog as MuiDialog, Box, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import SendSaveButton from '../Button/SendSaveButton';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import PauseCircleIcon from '../../icons/PauseCircle';

import styles from './Modal.module.css';

const TimerPausedDialog = ({ onClose, minTimeInMinutes }) => {
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { isMobile } = useResponsiveMode();

  const handleClose = () => {
    onClose();
  };

  // First message showing the minutes remaining in paused dialog
  const minutesRequiredText = () => {
    const messagePart1 = `${t('timer.required_minimum_spend')} ${minTimeInMinutes}`;
    const messagePart2 = t('timer.interacting_with_course');
    return `${messagePart1} ${messagePart2}`;
  };

  return (
    <MuiDialog
      maxWidth="md"
      open
      onClose={handleClose}
      sx={{ '.MuiDialog-paper': {
        width: isMobile ? '24rem' : '30rem',
      },
      }}
    >
      <Box className={styles.pausedDialogTitle} style={{ backgroundColor: palette.background.paused }}>
        <PauseCircleIcon className={styles.pausedIcon} />
        <span className={styles.pausedTitleText} style={{ color: palette.primary.dark }}>
          {t('timer.timer_paused')}
        </span>
      </Box>
      <Box className={styles.dialogMain}>
        <p className={styles.pausedDialogBodyText}>
          {minutesRequiredText()}
        </p>
        <p className={styles.pausedDialogBodyText}>
          {t('timer.taking_break')}
        </p>
      </Box>
      <Box display="flex" justifyContent="center" pt={0.4} pb={2}>
        <SendSaveButton
          name="unPauseButton"
          type="button"
          label={t('timer.continue')}
          data-cy="unPauseButton"
          onClick={onClose}
        />
      </Box>
    </MuiDialog>
  );
};

export default TimerPausedDialog;
