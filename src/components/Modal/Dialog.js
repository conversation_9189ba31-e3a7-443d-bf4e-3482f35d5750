/* eslint-disable no-nested-ternary */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog as MuiDialog, IconButton, Box, useTheme } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';

const Dialog = ({ children, dialogLabel, ariaLabelledby, open, onClose,
  sharedAssignmentDialog = false, whereTheProblem = false }) => {
  const { palette } = useTheme();
  const { isMobile } = useResponsiveMode();
  const { t } = useTranslation();

  const handleClose = () => {
    onClose();
  };

  return (
    <MuiDialog
      maxWidth="md"
      open={open}
      onClose={handleClose}
      sx={{ '.MuiDialog-paper': {
        maxWidth: '50rem',
        minWidth: isMobile ? '18rem' : '30rem',
        border: !whereTheProblem ? `5px solid ${palette.border.dialog}` : '',
        overflowX: 'visible !important',
        overflowY: !(sharedAssignmentDialog || whereTheProblem) ? 'scroll' : 'hidden',
        backgroundColor: (sharedAssignmentDialog || whereTheProblem) ? `${palette.background.default}` : '',
        height: whereTheProblem ? '32rem' : null,
        width: whereTheProblem ? '40rem' : null,
      },
      }}
      PaperProps={{
        'aria-modal': 'true',
        'aria-label': dialogLabel || null,
        'aria-labelledby': ariaLabelledby || null,
      }}
    >
      {!sharedAssignmentDialog && (
        <Box display="flex" justifyContent="flex-end" pt={0.7} pr={0.7}>
          <IconButton
            aria-label={t('platform.close')}
            onClick={handleClose}
            sx={{
              color: palette.primary.main,
              '& .MuiTouchRipple-root': { position: 'initial' },
              '&:focus': {
                outline: `solid ${palette.primary.white} 2px`,
                border: `solid ${palette.card.backgroundColor} 3px`,
                margin: '0.55rem',
                borderRadius: '2px',
                width: '23px',
                height: '23px',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      )}
      <Box px="1.5em" pb="1.5em">
        {children}
      </Box>
    </MuiDialog>
  );
};

export default Dialog;
