import React, { useState } from 'react';
import { Dialog as MuiDialog, Box, useTheme } from '@mui/material';
import { Trans, useTranslation } from 'react-i18next';
import SendSaveButton from '../Button/SendSaveButton';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import Clock from '../../icons/Clock';
import getCompletedTimeInMinutes from '../../features/assignment/Program/getCompletedTimeInMinutes';

import styles from './Modal.module.css';

const TimeRequirementDialog = ({ onClose, minTimeInMinutes, elapsedTime, programTitle, assignmentStatus, isScorm }) => {
  const { t } = useTranslation();
  const { isMobile } = useResponsiveMode();

  const firstScormView = isScorm && elapsedTime === 0;
  const completedMins = getCompletedTimeInMinutes({ elapsedTime, minTimeInMinutes });
  const timeRemaining = minTimeInMinutes - completedMins;

  const [initialStatus] = useState(assignmentStatus);
  const [isScormFirstView] = useState(firstScormView);

  const handleClose = () => {
    onClose();
  };

  const notStartedText = () => (
    <>
      <Trans i18nKey="timer.welcome" /> <strong>{programTitle}</strong>.<br /><br />
      <Trans i18nKey="timer.must_spend" /> <strong>{minTimeInMinutes}</strong> <Trans i18nKey="timer.must_spend_extended" />, <Trans i18nKey="timer.take_your_time" />
    </>
  );

  const inProgressText = () => (
    <>
      <Trans i18nKey="timer.welcome_back" /> <strong>{programTitle}</strong>.&nbsp;
      <Trans i18nKey="timer.returning_reminder" /> <strong>{minTimeInMinutes}</strong> <Trans i18nKey="timer.must_spend_extended" />.<br /><br />
      <Trans i18nKey="timer.have_spent" /> {completedMins} <Trans i18nKey="timer.so_far" /> <strong>{timeRemaining}</strong> <Trans i18nKey="timer.to_go" />, <Trans i18nKey="timer.take_your_time" />
    </>
  );

  return (
    <MuiDialog
      maxWidth="md"
      open
      onClose={handleClose}
      sx={{ '.MuiDialog-paper': {
        width: isMobile ? '24rem' : '30rem',
      },
      }}
    >
      <Box className={styles.timedDialogTitle}>
        <Clock className={styles.pausedIcon} isSolidIcon />
        <span className={styles.timedTitleText}>
          {t('timer.course_time_requirement')}
        </span>
      </Box>
      <Box className={styles.dialogMain}>
        {(initialStatus === 'inProgress' && !isScormFirstView) && (
        <p className={styles.pausedDialogBodyText}>
          {inProgressText()}
        </p>
        )}
        {(initialStatus === 'open' || isScormFirstView) && (
        <p className={styles.pausedDialogBodyText}>
          {notStartedText()}
        </p>
        )}
      </Box>
      <Box display="flex" justifyContent="center" pt={0.4} pb={2}>
        <SendSaveButton
          name="continueButton"
          type="button"
          label={t('timer.continue')}
          data-cy="continueButton"
          onClick={onClose}
        />
      </Box>
    </MuiDialog>
  );
};

export default TimeRequirementDialog;
