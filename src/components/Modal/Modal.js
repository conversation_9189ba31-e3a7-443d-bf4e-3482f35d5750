import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal as MaterialModal, useTheme } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';

import styles from './Modal.module.css';

const modalResponsiveStyles = {
  isSmallMobile: { width: '98vw', fontSize: '0.9em', padding: '2.5em 1em 1em 1.5em' },
  isMobile: { width: '90vw', fontSize: '1em', padding: '3em 1.5em 1.5em 2em ' },
  isTablet: { width: '70vw', fontSize: '1em', padding: '3em 1.5em 1.5em 2em ' },
  default: { width: '43vw', fontSize: '1.3em', padding: '2em' },
};

export default function Modal(props) {
  const { open, onClose, children } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();

  const { isSmallMobile, isMobile, isTablet } = useResponsiveMode();
  let responsiveStyles = modalResponsiveStyles.default;
  if (isSmallMobile) {
    responsiveStyles = modalResponsiveStyles.isSmallMobile;
  } else if (isMobile) {
    responsiveStyles = modalResponsiveStyles.isMobile;
  }
  if (isTablet) {
    responsiveStyles = modalResponsiveStyles.isTablet;
  }

  return (
    <MaterialModal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <div
        className={styles.modal}
        style={{
          width: responsiveStyles.width,
          fontSize: responsiveStyles.fontSize,
          padding: responsiveStyles.padding,
          background: palette.background.default,
          borderColor: palette.border.dialog,
        }}
      >
        <div className={styles.closeIcon}>
          <IconButton
            aria-label={t('platform.close')}
            onClick={onClose}
            sx={{
              '&:focus': {
                border: `solid ${palette.card.backgroundColor} 3px`,
              } }}
          >
            <CloseIcon />
          </IconButton>
        </div>
        {children}
      </div>
    </MaterialModal>
  );
}
