.modal {
  border-radius: 12px;
  border: 5px solid;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 98vw;
  max-height: 98vh;
  text-align: left;
  overflow-y: auto;
}

.closeIcon {
  display: inline-block;
  position: absolute;
  top: 2px;
  right: 2px;
}

.pausedDialogTitle {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 2.6rem;
}

.timedDialogTitle {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 2.6rem;
  background-color: #40405D;
}

.timedTitleText {
  padding-left: .6rem;
  font-weight: bold;
  color: #FFFFFF;
}

.pausedIcon {
  margin-top: .1rem;
  height: 1.2rem;
  width: 1.2rem;
}

.pausedTitleText {
  padding-left: .6rem;
  font-weight: bold;
}

.dialogMain {
  padding: 1.2rem 2rem 1.2rem 2rem;
  display: flex;
  flex-wrap: wrap;
}

.pausedDialogBodyText {
  font-size: .9rem;
  font-weight: normal;
  padding-bottom: 1rem;
}
