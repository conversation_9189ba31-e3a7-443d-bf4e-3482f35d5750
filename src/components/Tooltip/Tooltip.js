import React from 'react';
import { styled, Tooltip, tooltipClasses } from '@mui/material';

const StyledTooltip = styled(({ className, maxWidth, marginTop, marginRight, top, ...props }) => (
  <>
    <Tooltip
      {...props}
      tabIndex="0"
      describeChild // removes the aria label automatically added by MUI
      classes={{ popper: className }}
      arrow
    />
  </>
))(({ maxWidth, marginTop, marginRight, top }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    maxWidth: maxWidth || '11rem',
    marginTop: marginTop || '1px !important',
    marginRight: marginRight || '0 !important',
    top: top || '0 !important',
    lineHeight: 1.2,
  },
}));

export default StyledTooltip;
