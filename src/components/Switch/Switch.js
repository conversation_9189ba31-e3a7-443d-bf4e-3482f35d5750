import React from 'react';
import { styled, Switch as MuiSwitch } from '@mui/material';

const Switch = styled((props) => <MuiSwitch {...props} />)(({ theme }) => ({
  width: 32,
  height: 14,
  padding: 0,
  display: 'flex',
  borderRadius: '8px',
  '&:active': {
    '& .MuiSwitch-thumb': {
      width: 15,
    },
    '& .MuiSwitch-switchBase.Mui-checked': {
      transform: 'translateX(9px)',
    },
  },
  '& .MuiSwitch-switchBase': {
    '&.Mui-checked': {
      transform: 'translateX(18px)',
      color: theme.palette.primary.contrastText,
      '& + .MuiSwitch-track': {
        opacity: 1,
        backgroundColor: theme.palette.primary.darkLightPurple,
      },
    },
  },
  '& .MuiSwitch-thumb': {
    width: 10,
    height: 10,
    borderRadius: 6,
    transition: theme.transitions.create(['width'], {
      duration: 200,
    }),
  },
  '& .<PERSON>iSwitch-track': {
    borderRadius: 16 / 2,
    opacity: 1,
    backgroundColor: theme.palette.primary.greyPurple,
    boxSizing: 'border-box',
  },
}));
export default Switch;
