import React from 'react';
import Card from '@mui/material/Card';

import styles from './ErrorCard.module.css';

const ErrorCardWithRef = React.forwardRef((props, ref) => {
  const { errorMessage, className } = props;
  return (
    <Card
      ref={ref}
      data-cy={errorMessage}
      className={className ? `${styles.errorCard} ${className}` : styles.errorCard}
    >
      <div role="alert" aria-live="polite">
        {errorMessage}
      </div>
    </Card>
  );
});

export default ErrorCardWithRef;
