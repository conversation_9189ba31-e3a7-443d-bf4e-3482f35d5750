import React from 'react';
import Card from '@mui/material/Card';

import styles from './ErrorCard.module.css';

export default function ErrorCard({ children, className }) {
  return (
    <Card data-cy={children} className={className ? styles[className] : styles.errorCard}>
      <div
        role="alert"
        aria-live="polite"
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{ __html: children }}
      />
    </Card>
  );
}
