import React from 'react';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import { useMenuDrawer } from '../../hooks/useDrawer';
import ChevronIcon from '../../icons/Chevron';

const Breadcrumb = ({ breadcrumbTrail }) => {
  const { palette } = useTheme();
  const { drawerState } = useMenuDrawer();
  const { t } = useTranslation();
  const { isBtwDesktopAndAssignmentDrawer, isDesktop } = useResponsiveMode();

  const currentBreadcrumb = breadcrumbTrail.length - 1;

  return (
    <aside
      aria-label={t('lessons.breadcrumb_accessible_name')}
      style={{ maxWidth: (isBtwDesktopAndAssignmentDrawer || (isDesktop && drawerState === 'closed'))
        ? 'calc(100% - 200px)' : '100%' }}
    >
      <h1
        style={{
          marginTop: '-0.25rem',
          paddingLeft: '1.125rem',
          lineHeight: '1.5',
          fontSize: '1rem',
          fontWeight: 'normal',
          display: 'inline',
          color: palette.primary.main,
        }}
      >
        {breadcrumbTrail.map((v, i) => (
          <React.Fragment key={v}>
            {i === currentBreadcrumb ? <strong>{v}</strong> : v}
            {i !== currentBreadcrumb && (
            <ChevronIcon
              key={`icon-${v}`}
              direction="right"
              width="0.875rem"
              height="0.875rem"
              color={palette.primary.main}
              style={{ marginLeft: '0.25rem', marginRight: '0.25rem' }}
              role="img"
              aria-label="chevron right"
              aria-hidden={false}
            />
            )}
          </React.Fragment>
        ))}
      </h1>
    </aside>
  );
};

export default Breadcrumb;
