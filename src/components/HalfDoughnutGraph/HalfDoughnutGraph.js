/* eslint-disable react/jsx-indent */
import React, { useCallback, useState } from 'react';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { pick } from 'lodash';
import { VictoryPie, Slice } from 'victory';
import Tooltip from '../Tooltip/Tooltip';
import { getXOffset, getYOffset, getAverage } from '../../utils/graphMath';
import { useInterval } from '../../hooks/useInterval';
import HiddenAlert from '../../features/hidden-alert/HiddenAlert';
import styles from './HalfDoughnutGraph.module.css';
import { useUser } from '../../hooks/useUser';

function Tag(props) {
  const {
    datum,
    onlyShowValue,
    innerRadius,
    radius,
    slice: { startAngle, endAngle },
    valueKey,
    style,
    id,
    cx,
    cy,
    selectedColor,
    translatedPieLabels,
  } = props;

  const { palette } = useTheme();
  const value = Math.round(datum[valueKey]);
  const test = pick(style, ['fontSize', 'fill', 'fontWeight']);

  const middleRadius = getAverage([innerRadius, radius]);
  const midAngle = getAverage([endAngle, startAngle]);
  const labelOffset = radius + middleRadius / 3;
  const x = cx + getXOffset(labelOffset, midAngle);
  const y = cy + getYOffset(labelOffset, midAngle);
  const textAnchor = Math.round(cx) < Math.round(x) ? 'start' : 'end';
  const { name, label } = translatedPieLabels[id];
  const user = useUser();

  let fontWeight = test.fontWeight;
  let fill = test.fill;

  let reduceFontSizeTo = user.language === 'ja' ? 5 : 3;
  if (selectedColor && parseInt(selectedColor - 1, 10) === parseInt(id.split('-')[2], 10)) {
    fontWeight = 700;
    fill = palette.primary.dark;
  }
  if (onlyShowValue) {
    return (
      <g aria-hidden>
        <text x={x} y={y + 16} textAnchor={textAnchor} style={{ ...test, fontWeight }}>
          {`${value}%`}
        </text>
      </g>
    );
  }

  return (
    <g aria-hidden>
      <text x={x} y={y} textAnchor={textAnchor} style={{ ...test, fill, fontWeight }} data-cy={id}>
        {name}
      </text>
      {/* eslint-disable-next-line max-len */}
      <text x={x} y={y + 15} textAnchor={textAnchor} style={{ ...test, fill, fontWeight: 500, fontSize: test.fontSize - reduceFontSizeTo }} data-cy={`label-${id}`}>
        {label}
      </text>
    </g>
  );
}

const centerLabels = (middleLabels) => {
  return (
    <foreignObject x="145" y="151" width="6.8rem" height="3.8rem" xmlns="http://www.w3.org/1999/xhtml">
      {middleLabels.map(({ text, style }) => (
        <p
          key={text}
          style={style}
        >{text}
        </p>
      ))}
    </foreignObject>
  );
};

/**
 * Added forwardRef to access ref to element in parent component
 */
const HalfDoughnutGraph = React.forwardRef((props, ref) => {
  const {
    data,
    colorScale,
    innerRadius,
    radius,
    nameKey,
    valueKey,
    pieEvents = null,
    middleLabels = [],
    isMiddleLabelsTooltip = false,
    middleLabelsTooltip = '',
    onlyShowValue,
    hideOuterLabels,
    className,
    height,
    width,
    dataCY,
    setSelectedColor,
    setHoverColor,
    selectedColor,
    colorDescriptions,
    isResultsCard,
    translatedPieLabels,
  } = props;

  const { t } = useTranslation();

  const [endAngle, setEndAngle] = useState(-90);
  const [groupFocused, setGroupFocused] = useState(false);
  const [screenReaderDescription, setScreenReaderDescription] = useState(null);
  const [spaceOrEnterKeyInstructions, setSpaceOrEnterKeyInstructions] = useState('');

  const triggerScreenReaderDescriptionAlert = useCallback((val) => {
    // eslint-disable-next-line max-len
    setScreenReaderDescription(`${colorDescriptions[val].translatedKey}. ${t('general.selected')}. ${colorDescriptions[val].text}`);
  }, [colorDescriptions, t]);

  const { palette } = useTheme();

  useInterval(() => {
    setEndAngle(90);
  }, 250);

  const handleMouseOver = useCallback(() => {
    return [
      {
        target: 'labels',
        mutation: ({ style }) => ({
          style: {
            ...style,
            fontSize: 15,
            fontWeight: 600,
            fill: palette.primary.dark,
          },
        }),
      },
      {
        target: 'data',
        mutation: ({ index }) => {
          setHoverColor(index + 1);
        },
      },
    ];
  }, [setHoverColor, palette]);

  const handleMouseOut = useCallback(() => {
    return [
      {
        target: 'labels',
        mutation: () => null,
      },
      {
        target: 'data',
        mutation: () => {
          setHoverColor(null); // returns needle to center
        },
      },
    ];
  }, [setHoverColor]);

  const handleClick = useCallback(() => {
    return [
      {
        target: 'data',
        mutation: ({ index }) => {
          setSelectedColor(index + 1);
          triggerScreenReaderDescriptionAlert(index + 1);
        },
      },
    ];
  }, [setSelectedColor, triggerScreenReaderDescriptionAlert]);

  const handleKeyDown = (e) => {
    e.preventDefault();
    if (e.code === 'Space') {
      setSpaceOrEnterKeyInstructions('Use the arrow keys to make a selection.');
    }
    if (e.code === 'Enter') {
      setSpaceOrEnterKeyInstructions('Please use the arrow keys to make a selection.');
    }
    if (e.code === 'ArrowRight') {
      let newVal = selectedColor ? selectedColor + 1 : 3; // set to 3 if not yet set
      if (newVal > 4) {
        newVal = 4;
      }
      setSelectedColor(newVal);
      triggerScreenReaderDescriptionAlert(newVal);
    }
    if (e.code === 'ArrowLeft') {
      let newVal = selectedColor ? selectedColor - 1 : 2;
      if (newVal < 1) {
        newVal = 1;
      }
      setSelectedColor(newVal);
      triggerScreenReaderDescriptionAlert(newVal);
    }
    if (e.code === 'Tab') {
      const submitButton = document.getElementById('wcs_submit_button');
      // only respond to tab if selection has already been made -- pass focus to submit button
      // ignore shift-tab
      if (!e.shiftKey) {
        if (selectedColor && selectedColor > 0) {
          submitButton.focus();
        }
      }
    }
  };

  const centerX = width / 2;
  const centerY = height / 2;

  const blankLabelFunc = () => { ''; };
  const blankLabelProp = hideOuterLabels ? { labels: blankLabelFunc } : {};
  const groupOutlineStyle = groupFocused ? `3px solid ${palette.card.backgroundColor}` : 'none';

  const middleLabelsAriaLabelText = isMiddleLabelsTooltip && middleLabels
    ? `${middleLabels[0].text}: ${middleLabels[0].numResponsesText}. ${middleLabels[0].tooltip}`
    : '';

  return (
    <>
      <HiddenAlert text={screenReaderDescription} />
      <HiddenAlert text={spaceOrEnterKeyInstructions} />
      {!isResultsCard && (
        <div className="srOnly" id="screen_reader_graph_description">{t('colorSpectrum.graph_description')}</div>
      )}
      <svg
        aria-hidden={isResultsCard}
        ref={ref}
        viewBox={`0 0 ${width} 200`}
        className={className}
        style={{ outline: groupOutlineStyle, borderRadius: '8px' }}
        onFocus={() => { if (!isResultsCard) { setGroupFocused(true); } }}
        onBlur={() => { if (!isResultsCard) { setGroupFocused(false); } }}
      >
        <VictoryPie
          standalone={false}
          startAngle={-90}
          endAngle={endAngle}
          data={data}
          innerRadius={innerRadius}
          radius={radius}
          style={{
            data: {
              strokeWidth: 0.5,
              stroke: palette.doughnutGraph.strokeColor,
            },
          }}
          colorScale={colorScale}
          {...blankLabelProp}
          dataComponent={(
            <Slice
              className={styles.slicePath}
              tabIndex={isResultsCard ? -1 : 0}
              aria-hidden={isResultsCard}
              ariaLabel={({ datum }) => `${datum.ariaLabel || ''}`}
            />
          )}
          labelComponent={(
            <Tag
              onlyShowValue={onlyShowValue}
              innerRadius={innerRadius}
              radius={radius}
              width={width}
              nameKey={nameKey}
              valueKey={valueKey}
              cx={centerX}
              cy={centerY}
              selectedColor={selectedColor}
              style={{ fill: palette.primary.main, fontSize: '15', fontWeight: 500 }}
              translatedPieLabels={translatedPieLabels}
            />
          )}
          events={setSelectedColor ? [
            {
              target: 'data',
              eventHandlers: {
                onMouseOver: handleMouseOver,
                onMouseOut: handleMouseOut,
                onClick: handleClick,
                onKeyDown: (e) => handleKeyDown(e),
              },
            },
          ] : pieEvents}
          animate={{ duration: 1000, onLoad: { duration: 1000 }, easing: 'backOut' }}
        />
        {isMiddleLabelsTooltip && middleLabels && (
          <Tooltip
            title={middleLabelsTooltip}
            role="tooltip"
            style={{ outline: 'unset' }}
          >
            <g
              data-cy={dataCY}
              aria-label={middleLabelsAriaLabelText}
            >{centerLabels(middleLabels)}
            </g>
          </Tooltip>
        )}

        {!isMiddleLabelsTooltip && middleLabels && (
          centerLabels(middleLabels)
        )}

        {/* <polyline points="190,200 200,100 210,200 190,200" style={{ stroke: 'black', fill: 'black', strokeWidth: 2 }} /> */}
      </svg>
    </>
  );
});

HalfDoughnutGraph.defaultProps = {
  height: 400,
  width: 400,
  innerRadius: 62,
  radius: 120,
  nameKey: 'label',
  valueKey: 'y',
};

export default HalfDoughnutGraph;
