import React from 'react';
import { Box, styled } from '@mui/material';
import { useTranslation } from 'react-i18next';
import BarChartIcon from '../../images/bar-chart-icon.svg';
import DonutChartIcon from '../../images/donut-chart-icon.svg';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';

export function NotEnoughResults({ insights }) {
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();

  return (
    <NotEnoughResponseContainer sx={{ mb: isSmallMobile || insights ? '5rem !important' : 0 }}>
      <Box
        id="noResponseIcons"
        display="grid"
        gridTemplateColumns="70px 60px"
        columnGap="1em"
        mb={2}
        justifyContent="center"
      >
        <BarChartIcon width="70" height="63" />
        <DonutChartIcon width="60" height="63" />
      </Box>
      <NotEnoughResponseText>
        {t('lessonCards.notEnoughData')}
      </NotEnoughResponseText>
    </NotEnoughResponseContainer>
  );
}

const NotEnoughResponseContainer = styled((props) => (
  <Box
    id="noResponseContainer"
    data-cy="noResponseContainer"
    {...props}
  />
))(({ theme }) => ({
  background: theme.palette.background.noResultsBackground,
  width: '100%',
  borderRadius: '50px',
  margin: '3rem 0 1rem 0',
  padding: '1.5rem',
}));

const NotEnoughResponseText = styled((props) => (
  <p
    {...props}
    id="noResponseText"
    data-cy="noResponseText"
  />
))({
  textAlign: 'center',
  fontSize: '1em',
  lineHeight: '1em',
});
