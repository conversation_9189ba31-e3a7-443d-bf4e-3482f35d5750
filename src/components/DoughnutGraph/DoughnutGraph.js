/* eslint-disable max-len */
import React, { useState } from 'react';
import { useTheme } from '@mui/material';
import { capitalize, pick } from 'lodash';
import { VictoryPie } from 'victory';

import UserSquareIcon from '../../images/user-square-solid.svg';
import Tooltip from '../Tooltip/Tooltip';
import { getXOffset, getYOffset, getAverage } from '../../utils/graphMath';
import { useInterval } from '../../hooks/useInterval';

DoughnutGraph.defaultProps = {
  height: 410,
  width: 410,
  innerRadius: 108,
  radius: 170,
  nameKey: 'x',
  valueKey: 'y',
};

// NOTE: I ripped most of this off of this code sandbox
// and tweaked the math for the lines to fit our needs
// https://codesandbox.io/s/basic-victory-example-rfb7s?file=/PieChart.js
export default function DoughnutGraph(props) {
  const {
    data,
    middleLabels = [],
    height,
    width,
    innerRadius,
    radius,
    nameKey,
    valueKey,
    colorScale,
    legendLineColor,
    className,
    tooltipText,
    dataCY,
    insights,
  } = props;
  const [endAngle, setEndAngle] = useState(0);

  useInterval(() => {
    setEndAngle(360);
  }, 250);

  const centerX = width / 2;
  const centerY = height / 2;

  const middleLabelsAriaLabelText = middleLabels
    ? `${middleLabels[0].text}: ${middleLabels[0].numResponsesText}. ${tooltipText}`
    : '';

  return (
    <svg viewBox={`0 0 ${height} ${width}`} className={className}>
      <VictoryPie
        standalone={false}
        width={width}
        height={height}
        data={data}
        colorScale={colorScale}
        endAngle={endAngle}
        innerRadius={innerRadius}
        animate={{ duration: 1000, onLoad: { duration: 1000 }, easing: 'sinOut' }}
        labelComponent={(
          // eslint-disable-next-line jsx-a11y/label-has-for
          <Label
            width={width}
            height={height}
            innerRadius={innerRadius}
            radius={radius}
            nameKey={nameKey}
            valueKey={valueKey}
            cx={centerX}
            cy={centerY}
            legendLineColor={legendLineColor}
            dataCY={dataCY}
            insights={insights}
          />
        )}
      />
      <Tooltip
        title={tooltipText}
        role="tooltip"
        style={{ outline: 'unset' }}
      >
        <g
          data-cy={dataCY}
          aria-label={middleLabelsAriaLabelText}
        >
          <foreignObject x="99" y="165" width="13rem" height="7rem" requiredExtensions="http://www.w3.org/1999/xhtml">
            {middleLabels.map(({ text, style }) => (
              <p
                key={text}
                style={style}
              >{text}
              </p>
            ))}
          </foreignObject>
        </g>
      </Tooltip>
    </svg>
  );
}

function Label(props) {
  const {
    datum,
    innerRadius,
    radius,
    width,
    height,
    slice: { startAngle, endAngle },
    nameKey,
    valueKey,
    cx,
    cy,
    legendLineColor,
    dataCY,
    insights,
  } = props;
  const { palette } = useTheme();
  const name = datum[nameKey];
  const value = datum[valueKey];
  const style = pick(datum.style, ['fontSize', 'fill', 'fontWeight']);
  const myChoice = datum.myChoice;

  const middleRadius = getAverage([innerRadius, radius]);
  const midAngle = getAverage([endAngle, startAngle]);
  const labelOffset = radius - middleRadius;
  const x = cx + getXOffset(labelOffset, midAngle);
  const textAnchor = (Math.round(value) === 100) || (cx < x) ? 'start' : 'end';
  const xStart = cx + getXOffset(middleRadius, midAngle);
  const yStart = cy + getYOffset(middleRadius, midAngle);

  // eslint-disable-next-line max-len
  const xEnd = Math.round(xStart) < Math.round(cx) ? cx - ((width / 2) - (width / 15)) : cx + ((width / 2) - (width / 15));
  const xIconEnd = Math.round(xStart) < Math.round(cx) ? xEnd - (width / 5) : xEnd + (width / 6);

  return (
    <g>
      <text x={xEnd} y={yStart} textAnchor={textAnchor} style={{ ...style, fontWeight: 600 }} data-cy={`${dataCY}-${capitalize(name)}`}>
        {capitalize(name)}
      </text>
      <text x={xEnd} y={yStart + (height / 15)} textAnchor={textAnchor} style={{ ...style, fontWeight: 500 }} data-cy={`${dataCY}-${capitalize(name)}-percent`}>
        {`${Math.round(value)}%`}
      </text>

      {myChoice && !insights ? (
        <UserSquareIcon
          width="20px"
          height="20px"
          x={xIconEnd - 8}
          y={yStart}
          color={palette.primary.greyPurple}
        />
      ) : null}
      <LabelLine
        cx={cx}
        cy={cy}
        width={width}
        middleRadius={middleRadius}
        radius={radius}
        midAngle={midAngle}
        legendLineColor={legendLineColor}
      />
    </g>
  );
}

function LabelLine(props) {
  const { palette } = useTheme();
  const { cx, cy, midAngle, middleRadius, width, legendLineColor } = props;
  const xStart = cx + getXOffset(middleRadius, midAngle);
  const yStart = cy + getYOffset(middleRadius, midAngle);

  // eslint-disable-next-line max-len
  const xEnd = Math.round(xStart) < Math.round(cx) ? cx - ((width / 2) - (width / 10)) : cx + ((width / 2) - (width / 10));

  return (
    <svg>
      <circle
        cx={xStart}
        cy={yStart}
        r="5"
        style={{
          fill: legendLineColor || palette.primary.greyPurple,
          stroke: legendLineColor || palette.primary.greyPurple,
          strokeWidth: '1px',
        }}
      />
      <polyline
        style={{
          fill: 'none',
          stroke: legendLineColor || palette.primary.greyPurple,
          strokeWidth: '1px',
        }}
        points={`${xStart},${yStart} ${xEnd},${yStart}`}
      />
    </svg>
  );
}
