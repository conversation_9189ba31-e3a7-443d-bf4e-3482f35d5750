import React from 'react';
import { Button as MaterialButton, CircularProgress } from '@mui/material';

import styles from './Button.module.css';

export default function Button({ loading, containerStyle, children, ...rest }) {
  return (
    <div className={`${styles.wrapper}${containerStyle ? ` ${containerStyle}` : ''}`}>
      <MaterialButton
        sx={{
          borderRadius: '1.5rem',
          padding: '0.313rem',
          '&:disabled': {
            backgroundColor: 'background.highlighted',
            color: 'primary.main',
            fontWeight: 600,
          },
        }}
        disableRipple
        {...rest}
      >
        {children}
      </MaterialButton>
      {loading && <CircularProgress size={24} className={styles.loader} />}
    </div>
  );
}
