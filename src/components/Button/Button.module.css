.wrapper {
  position: relative;
  width: 100%;
}

.button {
  border-radius: 25px !important;
}

.loader {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 5px;
}

.onOffBtnContainer {
  position: absolute !important;
  top: 0.25rem;
  right: 0.25rem;
  z-index: 10 !important;
}

.onOffBtnQuizCard {
  top: 2.6rem !important;
}

.onOffBtnPopup {
  top: 2.35rem !important;
}

.headphone {
  height: 2.1rem;
  width: 2.1rem;
}

