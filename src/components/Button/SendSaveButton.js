import React from 'react';
import { Button, useTheme } from '@mui/material';

const SendSaveButton = ({ label, ...props }) => {
  const { palette } = useTheme();
  return (
    <Button
      variant="contained"
      sx={{
        textTransform: 'capitalize',
        borderRadius: '1.125rem',
        minWidth: '6.25rem',
        backgroundColor: 'primary.main',
        color: 'primary.white',
        boxShadow: 'none',
        fontWeight: 600,
        '&:hover': {
          backgroundColor: 'primary.dark',
          boxShadow: 'none',
        },
        '&:disabled': {
          color: 'primary.main',
          backgroundColor: 'background.highlighted',
        },
        '&:focus': {
          border: `solid ${palette.card.backgroundColor} 3px`,
        },
      }}
      // Had to add the style individually. Passing the full style object in props didn't work.
      // If there's a better solution, please check where I'm passing the margin as a prop.
      style={{ margin: props.margin }}
      {...props}
    >
      {label}
    </Button>
  );
};

export default SendSaveButton;
