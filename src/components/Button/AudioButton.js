import React from 'react';
import { useTheme, Button } from '@mui/material';
import { useMutation } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../hooks/useAuth';
import { useUser } from '../../hooks/useUser';
import { useSpeechAudio } from '../../hooks/useSpeechAudio';

import UpdateUserMutation from '../../features/user-profile/UpdateUserMutation.graphql';

import styles from './Button.module.css';

import audioOnBtn from '../../images/audio-on.png';
import audioOffBtn from '../../images/audio-off.png';

const AudioButton = ({ inQuizCard, inPopup }) => {
  const { palette } = useTheme();
  const { t } = useTranslation();
  const [updateUser] = useMutation(UpdateUserMutation);
  const { updateUserData } = useAuth();
  const user = useUser();
  const { pauseCardAudio, pausePopupAudio } = useSpeechAudio();

  const saveUserAudio = (audio) => {
    if (audio === false) {
      pauseCardAudio();
      pausePopupAudio();
    }
    updateUser({ variables: { audio } });
    updateUserData({ audio });
  };
  // const player = document.getElementById('audioNarrationControls');
  return (
    <Button
      // eslint-disable-next-line max-len
      className={`${styles.onOffBtnContainer} ${inQuizCard ? styles.onOffBtnQuizCard : ''} ${inPopup ? styles.onOffBtnPopup : ''}`}
      tabIndex={0}
      onClick={() => saveUserAudio(!user.audio)}
      disableRipple
      sx={{
        border: '5px solid transparent',
        '&:hover': {
          backgroundColor: 'transparent',
        },
        '&:focus': {
          outline: '2px solid white !important',
          border: `3px solid ${palette.card.backgroundColor}`,
        },
      }}
      aria-label={t('sideDrawer.audio_tooltip')}
      aria-haspopup="true"
      aria-controls="audioNarrationControls"
      aria-expanded={user.audio}
    >
      <img
        src={user.audio ? audioOnBtn : audioOffBtn}
        className={styles.headphone}
        alt={t('speech_audio.audio_narration')}
      />
    </Button>
  );
};

export default AudioButton;
