import React from 'react';
import { Button, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

const CancelButton = (props) => {
  const { t } = useTranslation();
  const { palette } = useTheme();
  return (
    <Button
      variant="outlined"
      sx={{
        textTransform: 'capitalize',
        borderRadius: '1.125rem',
        minWidth: '6.25rem',
        color: 'primary.main',
        boxShadow: 'none',
        fontWeight: 600,
        border: `1px solid ${palette.primary.darkGrey}`,
        '&:hover': {
          backgroundColor: 'transparent',
          boxShadow: 'none',
          color: 'primary.darkLightPurple',
          border: `1px solid ${palette.primary.darkLightPurple}`,
        },
        '&:focus': {
          border: `solid ${palette.card.backgroundColor} 3px`,
        },
      }}
      {...props}
    >
      {t('sideDrawer.cancel')}
    </Button>
  );
};

export default CancelButton;
