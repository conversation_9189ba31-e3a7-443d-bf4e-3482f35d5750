.paragraph {
  line-height: 1.4em;
}

.paragraph p {
  margin-bottom: 1.2rem;
  font-size: 1.1em;
}

.imageWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image {
  width: 100% !important;
  height: auto;
  border-radius: 0.188rem;
}

.videoDiv {
  position: relative;
  height: 100%;
  max-width: 100%;
  aspect-ratio: 16 / 9;
  opacity: 1;
}

.jwPlayer {
  display: flex;
  align-self: center;
  background: transparent !important;
  width: auto;
  height: 100%;
}

.iconButton {
  height: 1.25em;
  width: 1.25em;
  border-radius: 30px;
}

.closeIcon {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 1;
}

.imageParent {
  position: relative;
}

.playIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
}

.brokenImage {
  font-size: 20rem;
  width: 100%;
}

.outerModal {
  display: flex;
  justify-content: center;
  align-items: center;
  top:0;
  left:0;
  width: auto;
  height:100%;
}

.videoTranscriptLink {
  padding: 0.30rem;
  margin-top: -0.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100% !important;
  background: rgb(242, 240, 246);
  color: rgb(64, 64, 93);
  cursor: pointer;
  font-size: 0.75rem;
  border-radius: 0 0 0.188rem 0.188rem;
}

.videoTranscriptLink:hover {
  color: #FFFFFF !important;
  background-color: #222442;
}

.disabled {
  pointer-events: none; 
  cursor: not-allowed; 
}
