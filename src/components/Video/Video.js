import React, { useState } from 'react';
import { get } from 'lodash';
import { useQuery, useMutation } from '@apollo/client';

import { useTranslation } from 'react-i18next';
import ReactJWPlayer from 'react-jw-player';
import Dialog from '@mui/material/Dialog';

import BrokenImage from '@mui/icons-material/BrokenImage';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { useTheme, styled } from '@mui/material';
import styles from './Video.module.css';
import { apiURL, JW_LICENSE } from '../../config/constants';
import PlayCircle from '../../icons/PlayCircle';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import Spinner from '../Spinner/Spinner';
import { useSpeechAudio } from '../../hooks/useSpeechAudio';

import GetVideoQuery from './VideoQuery.graphql';
import SendVideoEventMutation from './SendVideoEventMutation.graphql';
import { JW_HIGH_BANDWIDTH_ESTIMATE } from '../../services/jwPlayerSettings';
import { downloadVideoTranscript } from '../../services/api/completionCert';

const subURLPrefix = apiURL();
const addSubtitleURLPrefix = (subtitles) => subtitles.map((sub) => {
  return {
    ...sub,
    file: subURLPrefix.concat(sub.file),
  };
});

export default function Video({
  id,
  videoId,
  title,
  playIconWH,
  setVideoDialogOpen,
  lessonId,
  lessonLifecycle,
  addViewedVideo,
  isTranscriptAdded,
  userLanguage,
  programId,
  viewedFullVideos,
  setShowVideoTooltip,
  enforceFullVideoView,
}) {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isMobile } = useResponsiveMode();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [duration, setDuration] = useState(null);
  const [downloadTranscript, setDownloadTranscript] = useState(false);

  const { pauseCardAudio } = useSpeechAudio();

  const { loading, error, data } = useQuery(GetVideoQuery, {
    variables: { videoId },
  });
  const videoData = get(data, 'getVideo', {});
  const playlist = [{
    file: videoData.videoURL,
    image: videoData.imageURL,
    tracks: addSubtitleURLPrefix(get(videoData, 'subtitles', [])),
  }];

  const [sendVideoEvent] = useMutation(SendVideoEventMutation);

  const playlistImage = get(playlist, '0.image');
  const playIconWidthAndHeight = playIconWH || (isMobile ? '4.375rem' : '6.563rem');

  // unique id for player instance
  const desktopPlayerKey = `player-desktop-${videoId}-welcome`;

  const generateVideoEvent = (type) => {
    if (!id || !lessonId || !lessonLifecycle) {
      return;
    }
    const videoEvent = { type, lessonId, lessonCardId: id, sourceLifecycle: lessonLifecycle };
    sendVideoEvent({
      variables: { input: videoEvent } },
    );
  };

  const sendStartVideoEvent = () => {
    generateVideoEvent('start');
  };

  const sendPlayVideoEvent = () => {
    generateVideoEvent('play');
  };

  const sendPauseVideoEvent = () => {
    generateVideoEvent('pause');
  };

  const sendResumeVideoEvent = () => {
    generateVideoEvent('resume');
  };

  /* const onVideoLoad = (playerKey) => {
    const player = window.jwplayer(desktopPlayerKey);
    if (player) {
      player.play();
    }
    const playerEl = document.getElementById(playerKey);
    if (playerEl) {
      playerEl.focus();
    }
  }; */

  const autoStartVideo = (playerKey) => {
    const videoPlayer = document.getElementById(playerKey);
    if (videoPlayer) {
      videoPlayer.focus();
      sendStartVideoEvent();
    }
  };

  const handleOpen = () => {
    setOpen(true);
    pauseCardAudio();
    // In some cases the parent component needs a state change notification
    // to let it know that the video dialog is open
    if (setVideoDialogOpen) {
      setVideoDialogOpen(true);
    }
    // add video id to local storage to track clicked videos
    addViewedVideo(videoId);
    setShowVideoTooltip(false);
  };
  const handleClose = (event) => {
    setOpen(false);
    if (setVideoDialogOpen) {
      setVideoDialogOpen(false);
    }
    generateVideoEvent('stop');

    if (event && event.type && event.type === 'click') {
      const player = window.jwplayer(desktopPlayerKey);
      player.on('time', (e) => {
        setDuration(e.position);
      });
    }

    if (!event) {
      setDuration(null);
    }
  };

  if (loading) {
    return (
      <Spinner width="38" />
    );
  }

  if (error) {
    return (
      <div data-cy={error.message}>
        <p>{error.message}</p>
      </div>
    );
  }

  const handleDownload = async (lessonCardId, lId, pId) => {
    setDownloadTranscript(true);
    const response = await downloadVideoTranscript(lessonCardId, lId, pId);
    if (response) {
      setDownloadTranscript(false);
    }
  };

  const onNinetyFivePercent = () => {
    // add videoId for viewedFullVideos (localStorage)
    addViewedVideo(videoId, true);
  };

  const disableSeekbar = () => {
    const player = window.jwplayer(desktopPlayerKey);
    const seekbar = player.getContainer().querySelector('.jw-slider-time');
    const viewedVideo = viewedFullVideos.includes(videoId);
    if (duration) {
      player.seek(duration);
    }

    if (seekbar && !viewedVideo) {
      seekbar.style.pointerEvents = 'none';
      seekbar.style.opacity = '0.5'; // Optional to make it clear to the learner that it's disabled
    }
  };

  const onDisableSeekbar = enforceFullVideoView ? disableSeekbar : undefined;
  return (
    <>
      <div className={styles.imageWrapper}>
        <div className={styles.imageParent}>
          {playlistImage && (
            <>
              <img
                className={styles.image}
                src={playlistImage}
                alt=""
                data-cy="video_img"
                onLoad={() => setImageLoaded(true)}
                style={{ display: imageLoaded ? '' : 'none' }}
              />
              {isTranscriptAdded && (
                <>
                  <a
                    href="#"
                    className={`${styles.videoTranscriptLink} ${downloadTranscript ? styles.disabled : ''}`}
                    onClick={(e) => {
                      e.preventDefault();
                      handleDownload(id, lessonId, programId);
                    }}
                    data-cy={`downloadTranscript-lesson-${lessonId}`}
                    aria-current="page"
                    aria-label={t('lessons.downloadVideoTranscript')}
                    role="button"
                    tabIndex="0"
                  >
                    {downloadTranscript ? <span aria-label="Downloading">Downloading...</span> :
                      t('lessons.downloadVideoTranscript')}
                  </a>
                  <div style={{ paddingBottom: '8px' }} />
                </>
              )}
            </>
          )}
          {!playlistImage && (
            <BrokenImage fontSize="inherit" className={styles.brokenImage} data-cy="video_img" />
          )}
          {(!playlistImage || imageLoaded) && (
            <div className={styles.playIcon}>
              <IconButton
                disableRipple
                aria-label={t('lessonCards.playVideo')}
                onClick={handleOpen}
                data-cy="play"
                sx={{ '&:focus': {
                  padding: '2px',
                  backgroundColor: palette.button.playVideo.backgroundFocusColor,
                  outline: `3px solid ${palette.button.playVideo.focusOutlineColor}`,
                } }}
              >
                <PlayCircle {...{
                  width: playIconWidthAndHeight,
                  height: playIconWidthAndHeight,
                  color: palette.button.playVideo.color,
                  backgroundColor: palette.button.playVideo.backgroundColor,
                  styleProps: {
                    '&:hover': {
                      color: palette.button.playVideo.backgroundHoverColor,
                    } } }}
                />
              </IconButton>
            </div>
          )}
        </div>
      </div>
      <div>
        <Dialog
          open={open}
          onClose={handleClose}
          fullScreen
          PaperProps={{
            style: {
              backgroundColor: palette.background.modal.videoPlayer,
              borderRadius: 0,
              padding: 0,
            },
            'aria-label': `${title}`,
            'aria-modal': 'true',
            'aria-labelledby': null,
          }}
        >
          <div className={styles.outerModal}>
            <div className={styles.closeIcon}>
              <IconButton
                aria-label={t('platform.close')}
                onClick={handleClose}
                className={styles.iconButton}
                sx={{
                  background: palette.button.closeVideoModal.background,
                  '&:hover': {
                    backgroundColor: palette.button.closeVideoModal.backgroundHoverColor,
                  },
                  '&:focus': {
                    border: `solid ${palette.card.backgroundColor} 3px`,
                  } }}
              >
                <CloseIcon sx={{ color: palette.button.closeVideoModal.icon.color }} />
              </IconButton>
            </div>
            <div className={styles.videoDiv}>
              <ReactJWPlayer
                playerId={desktopPlayerKey}
                playerScript="/jwplayer_8_27/jwplayer.js"
                playlist={playlist}
                licenseKey={JW_LICENSE}
                // eslint-disable-next-line max-len
                customProps={{ width: '100%', aspectratio: '16:9', defaultBandwidthEstimate: JW_HIGH_BANDWIDTH_ESTIMATE }}
                className={styles.jwPlayer}
                // Add events here when we get to a proper implementation
                // for events in the learner app
                onPlay={() => { sendPlayVideoEvent(); }}
                onPause={() => { sendPauseVideoEvent(); }}
                onResume={() => { sendResumeVideoEvent(); }}
                isAutoPlay
                onAutoStart={() => autoStartVideo(desktopPlayerKey)}
                // onVideoLoad={() => onVideoLoad(desktopPlayerKey)}
                onOneHundredPercent={() => handleClose()}
                key={desktopPlayerKey}
                enableFullscreen="false"
                onReady={onDisableSeekbar}
                onNinetyFivePercent={onNinetyFivePercent}
              />
            </div>
          </div>
        </Dialog>
      </div>
    </>
  );
}
