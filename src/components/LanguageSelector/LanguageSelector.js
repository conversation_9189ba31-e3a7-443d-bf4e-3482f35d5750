import React, { useState } from 'react';
import { Box, List, ListItem, Paper, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { languageNames } from '../../translations';
import { setLanguage } from '../../i18next';
import Chevron from '../../icons/Chevron';
import SendSaveButton from '../Button/SendSaveButton';
import CancelButton from '../Button/CancelButton';

const listOfLanguages = Object.keys(languageNames).map((v) => ({ label: languageNames[v], value: v }));

export default function LanguageSelector(props) {
  const { onClose, onSaveUserLanguage, selectedLanguages } = props;
  const { palette } = useTheme();
  const { t, i18n } = useTranslation();

  const accountLanguages = !selectedLanguages
    ? listOfLanguages
    : listOfLanguages.filter(({ value }) => selectedLanguages.split(',').includes(value));

  const [selectedLanguage, setSelectedLanguage] = useState(() => i18n.language.toLocaleLowerCase());
  const handleSave = () => {
    setLanguage(selectedLanguage.toLowerCase());
    onSaveUserLanguage(selectedLanguage.toLowerCase());
    onClose();
  };

  return (
    <Paper
      sx={{
        bgcolor: 'background.default',
        px: 2,
        mb: 2,
        border: `1px solid ${palette.primary.darkGrey}`,
      }}
    >
      <Box
        display="grid"
        gridTemplateColumns="13px 1fr"
        alignItems="center"
        justifyItems="center"
        onClick={onClose}
        my={1}
        sx={{ cursor: 'pointer' }}
      >
        <Chevron {...{ direction: 'left', width: '12px', height: '12px' }} />
        <Typography sx={{ fontSize: '0.875rem', fontWeight: 700 }}>{t('sideDrawer.language')}</Typography>
      </Box>
      <List
        dense
        disablePadding
        sx={{
          maxHeight: '12.5rem',
          overflowY: 'auto',
          width: '100%',
          borderRadius: '0.5rem',
          border: `1px solid ${palette.primary.lightGreyOpacity60}`,
          '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
            width: '0.6rem',
          },
          '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
            borderRadius: '0.5rem',
            backgroundColor: palette.customScrollbar.thumbColor,
          },
          '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
            backgroundColor: 'transparent',
            borderLeft: `1px solid ${palette.primary.lightGreyOpacity60}`,
          },
        }}
      >
        {accountLanguages.map((lang) => (
          <ListItem
            key={lang.value}
            dense
            onClick={() => setSelectedLanguage(lang.value)}
            selected={lang.value === selectedLanguage}
            tabIndex="0"
            role="button"
            sx={{ '&:hover': { backgroundColor: 'background.highlighted' }, cursor: 'pointer' }}
          >
            <Typography sx={{ fontSize: '0.875rem', fontWeight: 600 }}>{lang.label}</Typography>
          </ListItem>
        ))}
      </List>
      <Box display="flex" my={1} justifyContent="center" gap={1}>
        <CancelButton onClick={onClose} data-cy="langSelectorCancel" />
        <SendSaveButton label={t('sideDrawer.save')} onClick={handleSave} data-cy="langSelectorSave" />
      </Box>
    </Paper>
  );
}
