.container {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}

.itemButton {
  padding: 0.4rem 1rem;
  display: flex;
  width: 100%;
  cursor: pointer;
  text-transform: none;
  margin: 0;
  font-weight: 600;
  width: 100%;
  border: 2px solid transparent;
  border-radius: 0.25rem;
}

.iconContainer {
  display: flex;
  align-items: center;
}

.icon {
  height: 100%;
}

.text {
  margin-left: 0.5rem;
}

.text p {
  margin: 0;
  font-weight: 600;
}

h2.expandableCardH2 {
  font-size: 1rem;
  margin: 0;
  font-weight: 600;  
}

.chevron {
  margin-left: auto;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.blink {
  animation: fadeInOut 1.5s linear forwards;
}
 
@keyframes fadeInOut{
  0%,100% { opacity: 1; }
  50% { opacity: 1; background-color: #B7CEFF; }
} 