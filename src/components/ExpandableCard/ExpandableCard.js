import { Paper, useTheme, Button } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import ChevronIcon from '../../icons/Chevron';
import styles from './ExpandableCard.module.css';

export default function ExpandableCard(props) {
  const { isOpen, onClick, title, icon: MenuIcon, children, drawerState, isAskExpert, manageView,
    preview = false } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();

  return (
    <Paper
      className={(drawerState === 'expert' && title === `${t('sideDrawer.expertTitle')}`) ?
        `${styles.container} ${styles.blink} accordion` : `${styles.container} accordion`}
      sx={{
        bgcolor: isOpen ? 'background.default' : 'background.paper',
        border: `1px solid ${palette.primary.darkGrey}`,
        '&:hover': {
          bgcolor: isOpen ? 'background.default' : 'primary.greyShade',
        },
      }}
    >
      {!preview && (
        <h2 className={styles.expandableCardH2}>
          <Button
            variant="outlined"
            onClick={onClick}
            className={`${styles.itemButton} accordion-trigger`}
            aria-expanded={isOpen}
            aria-disabled={isOpen}
            aria-controls={`${props['data-cy']}`}
            id={`${props['data-cy']}-button`}
            data-cy={props['data-cy']}
            disableRipple
            sx={{
              '&:hover': {
                border: '2px solid transparent',
              },
              '&:focus': {
                border: `2px solid ${palette.card.backgroundColor}`,
              },
            }}
          >
            <div aria-hidden className={styles.iconContainer}>
              <MenuIcon aria-hidden />
            </div>
            {isAskExpert && (
              <label htmlFor="question" className={styles.text}>{title}</label>
            )}
            {!isAskExpert && (
              <div className={styles.text}>{title}</div>
            )}
            <div aria-hidden className={styles.chevron}>
              {!manageView && (<ChevronIcon direction={isOpen ? 'up' : 'down'} />)}
            </div>
          </Button>
        </h2>
      )}
      {isOpen && children}
    </Paper>
  );
}
