import { Paper, useTheme, Button } from '@mui/material';
import React from 'react';
import ChevronIcon from '../../icons/Chevron';
import styles from './ExpandableFilterCard.module.css';

export default function ExpandableFilterCard(props) {
  const { isOpen, onClick, title, children } = props;
  const { palette } = useTheme();

  return (
    <Paper
      className={`${styles.container} accordion`}
      sx={{
        bgcolor: isOpen ? 'background.default' : 'background.paper',
        border: `1px solid ${palette.primary.darkGrey}`,
        '&:hover': {
          bgcolor: isOpen ? 'background.default' : 'primary.greyShade',
        },
      }}
    >
      <h2 className={styles.expandableCardH2}>
        <Button
          variant="outlined"
          onClick={onClick}
          className={`${styles.itemButton} accordion-trigger`}
          aria-expanded={isOpen}
          aria-disabled={isOpen}
          aria-controls={`${props['data-cy']}`}
          id={`${props['data-cy']}-button`}
          data-cy={props['data-cy']}
          disableRipple
          sx={{
            '&:hover': {
              border: '2px solid transparent',
            },
            '&:focus': {
              border: `2px solid ${palette.card.backgroundColor}`,
            },
          }}
        >
          <div className={styles.text}>{title}</div>
          <div aria-hidden className={styles.chevron}>
            <ChevronIcon direction={isOpen ? 'up' : 'down'} />
          </div>
        </Button>
      </h2>
      {isOpen && children}
    </Paper>
  );
}
