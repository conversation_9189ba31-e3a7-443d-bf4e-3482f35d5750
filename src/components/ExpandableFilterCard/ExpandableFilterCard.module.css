.container {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}

.itemButton {
  padding: 0.2rem 0.4rem;
  display: flex;
  width: 100%;
  cursor: pointer;
  text-transform: none;
  margin: 0;
  font-weight: 600;
  width: 100%;
  border: 2px solid transparent;
  border-radius: 0.25rem;
  font-size: 0.975rem;
}

.text {
  margin-left: 0.5rem;
}

.text p {
  margin: 0;
  font-weight: 600;
}

h2.expandableCardH2 {
  font-size: 1rem;
  margin: 0;
  font-weight: 600;  
}

.chevron {
  margin-left: auto;
  cursor: pointer;
  display: flex;
  align-items: center;
}
