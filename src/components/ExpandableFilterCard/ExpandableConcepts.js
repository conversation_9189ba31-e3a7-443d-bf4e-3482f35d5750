import { Paper, Typography } from '@mui/material';
import React from 'react';
import ChevronIcon from '../../icons/Chevron';
import styles from './ExpandableConcepts.module.css';

export default function ExpandableConcepts(props) {
  const { isOpen, onClick, title, children } = props;

  return (
    <Paper
      sx={{ bgcolor: 'background.default' }}
    >
      <h2 className={styles.expandableCardH2}>
        <Typography
          component="div"
          onClick={onClick}
          className={`${styles.itemTitle} accordion-trigger`}
          aria-expanded={isOpen}
          aria-disabled={isOpen}
          aria-controls={`${props['data-cy']}`}
          id={`${props['data-cy']}-button`}
          data-cy={props['data-cy']}
        >
          <div aria-hidden className={styles.chevron}>
            <ChevronIcon className={styles.chevronIcon} direction={isOpen ? 'up' : 'down'} />
          </div>
          <div className={styles.text}>{title}</div>
        </Typography>
      </h2>
      {isOpen && children}
    </Paper>
  );
}
