/* eslint-disable react/no-danger */
import React from 'react';
import { useTheme, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import styles from './CardTitleDescription.module.css';

// eslint-disable-next-line max-len
export default function CardTitleDescription({ title, description, fontColor, descriptionMarginTop, boxProps, hiddenText, descriptionPaddingTop, titleId }) {
  const { palette } = useTheme();
  const { t } = useTranslation();

  return (
    <>
      {title && <h1 className="srOnly">{t('lessonCards.lessonCard')}</h1>}
      {/* eslint-disable-next-line max-len */}
      {titleId && title && <h2 id={titleId} className={styles.title} style={{ color: fontColor || '' }} data-cy="text_card_title">{title}</h2>}
      {/* eslint-disable-next-line max-len */}
      {!titleId && title && <h2 className={styles.title} style={{ color: fontColor || '' }} data-cy="text_card_title">{title}</h2>}
      {/* if screen reader text does not match on-screen text */}
      {hiddenText && <div className="srOnly">{hiddenText}</div>}
      {!title && <div style={{ height: '2.4rem' }} />}
      {description && (
        <Box
          data-cy="text_card_description"
          {...boxProps}
          className={styles.paragraph}
          dangerouslySetInnerHTML={{ __html: description }}
          sx={{
            color: fontColor || '',
            mt: descriptionMarginTop || '',
            pt: descriptionPaddingTop || '',
            // eslint-disable-next-line max-len
            '& p > a, & li > a ': { color: `${palette.link.document} !important`, textDecoration: 'none', fontWeight: 'bold' },
          }}
        />
      )}
    </>
  );
}
