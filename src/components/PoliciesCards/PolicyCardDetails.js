/* eslint-disable max-len */
import React, { useState } from 'react';
import * as yup from 'yup';
import { useFormik } from 'formik';
import { Visibility } from '@mui/icons-material';

import {
  Box,
  TextField,
  Button,
  Typography,
  Divider,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';

import { policyCardUpdate } from '../../services/api/policyCardUpdate';

import styles from '../../features/manage-content-library/content-library-details/ContentLibraryDetailsContainer.module.css';
import SendSaveButton from '../Button/SendSaveButton';
import FileUploadWithUrlOption from './FileUploadWithUrlOption';
import Spinner from '../Spinner/Spinner';

export default function PolicyCardDetails({ cardData, onSavePolicies, lessonId, isPolicyPresent }) {
  const [policyUpdateConfirm, setPolicyUpdateConfirm] = useState(false);
  const getPolicyType = (item) => {
    return item?.list1 === 'LINK' ? 'link' : 'file';
  };

  function cardDescription(description) {
    const parser = new DOMParser();
    return typeof description === 'string' && description.trim().length > 0
      ? parser.parseFromString(description, 'text/html').body.textContent
      : '';
  }

  function hasPolicyLink(link) {
    return !!(typeof link === 'string' && link.trim().length > 0);
  }

  // eslint-disable-next-line no-shadow
  function extractPolicyData(policy) {
    // eslint-disable-next-line no-shadow
    const { id, list1, cardType, accountLessonCards = [], images = [], title, description, policyLink } = policy;

    if (cardType !== 'policyAcknowledgement') return null;

    if (accountLessonCards && accountLessonCards.length > 0) {
      const match = accountLessonCards.find((card) => card.lessonCardId === id);
      if (match) {
        if (match.policyType === 'file') {
          const policyPresent = !!match?.file?.path;
          return {
            id,
            title: match.title ? match.title : '',
            description: match.description ? cardDescription(match.description) : '',
            path: policyPresent ? match.file.path : null,
            fileId: match.fileId || null,
            list1: 'View Document',
            policyPresent,
          };
        }

        return {
          id,
          title: match.title,
          description: cardDescription(match.description),
          link: hasPolicyLink(match.link) ? match.link : null,
          list1: 'LINK',
          policyPresent: hasPolicyLink(match.link),
        };
      }
    }

    if (list1 === 'LINK') {
      return {
        id,
        title,
        description: cardDescription(description),
        link: hasPolicyLink(policyLink) ? policyLink : null,
        list1,
        policyPresent: hasPolicyLink(policyLink),
      };
    }

    if (list1 === 'View Document') {
      if (images && images.length > 0) {
        const imageMatch = images.find((img) => img.fileableId === id);
        if (imageMatch) {
          const policyPresent = !!imageMatch?.path;
          return {
            id,
            title,
            description: cardDescription(description),
            path: policyPresent ? imageMatch.path : null,
            list1,
            fileId: null,
            policyPresent,
          };
        }
      } else {
        return {
          id,
          title,
          description: cardDescription(description),
          path: null,
          list1,
          fileId: null,
          policyPresent: false,
        };
      }
    }
    return null;
  }

  // eslint-disable-next-line no-param-reassign
  cardData = extractPolicyData(cardData);
  const [curentPolicyType, setCurentPolicyType] = useState(getPolicyType(cardData));
  const validationSchema = yup.object({
    title: yup
      .string()
      .max(255, 'Title must be at most 255 characters'),
    description: yup
      .string()
      .max(255, 'Instructions must be at most 255 characters'),
  });

  const formikTextForm = useFormik({
    enableReinitialize: true,
    initialValues: {
      title: cardData?.title || '',
      description: cardData.description ? cardDescription(cardData.description) : '',
      lessonCardId: cardData?.id || '',
      link: cardData?.link || '',
      policyType: getPolicyType(cardData),
      fileId: cardData?.fileId || null,
    },
    validationSchema,
    onSubmit: handleTextFormSubmit,
  });

  async function handleTextFormSubmit(values) {
    try {
      await policyCardUpdate(values).then(() => onSavePolicies());
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Submit failed:', err);
    }
  }

  const handleRadioChange = (event) => {
    setCurentPolicyType(event.target.value);
    setPolicyUpdateConfirm(true);
  };
  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          px={2} // left and right padding = 0
          py={1} // optional top and bottom padding
          gap={2}
        >
          <Typography component="span" className={styles.spanCardDetails}>
            <strong>Card</strong> - #{cardData.id}
          </Typography>
          <Typography component="div" sx={{ flex: 2 }} />
          <Button
            variant="text"
            startIcon={<Visibility />}
            className={styles.actionButton}
            disableRipple
            onClick={() => window.open(`/preview/microlesson/${lessonId}/card/${cardData.id}?preview=true`, '_blank', 'noopener,noreferrer')}
            sx={{
              '& .MuiButton-startIcon': {
                marginRight: 0,
              },
            }}
          >
            <Typography component="span" className={styles.actionButton}>
              Preview
            </Typography>
          </Button>
        </Box>

        <Box
          sx={{
            px: 2,
          }}
        >
          <Typography variant="body2" className={styles.policyFormTitle}>
            Title
          </Typography>
          <form onSubmit={formikTextForm.handleSubmit}>
            <TextField
              id="title"
              name="title"
              type="text"
              placeholder="title"
              onChange={formikTextForm.handleChange}
              value={formikTextForm.values.title}
              onBlur={formikTextForm.handleBlur}
              error={formikTextForm.touched.title && Boolean(formikTextForm.errors.title)}
              data-cy="title"
              helperText={formikTextForm.touched.title && formikTextForm.errors.title}
              size="small"
              InputProps={{ style: { borderRadius: 5, width: 600, height: 36 } }}
              sx={{ pb: '4px' }}
            />

            <Typography variant="body2" className={styles.policyFormTitle}>
              Instructions
            </Typography>

            <TextField
              id="description"
              name="description"
              type="text"
              placeholder="instructions"
              onChange={formikTextForm.handleChange}
              value={formikTextForm.values.description}
              onBlur={formikTextForm.handleBlur}
              error={formikTextForm.touched.description && Boolean(formikTextForm.errors.description)}
              data-cy="description"
              helperText={formikTextForm.touched.description && formikTextForm.errors.description}
              size="larg"
              InputProps={{ style: { borderRadius: 5, height: 38 } }}
              fullWidth
              sx={{ pb: '4px' }}
            />

            <Box className={styles.saveSaveButtonBox}>
              <SendSaveButton
                name="save button"
                type="submit"
                disabled={!formikTextForm.isValid || (!formikTextForm.dirty)}
                label="save"
                data-cy="save-button"
              />
              <div width="10%">{formikTextForm && formikTextForm.isSubmitting && (
              <Spinner customWidth={28} />)}
              </div>
            </Box>
          </form>

          <Divider className={styles.policyFormDivider} />

          <Box
            display="flex"
            alignItems="center"
            px={0} // left and right padding = 0
            py={1} // top and bottom padding
            gap={2}
          >
            <Typography variant="body1" className={styles.policyUploadText}>
              Policy
            </Typography>
            <Typography component="div" sx={{ flex: 2 }} />
            <Box sx={{ flexShrink: 0 }} />
          </Box>

          <Box className={styles.policyUploadContainer}>
            <FormLabel component="legend" className={styles.policyUploadTextStyle}>
              Policy type*
            </FormLabel>
            <RadioGroup
              row
              name="options"
              defaultValue={curentPolicyType}
              value={curentPolicyType}
              onChange={handleRadioChange}
            >
              <FormControlLabel
                value="file"
                control={<Radio size="small" />}
                label={<Typography className={styles.remText}>PDF</Typography>}
                onClick={() => curentPolicyType === 'link' && setPolicyUpdateConfirm(true)}
              />
              <FormControlLabel
                value="link"
                control={<Radio size="small" />}
                label={<Typography className={styles.remText}>Link</Typography>}
                onClick={() => curentPolicyType === 'file' && setPolicyUpdateConfirm(true)}
              />
            </RadioGroup>
          </Box>
          <FileUploadWithUrlOption
            cardData={cardData}
            curentPolicyType={curentPolicyType}
            onSavePolicies={onSavePolicies}
            setCurentPolicyType={setCurentPolicyType}
            policyUpdateConfirm={policyUpdateConfirm}
            setPolicyUpdateConfirm={setPolicyUpdateConfirm}
            isPolicyPresent={isPolicyPresent}
          />
        </Box>
      </Box>
    </>
  );
}
