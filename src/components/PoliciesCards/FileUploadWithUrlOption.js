/* eslint-disable max-len */
import React, { useRef, useState, useEffect } from 'react';
import * as yup from 'yup';
import { Box, Typography, IconButton, Paper, TextField, Dialog, DialogActions, Button, useTheme } from '@mui/material';
import { useFormik } from 'formik';
import ClearIcon from '@mui/icons-material/Clear';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import { policyCardUpdate } from '../../services/api/policyCardUpdate';
import SendSaveButton from '../Button/SendSaveButton';
import Spinner from '../Spinner/Spinner';
import styles from '../../features/manage-content-library/content-library-details/ContentLibraryDetailsContainer.module.css';

function FileUploadWithUrlOption({ cardData, curentPolicyType, onSavePolicies, setCurentPolicyType, policyUpdateConfirm, setPolicyUpdateConfirm, isPolicyPresent }) {
  const { palette } = useTheme();
  const fileInputRef = useRef(null);
  const [showConfirm, setShowConfirm] = useState(false);
  const [linkUrlDelete, setLinkUrlDelete] = useState(false);
  const [changePolicy, setChangePolicy] = useState(false);
  const [selectedFile, setSelectedFile] = useState(false);

  const getFileNameByLanguage = (path) => {
    if (!path) return null;
    const fileName = path.split('/').pop();
    const parts = fileName.split('_');

    return parts.length > 1 ? parts.slice(1).join('_') : fileName;
  };

  // eslint-disable-next-line no-shadow
  const getPolicyDocs = (data) => {
    let policyDoc = '';
    if (data) {
      if (isPolicyPresent && curentPolicyType === 'file' && data.policyPresent) {
        policyDoc = getFileNameByLanguage(data?.path || '');
      }

      if (isPolicyPresent && curentPolicyType === 'link' && data.policyPresent) {
        policyDoc = data?.link || '';
      }
    }

    return policyDoc;
  };
  let isFileSave = false;
  const policyDocFileName = getPolicyDocs(cardData);
  if (isPolicyPresent && policyDocFileName) {
    isFileSave = true;
  }

  const handleBoxClick = () => {
    fileInputRef.current?.click();
  };

  function cardDescription(description) {
    const parser = new DOMParser();
    return typeof description === 'string' && description.trim().length > 0
      ? parser.parseFromString(description, 'text/html').body.textContent
      : '';
  }

  const validationSchema = yup.object({
    link: yup
      .string()
      .nullable()
      .notRequired()
      .matches(
        /^(https?:\/\/)(localhost|([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63})(:\d+)?(\/[^\s]*)?(\?[^\s]*)?(#[^\s]*)?$/,
        'Please enter a valid URL like https://example.com',
      ),
    file: yup
      .mixed()
      .nullable()
      .notRequired()
      .test('fileType', 'Only PDF files are allowed', (value) => {
        if (!value) return true; // allow empty (optional)
        return value && value.type === 'application/pdf';
      }),
  });

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      title: cardData?.title || '',
      description: cardData.description ? cardDescription(cardData.description) : '',
      link: policyDocFileName,
      lessonCardId: cardData?.id || '',
      policyType: curentPolicyType,
      fileId: cardData?.fileId || null,
      file: null,
    },
    validationSchema,
    onSubmit,
  });

  useEffect(() => {
    const hasChanged = Object.keys(formik.initialValues).some((key) => {
      return formik.values[key] !== formik.initialValues[key];
    });

    setChangePolicy(!hasChanged);
  }, [formik.values]);

  async function onSubmit(values) {
    try {
      await policyCardUpdate({ ...values, file: selectedFile }).then(() => onSavePolicies());
      setSelectedFile(null);
      setLinkUrlDelete(false);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Submit failed:', err);
    }
  }

  const handleFileChange = async (event) => {
    const file = event.currentTarget.files[0];

    await formik.setFieldValue('file', file); // set the file
    formik.setFieldTouched('file', true, false); // mark field as touched
    await formik.validateField('file'); // manually trigger validation

    setSelectedFile(file);
    setChangePolicy(false);
  };

  const handleClear = (event) => {
    event.stopPropagation();
    setSelectedFile(null);
    fileInputRef.current.value = null;
    // Clear Formik error for file field
    formik.setFieldError('file', '');
    formik.setFieldTouched('file', false, false);
  };
  return (
    <>
      <form onSubmit={formik.handleSubmit} encType="multipart/form-data">
        <Box className={styles.flexAlignZeroGap}>
          {curentPolicyType && curentPolicyType === 'file' ? (
            <>
              <input type="file" name="file" ref={fileInputRef} style={{ display: 'none' }} onChange={handleFileChange} />
              <Paper
                className={styles.flexBoxDashed}
                elevation={0}
                variant="outlined"
                onClick={curentPolicyType === 'file' && !isFileSave ? handleBoxClick : undefined}
                sx={{
                  cursor: isFileSave === true ? 'default' : 'pointer',
                  backgroundColor: isFileSave === true ? '#F2F2F5' : 'transparent',
                  borderWidth: isFileSave === true ? '1px' : '1.5px',
                  borderStyle: isFileSave === true ? 'solid' : 'dotted',
                  borderColor: isFileSave === true ? '#ccc' : '#999',
                }}
              >
                {curentPolicyType === 'file' && !isFileSave && !selectedFile && (
                  <Typography className={styles.centeredText}>
                    Choose a file
                  </Typography>
                )}

                {curentPolicyType === 'file' && isFileSave && (
                  <Typography className={styles.fullWidthSemiBoldText}>
                    {policyDocFileName}
                  </Typography>
                )}

                {curentPolicyType === 'file' && selectedFile && !isFileSave && (
                  <>
                    <Typography className={styles.ellipsisText}>
                      {selectedFile?.name}
                    </Typography>
                    <IconButton sx={{ height: '0.75em', padding: 0 }} onClick={handleClear}>
                      <ClearIcon sx={{ height: '0.75em' }} />
                    </IconButton>
                  </>
                )}
              </Paper>

              {curentPolicyType === 'file' && isFileSave && (
                <IconButton onClick={() => setShowConfirm(true)}>
                  <DeleteIcon sx={{ width: '1.2rem' }} />
                </IconButton>
              )}
            </>
          ) : (
            <>
              <Paper
                elevation={0}
                variant="outlined"
                className={styles.dashedFlexBox}
              >
                <TextField
                  id="link"
                  name="link"
                  type="text"
                  placeholder="Enter URL"
                  size="small"
                  variant="outlined"
                  fullWidth
                  value={formik.values.link}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.link && Boolean(formik.errors.link)}
                  helperText={formik.touched.link && formik.errors.link}
                  InputProps={{
                    readOnly: isFileSave,
                    sx: {
                      fontSize: '0.95rem',
                      py: 0.2,
                      height: '38px',
                      marginLeft: '-10px',
                      backgroundColor: isFileSave ? '#F2F2F5' : 'transparent',
                      fontWeight: curentPolicyType === 'link' && isFileSave ? 600 : 500,
                      color: '#6b7280',
                    },
                  }}
                  FormHelperTextProps={{
                    sx: {
                      mt: '2px',
                      fontSize: '0.90rem',
                      marginLeft: '-10px',
                    },
                  }}
                />
              </Paper>

              {curentPolicyType === 'link' && isFileSave && !linkUrlDelete && (
                <IconButton
                  onClick={() => {
                    setShowConfirm(true);
                    setLinkUrlDelete(true);
                  }}
                  sx={{ padding: 0 }}
                >
                  <DeleteIcon sx={{ width: '1.2rem' }} />
                </IconButton>
              )}
            </>
          )}
        </Box>

        {formik.errors.file && (
          <Typography
            sx={{
              fontSize: '0.90rem',
            }}
            color="error"
          >
            {formik.errors.file}
          </Typography>
        )}

        {(!(curentPolicyType === 'link' && isFileSave) || linkUrlDelete) && (
        <Box className={styles.flexContainer}>
          <SendSaveButton
            name="save button"
            type="submit"
            disabled={!formik.isValid || (!formik.dirty && !selectedFile) || (changePolicy)}
            label="save"
            data-cy="save-button"
          />
          <div style={{ width: '10%' }}>
            {formik && formik.isSubmitting && <Spinner customWidth={28} />}
          </div>
        </Box>
        )}

      </form>

      <Dialog
        open={policyUpdateConfirm || showConfirm}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            // eslint-disable-next-line no-unused-expressions
            policyUpdateConfirm ? setPolicyUpdateConfirm(false) : setShowConfirm(false);
          }
        }}
      >
        <Box className={styles.relativeBox}>
          <IconButton
            aria-label="close"
            onClick={() => {
              // eslint-disable-next-line no-unused-expressions
              policyUpdateConfirm ? setPolicyUpdateConfirm(false) : setShowConfirm(false);
              // eslint-disable-next-line no-unused-expressions
              policyUpdateConfirm ? setCurentPolicyType(curentPolicyType === 'file' ? 'link' : 'file') : '';
            }}
            sx={{
              position: 'absolute',
              right: 20,
              top: 16,
              width: '17px',
              height: '17px',
              color: (theme) => theme.palette.darkGrey,
            }}
          >
            <CloseIcon />
          </IconButton>
          <Box className={styles.centeredBox}>
            <Typography className={styles.confirmText}>
              {policyUpdateConfirm ? 'Changing the policy type will remove the current policy. Do you want to continue?' : `Are you sure you want to remove this ${curentPolicyType}?`}

            </Typography>
          </Box>

          <DialogActions
            className={styles.dialogActions}
          >
            <Button
              variant="contained"
              sx={{
                textTransform: 'capitalize',
                borderRadius: '1.5rem',
                color: 'primary.main',
                backgroundColor: 'background.highlighted',
                boxShadow: 'none',
                fontWeight: 600,
                fontSize: '24px',
                width: '133px',
                height: '49px',
                border: `1px solid ${palette.primary.darkGrey}`,
                '&:hover': {
                  backgroundColor: 'transparent',
                  boxShadow: 'none',
                  color: 'primary.darkLightPurple',
                  border: `1px solid ${palette.primary.darkLightPurple}`,
                },
                '&:focus': {
                  border: `solid ${palette.card.backgroundColor} 3px`,
                },
              }}
              onClick={() => {
                // eslint-disable-next-line no-unused-expressions
                policyUpdateConfirm ? setPolicyUpdateConfirm(false) : setShowConfirm(false);
                // eslint-disable-next-line no-unused-expressions
                policyUpdateConfirm ? setCurentPolicyType(curentPolicyType === 'file' ? 'link' : 'file') : '';
                setLinkUrlDelete(false);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                textTransform: 'capitalize',
                borderRadius: '1.5rem',
                backgroundColor: 'primary.main',
                color: 'primary.white',
                boxShadow: 'none',
                fontWeight: 600,
                fontSize: '24px',
                width: '152px',
                height: '49px',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                  boxShadow: 'none',
                },
                '&:disabled': {
                  color: 'primary.main',
                  backgroundColor: 'background.highlighted',
                },
                '&:focus': {
                  border: `solid ${palette.card.backgroundColor} 3px`,
                },
              }}
              onClick={async () => {
                formik.setFieldValue('fileId', null);
                formik.setFieldValue('link', '');
                if (policyUpdateConfirm) {
                  formik.setFieldValue('path', null);
                  formik.setFieldValue('policyType', curentPolicyType);
                  await formik.validateForm();
                  setPolicyUpdateConfirm(false);
                  setChangePolicy(true);
                } else {
                  setSelectedFile(null);
                  await formik.validateForm();
                  setShowConfirm(false);
                  setChangePolicy(false);
                }
                const errors = formik.errors;
                if (linkUrlDelete) return;

                if (Object.keys(errors).length === 0) {
                  formik.handleSubmit();
                } else {
                  // eslint-disable-next-line no-console
                  console.warn('Validation failed', errors);
                }
              }}
            >
              { policyUpdateConfirm ? 'Yes' : 'Remove'}
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
}

export default FileUploadWithUrlOption;
