import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCertificate } from '@fortawesome/free-solid-svg-icons';

const Certificate = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  ...props
}) => (
  <FontAwesomeIcon icon={faCertificate} {...props} />))(({ width, height, color }) => ({
  width,
  height,
  color,
}));

Certificate.defaultProps = {
  width: '16px',
  height: '16px',
  color: '#F64A93',
};

Certificate.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default Certificate;
