import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle } from '@fortawesome/free-solid-svg-icons';

const CheckCircle = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  ...props
}) => (
  <FontAwesomeIcon aria-hidden={false} icon={faCheckCircle} {...props} />))(({
  width, height, color, backgroundColor, borderRadius, theme }) => ({
  width,
  height,
  color: color || theme.palette.primary.greyPurple,
  backgroundColor,
  borderRadius,
}));

CheckCircle.defaultProps = {
  width: '17px',
  height: '17px',
  backgroundColor: 'transparent',
  borderRadius: '50%',
  focusable: false,
};

CheckCircle.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderRadius: PropTypes.string,
};

export default CheckCircle;
