import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileAlt } from '@fortawesome/free-regular-svg-icons';

const File = styled(({
  width,
  height,
  color,
  ...props
}) => (
  <FontAwesomeIcon icon={faFileAlt} {...props} />))(({ width, height, color }) => ({
  width,
  height,
  color,
}));

File.defaultProps = {
  width: '22px',
  height: '29px',
  color: '#166FD7',
};

File.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default File;
