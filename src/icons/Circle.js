import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircle } from '@fortawesome/free-regular-svg-icons';

const Circle = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  ...props
}) => (
  <FontAwesomeIcon icon={faCircle} {...props} />))(
  ({ width, height, color, backgroundColor, borderRadius }) => ({
    width,
    height,
    color,
    backgroundColor,
    borderRadius,
  }));

Circle.defaultProps = {
  width: '18px',
  height: '18px',
  color: '#555572',
  backgroundColor: 'transparent',
  borderRadius: '50%',
  focusable: false,
};

Circle.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderRadius: PropTypes.string,
};

export default Circle;
