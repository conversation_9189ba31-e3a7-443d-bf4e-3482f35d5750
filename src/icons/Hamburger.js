import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';

const Hamburger = styled(({
  width,
  height,
  color,
  ...props
}) => (
  <FontAwesomeIcon
    icon={faBars}
    tabIndex="0"
    {...props}
  />
))(({ width, height, color, theme }) => ({
  width,
  height,
  color,
  cursor: 'pointer',
  outline: 'none',
  '&:focus': {
    outline: '2px solid white !important',
    border: `2px solid ${theme?.palette?.card?.backgroundColor || '#000'} !important`,
    borderRadius: '4px',
  },
}));

Hamburger.defaultProps = {
  width: '20px',
  height: '20px',
};

Hamburger.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default Hamburger;
