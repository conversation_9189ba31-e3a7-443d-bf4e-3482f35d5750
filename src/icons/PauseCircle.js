import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPauseCircle } from '@fortawesome/free-solid-svg-icons';

const PauseCircle = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  ...props
}) => (
  <FontAwesomeIcon icon={faPauseCircle} {...props} />))(
  ({ width, height, color, backgroundColor, borderRadius }) => ({
    width,
    height,
    color,
    backgroundColor,
    borderRadius,
  }));

PauseCircle.defaultProps = {
  width: '14px',
  height: '14px',
  color: '#222442',
  backgroundColor: 'transparent',
  borderRadius: '50%',
};

PauseCircle.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderRadius: PropTypes.string,
};

export default PauseCircle;
