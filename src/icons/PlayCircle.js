import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlayCircle } from '@fortawesome/free-solid-svg-icons';

const PlayCircle = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  styleProps,
  ...props
}) => (
  <FontAwesomeIcon aria-hidden icon={faPlayCircle} {...props} />))(
  ({ width, height, color, backgroundColor, borderRadius, styleProps }) => ({
    width,
    height,
    color,
    backgroundColor,
    borderRadius,
    ...styleProps,
  }));

PlayCircle.defaultProps = {
  width: '18px',
  height: '18px',
  color: '#48C076',
  backgroundColor: 'transparent',
  borderRadius: '50%',
  focusable: false,
};

PlayCircle.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderRadius: PropTypes.string,
};

export default PlayCircle;
