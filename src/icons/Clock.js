import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock as faClockOutlined } from '@fortawesome/free-regular-svg-icons';
import { faClock as faClockSolid } from '@fortawesome/free-solid-svg-icons';

const Clock = styled(({ isSolidIcon, width, height, ...props }) => (
  <FontAwesomeIcon icon={isSolidIcon ? faClockSolid : faClockOutlined} {...props} />))(
  ({ width, height, isSolidIcon }) => ({
    width,
    height,
    color: isSolidIcon ? '#ffffff' : '#222442',
  }));

Clock.defaultProps = {
  isSolidIcon: false,
  width: '15px',
  height: '15px',
};

Clock.propTypes = {
  isSolidIcon: PropTypes.bool,
  width: PropTypes.string,
  height: PropTypes.string,
};

export default Clock;
