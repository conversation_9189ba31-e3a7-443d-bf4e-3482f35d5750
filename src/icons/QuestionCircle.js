import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faQuestionCircle } from '@fortawesome/free-solid-svg-icons';

const QuestionCircle = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  ...props
}) => (
  <FontAwesomeIcon icon={faQuestionCircle} {...props} />))(
  ({ width, height, color, backgroundColor, borderRadius }) => ({
    width,
    height,
    color,
    backgroundColor,
    borderRadius,
  }));

QuestionCircle.defaultProps = {
  width: '16px',
  height: '16px',
  color: '#40405D',
  backgroundColor: 'transparent',
  borderRadius: '50%',
};

QuestionCircle.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderRadius: PropTypes.string,
};

export default QuestionCircle;
