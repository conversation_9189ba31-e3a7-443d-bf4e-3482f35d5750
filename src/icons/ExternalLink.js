import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';

const ExternalLink = styled(({
  width,
  height,
  color,
  styleProps,
  ...props
}) => (
  <FontAwesomeIcon icon={faExternalLinkAlt} {...props} />))(
  ({ width, height, color, styleProps }) => ({
    width,
    height,
    color,
    ...styleProps,
  }));

ExternalLink.defaultProps = {
  width: '14px',
  height: '14px',
  color: '#40405D',
};

ExternalLink.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default ExternalLink;
