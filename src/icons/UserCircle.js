import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUserCircle } from '@fortawesome/free-solid-svg-icons';

const UserCircle = styled(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  ...props
}) => (
  <FontAwesomeIcon icon={faUserCircle} {...props} />))(({
  width,
  height,
  color,
  backgroundColor,
  borderRadius,
  theme,
}) => ({
  width,
  height,
  color: color || theme.palette.primary.greyPurple,
  backgroundColor,
  borderRadius,
}));

UserCircle.defaultProps = {
  width: '17px',
  height: '17px',
  backgroundColor: 'transparent',
  borderRadius: '50%',
};

UserCircle.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  backgroundColor: PropTypes.string,
  borderRadius: PropTypes.string,
};

export default UserCircle;
