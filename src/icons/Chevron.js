import React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp, faChevronRight, faChevronLeft } from '@fortawesome/free-solid-svg-icons';

const iconMapping = {
  up: faChevronUp,
  down: faChevronDown,
  left: faChevronLeft,
  right: faChevronRight,
};

const Chevron = styled(({
  direction,
  width,
  height,
  color,
  styleProps,
  ...props
}) => (
  <FontAwesomeIcon icon={iconMapping[direction]} {...props} />))(
  ({ width, height, color, styleProps, theme }) => ({
    width: width || '1.063rem',
    height: height || '1.063rem',
    color: color || theme.palette.primary.darkLightPurple,
    ...styleProps,
  }));

Chevron.defaultProps = {
  direction: 'up',
  styleProps: {},
};

Chevron.propTypes = {
  direction: PropTypes.string,
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
  styleProps: PropTypes.object,
};

export default Chevron;
