import React, { useState, useEffect } from 'react';
import { useApolloClient, useQuery } from '@apollo/client';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { get } from 'lodash';

import { useTranslation } from 'react-i18next';
import { pushToProgram, pushToMicroLesson, pushToHome, appRoutes } from '../features/navigation/Routes/AppRoutes';
import AssignmentQuery from '../features/assignment/AssignmentQuery.graphql';
import { useResponsiveMode } from '../hooks/useResponsiveMode';
import { usePreviewMode } from '../hooks/usePreviewMode';
import UserProfile from '../features/user-profile/UserProfile';
import Breadcrumb from '../components/Breadcrumb/Breadcrumb';
import Lessons from '../features/lessons/Lessons';
import AssignmentList from '../features/assignment/AssignmentList';
import Footer from '../features/footer/Footer';
import MobileHeader from '../features/mobile-header/MobileHeader';
import Banner from '../features/banner/Banner';
import UserDemographicData from '../features/user-profile/UserDemographicData';
import { useUser } from '../hooks/useUser';
import { useActiveAssignment } from '../hooks/useActiveAssignment';
import Welcome from '../features/welcome/Welcome';
import CompletedItemsIntro from '../features/completed-items/CompletedItemsIntro';
// import HiddenAlert from '../features/hidden-alert/HiddenAlert';
import SideDrawerOpenButton from '../features/navigation/SideDrawer/SideDrawerOpenButton';
import SideDrawer from '../features/navigation/SideDrawer/SideDrawer';
import AssignmentsDrawer from '../features/navigation/AssignmentsDrawer/AssignmentsDrawer';
import styles from './LearnLayout.module.css';
import ExpertsQA from '../features/user-profile/ExpertsQA';
import { getCurrentAccount } from '../services/api/currentAccount';
import { getIsCompletedItemView, getIsPreviewModeEnable,
  setIsCompletedItemView, setIsPreviewModeEnable } from '../services/api/authentication';
import PrivacyNotice from '../features/privacy-notice/PrivacyNotice';

export default function LearnLayout({ profileView, expertsQAview, fromExternal }) {
  const { path, params: { assignmentId, programId, lessonId, cardId } } = useRouteMatch();
  const [breadcrumb, setBreadcrumb] = useState([]);
  const [completedItems, setCompletedItems] = useState(getIsCompletedItemView());
  const [loginRedirectDone, setLoginRedirectDone] = useState(false);
  const [showCompletionCertificate, setShowCompletionCertificate] = useState(false);
  const [scrollToAssignment, setScrollToAssignment] = useState(false);
  const [timerPaused, setTimerPaused] = useState(false);
  const [loadedRouteParams, setLoadedRouteParams] = useState(null);
  const [urlLoadedCardDone, setUrlLoadedCardDone] = useState(false);
  const [selectedLanguages, setSelectedLanguages] = useState(null);
  const [currentAccount, setCurrentAccount] = useState(null);
  const { isAssignmentsDrawerView, getDevice } = useResponsiveMode();
  const client = useApolloClient();
  const history = useHistory();
  const [itemCount, setItemCount] = useState(0);
  const user = useUser();
  const isContentReviewer = get(user, 'accounts[0].accountUsers.roleId') === 7;
  const isAdminUser = [4, 5, 6, 8, 10].includes(get(user, 'accounts[0].accountUsers.roleId'));
  const { setActiveAssignmentInfoFromCacheItem } = useActiveAssignment();
  const { previewMode, previewAssignmentData } = usePreviewMode();
  const { t } = useTranslation();
  const [privacyViewed, setPrivacyViewed] = useState(user.privacyNoticeAt);

  // Hide privacy notice for accounts 1163 CHOP (NLE-318), 1248 Go1 (NLE-270), 1271 HR Transform (NLE-350) 1352 Zoura (NLE-242).
  // eslint-disable-next-line max-len
  const checkPrivacyPermissions = get(user, 'accounts[0].id') !== 1352 && get(user, 'accounts[0].id') !== 1271 && get(user, 'accounts[0].id') !== 1248 && get(user, 'accounts[0].id') !== 1163;
  // eslint-disable-next-line no-unneeded-ternary
  const enforceScormSequence = (user?.scorm && user?.enforceSequence) ? true : false;

  const { loading: assignmentListLoading, data: assignmentListData, refetch } =
    useQuery(AssignmentQuery,
      { variables: { completed: completedItems,
        scormProgramId: user.scorm ? user.scormProgramId : null,
        assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : null,
        assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : null,
        scormEnforceSequence: enforceScormSequence,
      } });

  function setCompletedItemsView(showCompletedItems) {
    setIsCompletedItemView(showCompletedItems);
    if (showCompletedItems !== completedItems) {
      setCompletedItems(showCompletedItems);
      setShowCompletionCertificate(false);
    }
  }

  function findIncompleteProgramLessonIdToOpen(assignmentItem, defaultToNext = false) {
    if (assignmentItem && assignmentItem.type === 'program' && get(assignmentItem, 'content.lessons[0]')) {
      const lesson = assignmentItem.content.lessons.find(({ status }) => status !== 'completed');
      if (lesson) {
        return lesson.uid;
      }
      // if there are no lessons that aren't completed, find the next one in sequence, or the first one.
      const currentLessonIndex = assignmentItem.content.lessons.findIndex(({ uid }) => uid === lessonId);
      // default to next and we can find a next lesson, then return it.
      if (defaultToNext && currentLessonIndex !== -1 &&
        currentLessonIndex < assignmentItem.content.lessons.length - 1) {
        return assignmentItem.content.lessons[currentLessonIndex + 1].uid;
      }
      // if !defaultToNext, return the current lesson. This handles the reload case.
      if (!defaultToNext && currentLessonIndex !== -1) {
        return lessonId;
      }
      return assignmentItem.content.lessons[0].uid;
    }
    return null;
  }

  // Utility function to update the isLastViewedCard property in the cache
  const updateLastViewedAssignment = (selectedAssignmentId) => {
    try {
      // Read the current assignment list from the cache
      const assignmentsData = client.readQuery({
        query: AssignmentQuery,
        variables: { completed: completedItems,
          scormProgramId: user.scorm ? user.scormProgramId : null,
          assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : null,
          assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : null,
          scormEnforceSequence: enforceScormSequence,
        },
      });

      if (!assignmentsData) {
        return null;
      }

      const assignmentItems = get(assignmentsData, 'getAssignmentList', []);

      // Create a new list with updated isLastViewedCard properties
      const updatedAssignmentItems = assignmentItems.map((assignment) => {
        // Set isLastViewedCard to true for the selected assignment, null for all others
        const isLastViewed = assignment.id === selectedAssignmentId;
        return {
          ...assignment,
          isLastViewedCard: isLastViewed ? true : null,
        };
      });

      // Write the updated list back to the cache
      const updatedData = { getAssignmentList: updatedAssignmentItems };
      client.writeQuery({
        query: AssignmentQuery,
        variables: { completed: completedItems,
          scormProgramId: user.scorm ? user.scormProgramId : null,
          assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : null,
          assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : null,
          scormEnforceSequence: enforceScormSequence,
        },
        data: updatedData,
      });

      return updatedAssignmentItems;
    } catch (error) {
      console.error('Error updating last viewed assignment:', error);
      return null;
    }
  };

  const openAssignmentFirstItem = (assignmentItem) => {
    if (assignmentItem.type === 'program') {
      history.push(pushToProgram(
        {
          assignmentId: assignmentItem.id,
          programId: assignmentItem.content.uid,
          lessonId: assignmentItem.content.lessons[0].uid,
        },
      ));
      // Update the last viewed assignment in the cache
      updateLastViewedAssignment(assignmentItem.id);
    } else if (assignmentItem.type === 'lesson') {
      history.push(pushToMicroLesson(
        { assignmentId: assignmentItem.id, lessonId: assignmentItem.content.uid },
      ));
      // Update the last viewed assignment in the cache
      updateLastViewedAssignment(assignmentItem.id);
    }
  };

  const openNextCompletedAssignment = (assignmentItems) => {
    const assignmentIndex = assignmentItems.findIndex(({ id }) => id === assignmentId);
    if (assignmentIndex !== -1) {
      const currentAssignment = assignmentItems[assignmentIndex];
      if (currentAssignment && currentAssignment.type === 'program') {
        const currentLessonIndex = currentAssignment.content.lessons.findIndex(({ uid }) => uid === lessonId);
        if (currentLessonIndex < currentAssignment.content.lessons.length - 1) {
          const lessonIdToOpen = currentAssignment.content.lessons[currentLessonIndex + 1];
          history.push(pushToProgram(
            {
              assignmentId: currentAssignment.id,
              programId: currentAssignment.content.uid,
              lessonId: lessonIdToOpen.uid },
          ));
          // Update the last viewed assignment in the cache
          updateLastViewedAssignment(currentAssignment.id);
          return;
        }
        if (assignmentIndex < assignmentItems - 1) {
          openAssignmentFirstItem(assignmentItems[assignmentIndex + 1]);
          return;
        }
      } else if (currentAssignment &&
        currentAssignment.type === 'lesson' &&
        assignmentIndex < assignmentItems.length - 1) {
        openAssignmentFirstItem(assignmentItems[assignmentIndex + 1]);
        return;
      }
    }
    history.push(pushToHome());
  };

  const openNextToDoAssignment = (assignmentItems) => {
    // First chack to see if we are attempting to load an assignment that isn't present.
    const loadedTargetAssignment = assignmentItems.find(({ id }) => id === assignmentId);
    const loadedTargetNotFound =
      !loadedTargetAssignment && loadedRouteParams && loadedRouteParams.assignmentId === assignmentId;

    if (loadedRouteParams && !urlLoadedCardDone && !loadedTargetNotFound) {
      return;
    }
    // first check to see if we are currently in a program assignment
    const assignmentIndex = assignmentItems.findIndex(({ id }) => id === assignmentId);
    if (assignmentIndex !== -1) {
      const currentAssignment = assignmentItems[assignmentIndex];
      if (currentAssignment &&
        currentAssignment.type === 'program' &&
        currentAssignment.content.status !== 'completed') {
        // if we are currently in a program we should go to the next assignment in the program
        const currentLessonIndex = currentAssignment.content.lessons.findIndex(({ uid }) => uid === lessonId);
        if (currentLessonIndex < currentAssignment.content.lessons.length - 1) {
          const lessonIdToOpen = currentAssignment.content.lessons[currentLessonIndex + 1];
          history.push(pushToProgram(
            {
              assignmentId: currentAssignment.id,
              programId: currentAssignment.content.uid,
              lessonId: lessonIdToOpen.uid },
          ));
          // Update the last viewed assignment in the cache
          updateLastViewedAssignment(currentAssignment.id);
          return;
        }
        // if we were already at the end of the program, go to the first open assignment in that program
        const lessonIdToOpen = findIncompleteProgramLessonIdToOpen(currentAssignment, true);
        if (lessonIdToOpen) {
          history.push(pushToProgram(
            { assignmentId: currentAssignment.id, programId: currentAssignment.content.uid, lessonId: lessonIdToOpen },
          ));
          // Update the last viewed assignment in the cache
          updateLastViewedAssignment(currentAssignment.id);
          return;
        }
      }
    }

    const lastViewedAssignments = assignmentItems.filter(({ isLastViewedCard }) => isLastViewedCard === true);

    // If multiple assignments have isLastViewedCard set to true, update the cache to fix it
    if (lastViewedAssignments.length > 1) {
      // Keep the most recently updated one (assuming the first one in the list)
      updateLastViewedAssignment(lastViewedAssignments[0].id);
    }

    let todoAssignment = assignmentItems.find(({ isLastViewedCard }) => isLastViewedCard === true);
    if (!todoAssignment) {
      // find the next assignment that isn't completed
      todoAssignment = assignmentItems.find(({ content }) => content.status !== 'completed');
    }
    // if this is a scorm user, and there is only one assignment that has already been completed, we open it here.
    if (user.scorm && !todoAssignment && assignmentItems.length === 1) {
      todoAssignment = assignmentItems[0];
    }
    if (todoAssignment && todoAssignment.type === 'program') {
      const lessonIdToOpen = findIncompleteProgramLessonIdToOpen(todoAssignment, true);
      if (lessonIdToOpen) {
        history.push(pushToProgram(
          // eslint-disable-next-line max-len
          { assignmentId: todoAssignment.id, programId: todoAssignment.content.uid, lessonId: todoAssignment.lastViewedLessonId || lessonIdToOpen },
        ));
        // Update the last viewed assignment in the cache
        updateLastViewedAssignment(todoAssignment.id);
        setScrollToAssignment(true);
        return;
      }
    }
    if (todoAssignment && todoAssignment.type === 'lesson') {
      history.push(pushToMicroLesson(
        { assignmentId: todoAssignment.id, lessonId: todoAssignment.lastViewedLessonId || todoAssignment.content.uid },
      ));
      // Update the last viewed assignment in the cache
      updateLastViewedAssignment(todoAssignment.id);
      setScrollToAssignment(true);
      return;
    }
    history.push(pushToHome());
  };

  const removeCompletedTodoItems = () => {
    if (user.scorm || previewMode || isContentReviewer) {
      return;
    }
    const todoAssignmentListData = client.readQuery({
      query: AssignmentQuery,
      variables: { completed: false,
        scormProgramId: user.scorm ? user.scormProgramId : null,
        assignmentLessonId: null,
        assignmentProgramId: null,
        scormEnforceSequence: enforceScormSequence,
      },
    });
    const todoAssignmentItems = get(todoAssignmentListData, 'getAssignmentList', []);
    // const completedTodoAssignmentItems = todoAssignmentItems.filter(({ content }) => content.status !== 'completed');
    const newTodoAssignmentItems = todoAssignmentItems.filter(({ content }) => content.status !== 'completed');
    const newTodoQueryData = { getAssignmentList: newTodoAssignmentItems };
    client.writeQuery({
      query: AssignmentQuery,
      variables: { completed: false,
        scormProgramId: user.scorm ? user.scormProgramId : null,
        assignmentLessonId: null,
        assignmentProgramId: null,
        scormEnforceSequence: enforceScormSequence,
      },
      data: newTodoQueryData,
    });
  };

  const openNextAssignment = () => {
    if (previewAssignmentData && previewAssignmentData.cardId) {
      return;
    }
    const assignmentsData = client.readQuery({
      query: AssignmentQuery,
      variables: { completed: completedItems,
        scormProgramId: user.scorm ? user.scormProgramId : null,
        assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : null,
        assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : null,
        scormEnforceSequence: enforceScormSequence,
      },
    });
    if (!assignmentsData) {
      return;
    }
    const assignmentItems = get(assignmentsData, 'getAssignmentList', []);
    const previewModeEnable = getIsPreviewModeEnable();
    if (previewModeEnable) {
      setIsPreviewModeEnable(JSON.stringify(previewAssignmentData));
    }
    if ((!assignmentItems || assignmentItems.length === 0) && !assignmentListLoading) {
      history.push(pushToHome());
      return;
    }
    if (completedItems) {
      openNextCompletedAssignment(assignmentItems);
    } else {
      removeCompletedTodoItems();
      openNextToDoAssignment(assignmentItems);
    }
  };

  // when the assignment list updates, we need to ensure we are not trying to navigate
  // to an assignment that isn't present in the list. This could potentially happen
  // if the user reloads the page when viewing an item in the completed list.
  const onAssignmentListUpdate = () => {
    const assignmentsData = client.readQuery({
      query: AssignmentQuery,
      variables: { completed: completedItems,
        scormProgramId: user.scorm ? user.scormProgramId : null,
        assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : null,
        assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : null,
        scormEnforceSequence: enforceScormSequence,
      },
    });

    const assignmentItems = get(assignmentsData, 'getAssignmentList', []);
    let cardPreviewItem;
    if (assignmentItems) {
      setItemCount(assignmentItems.length);

      const lastViewedAssignments = assignmentItems.filter(({ isLastViewedCard }) => isLastViewedCard === true);

      // If multiple assignments have isLastViewedCard set to true, update the cache to fix it
      if (lastViewedAssignments.length > 1) {
        // Keep the most recently updated one (assuming the first one in the list)
        updateLastViewedAssignment(lastViewedAssignments[0].id);
      }

      if (assignmentItems.length > 0 && assignmentItems[0].assignmentType === 'preview'
        && previewAssignmentData && previewAssignmentData.cardId) {
        cardPreviewItem = assignmentItems[0];
      }
    }
    if (cardPreviewItem) {
      // eslint-disable-next-line max-len
      const previewPath = `/assignment/${cardPreviewItem.id}/microlesson/${previewAssignmentData.lessonId}/card/${previewAssignmentData.cardId}`;
      history.push(previewPath);
      return;
    }

    if (assignmentsData && (!programId && !lessonId &&
       (isAdminUser || user.displayedWelcome || !currentAccount?.welcomeMessageEnabled)) &&
      path !== appRoutes.USER_PROFILE && (!loginRedirectDone || !completedItems)) {
      if (!loginRedirectDone) {
        setLoginRedirectDone(true);
      }
      openNextAssignment();
      return;
    }
    if (assignmentId && assignmentItems) {
      const targetAssignment = assignmentItems.find(({ id }) => id === assignmentId);
      if (!targetAssignment) {
        openNextAssignment();
      } else {
        // If we found the target assignment, ensure it's marked as the last viewed
        updateLastViewedAssignment(assignmentId);
      }
    }
  };

  const checkMoreAssignmentsAvailable = () => {
    const assignmentsData = client.readQuery({
      query: AssignmentQuery,
      variables: { completed: completedItems,
        scormProgramId: user.scorm ? user.scormProgramId : null,
        assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : null,
        assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : null,
        scormEnforceSequence: enforceScormSequence,
      },
    });
    const assignmentItems = get(assignmentsData, 'getAssignmentList', []);
    if (!completedItems) {
      const incompleteTodoAssignment = assignmentItems.find(({ content }) => content.status !== 'completed');
      if (incompleteTodoAssignment) {
        return true;
      }
      return false;
    }
    // if we are in the completed list, just check to see if the current assignment is the last one.
    const assignmentIndex = assignmentItems.findIndex(({ id }) => id === assignmentId);
    if (assignmentIndex === -1) {
      return false;
    }
    if (assignmentIndex < assignmentItems.length - 1) {
      return true;
    }
    return false;
  };

  function handleBreadCrumb({ program, lesson }) {
    if (!program && !lesson) return;
    const trail = program ? [program.title, lesson.title] : [lesson.title];
    setBreadcrumb(trail);
  }

  // This effect is to set URL params that are present when the app is first loaded
  // This is used to determine if we need to naviage directly to an assignment URL.
  useEffect(() => {
    if (assignmentId) {
      setLoadedRouteParams({ assignmentId, programId, lessonId, cardId });
    } else {
      setLoadedRouteParams(null);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // If we're coming from external, be sure to clear the forward route once we land here (our destination)
  useEffect(() => {
    if (fromExternal) {
      localStorage.removeItem('forwardToRoute');
    }
  }, [fromExternal]);

  // This effect will ensure that the assignment list is refetched from the server
  // every time the tab is changed from todo to completed. We should look at how
  // this performs and maybe add some additional checks if we determine that this is overfetching.
  useEffect(() => {
    refetch();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [completedItems]);

  useEffect(() => {
    if (assignmentListLoading) {
      return;
    }
    if (assignmentListData) {
      onAssignmentListUpdate();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignmentListData, assignmentListLoading]);

  // this effect ensures that the breadcrumb is in sync with the currently loaded assignment.
  useEffect(() => {
    const assignmentItems = get(assignmentListData, 'getAssignmentList', []);
    const assignment = assignmentItems.find(({ id }) => id === assignmentId);
    // Once we've accessed an assignment, we need to mark that we've already displayed the welcome message.
    if (assignment && !user.displayedWelcome) {
      user.displayedWelcome = true;
    }
    if (assignment && programId) {
      const lessons = get(assignment, 'content.lessons');
      const lesson = lessons.find(({ uid }) => uid === lessonId);
      if (assignment && lesson) {
        handleBreadCrumb({ program: assignment.content, lesson });
      }
    } else if (assignment) {
      handleBreadCrumb({ lesson: assignment.content });
    }
    if (assignment) {
      setActiveAssignmentInfoFromCacheItem(assignment);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignmentId, programId, lessonId, assignmentListData]);

  useEffect(() => {
    refetch();
  }, [user.language]);

  const assignmentsComponent = () => {
    return (
      <AssignmentList
        assignmentListData={assignmentListData}
        completedItems={completedItems}
        handleBreadCrumb={handleBreadCrumb}
        onSetCompletedItems={setCompletedItemsView}
        findProgramLessonIdToOpen={findIncompleteProgramLessonIdToOpen}
        onAssignmentListUpdate={onAssignmentListUpdate}
        setShowCompletionCertificate={setShowCompletionCertificate}
        timerPaused={timerPaused}
        loadedRouteParams={loadedRouteParams}
        setScrollToAssignment={setScrollToAssignment}
        scrollToAssignment={scrollToAssignment}
        updateLastViewedAssignment={updateLastViewedAssignment}
      />
    );
  };

  useEffect(() => {
    async function getSelectedLanguages() {
      const account = await getCurrentAccount();
      setSelectedLanguages(get(account, 'selectedLanguages'));
      setCurrentAccount(account);
    }
    getSelectedLanguages();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const showWelcome = !completedItems && !lessonId && !user.scorm && !previewMode && !isContentReviewer;
  const showCompleteItemsIntro = completedItems && !lessonId;
  const showPreviewBanner = previewMode && !user.scorm;
  const showScormBanner = !previewMode && user.scorm;

  // lesson card inputs scroll to the top element in the content area
  const scrollTargets = { previewBanner: '', breadcrumb: '' };
  if (showPreviewBanner) { scrollTargets.previewBanner = 'card-scrolltop-target'; }
  if (!showPreviewBanner) { scrollTargets.breadcrumb = 'card-scrolltop-target'; }

  // banner height is passed to the drawers to set top margins and padding top for the content container
  let bannerHeight = '0';
  const device = getDevice();
  const bannerHeights = { smallMobile: 6, mobile: 5.8, tablet: 5.25 };
  if (showScormBanner || showPreviewBanner) {
    bannerHeight = bannerHeights[device] || 5.1;
    if (showScormBanner) { bannerHeight -= 1.5; }
    if (showPreviewBanner && ['smallMobile', 'mobile', 'tablet'].includes(device)) { bannerHeight = 0; }
  }
  const contentPaddingTop = (isAssignmentsDrawerView || !showScormBanner) ? '0' : `${bannerHeight}rem`;

  return (
    <>
      {showPreviewBanner && (
        <Banner name="preview" bannerHeight={bannerHeight} id={scrollTargets.previewBanner} />
      )}
      {showScormBanner && (
        <Banner name="scormCloseExit" bannerHeight={bannerHeight} />
      )}
      {isAssignmentsDrawerView && (
        <div style={{ paddingTop: `${bannerHeight}rem` }}>
          <MobileHeader completedItems={completedItems} onSetCompletedItems={setCompletedItemsView} />
        </div>
      )}
      <div
        className={styles.container}
        style={{ paddingTop: contentPaddingTop,
          minHeight: ((isAssignmentsDrawerView && !user.scorm) || previewMode) ? 'calc(100vh - 4.5rem)' : '100vh' }}
      >
        {isAssignmentsDrawerView && (
          <AssignmentsDrawer position="left" bannerHeight={bannerHeight}>
            {assignmentsComponent()}
          </AssignmentsDrawer>
        )}
        {!isAssignmentsDrawerView && (
          assignmentsComponent()
        )}
        {!isAssignmentsDrawerView && profileView && currentAccount && (
          <div className={styles.contentContainer}>
            <div className={styles.breadcrumbAndUserMenuContainer} id={scrollTargets.breadcrumb}>
              <div />
              <SideDrawerOpenButton role="region" aria-label={t('userMenu.userMenu')} />
            </div>
            <UserProfile currentAccount={currentAccount} />
          </div>
        )}
        {isAssignmentsDrawerView && profileView && currentAccount && (
          <div className={styles.contentContainer}>
            <UserProfile currentAccount={currentAccount} />
          </div>
        )}
        {expertsQAview && (
          <div className={styles.contentContainer}>
            <ExpertsQA />
          </div>
        )}
        {(!profileView && !expertsQAview) && (
          <div className={styles.contentContainer} id="content-container">
            <div className={styles.breadcrumbAndUserMenuContainer} id="card-scrolltop-target">
              <Breadcrumb breadcrumbTrail={(lessonId || programId || assignmentId) ? breadcrumb : []} />
              {!isAssignmentsDrawerView && (
                <SideDrawerOpenButton />
              )}
            </div>
            {showWelcome && !assignmentListLoading && (
              <Welcome
                itemCount={itemCount}
                loginRedirectDone={loginRedirectDone}
                onSetCompletedItems={setCompletedItemsView}
              />
            )}
            {showCompleteItemsIntro && !assignmentListLoading && (
              <>
                <CompletedItemsIntro itemCount={itemCount} setCompletedItems={setCompletedItems} />
                {/* <HiddenAlert text={t('completedItems.selectCompletedList')} /> */}
              </>
            )}
            {lessonId && (
              <>
                <Lessons
                  onNextAssignment={openNextAssignment}
                  checkMoreAssignmentsAvailable={checkMoreAssignmentsAvailable}
                  showCompletionCertificate={showCompletionCertificate}
                  setShowCompletionCertificate={setShowCompletionCertificate}
                  removeCompletedTodoItems={removeCompletedTodoItems}
                  timerPaused={timerPaused}
                  setTimerPaused={setTimerPaused}
                  loadedRouteParams={loadedRouteParams}
                  setUrlLoadedCardDone={setUrlLoadedCardDone}
                  urlLoadedCardDone={urlLoadedCardDone}
                />
              </>
            )}
            {!lessonId && (
              <Footer />
            )}
          </div>
        )}
        <SideDrawer
          position="right"
          bannerHeight={bannerHeight}
          selectedLanguages={selectedLanguages}
          preview={previewMode}
        />
        {(privacyViewed || !checkPrivacyPermissions) && <UserDemographicData />}
      </div>
      {checkPrivacyPermissions && !privacyViewed && (
        <PrivacyNotice privacyViewed={!!privacyViewed} setPrivacyViewed={setPrivacyViewed} />
      )}
    </>
  );
}
