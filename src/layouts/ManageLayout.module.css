/* the entire screen */
.container {
  display: flex;
  height: 100%;
  overflow: auto;
}

.containerWidth {
  min-width: 1180px;
}

/* the entire app to the right of the left menu */
.contentContainer {
  width: 100%;
  flex: 2.6;
  padding: .5rem 0 0 0;
  overflow: auto;
}

.topNavigationContainer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0.2rem 0rem 0rem 1.2rem;
  align-items: flex-end;
}
