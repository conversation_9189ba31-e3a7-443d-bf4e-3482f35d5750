/* eslint-disable max-len */
import React, { useState, useEffect, lazy } from 'react';
import { Switch, useLocation } from 'react-router-dom';
import { get } from 'lodash';

import { Typography } from '@mui/material';
import { useTheme } from '@mui/system';
import SideDrawerOpenButton from '../features/navigation/SideDrawer/SideDrawerOpenButton';
import SideDrawer from '../features/navigation/SideDrawer/SideDrawer';
import styles from './ManageLayout.module.css';
import { getCurrentAccount } from '../services/api/currentAccount';
import CustomerAdminNavigation from '../features/navigation/ManageSideNavigation/CustomerAdminNavigation';
import AuthedRoute from '../features/navigation/Routes/AuthedRoute';
import { appRoutes } from '../features/navigation/Routes/AppRoutes';
import { canAccessContentConfiguration } from '../services/api/permissions';
import { useUser } from '../hooks/useUser';
import { ContentLibraryProvider } from '../hooks/useContentLibrary';

const InsightsContainer = lazy(() => import('../features/insights-report/insightsContainer'));
const ResourceContainer = lazy(() => import('../features/manage-resources/resourceContainer'));
const AnalyticsContainer = lazy(() => import('../features/manage-analytics/analyticsContainer'));
const ContentLibraryContainer = lazy(() => import('../features/manage-content-library/ContentLibraryContainer'));
const ContentLibraryDetailsContainer = lazy(() => import('../features/manage-content-library/content-library-details/ContentLibraryDetailsContainer'));

const { INSIGHTS_PROGRAMS, INSIGHTS_LESSONS, RESOURCES, CONTENT_LIBRARY,
  ANALYTICS, ANALYTICS_PAGE, CONTENT_LIBRARY_DETAILS, CONTENT_CONFIGURE,
  CONTENT_POLICIES, CONTENT_COURSE_LESSON_DETAILS, COURSE_LESSON_DETAILS } = appRoutes;

const pageTitles = {
  dashboard: 'Dashboard',
  campaigns: 'Campaigns',
  contentLibrary: 'Content Library',
  'new-content-library': 'Content Library',
  resources: 'Resources',
  library: 'Master Library',
  insights: 'Reports',
  config: 'Site Config',
  users: 'Users',
  groups: 'Groups',
  analytics: 'Analytics Original',
  analytics2: 'Emtrain Intelligence',
};

export default function ManageLayout({ manageView = false }) {
  const location = useLocation();
  const { pathname } = location;
  const { palette } = useTheme();
  const user = useUser();
  const permissions = get(user, 'permissions', true);
  const [title, setTitle] = useState([]);
  const [selectedLanguages, setSelectedLanguages] = useState(null);
  const [navSelection, setNavSelection] = useState(null);
  const [currentAccount, setCurrentAccount] = useState(null);
  const isContentLibraryPath = pathname && pathname.includes('/new-content-library');
  const mainContainer = isContentLibraryPath ? `${styles.container} ${styles.containerWidth}` : `${styles.container}`;

  const manageSideLayoutComponent = () => {
    return (
      <CustomerAdminNavigation
        navSelection={navSelection}
        setNavSelection={setNavSelection}
        currentAccount={currentAccount}
      />
    );
  };

  useEffect(() => {
    async function getSelectedLanguages() {
      const account = await getCurrentAccount();
      setCurrentAccount(account);
      setSelectedLanguages(get(account, 'selectedLanguages'));
    }
    getSelectedLanguages();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    let titleText = pageTitles[navSelection] || '';
    titleText += navSelection === 'analytics2' && currentAccount?.name ? ` for ${currentAccount.name}` : '';
    setTitle(titleText);
  }, [navSelection, currentAccount]);

  return (
    <>
      <div className={mainContainer} style={{ background: palette.background.lightShadeGrey }}>
        {manageSideLayoutComponent()}
        {manageView && (
          <div className={`${styles.contentContainer} ${styles.containerWidth}`}>
            <div className={styles.topNavigationContainer}>
              <Typography sx={{ fontSize: '2em',
                fontWeight: 600,
                color: palette.primary.blackCoral }}
              >{title}
              </Typography>
              <SideDrawerOpenButton manageView />
            </div>
            <Switch>
              <AuthedRoute exact path={[INSIGHTS_PROGRAMS, INSIGHTS_LESSONS]}>
                <InsightsContainer />
              </AuthedRoute>
              <AuthedRoute exact path={[RESOURCES]}>
                <ResourceContainer />
              </AuthedRoute>
              <AuthedRoute exact path={[ANALYTICS, ANALYTICS_PAGE]}>
                <AnalyticsContainer />
              </AuthedRoute>
              {canAccessContentConfiguration(permissions) && (
                <ContentLibraryProvider>
                  <AuthedRoute exact path={[CONTENT_LIBRARY]}>
                    <ContentLibraryContainer />
                  </AuthedRoute>
                  <AuthedRoute exact path={[CONTENT_LIBRARY_DETAILS, CONTENT_CONFIGURE, CONTENT_POLICIES, CONTENT_COURSE_LESSON_DETAILS, COURSE_LESSON_DETAILS]}>
                    <ContentLibraryDetailsContainer />
                  </AuthedRoute>
                </ContentLibraryProvider>
              )}
            </Switch>
          </div>
        )}
        <SideDrawer position="right" selectedLanguages={selectedLanguages} manageView={manageView} />
      </div>
    </>
  );
}
