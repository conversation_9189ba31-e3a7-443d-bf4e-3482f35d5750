import React, { useEffect, useState, useMemo } from 'react';
import { Paper, Typography, useTheme } from '@mui/material';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { get } from 'lodash';
import styles from './Insights.module.css';
import Insights from './Insights';
import GetProgramsQuery from './GetProgramsQuery.graphql';
import GetLessonsQuery from './GetLessonsQuery.graphql';
import GetInsightReportsQuery from './GetInsightReportsQuery.graphql';
import { pushToInsightLessonReports, pushToInsightProgramReports } from '../navigation/Routes/AppRoutes';
import Spinner from '../../components/Spinner/Spinner';
import InsightsReport from './InsightsReport';

function InsightsContainer() {
  const { palette } = useTheme();
  const { params: routeParams } = useRouteMatch();
  const type = routeParams.programId ? 'program' : 'lesson';
  const contentId = routeParams.programId ? routeParams.programId : routeParams.lessonId;
  const queryParams = window.location.search;

  const history = useHistory();

  const { loading, data: getPrograms } = useQuery(GetProgramsQuery);
  const programs = get(getPrograms, 'getPrograms.data', []);

  const { loading: lessonsLoading, data: getLessons } = useQuery(GetLessonsQuery);
  const lessons = get(getLessons, 'getLessons.data', []);

  const contents = [...programs, ...lessons];

  let startDate = '';
  let endDate = '';

  if (queryParams && queryParams.length > 0) {
    const idx = queryParams.indexOf('?');
    if (idx !== -1) {
      const newParams = queryParams.substr(idx + 1, queryParams.length);
      const params = newParams.split('&');
      if (params.length > 0) {
        const parts = params[0].split('=');
        if (parts.length > 1 && parts[1].length > 0) {
          const dateIdx = parts[0].indexOf('startDate');
          if (dateIdx !== -1) {
            startDate = parts[1];
          } else {
            endDate = parts[1];
          }
        }
      }
      if (params.length > 1) {
        const parts = params[1].split('=');
        if (parts.length > 1 && parts[1].length > 0) {
          const dateIdx = parts[0].indexOf('startDate');
          if (dateIdx !== -1) {
            startDate = parts[1];
          } else {
            endDate = parts[1];
          }
        }
      }
    }
  }

  const { loading: insightReportsLoading, data: getInsightReports, refetch } = useQuery(GetInsightReportsQuery,
    { variables: { contentId, type, startDate, endDate } });

  const getInsightReportsLessons = get(getInsightReports, 'getInsightReports.data', []);

  const microLessons = useMemo(() => {
    const programLessons = [];
    getInsightReportsLessons.map(({ lessonId, lessonName }) => {
      return programLessons.push({ __typename: 'Lesson', id: lessonId, title: lessonName });
    });
    return programLessons;
  }, [getInsightReportsLessons]);

  // eslint-disable-next-line array-callback-return
  const contentTitle = contents.find((opt) => {
    const { id, __typename } = opt;
    if (id === contentId && __typename.toLowerCase() === type) { return opt; }
  });

  const initialValues = {
    startDate,
    endDate,
    contentId: contentTitle,
  };
  const [contentData, setContentData] = useState(initialValues);
  const [selectedLesson, setSelectedLesson] = useState({ __typename: 'Lesson', id: '0', title: 'All Lessons' });

  useEffect(() => {
    setContentData({ ...contentData, contentId: contentTitle });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, contentId, loading, lessonsLoading]);

  useEffect(() => {
    refetch();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contentId]);

  const handleUpdateReports = (e) => {
    e.preventDefault();
    setSelectedLesson({ __typename: 'Lesson', id: '0', title: 'All Lessons' });
    const { startDate: fromDate, endDate: toDate, contentId: { __typename, id } } = contentData;
    let queryString = '';
    if (fromDate || toDate) {
      const startDateString = fromDate ? `startDate=${fromDate}` : '';
      const endDateString = toDate ? `endDate=${toDate}` : '';
      if (fromDate && toDate) {
        queryString = `?${startDateString}&${endDateString}`;
      } else if (fromDate) {
        queryString = `?${startDateString}`;
      } else if (toDate) {
        queryString = `?${endDateString}`;
      }
    }
    if (__typename === 'Program') {
      history.push(pushToInsightProgramReports(id, queryString));
    } else {
      history.push(pushToInsightLessonReports(id, queryString));
    }
  };

  const insightReports = (selectedLesson.id === '0') ? getInsightReportsLessons
    : getInsightReportsLessons.filter(({ lessonId }) => selectedLesson.id === lessonId);

  if (loading || lessonsLoading) {
    return <Spinner />;
  }

  return (
    <div className={styles.container}>
      <Typography sx={{
        display: 'flex',
        fontSize: '1.5em',
        marginTop: '0.5em',
        color: palette.primary.darkBlackOpacity87 }}
      >
        Insights Report
      </Typography>
      <Paper className={styles.insightsContainer} sx={{ backgroundColor: 'background.default' }}>
        {!loading && !lessonsLoading && (
        <Insights
          handleUpdateReports={handleUpdateReports}
          type={type}
          contentId={contentId}
          startDate={startDate}
          endDate={endDate}
          contentData={contentData}
          setContentData={setContentData}
          contents={contents}
          selectedLesson={selectedLesson}
          setSelectedLesson={setSelectedLesson}
          microLessons={microLessons}
        />
        )}
        {insightReportsLoading ? <Spinner /> : (
          insightReports.map((lesson) => {
            const { lessonName, reportData: lessonCards, __typename, lessonId } = lesson;
            return (
              <div key={lessonName} style={{ padding: '.8rem' }}>
                <InsightsReport
                  type={type}
                  index={getInsightReportsLessons.findIndex((x) => x.lessonId === lessonId)}
                  lessonName={lessonName}
                  lessonCards={lessonCards}
                  insightsReport={__typename}
                  lessonId={lessonId}
                />
              </div>
            );
          })
        )}
      </Paper>
    </div>
  );
}
export default InsightsContainer;
