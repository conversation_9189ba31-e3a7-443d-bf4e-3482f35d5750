import React from 'react';
import {
  Autocomplete, Box, Button, InputAdornment, InputLabel, TextField,
  Typography, styled, useTheme,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import styles from './Insights.module.css';

import { useUser } from '../../hooks/useUser';

function Insights({ contentId, contents, type,
  contentData, setContentData, setSelectedLesson, microLessons,
  selectedLesson, handleUpdateReports, startDate, endDate }) {
  const { palette } = useTheme();

  const GroupHeader = styled('div')(() => ({
    padding: '0.5rem 1rem',
    opacity: '0.45',
    color: palette.primary.lightPictonBlue,
    '&:hover': {
      backgroundColor: palette.background.lightGreenBlue,
      color: `${palette.background.white} !important`,
    },
  }));

  const GroupItems = styled('ul')({
    padding: 0,
    color: `${palette.primary.lightPictonBlue} !important`,
    backgroundColor: palette.background.white,
    "& li[aria-selected='true']": {
      color: `${palette.primary.lightPictonBlue} !important`,
      fontWeight: 'bold',
    },
    '& li:hover': {
      backgroundColor: `${palette.background.lightGreenBlue} !important`,
      color: `${palette.background.white} !important`,
    },
    fontSize: '0.935rem',
    lineHeight: 'initial',
  });

  const StyledTypography = styled(Typography)({
    fontSize: '0.9em',
    lineHeight: '1.5',
  });

  const useStyles = makeStyles({
    paper: {
      border: palette.border.bobbyBlue,
      boxShadow: palette.border.dropDownShadow,
    },
  });
  const classes = useStyles();
  const legends = {
    myOrg: '<strong>My Org</strong>: Includes all answers from people in your company',
    global: '<strong>Global</strong>: Includes all answers from Emtrain’s learner community',
  };

  const lessons = [{ __typename: 'Lesson', id: '0', title: 'All Lessons' }, ...microLessons];

  const handleChange = (side) => (event) => {
    setContentData({ ...contentData,
      startDate: side === 'start' ? event.target.value : contentData.startDate,
      endDate: side === 'end' ? event.target.value : contentData.endDate });
  };

  const showUpdateButton = contentData && contentData.contentId ?
    (contentData.contentId.id === contentId && (contentData.startDate === startDate && contentData.endDate === endDate))
    : false;

  const user = useUser();

  return (
    <>
      <div className={styles.header} style={{ background: palette.background.ribbon }}>
        <div className={styles.innerContainer}>
          <InputLabel
            className={styles.label}
            style={{ color: palette.primary.white }}
            htmlFor="contents"
          >
            Title:
          </InputLabel>
          <Autocomplete
            name="contents"
            id="contents"
            disableClearable
            className={styles.selectField}
            style={{ backgroundColor: palette.background.white }}
            label="Contents"
            aria-label="contents"
            value={contentData.contentId || ''}
            selectOnFocus
            clearOnBlur
            data-cy="contents"
            classes={{ paper: classes.paper }}
            sx={{
              '.MuiOutlinedInput-root': {
                padding: '0px !important',
              },
            }}
            onChange={(event, selectedItem) => {
              setContentData({ ...contentData, contentId: selectedItem });
            }}
            options={contents}
            isOptionEqualToValue={(option, value) => option.title === value.title}
            groupBy={({ __typename }) => __typename}
            getOptionLabel={(option) => `${option.title}`}
            renderInput={(params) => <TextField style={{ textAlign: 'center' }} {...params} />}
            renderGroup={(params) => (
              <li key={params.key}>
                <GroupHeader>{params.group === 'Program' ? 'Courses' : 'Microlessons'}</GroupHeader>
                <GroupItems>{params.children}</GroupItems>
              </li>
            )}
            renderOption={(props, { id, title, __typename }) => (
              <li {...props}>
                <Box
                  sx={{
                    flexGrow: 1,
                    '& span': {
                      color: 'grey !important',
                      fontWeight: 400,
                      fontSize: '0.9rem',
                    },
                  }}
                >
                  {title}
                  <br />
                  <span>{__typename === 'Program' ? 'Course ID: ' : 'Microlesson ID: '}{id}</span>
                </Box>
              </li>
            )}
          />
        </div>
        <div className={styles.innerContainer}>
          <TextField
            id="startDate"
            name="startDate"
            type="date"
            value={contentData.startDate}
            sx={{
              '.MuiOutlinedInput-input': {
                backgroundColor: palette.background.white,
                borderTopRightRadius: '0.5rem',
                borderBottomRightRadius: '0.5rem',
                padding: '0.3rem 0.5rem',
              },
              '.MuiTypography-root': {
                fontWeight: '600',
              },
            }}
            InputProps={{
              style: {
                borderRadius: '0.5rem',
                height: '2.313rem',
                borderColor: palette.border.greyShade,
                background: palette.background.lightGrey,
              },
              'data-cy': 'startDate',
              startAdornment: <InputAdornment position="start">Start Date</InputAdornment>,
            }}
            onChange={handleChange('start')}
          />
          <TextField
            id="endDate"
            name="endDate"
            type="date"
            value={contentData.endDate}
            sx={{
              '.MuiOutlinedInput-input': {
                backgroundColor: palette.background.white,
                borderTopRightRadius: '0.5rem',
                borderBottomRightRadius: '0.5rem',
                padding: '0.3rem 0.5rem',
              },
              '.MuiTypography-root': {
                fontWeight: '600',
              },
            }}
            InputProps={{
              style: { borderRadius: '0.5rem',
                height: '2.313rem',
                borderColor: palette.border.greyShade,
                background: palette.background.lightGrey,
              },
              'data-cy': 'endDate',
              startAdornment: <InputAdornment position="start">End Date</InputAdornment>,
            }}
            onChange={handleChange('end')}
          />
        </div>
        <Button
          variant="contained"
          type="submit"
          disabled={showUpdateButton}
          onClick={(e) => handleUpdateReports(e)}
          data-cy="updateReport"
          className={styles.updateButton}
          sx={{
            color: palette.primary.dark,
            backgroundColor: palette.background.lightGreyShade,
            '&:hover': {
              color: palette.primary.dark,
              backgroundColor: `${palette.background.darkGreyPurple} !important`,
            },
            '&:disabled': {
              color: palette.primary.dark,
              backgroundColor: palette.background.darkGreyPurple,
            },
            '&:focus': {
              border: `solid ${palette.card.backgroundColor} 3px`,
            },
          }}
        >
          Update Report
        </Button>
      </div>
      <div
        className={styles.lessonLegendContainer}
        style={{
          justifyContent: (type === 'lesson') && 'flex-end',
          gridTemplateColumns: (type === 'program') ? '40% 35%' : '35%' }}
      >
        {type === 'program' && (
          <div className={styles.innerContainer}>
            <InputLabel className={styles.label} htmlFor="lessons" style={{ color: palette.text.primary }}>
              Show:
            </InputLabel>
            <Autocomplete
              name="lessons"
              id="lessons"
              disableClearable
              className={styles.selectField}
              style={{ paddingLeft: '0.375rem' }}
              label="Lessons"
              data-cy="lessons"
              classes={{ paper: classes.paper }}
              sx={{
                '.MuiOutlinedInput-root': {
                  padding: '0px !important',
                },
              }}
              options={lessons}
              value={selectedLesson}
              onChange={(e, selectedItem) => { setSelectedLesson(selectedItem); }}
              isOptionEqualToValue={(option, value) => option.title === value.title}
              getOptionLabel={(option) => `${option.title}`}
              renderInput={(params) => <TextField style={{ textAlign: 'center' }} {...params} />}
              groupBy={({ __typename }) => __typename}
              renderGroup={(params) => (
                <li key={params.key}>
                  <GroupItems>{params.children}</GroupItems>
                </li>
              )}
              renderOption={(props, { id, title }) => (
                <li {...props}>
                  <Box
                    sx={{ flexGrow: 1,
                      '& span': {
                        color: 'grey !important',
                        fontWeight: 400,
                        fontSize: '0.9rem',
                      },
                    }}
                  >
                    {title}<br />
                    {id !== '0' && <span>{'Microlesson ID: '}{id}</span>}
                  </Box>
                </li>
              )}
            />
          </div>
        )}
        <div className={styles.legendBox} style={{ border: palette.border.lightGreyShade }}>
          <StyledTypography dangerouslySetInnerHTML={{ __html: legends.myOrg }} />
          <StyledTypography dangerouslySetInnerHTML={{ __html: legends.global }} />
        </div>
      </div>
    </>
  );
}
export default Insights;
