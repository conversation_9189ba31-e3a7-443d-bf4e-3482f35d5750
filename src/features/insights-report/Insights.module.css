.container {
    padding: 0rem 1.2rem;
}

.insightsContainer {
    border: 0;
    border-radius: .5rem;
    padding-bottom: 2rem;
    margin-top: 1rem;
    display: grid;
}

.innerContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.header {
    border-top-left-radius: 0.625rem;
    border-top-right-radius: 0.625rem;
    min-height: 4.938rem;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: 40% 45% 14%;
    align-items: center;
    padding: 1rem 1rem;
    flex-wrap: wrap;
}

.label {
    width: 2.8rem;
    font-weight: 600;
}

.lessonLegendContainer {
    display: grid;
    padding: 1rem;
    justify-content: space-between;
    align-items: start;
}

.legendBox {
    display: flex; 
    padding: 0.5rem; 
    flex-direction: column; 
    justify-content: flex-end;
    border-radius: 0.25rem;
}

.updateButton {
    font-size: 1.1rem !important;
    line-height: 1.6rem !important;
    font-weight: 600 !important;
    text-transform: none !important;
    border-radius: 100px !important;
    left: 0.3rem;
}

.selectField {
    width: 85%;
    height: 2.313rem;
    border-radius: 0.25rem;
}

.lessonTitle {
    margin: 1rem;
    padding-bottom: 0.2rem;
}

.lessonTitle span {
   font-weight: 600;     
}

.noLessonCard {
    display: flex;
    justify-content: center;
    padding: 0.6rem;
    margin: 0rem 1rem;
    border-radius: 0.4rem
}

.adminSSOAccountName {
    display: flex;
    margin-left: auto;
    color: red;
    white-space: nowrap;
}

.signOutButton {
    padding-right: 1rem;
    padding-left: 1rem;
    font-size: 1rem;
    text-transform: none;
    font-weight: 600;
    background: white;
    color: red;
    &:hover {
        background: white;
        color: red;
    }
  }


.adminSSOAccountIdSpacer {
    margin-left: 0.25rem;
}