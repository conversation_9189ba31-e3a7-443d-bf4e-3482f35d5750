/* eslint-disable no-unused-expressions */
import React from 'react';
import { useTheme } from '@mui/material';
import { get } from 'lodash';
import styles from './InsightsReport.module.css';
import LessonCard from '../../components/LessonCard/LessonCard';
import { getLessonCardComponent } from '../lessons/lessonCardUtils';
import { useUser } from '../../hooks/useUser';

function InsightsReport({ lessonName, type, insightsReport, index, lessonId, lessonCards }) {
  const { palette } = useTheme();
  const user = useUser();
  const showCompletionCertificate = false;
  const LessonCardComponents = [];

  if (lessonCards.length > 0) {
    lessonCards && lessonCards.map((cardProps) => {
      return LessonCardComponents.push(getLessonCardComponent(cardProps, showCompletionCertificate, insightsReport));
    });
  }

  return (
    <div key={lessonName}>
      {type === 'program' && (
        <div className={styles.lessonTitle} style={{ borderBottom: palette.border.lightGreyShade }}>
          Lesson&nbsp;{index + 1}:&nbsp;
          <span>{lessonName}</span>
        </div>
      )}
      {lessonCards && LessonCardComponents.length > 0 && (
      <div className={styles.container}>
        {LessonCardComponents.map((LessonCardComponent, i) => (
          <div className={styles.cardsContainer} key={lessonCards[i].id}>
            <LessonCard
              key={lessonCards[i].id}
              type={lessonCards[i].type}
              isResultCard={showCompletionCertificate}
              insights
              lessonCardId={lessonCards[i].id}
              microLessonId={lessonId}
            >
              <LessonCardComponent
                {...lessonCards[i]}
                lessonTitle={lessonCards[i].title}
                insights
                responseThreshold={get(user, 'accounts.0.responseThreshold')}
                setShowCompletionCertificate={showCompletionCertificate}
              />
            </LessonCard>
          </div>
        ))}
      </div>
      )}
      {lessonCards.length === 0 && (
        <div
          className={styles.noLessonCard}
          style={{ border: palette.border.lightGreyShade }}
        >This lesson does not include any questions.
        </div>
      )}
    </div>
  );
}
export default InsightsReport;
