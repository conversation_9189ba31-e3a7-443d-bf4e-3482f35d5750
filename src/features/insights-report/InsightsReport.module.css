.container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.cardsContainer {
    padding: 0.5rem 1rem;
    display: inherit;
    min-height: 30rem;
  }
  
.lessonTitle {
    margin: 1rem;
    font-size: 1.2rem;
    padding-bottom: 0.2rem;
    font-family: Source Sans Pro, sans-serif;
}

.lessonTitle span {
   font-weight: 600;     
}

.noLessonCard {
    display: flex;
    justify-content: center;
    padding: 0.6rem;
    margin: 0rem 1rem;
    border-radius: 0.4rem
}