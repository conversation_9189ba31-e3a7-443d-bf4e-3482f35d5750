query GetInsightReports($contentId: ID!, $type: String!, $startDate: String, $endDate: String) {
    getInsightReports(contentId: $contentId, type: $type, startDate: $startDate, endDate: $endDate) {
        data {
            lessonId
            lessonName
            reportData {
                id
                type
                title
                description
                organization {
                    ... on BooleanQuizReport {
                        booleanReportData {
                            total
                            percentage
                        }
                    }
                    ... on SingleChoiceQuizReport {
                        singleChoiceReportData {
                            total
                            percentages
                        }
                    }
                    ... on MultiChoiceQuizReport {
                        multiChoiceReportData {
                            total
                            percentages
                        }
                    }
                    ... on SliderQuizReport {
                        sliderReportData {
                            total
                            percentages {
                                response
                                percentage
                            }
                        }
                    }
                    ... on ColorSpectrumQuizReport {
                        colorSpectrumReportData {
                            total
                            percentages {
                                response
                                percentage
                            }
                        }
                    }
                    ... on WordCloudReport {
                        freeFormReportData {
                            text
                            value
                        }
                    }
                }
                global {
                    ... on BooleanQuizReport {
                        booleanReportData {
                            total
                            percentage
                        }
                    }
                    ... on SingleChoiceQuizReport {
                        singleChoiceReportData {
                            total
                            percentages
                        }
                    }
                    ... on MultiChoiceQuizReport {
                        multiChoiceReportData {
                            total
                            percentages
                        }
                    }
                    ... on SliderQuizReport {
                        sliderReportData {
                            total
                            percentages {
                                response
                                percentage
                            }
                        }
                    }
                    ... on WordCloudReport {
                        freeFormReportData {
                            text
                            value
                        }
                    }
                    ... on ColorSpectrumQuizReport {
                        colorSpectrumReportData {
                            total
                            percentages {
                                response
                                percentage
                            }
                        }
                    }
                }
                booleanLabel {
                    trueText
                    falseText
                }
                spectrumText {
                    minText
                    maxText
                }
                choices {
                    gatedChoice
                    text
                }
                answer {
                    booleanAnswer
                    sliderAnswer
                    singleChoiceAnswer
                    freeFormTextAnswer
                    policyAcknowledgementAnswer
                    workplaceColorSpectrumAnswer
                    multipleChoiceAnswer {
                        answer1
                        answer2
                        answer3
                        answer4
                        answer5
                        answer6
                        answer7
                        answer8
                        answer9
                        answer10
                    }
                    checkboxAnswer {
                        answer1
                        answer2
                        answer3
                        answer4
                        answer5
                    }
                }
                fontColor
                labelText
                sliderAnswer
                regionAbbreviation
                correctAnswer
                multipleChoices
                freeFormTextAnswer
                workplaceColorSpectrumAnswer
            }
        }
    }
}