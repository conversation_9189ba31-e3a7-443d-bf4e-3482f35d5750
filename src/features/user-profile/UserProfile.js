import React, { useEffect, useState, useRef } from 'react';
import * as yup from 'yup';
import { get } from 'lodash';
import { useFormik } from 'formik';
import { useMutation, useQuery } from '@apollo/client';
import { Paper, Box, Tabs, Tab, Switch, Typography, Alert, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { styled } from '@mui/styles';
import { useUser } from '../../hooks/useUser';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
// import { useColorMode } from '../../hooks/useColorMode';
// import { LIGHT_MODE } from '../../theme/themeNames';
import Footer from '../footer/Footer';
import Button from '../../components/Button/Button';
import FormikTextField from '../form/FormikTextField';
import ChangePasswordMutation from './ChangePasswordMutation.graphql';
import UpdateUserMutation from './UpdateUserMutation.graphql';
import { GetQuestionsQuery } from './GetQuestionsQuery.graphql';
import styles from './UserProfile.module.css';
import MyQuestions from './MyQuestions';
import UserDemographicDataForm from './UserDemographicDataForm';
import { useMenuDrawer } from '../../hooks/useDrawer';
import { PASSWORD_DEFAULT_MIN_LENGTH, PASSWORD_DEFAULT_MAX_LENGTH } from '../../config/constants';

export default function UserProfile({ currentAccount }) {
  const user = useUser();
  // const { mode, toggleColorMode } = useColorMode();
  const [success, setSuccess] = useState(null);
  const [changePassword, { loading, error }] = useMutation(ChangePasswordMutation);
  const [updateUser] = useMutation(UpdateUserMutation);
  const { data: getQuestions, refetch: refetchQuestions } = useQuery(GetQuestionsQuery);
  const myQuestions = get(getQuestions, 'getQuestions', []);
  const { drawerState } = useMenuDrawer();
  const { getDevice } = useResponsiveMode();

  const enableDemographics = get(user, 'accounts[0].enableAccountDemographicsFeature', false);
  const demographicFields = get(user, 'accounts[0].demographicFields', false);
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);

  const { t } = useTranslation();
  const { palette } = useTheme();
  const device = getDevice();

  const initialFocusRef = useRef(null);
  useEffect(() => {
    if (initialFocusRef && initialFocusRef.current) {
      initialFocusRef.current.focus();
    }
  }, []);

  const { minPasswordLength, maxPasswordLength,
    upperCaseRequiredInPassword, numberRequiredInPassword, specialCharRequiredInPassword } = currentAccount;

  const pwMinLen = minPasswordLength || PASSWORD_DEFAULT_MIN_LENGTH;
  const pwMaxLen = maxPasswordLength || PASSWORD_DEFAULT_MAX_LENGTH;

  const numRegx = numberRequiredInPassword ? '(?=.*[0-9].*)' : '(?=.{0,})';
  const upperRegx = upperCaseRequiredInPassword ? '(?=.*[A-Z].*)' : '(?=.{0,})';
  const specialCharRegx = specialCharRequiredInPassword ? '(?=.*[*@!#%&()^~{}`!$=_+-].*)' : '';

  const validationSchema = yup.object({
    oldPassword: yup.string(),
    newPassword: yup
      .string()
      .min(pwMinLen, t('platform.password_min_chars', { num: pwMinLen }))
      .matches(new RegExp(numRegx), t('platform.password_number'))
      .matches(new RegExp(upperRegx), t('platform.password_upper_case'))
      .matches(new RegExp(specialCharRegx), t('platform.password_special_char'))
      .max(pwMaxLen, t('platform.password_max_chars', { num: pwMaxLen }))
      .required(t('platform.password_required')),
    confirmNew: yup.string().oneOf([yup.ref('newPassword'), null], 'Passwords must match.'),
  });

  const getField = (field) => field || '-';

  const textFields = [
    { id: 'oldPassword', label: t('password.oldPassword') },
    { id: 'newPassword', label: t('password.newPassword') },
    { id: 'confirmNew', label: t('password.confirmNew') },
  ];

  const userDataFields = [
    { title: t('profile.firstName'), display: ({ firstName }) => `${firstName}` },
    { title: t('profile.lastName'), display: ({ lastName }) => `${lastName}` },
    { title: t('profile.email'), display: ({ email }) => getField(email) },
    { title: t('profile.employeeId'), display: ({ employeeId }) => getField(employeeId) },
    { title: t('profile.title'), display: ({ title }) => getField(title) },
    { title: t('profile.role'), display: ({ role }) => getField(role) },
    { title: t('profile.hireDate'),
      display: ({ hireDate }) => (hireDate ? getField(moment(hireDate).format('MM/DD/YYYY')) : getField(hireDate)) },
    { title: t('profile.supervisor'), display: ({ supervisor }) => getField(supervisor) },
  ];

  const userLocationFields = [
    { title: t('profile.country'), display: ({ countryCode }) => getField(countryCode) },
    { title: t('profile.city'), display: ({ city }) => getField(city) },
    { title: t('profile.location'), display: ({ location }) => getField(location) },
    { title: t('profile.state'), display: ({ state }) => getField(state) },
  ];

  const scormDataFields = [
    { title: t('profile.firstName'), display: ({ firstName }) => `${firstName}` },
    { title: t('profile.lastName'), display: ({ lastName }) => `${lastName}` },
    { title: t('profile.scormId'), display: ({ scormId }) => getField(scormId) },
  ];

  const formik = useFormik({
    initialValues: {
      oldPassword: '',
      newPassword: '',
      confirmNew: '',
    },
    validationSchema,
    onSubmit,
  });

  const valuesSet = () => {
    if (formik.values.oldPassword === '' || formik.values.newPassword === '' ||
      formik.values.confirmNew === '') {
      return false;
    }
    return true;
  };

  async function onSubmit({ oldPassword, newPassword }) {
    if (success) {
      setSuccess(null);
    }

    const res = await changePassword({
      variables: {
        userId: user.id,
        oldPassword,
        newPassword,
        accountId: get(user, 'accounts.0.id'),
      },
    });

    if (res && res.data) {
      formik.resetForm();
      setSuccess(true);
    }
  }
  const paramsToTabs = { about: 'tab1', pw: 'tab2', questions: 'tab3' };
  const myProfileTab = localStorage.getItem('myProfileTab')
    ? localStorage.getItem('myProfileTab')
    : null;
  const itemId = null;

  const [value, setValue] = useState(myProfileTab ? paramsToTabs[myProfileTab] : 'tab1');
  const [noConsent, setNoConsent] = useState(!!user.noConsent);
  const [noConsentSuccess, setNoConsentSuccess] = useState(null);
  const [noConsentFocused, setNoConsentFocused] = useState(false);

  if (myProfileTab) {
    localStorage.removeItem('answersForwardTo');
    localStorage.removeItem('myProfileTab');
  }

  useEffect(() => {
    if (drawerState === 'my_questions') {
      refetchQuestions();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawerState]);

  const toggleNoConsent = async () => {
    if (noConsentSuccess) {
      setNoConsentSuccess(null);
    }
    const response = await updateUser({ variables: { noConsent: !noConsent } });
    if (response.data) {
      setNoConsentSuccess(true);
    }
    setNoConsent(!noConsent);
  };

  const StyledTabs = styled((props) => (
    <Tabs
      {...props}
      TabIndicatorProps={{ children: <span className="MuiTabs-indicatorSpan" /> }}
      sx={{ width: '100%', display: 'flex', minHeight: '30px' }}
    />
  ))({
    '& .MuiTabs-flexContainer': {
      justifyContent: device === 'smallMobile' ? 'space-between' : 'flex-start',
    },
    '& .MuiTabs-indicator': {
      display: 'flex',
      justifyContent: 'center',
      backgroundColor: 'transparent',
    },
    '& .MuiTabs-indicatorSpan': {
      maxWidth: value === 'tab3' ? 110 : 80,
      width: '100%',
      backgroundColor: palette.primary.main,
    },
  });

  const StyledTab = styled((props) => <Tab disableRipple {...props} />)(
    () => ({
      border: '1px solid transparent',
      borderRadius: '3px',
      color: palette.primary.main,
      minHeight: '30px',
      '&.Mui-selected': {
        color: palette.primary.darkLightPurple,
        fontWeight: 'bold',
      },
      '&:focus': {
        borderBottom: `2px solid ${palette.card.backgroundColor} !important`,
      },
    }),
  );
  return (
    <div className={styles.container}>
      <div className={styles.wrapper} style={{ width: device === 'smallMobile' ? '95%' : '90%' }}>
        {/* <Paper className={styles.info}>
          <div className={styles.themeContainer}>
            <h3>{`${mode === LIGHT_MODE ? t('profile.light') : t('profile.dark')} ${t('profile.mode')}`}</h3>
            <Switch checked={mode !== LIGHT_MODE} onClick={toggleColorMode} />
          </div>
        </Paper> */}
        <Paper sx={{ backgroundColor: 'background.default', borderRadius: '8px' }}>
          <div className={styles.info}>
            <h2
              // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
              tabIndex={0}
              ref={initialFocusRef}
              style={{ fontWeight: 600, marginBottom: '1.8rem' }}
            >
              {t('userMenu.my_profile')}
            </h2>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <StyledTabs value={value} selectionFollowsFocus>
                <StyledTab
                  className={device === 'smallMobile' ? styles.tabHeadingSmallMobile : styles.tabHeading}
                  label={t('profile.about_me')}
                  onClick={() => { setValue('tab1'); }}
                  value="tab1"
                  disableRipple
                  aria-controls="profile-tabpanel"
                  aria-selected={value === 'tab1'}
                  id="about-me-tab"
                  role="tab"
                  tabIndex={0}
                  index={0}
                />
                {(user && !user.sso && !user.scorm && !user.isWorkdayCCL) && (
                <StyledTab
                className={device === 'smallMobile' ? styles.tabHeadingSmallMobile : styles.tabHeading}
                  label={t('profile.password')}
                  value="tab2"
                  onClick={() => { setValue('tab2'); }}
                  disableRipple
                  aria-controls="password-tabpanel"
                  aria-selected={value === 'tab2'}
                  id="password-tab"
                  role="tab"
                  tabIndex={0}
                  index={1}
                />
                )}
                {((myQuestions.length > 0 && !askExpertEnabled) || (!!askExpertEnabled)) && (
                <StyledTab
                className={device === 'smallMobile' ? styles.tabHeadingSmallMobile : styles.tabHeading}
                  label={t('profile.my_questions')}
                  value="tab3"
                  onClick={() => { setValue('tab3'); }}
                  disableRipple
                  aria-controls="questions-tabpanel"
                  aria-selected={value === 'tab3'}
                  id="my-questions-tab"
                  role="tab"
                  tabIndex={0}
                  index={2}
                />
                )}
              </StyledTabs>
            </Box>
          </div>
          {value === 'tab1' && (
          <div className={styles.info} id="profile-tabpanel">
            {(enableDemographics && demographicFields) && (
            <>
              <UserDemographicDataForm />
              <div
                className={styles.bottomLine}
                style={{ borderBottom: `solid 1px ${palette.primary.lightGreyShade}` }}
              />
            </>
            )}
            <div className={styles.infoSection}>
              <div className={styles.infoTitle}>{t('profile.employee_info')}</div>
              <div className={styles.protectingPrivacyMessage}>{t('profile.employee_info_message')}</div>
              <div className={styles.infoGrid}>
                {user && !user.scorm && (
                <>
                  <div>
                        {!user.isWorkdayCCL && (<div
                          className={styles.employeeInfoSection}
                          style={{ borderBottom: `solid 1px ${palette.primary.lightGreyShade}` }}
                        >
                          <div className={styles.employeeInfo}>{t('profile.employee')}</div>
                        </div>
                        )}
                    {userDataFields
                      .filter(({ title }) => !(user.isWorkdayCCL && title === t('profile.employeeId')))
                      .map(({ title, display }) => (
                      display(user) !== '-' && (
                      <div key={title} className={styles.employeeInfo}>
                        <span className={styles.employeeTitle}>{title} : </span>
                        <span>{display(user)}</span>
                      </div>
                      )
                    ))}
                  </div>
                  {!user.isWorkdayCCL && (
                  <div>
                    <div
                      className={styles.employeeInfoSection}
                      style={{ borderBottom: `solid 1px ${palette.primary.lightGreyShade}` }}
                    >
                      <div className={styles.employeeInfo}>{t('profile.location')}</div>
                    </div>
                    {userLocationFields.map(({ title, display }) => (
                      display(user) !== '-' && (
                      <div key={title} className={styles.employeeInfo}>
                        <span className={styles.employeeTitle}>{title} : </span>
                        <span>{display(user)}</span>
                      </div>
                      )
                    ))}
                  </div>
                  )}
                </>
                )}
                {user && user.scorm && (
                <div>
                  {scormDataFields.map(({ title, display }) => (
                    display(user) !== '-' && (
                    <div key={title} className={styles.employeeInfo}>
                      <span className={styles.employeeTitle}>{title} : </span>
                      <span>{display(user)}</span>
                    </div>
                    )
                  ))}
                </div>
                )}
              </div>
            </div>
            <div
              className={styles.bottomLine}
              style={{ borderBottom: `solid 1px ${palette.primary.lightGreyShade}` }}
            />
            <div className={styles.infoSection}>
              <div className={styles.infoTitle}>{t('profile.no_consent')}</div>
              {noConsentSuccess && (
                <Alert
                  icon={false}
                  className={styles.successCard}
                  style={{ background: palette.background.success }}
                  action={(
                    <CloseIcon
                      fontSize="inherit"
                      style={{ marginBottom: '1rem', cursor: 'pointer' }}
                      onClick={() => { setNoConsentSuccess(null); }}
                    />
                  )}
                >
                  {t('profile.no_consent_message')}&nbsp;
                  { noConsent ? t('profile.no_consent_disable') : t('profile.no_consent_enable')}.
                </Alert>
              )}
              <div className={styles.protectingPrivacyMessage}>{t('profile.no_consent_info')}</div>
              <div className={styles.toggle}>
                <Switch
                  inputProps={{ 'data-cy': 'no_consent' }}
                  checked={!noConsent}
                  onClick={() => toggleNoConsent()}
                  id="no-consent-switch"
                  tabIndex={0}
                  // Using the state to alter the focus style was necessary because I couldn't get a :focus style to work on this element.
                  onFocus={() => setNoConsentFocused(true)}
                  onBlur={() => setNoConsentFocused(false)}
                  sx={{ marginRight: '0.25rem',
                    border: noConsentFocused ? `3px solid ${palette.card.backgroundColor}` : 'none',
                    borderRadius: '30px',
                    '& .MuiSwitch-switchBase': { padding: noConsentFocused ? '6px' : '8px' } }}
                />
                <Typography sx={{ fontSize: '1rem', fontWeight: 600, textTransform: 'capitalize' }}>
                  <label htmlFor="no-consent-switch">
                    {noConsent ? t('profile.no_consent_disable') : t('profile.no_consent_enable') }
                  </label>
                </Typography>
              </div>
            </div>
          </div>
          )}

          {value === 'tab2' && (
          <Paper
            id="password-tabpanel"
            className={styles.info}
            sx={{ backgroundColor: 'background.default', marginBottom: '5rem' }}
          >
            <div className={styles.formContainer}>
              <div className={styles.changePasswordTitle}>{t('profile.change_password')}</div>
              {success && (
              <Alert
                icon={false}
                className={styles.successCard}
                style={{ background: palette.background.success }}
                action={(
                  <CloseIcon
                    fontSize="inherit"
                    style={{ marginBottom: '1rem', cursor: 'pointer' }}
                    onClick={() => { setSuccess(null); }}
                  />
                )}
              >
                {t('profile.password_success')}
              </Alert>
              )}
              <form className={styles.form} onSubmit={formik.handleSubmit}>
                {textFields.map(({ id, label }) => (
                  <div key={id} className={styles.dataField}>
                    <FormikTextField
                      required
                      name={id}
                      id={id}
                      value={formik.values[id]}
                      type="password"
                      formik={formik}
                      labelText={label}
                      size="small"
                      labelClassName={`${styles.label}`}
                      labelStyle={{ color: palette.primary.darkLightPurple }}
                      className={styles.textField}
                      customError={error && (id === 'oldPassword') && t('profile.password_failure')}
                    />
                  </div>
                ))}
                <Button
                  loading={loading}
                  color="primary"
                  variant="contained"
                  disabled={loading || !(formik.isValid && formik.dirty) || !valuesSet()}
                  type="submit"
                  className={styles.button}
                  sx={{
                    '&:focus': {
                      border: `3px solid ${palette.card.backgroundColor} !important`,
                    },
                  }}
                >
                  {t('platform.saveChanges')}
                </Button>
              </form>
            </div>
          </Paper>
          )}

          {value === 'tab3' && (
          <MyQuestions getQuestions={myQuestions} itemId={itemId} refetchQuestions={refetchQuestions} />
          )}
        </Paper>
        <div className={styles.footerContainer}>
          <Footer />
        </div>
      </div>
    </div>
  );
}
