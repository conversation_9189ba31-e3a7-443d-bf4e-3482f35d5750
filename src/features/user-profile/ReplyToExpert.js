import React from 'react';
import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import { ReplyContainer, ReplyToButton } from './HelpfulButtonsStyles';

const ReplyToExpert = ({ expertFirstName, onReplyClick, replyOpen }) => {
  const { t } = useTranslation();

  return (
    <ReplyContainer>
      <ReplyToButton
        onClick={onReplyClick}
      >{t('sideDrawer.askExpert.reply_to')} {expertFirstName}
        {replyOpen ? <FontAwesomeIcon icon={faChevronUp} /> : (
          <FontAwesomeIcon icon={faChevronDown} />
        )}
      </ReplyToButton>
    </ReplyContainer>
  );
};

export default ReplyToExpert;
