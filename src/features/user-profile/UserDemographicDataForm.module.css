.container {
  justify-content: center;
  align-items: center;
}

.wrapper {
  display: flex;
  flex-direction: column;
}

.infoTitle { 
  font-weight: 600;
  font-size: 1.3rem;
  padding-top: 1rem;
}

.successCard {
  height: 2rem;
  display: flex;
  align-content: center;
  align-items: center;
  padding: 15px;
  font-size: 0.9rem;
}

.heading {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
  font-size: 1.4rem;
  font-weight: bold;
}

.headingLine {
  width: 100%;
  border: none;
  height: 1px;
}

.customMessage {
  text-align: left;
  font-size: 0.9rem;

}

.optionalMessage {
  text-align: left;
  font-style: italic;
  font-size: 0.9rem;
  padding: .9rem 0rem;
}

.label {
  font-weight: 600;
  font-size: 1rem;
  padding: 0.2rem 0rem;
  white-space: inherit;
}

.dataField {
  padding-bottom: 1rem;
}

.textField {
  width: 20rem;
}

.selectField {
  width: 20rem;
  height: 2.6rem;
  border-radius: 8px;
}

.selectRaceEthnicityField {
  width: 25rem;
  height: 2.6rem;
  border-radius: 8px;
}

.protectingPrivacyHeader {
  padding-top: 1rem;
  font-weight: bold;
  font-size: .9rem;
}

.protectingPrivacyMessage {
  padding: .2rem .2rem 0;
  font-weight: normal;
  font-size: .9rem;
}

.protectingPrivacyMessage a, a:visited {
  color: inherit !important;
  padding-bottom: 1rem;
}

.formContainer {
  width: 100%;
}

.form {
  display: flex;
  flex-direction: column;
}

.buttonsSection {
  display: flex;
  align-items: center;
  justify-content: left;
  padding: 0 0 1rem 3rem;
}

.saveButton {
  margin: 1rem 0rem;
  padding: 0.45rem 2.4rem;
  font-size: 1rem;
  text-transform: none;
  font-weight: 600;
  margin-top: 0.6rem !important;
}

.scrollable {
  display: block;
  overflow-y: auto;
  max-height: calc(95vh - 10rem);
  padding: 0rem 2rem 0rem 3rem;
}

.bottomSection {
  height: auto;
  width: 100%;
  position: absolute;
  bottom: 0rem;
}

.buttonsSection {
  display: flex;
  align-items: center;
  justify-content: left;
  padding: 0 0 1rem 3rem;
}

.dontShareLink {
  margin-top: .8rem;
  font-weight: 600;
  font-size: .9rem;
  margin-left: 1rem;
}

.dontShareLink a, a:visited {
  text-decoration: none;
  color: inherit !important;
}

.dontShareLink a:hover {
  font-weight: bold;
}