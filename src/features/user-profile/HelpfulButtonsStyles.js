import styled from 'styled-components';

export const YesNoButton = styled.button`
  width: 50px;
  height: 22px;
  padding: 2px 2px 2px 2px;
  border-radius: 150px;
  border: 1px solid #bdbfd0;
  background-color: #ffffff;
  box-sizing: border-box;
  font-family: Source Sans Pro, sans-serif;
  font-weight: 650;
  color: #40405d;
  text-align: center;
  line-height: normal;
  margin-left: 7px;
  cursor: pointer;

  &.selected {
    color: white;
    background-color: #40405D;
  }

  &.not-selected {
    color: #BDBFD0;
    background-color: #FFFFFF;
  }

  &.neutral {
    background-color: #FFFFFF;
    color: #40405D;
  }

  &.neutral:hover {
    background-color: #E0E1EC;
  }

`;

export const ReplyToButton = styled.button`
  color: #40405D;
  font-weight: 600;
  border: none;
  font-size: 13px;
  background: none;
  font-family: Source Sans Pro, sans-serif;
  padding: 0;
  cursor: pointer;

  svg {
    padding-left: 0.5em;
  } 
`;

export const ReplySectionContainer = styled.div`
  padding: 10px 1rem 10px 3rem;
  font-size: 13px;
  width: 100%
`;

export const SubmitButton = styled.button`
  width: 100px;
  height: 35px;
  padding: 2px 2px 2px 2px;
  border-radius: 150px;
  background-color: #555672;
  box-sizing: border-box;
  font-family: Source Sans Pro, sans-serif;
  font-weight: 600;
  color: white;
  text-align: center;
  line-height: normal;
  border: none;

  &:disabled {
    color: #555672;
    background-color: #E0E1EC;
  }
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
`;

export const PrivacyNote = styled.span`
    text-align: inherit;
    font-size: .75rem;
    font-weight: 300;
    margin-top: 1rem;
`;

export const HelpfulText = styled.span`
  color: #40405D;
  font-weight: 600;
  font-size: 13px;
`;

export const HelpfulContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-left: auto;
`;

export const ReplyContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  font-family: Source Sans Pro,sans-serif;
`;

export const ErrorCard = styled.div`
  padding: 1rem;
  color: #CC0034 !important;
  background: #FFE9ED !important;
  border-radius: 0.25rem;
  font-size: 1rem;
  line-height: 1.2rem;
`;
