import React, { useEffect, useState } from 'react';
import { Paper, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { get, orderBy } from 'lodash';
import AskAnExpertIcon from '../../images/ask_an_expert.svg';
import ChevronIcon from '../../icons/Chevron';
import styles from './MyQuestions.module.css';
import { pushToExpertQA } from '../navigation/Routes/AppRoutes';
import { GetSimilarQuestionsQuery } from './GetSimilarQuestionsQuery.graphql';
import { useUser } from '../../hooks/useUser';
import { useMenuDrawer } from '../../hooks/useDrawer';
import Replies from './Replies';

export default function MyQuestions({ getQuestions, refetchQuestions }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { openDrawer } = useMenuDrawer();
  const user = useUser();
  const grayColor = palette.primary.greyPurple;
  const borderDarkGray = palette.border.darkGrey;
  const [open, setOpen] = useState(false); // my question & answer open
  const [myQuestions, setMyQuestions] = useState(null); // my question & answer
  const history = useHistory();
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);

  const { data: relatedQuestions, refetch } = useQuery(GetSimilarQuestionsQuery,
    { variables: { searchText: myQuestions ? myQuestions.question : '' } });

  const answeredQuestions = (getQuestions && getQuestions.filter((questions) => (questions.status === 'answered' || questions.status === 'replied' || questions.status === 'closed')));
  const supportQuestions = (getQuestions && getQuestions.filter((questions) => (questions.status === 'support')));
  // eslint-disable-next-line max-len
  const awaitingQuestions = (getQuestions && getQuestions.filter((questions) => (questions.status === 'pending' || questions.status === 'assigned')));
  const [questionsAndReplies, setQuestionsAndReplies] = useState([]);

  const createQuestionReplyList = (question) => {
    const children = orderBy(question?.children, 'createdAt', 'asc');
    const fullList = [{ ...question, type: 'question' }];
    const allReplies = children?.map((child) => ({ ...child, type: 'reply' }));
    return [...fullList, ...allReplies];
  };

  const handleMyQuestion = (question) => {
    setOpen(!open);
    setMyQuestions(question);
    const fullList = createQuestionReplyList(question);
    setQuestionsAndReplies(fullList);
  };

  const handleMyQuestionClose = () => {
    setOpen(!open);
  };

  useEffect(() => {
    if (open) {
      refetch();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const setNewChild = (data) => {
    setQuestionsAndReplies([...questionsAndReplies, data]);
  };
  const getSimilarQuestions = get(relatedQuestions, 'getSimilarQuestions', []);

  return (
    <Paper id="questions-tabpanel" className={styles.info} sx={{ backgroundColor: 'background.default' }}>
      <div className={styles.formContainer}>
        {/* for empty questions list message */}
        {getQuestions && !getQuestions.length > 0 && (
        <Typography sx={{ fontSize: '1rem', fontWeight: 600 }}>
          {t('qa.questions_empty_state')}
        </Typography>
        )}

        {/* Answered Questions */}
        { (!open && answeredQuestions && answeredQuestions.length > 0) && (
          <dl
            className={styles.descriptiveList}
            id="answered_question"
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex={0}
          >
            <dt className={styles.title}>{t('qa.answered')}</dt>
            {/* here style will apply based on new response -> `${styles.questionsResponseGrid} ${styles.layout}` */}
            {answeredQuestions.map((questions) => (
              <dd key={`answered_question_${questions.id}`} className={styles.descriptiveListItem}>
                <div
                  className={questions.status === 'assigned' ? `${styles.questionsResponseGrid} ${styles.layout}` :
                    `${styles.questionGrid} ${styles.layout}`}
                  style={{ border: borderDarkGray }}
                  key={`answered_question_${questions.id}`}
                  onClick={() => handleMyQuestion(questions)}
                  onKeyDown={(e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      e.preventDefault();
                      handleMyQuestion(questions);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                >
                  <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.question')}>
                    {t('qa.question_label')}
                  </p>
                  <p className={styles.lineClamp}>{ questions.question }</p>
                  {/* here condition needs to apply based on new response */}
                  {questions.status === 'assigned' && (
                    <p
                      className={styles.newResponse}
                      style={{ backgroundColor: palette.card.backgroundColor, color: palette.primary.white }}
                    >{t('qa.new_response')}
                    </p>
                  )}
                </div>
              </dd>
            ))}
          </dl>
        )}
        { (!open && supportQuestions && supportQuestions.length > 0) && (
        <>
          <div className={styles.title}>{t('qa.support_questions')}</div>
          {supportQuestions.map((questions) => (
            <div
              className={`${styles.questionGrid} ${styles.layout}`}
              style={{ border: borderDarkGray }}
              key={`support_question_${questions.id}`}
              onClick={() => handleMyQuestion(questions)}
            >
              <p className={styles.textQA} style={{ color: grayColor }}>{t('qa.question_label')}</p>
              <p className={styles.lineClamp}>{questions.question}</p>
            </div>
          ))}
        </>
        )}
        { (!open && awaitingQuestions && awaitingQuestions.length > 0) && (
        <dl
          className={styles.descriptiveList}
          id="awaiting_answer"
          // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
          tabIndex={0}
        >
          <dt className={styles.title}>{t('qa.awaiting_response')}</dt>
          {awaitingQuestions.map((questions) => (
            <dd key={`awaiting_answer_${questions.id}`} className={styles.descriptiveListItem}>
              <div
                className={`${styles.questionGrid} ${styles.layout}`}
                style={{ border: borderDarkGray }}
                key={`awaiting_question_${questions.id}`}
                onClick={() => handleMyQuestion(questions)}
                onKeyDown={(e) => {
                  if (e.code === 'Enter' || e.code === 'Space') {
                    e.preventDefault();
                    handleMyQuestion(questions);
                  }
                }}
                role="button"
                tabIndex={0}
              >
                <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.question')}>
                  {t('qa.question_label')}
                </p>
                <p className={styles.lineClamp}>{questions.question}</p>
              </div>
            </dd>
          ))}
        </dl>
        )}

        {/* My Questions */}
        {(open && myQuestions) && (
        <>
          <div
            className={styles.title}
            style={{ display: 'flex', padding: '0.5rem 0rem', cursor: 'pointer' }}
            onClick={() => handleMyQuestionClose()}
            onKeyDown={(e) => {
              if (e.code === 'Enter' || e.code === 'Space') {
                e.preventDefault();
                handleMyQuestionClose();
              }
            }}
            role="button"
            tabIndex={0}
            aria-label={t('profile.back_to_my_questions')}
          >
            <div className={styles.chevron}>
              <ChevronIcon direction="left" />
            </div>
            <div>{t('profile.my_questions')}</div>
          </div>
          <div
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex={0}
            className={styles.layout}
            style={{ border: borderDarkGray }}
          >
            {questionsAndReplies?.length > 0 && questionsAndReplies?.map((qa, index) => (
              <Replies
                questionAnswer={qa}
                parent={myQuestions}
                key={qa?.id}
                setNewChild={setNewChild}
                isLastChild={index === questionsAndReplies?.length - 1}
                refetchQuestions={refetchQuestions}
              />
            ))}
          </div>
          {/* Similar Questions */}
          {getSimilarQuestions.length > 0 && (
          <dl
            className={styles.descriptiveList}
            id="similar_questions"
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex={0}
          >
            <dt className={styles.title}>{t('qa.related_question')}</dt>
            {getSimilarQuestions.map(({ question, id }) => (
              <dd key={`similar_question_${getSimilarQuestions.id}`} className={styles.descriptiveListItem}>
                <div
                  className={`${styles.layout}`}
                  style={{ borderBottom: borderDarkGray, borderRadius: '0rem' }}
                  key={`related_questions_${id}`}
                  onClick={() => {
                    history.push(pushToExpertQA({ questionId: id }));
                  }}
                  onKeyDown={(e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      e.preventDefault();
                      history.push(pushToExpertQA({ questionId: id }));
                    }
                  }}
                  role="button"
                  tabIndex={0}
                >
                  <div className={styles.questions}>
                    <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.question')}>
                      {t('qa.question_label')}
                    </p>
                    <p className={styles.lineClamp}>{question}</p>
                  </div>
                </div>
              </dd>
            ))}
          </dl>
          )}
        </>
        )}

        {!!askExpertEnabled && (
        <div
          className={styles.askExpert}
          onClick={() => openDrawer('expert')}
          onKeyDown={(e) => {
            if (e.code === 'Enter' || e.code === 'Space') {
              e.preventDefault();
              openDrawer('expert');
            }
          }}
          role="button"
          tabIndex={0}
        >
          <div className={styles.iconContainer}>
            <AskAnExpertIcon aria-hidden />
          </div>
          <div className={styles.text}>
            <p>{getQuestions && !getQuestions.length > 0 ? t('qa.ask_question') : t('qa.ask_another_question')}</p>
          </div>
          <div className={styles.chevron}>
            <ChevronIcon direction="right" />
          </div>
        </div>
        )}
      </div>
    </Paper>
  );
}
