import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import { SET_ANSWER_HELPFUL } from './Mutations';
import { YesNoButton, HelpfulText, HelpfulContainer, ErrorCard } from './HelpfulButtonsStyles';

const determineClass = (answerHelpfulValue, buttonName) => {
  if (answerHelpfulValue === null) {
    return 'neutral';
  }
  if (buttonName === 'yes') {
    return answerHelpfulValue === true ? 'selected' : 'not-selected';
  }
  if (buttonName === 'no') {
    return answerHelpfulValue === false ? 'selected' : 'not-selected';
  }
};

const ButtonComponent = ({ initialAnswerHelpful, questionAnswerId }) => {
  const { t } = useTranslation();
  const [answerHelpfulValue, setAnswerHelpfulValue] = useState(initialAnswerHelpful);
  const [setAnswerHelpfulMutation, { loading, error }] = useMutation(SET_ANSWER_HELPFUL);

  const handleHelpfulButtonClick = async (answerHelpful) => {
    await setAnswerHelpfulMutation({ variables: { questionAnswerId, answerHelpful } });
    setAnswerHelpfulValue(answerHelpful);
  };

  return (
    <>
      <HelpfulContainer>
        <HelpfulText>{t('sideDrawer.askExpert.answer_helpful')}</HelpfulText>
        <YesNoButton
          className={determineClass(answerHelpfulValue, 'yes')}
          disabled={answerHelpfulValue != null || loading}
          onClick={() => handleHelpfulButtonClick(true)}
        >{t('boolean.yes')}
        </YesNoButton>
        <YesNoButton
          className={determineClass(answerHelpfulValue, 'no')}
          disabled={answerHelpfulValue != null || loading}
          onClick={() => handleHelpfulButtonClick(false)}
        >{t('boolean.no')}
        </YesNoButton>
      </HelpfulContainer>
      {error && <ErrorCard>{error?.message}</ErrorCard>}
    </>
  );
};

export default ButtonComponent;
