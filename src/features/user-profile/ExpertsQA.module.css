.container {
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.wrapper {
  width: 90%;
  margin: auto;
  display: flex;
  flex-direction: column;
}

.info {
  padding: 0px 25px 25px;
  border-radius: 0;
}

.formContainer {
  width: 100%;
}

.Header { 
  font-weight: 600;
  margin-top: 1rem;
  font-size: 1.3rem;
}

h2.textMessageH2 {
  padding: .2rem 0 1rem;
  font-weight: normal;
  font-size: .9rem;
  margin: 0;
}

.askExpert {
  display: flex;
  padding: 0.2rem;
  justify-content: flex-end;
  align-items: center;
  margin-top: 1.5rem;
}

.title {
  margin-top: 1rem;
  font-weight: 600;
}

.iconContainer {
  display: flex;
  align-items: center;
  width: 1.2rem;
}

.icon {
  height: 90%;
}

.text {
  margin-left: 0.5rem;
  cursor: pointer;
}

.text :hover {
  font-weight: 600;
}

.chevron {
  display: flex;
  align-items: center;
}

.layout {
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-weight: 400;
  font-size: 1rem;
  cursor: pointer;
}

/* .layout :hover {
  font-weight: 600;
  border-radius: 1rem;
} */

.questions {
  display: grid;
  grid-template-columns: 0.04fr 1fr;
  padding: 0.2rem 0.2rem;
  font-weight: 400;
  font-size: 1rem;
}

.userInfo {
  display: grid;
  grid-template-columns: 0.09fr 1fr 0.2fr;
  padding: 0.2rem;
  font-weight: 400;
  font-size: 1rem;
}

.indicationOfTime {
  margin: -0.3rem;
  text-align: center;
  font-size: 0.8rem;
}

.answered{
    padding-left: 0.5rem;
  }

.textQA {
  width: 16px;
  font-size: 20px;
  line-height: 19px;
  font-weight: bold;
  padding: 0.2rem;
}

.lineClamp {
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}

.awaitingResponse {
  display: flex;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-weight: 600;
}

.descriptiveList {
  list-style: none;
  list-style-type: none;
}
.descriptiveListItem {
  margin-inline-start: 0px !important;
}