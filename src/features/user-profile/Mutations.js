import { gql } from '@apollo/client';

export const SET_ANSWER_HELPFUL = gql`
  mutation setAnswerHelpful($questionAnswerId: ID!, $answerHelpful: Boolean!) {
    updateQuestionAnswer(questionAnswerId: $questionAnswerId, answerHelpful: $answerHelpful) {
      id
      answerHelpful
    }
  }
`;

export const SEND_REPLY = gql`
  mutation createReply($questionAnswerId: ID!, $reply: String!) {
    createReply(questionAnswerId: $questionAnswerId, reply: $reply) {
      id
      question
      status
      answer
      parentId
      createdAt
      answerHelpful
      expert {
        id
        firstName
        lastName
        role
        avatarPath
      }
    }
  }
`;
