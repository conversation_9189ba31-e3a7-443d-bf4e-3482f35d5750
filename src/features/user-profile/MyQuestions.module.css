.info {
  padding: 0px 25px 25px;
  border-radius: 0;
}

.dividerLine {
  border-width: 1px 1px 0;
  border-style: solid;
  border-color: #BDBFD0;
  margin: 0.5rem;
}

.replyContainer {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  flex-wrap: wrap;
  width: 100%;
}

.formContainer {
  width: 100%;
  margin-top: 1rem;
}

.askExpert {
  display: flex;
  padding: 0.2rem;
  justify-content: flex-end;
  align-items: center;
  margin-top: 1.5rem;
}

.title {
  margin-bottom:0.5rem;
  font-weight: 600;
}

.iconContainer {
  display: flex;
  align-items: center;
  width: 1.2rem;
}

.icon {
  height: 90%;
}

.text {
  margin-left: 0.5rem;
  cursor: pointer;
}

.text :hover {
  font-weight: 600;
}

.chevron {
  display: flex;
  align-items: center;
}

.questionGrid {
  display: grid;
  grid-template-columns: 0.04fr 1fr;
}

.questionsResponseGrid {
  display: grid;
  grid-template-columns: 0.05fr 1fr 0.2fr;
}

.descriptiveList {
  list-style: none;
  list-style-type: none;
}
.descriptiveListItem {
  margin-inline-start: 0px !important;
  cursor: pointer;
}

.layout {
  border-radius: 0.5rem;
  padding: 0.2rem;
  font-weight: 400;
  font-size: 1rem;
  margin-bottom: 0.6rem;
}

/* .layout :hover {
  font-weight: 600;
  border-radius: 1rem;
} */

.questions {
  display: grid;
  grid-template-columns: 0.04fr 1fr;
  padding: 0.5rem 0rem;
  font-weight: 400;
  font-size: 1rem;
}

.userInfo {
  display: grid;
  grid-template-columns: 0.09fr 1fr 0.2fr;
  padding: 0.2rem;
  font-weight: 400;
  font-size: 1rem;
}

.indicationOfTime {
  margin: -0.3rem;
  text-align: center;
  font-size: 0.8rem;
}

.answered{
    padding-left: 0.5rem;
  }

.newResponse {
  margin: 0.5rem;
  text-align: center;
  font-size: 0.8rem;
  align-items: center;
  height: fit-content;
  max-width: 100%;
  min-width: 90%;
  border-radius: 5px;
}

.textQA {
  width: 16px;
  font-size: 20px;
  line-height: 19px;
  font-weight: bold;
  padding: 0.2rem;
}

.lineClamp {
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}

.awaitingResponse {
  display: flex;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-weight: 600;
}