/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import * as yup from 'yup';
import { get } from 'lodash';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { useFormik } from 'formik';
import { useMutation } from '@apollo/client';
import { TextField, Select, MenuItem, InputLabel, Paper, useTheme, Alert } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { getCurrentAccount } from '../../services/api/currentAccount';
import { useTranslation, Trans } from 'react-i18next';
import { useUser } from '../../hooks/useUser';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import UpdateUserMutation from './UpdateUserMutation.graphql';
import styles from './UserDemographicDataForm.module.css';
import Button from '../../components/Button/Button';
import SendSaveButton from '../../components/Button/SendSaveButton';

export default function UserDemographicDataForm({ modalOpen, handleClose }) {
  const user = useUser();
  const [success, setSuccess] = useState(null);
  const [updateUser, { loading }] = useMutation(UpdateUserMutation);
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isTablet, isMobile, isSmallMobile } = useResponsiveMode();
  const demographicFields = get(user, 'accounts[0].demographicFields', false);

  const tabletOrLess = isTablet || isMobile;

  const [currentAccount, setCurrentAccount] = useState(null);

  useEffect(() => {
    async function fetchCurrentAccount() {
      const account = await getCurrentAccount();
      setCurrentAccount(account);
    }
    fetchCurrentAccount();
  }, []);

  const privacyPolicyLink = (currentAccount && currentAccount.gdprPolicyUrl) || 'https://emtrain.com/emtrain-privacy-policy/';

  const validationSchema = yup.object({
    birthDate: yup.string()
      .test({
        message: () => t('profile.date_format_error'),
        test: async (value) => {
          if (value && !value.includes('/') && !moment(value, 'YYYY', true).isValid()) {
            return false;
          }
          if (value && value.includes('/') && !moment(value, 'MM/DD/YYYY', true).isValid()) {
            return false;
          }
          return true;
        },
      }),
  });

  const getDefaultOptions = () => {
    return [
      { text: t('profile.yes'), key: 'YES' },
      { text: t('profile.no'), key: 'NO' },
      { text: t('profile.prefer_not_say'), key: 'NONE' },
    ];
  };

  const getGenderOptions = () => {
    return [
      { text: t('profile.male'), key: 'MALE' },
      { text: t('profile.female'), key: 'FEMALE' },
      { text: t('profile.non_binary'), key: 'NON_BINARY' },
      { text: t('profile.prefer_not_say'), key: 'NONE' },
    ];
  };

  const getRaceEthnicityOptions = () => {
    return [
      { text: t('profile.american_indian_alaskan'), key: 'AMERICAN_INDIAN_ALASKAN' },
      { text: t('profile.asian'), key: 'ASIAN' },
      { text: t('profile.hispanic'), key: 'HISPANIC' },
      { text: t('profile.hawaiian_pacific'), key: 'HAWAIIAN_PACIFIC' },
      { text: t('profile.african'), key: 'AFRICAN' },
      { text: t('profile.white'), key: 'WHITE' },
      { text: t('profile.multiple'), key: 'MULTIPLE' },
      { text: t('profile.prefer_not_say'), key: 'NONE' },
    ];
  };

  const getDenormDemographicValue = (value) => {
    // check for boolean value
    if (value === true || value === false) {
      return value;
    }
    // if it's null or undefined, just return null
    if (!value) {
      return undefined;
    }
    if (value.includes('-')) {
      switch (value) {
        case 'prefer-not-say':
          return 'NONE';
        default:
          return value.replace(/-/g, '_').toUpperCase();
      }
    } else {
      return value.toUpperCase();
    }
  };

  // An empty string initial value doesn't seem to work for select dropdowns. Using '-' as the initial value.
  const formik = useFormik({
    initialValues: {
      birthDate: user && user.birthDate ? user.birthDate : '',
      gender: user && user.gender ? getDenormDemographicValue(user.gender) : '-',
      transgender: user && user.transgender ? getDenormDemographicValue(user.transgender) : '-',
      heterosexual: user && user.heterosexual ? getDenormDemographicValue(user.heterosexual) : '-',
      raceEthnicity: user && user.raceEthnicity ? getDenormDemographicValue(user.raceEthnicity) : '-',
      disabilityStatus: user && user.disabilityStatus ? getDenormDemographicValue(user.disabilityStatus) : '-',
      veteranStatus: user && user.veteranStatus ? getDenormDemographicValue(user.veteranStatus) : '-',
    },
    validationSchema,
    onSubmit,
  });

  async function onSubmit(
    { birthDate, gender, transgender, heterosexual, raceEthnicity, disabilityStatus, veteranStatus },
  ) {
    if (success) {
      setSuccess(null);
    }
    const demographicVariables = { demographicDataRequested: true };
    if (birthDate) {
      demographicVariables.birthDate = birthDate;
    }
    if (gender && gender !== '-') {
      demographicVariables.gender = gender;
    }
    if (transgender && transgender !== '-') {
      demographicVariables.transgender = transgender;
    }
    if (heterosexual && heterosexual !== '-') {
      demographicVariables.heterosexual = heterosexual;
    }
    if (raceEthnicity && raceEthnicity !== '-') {
      demographicVariables.raceEthnicity = raceEthnicity;
    }
    if (disabilityStatus && disabilityStatus !== '-') {
      demographicVariables.disabilityStatus = disabilityStatus;
    }
    if (veteranStatus && veteranStatus !== '-') {
      demographicVariables.veteranStatus = veteranStatus;
    }
    updateUser({ variables: demographicVariables });
    setSuccess(true);
    if (modalOpen) { handleClose(); }
  }

  async function setNoShare() {
    const demographicVariables = { demographicDataRequested: true };
    updateUser({ variables: demographicVariables });
    handleClose();
  }

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <Paper
            className={modalOpen && modalOpen ? `${styles.scrollable} ${styles.info}` : `${styles.info}`}
            sx={{ backgroundColor: 'background.default' }}
            style={modalOpen && ({
              maxHeight: `${tabletOrLess ? (isSmallMobile ? 'calc(95vh - 14rem)' : 'calc(95vh - 12rem)') : ''}`,
            })}
          >
            <div>
              {!modalOpen && (<div className={styles.infoTitle}>{t('profile.personal_information')}</div>)}
              {success && (
              <Alert
                icon={false}
                className={styles.successCard}
                style={{ background: palette.background.success }}
                action={(
                  <CloseIcon
                    fontSize="inherit"
                    style={{ marginBottom: '1rem', cursor: 'pointer' }}
                    onClick={() => { setSuccess(null); }}
                  />
                )}
              >
                {t('profile.personal_info_success')}
              </Alert>
              )}
              <div className={styles.customMessage}>
                {demographicFields && demographicFields.customMessage ?
                  demographicFields.customMessage :
                  t('profile.demographics_header_message')}
              </div>
              <div className={styles.optionalMessage}>
                {t('profile.fields_optional')}
              </div>
            </div>
            <div className={styles.formContainer}>
              <div>
                {!!demographicFields.dateOfBirth && (
                <div className={styles.dataField}>
                  <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                    {t('profile.date_of_birth')}
                  </InputLabel>
                  <TextField
                    id="birthDate"
                    name="birthDate"
                    type="text"
                    placeholder={t('profile.date_helper')}
                    onChange={formik.handleChange}
                    value={formik.values.birthDate}
                    onBlur={formik.handleBlur}
                    error={formik.touched.birthDate && Boolean(formik.errors.birthDate)}
                    data-cy="date-of-birth"
                    helperText={formik.touched.birthDate && formik.errors.birthDate}
                    className={styles.textField}
                    size="small"
                    style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                    InputProps={{ style: { borderRadius: '8px' } }}
                  />
                </div>
                )}
                {!!demographicFields.gender && (
                <div className={styles.dataField}>
                  <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                    {t('profile.gender')}
                  </InputLabel>
                  <Select
                    id="gender"
                    name="gender"
                    value={formik.values.gender}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.gender && Boolean(formik.errors.gender)}
                    data-cy="gender-select"
                    className={styles.selectField}
                    MenuProps={{
                      sx: {
                        '&& .MuiPopover-paper': {
                          backgroundColor: palette.background.default,
                        },
                      },
                    }}
                    style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                  >
                    <MenuItem value="-" disabled>
                      {t('profile.select')}
                    </MenuItem>
                    {getGenderOptions().map((gender) => (
                      <MenuItem key={gender.key} value={gender.key} data-cy={`gender-${gender.key}`}>
                        {gender.text}
                      </MenuItem>
                    ))}
                  </Select>
                </div>
                )}
                {!!demographicFields.sexuality && (
                <>
                  <div className={styles.dataField}>
                    <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                      {t('profile.identify_transgender')}
                    </InputLabel>
                    <Select
                      id="transgender"
                      name="transgender"
                      value={formik.values.transgender}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.transgender && Boolean(formik.errors.transgender)}
                      data-cy="transgender-select"
                      className={styles.selectField}
                      MenuProps={{
                        sx: {
                          '&& .MuiPopover-paper': {
                            backgroundColor: palette.background.default,
                          },
                        },
                      }}
                      style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                    >
                      <MenuItem value="-" disabled>
                        {t('profile.select')}
                      </MenuItem>
                      {getDefaultOptions().map((option) => (
                        <MenuItem key={option.key} value={option.key} data-cy={`transgender-${option.key}`}>
                          {option.text}
                        </MenuItem>
                      ))}
                    </Select>
                  </div>
                  <div className={styles.dataField}>
                    <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                      {t('profile.identify_heterosexual')}
                    </InputLabel>
                    <Select
                      id="heterosexual"
                      name="heterosexual"
                      value={formik.values.heterosexual}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.heterosexual && Boolean(formik.errors.heterosexual)}
                      data-cy="heterosexual-select"
                      className={styles.selectField}
                      MenuProps={{
                        sx: {
                          '&& .MuiPopover-paper': {
                            backgroundColor: palette.background.default,
                          },
                        },
                      }}
                      style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                    >
                      <MenuItem value="-" disabled>
                        {t('profile.select')}
                      </MenuItem>
                      {getDefaultOptions().map((option) => (
                        <MenuItem key={option.key} value={option.key} data-cy={`heterosexual-${option.key}`}>
                          {option.text}
                        </MenuItem>
                      ))}
                    </Select>
                  </div>
                </>
                )}
                {!!demographicFields.raceEthnicity && (
                <div className={styles.dataField}>
                  <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                    {t('profile.race_ethnicity')}
                  </InputLabel>
                  <Select
                    id="raceEthnicity"
                    name="raceEthnicity"
                    value={formik.values.raceEthnicity}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.raceEthnicity && Boolean(formik.errors.raceEthnicity)}
                    data-cy="raceEthnicity-select"
                    className={styles.selectRaceEthnicityField}
                    MenuProps={{
                      sx: {
                        '&& .MuiPopover-paper': {
                          backgroundColor: palette.background.default,
                        },
                      },
                    }}
                    style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                  >
                    <MenuItem value="-" disabled>
                      {t('profile.select')}
                    </MenuItem>
                    {getRaceEthnicityOptions().map((race) => (
                      <MenuItem key={race.key} value={race.key} data-cy={`raceEthnicity-${race.key}`}>
                        {race.text}
                      </MenuItem>
                    ))}
                  </Select>
                </div>
                )}
                {!!demographicFields.disabilityStatus && (
                <div className={styles.dataField}>
                  <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                    {t('profile.disability_prompt')}
                  </InputLabel>
                  <Select
                    id="disabilityStatus"
                    name="disabilityStatus"
                    value={formik.values.disabilityStatus}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.disabilityStatus && Boolean(formik.errors.disabilityStatus)}
                    data-cy="disabilityStatus-select"
                    className={styles.selectField}
                    MenuProps={{
                      sx: {
                        '&& .MuiPopover-paper': {
                          backgroundColor: palette.background.default,
                        },
                      },
                    }}
                    style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                  >
                    <MenuItem value="-" disabled>
                      {t('profile.select')}
                    </MenuItem>
                    {getDefaultOptions().map((disability) => (
                      <MenuItem
                        key={disability.key}
                        value={disability.key}
                        data-cy={`disabilityStatus-${disability.key}`}
                      >
                        {disability.text}
                      </MenuItem>
                    ))}
                  </Select>
                </div>
                )}
                {!!demographicFields.veteranStatus && (
                <div className={styles.dataField}>
                  <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                    {t('profile.veteran_prompt')}
                  </InputLabel>
                  <Select
                    id="veteranStatus"
                    name="veteranStatus"
                    value={formik.values.veteranStatus}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.veteranStatus && Boolean(formik.errors.veteranStatus)}
                    data-cy="veteranStatus-select"
                    className={styles.selectField}
                    MenuProps={{
                      sx: {
                        '&& .MuiPopover-paper': {
                          backgroundColor: palette.background.default,
                        },
                      },
                    }}
                    style={{ width: `${tabletOrLess ? (isSmallMobile ? '12rem' : '18rem') : ''}` }}
                  >
                    <MenuItem value="-" disabled>
                      {t('profile.select')}
                    </MenuItem>
                    {getDefaultOptions().map((veteran) => (
                      <MenuItem key={veteran.key} value={veteran.key} data-cy={`veteranStatus-${veteran.key}`}>
                        {veteran.text}
                      </MenuItem>
                    ))}
                  </Select>
                </div>
                )}
                <div className={styles.protectingPrivacyHeader}>
                  {t('profile.protecting_privacy_header')}
                </div>
                <div className={styles.protectingPrivacyMessage} style={{ paddingBottom: modalOpen && '2.5rem' }}>
                  <Trans i18nKey="profile.protecting_privacy_message" data-cy="protecting-privacy-message">
                    xx
                    <Link
                      data-cy="privacy-policy-link"
                      target="_blank"
                      to={{ pathname: privacyPolicyLink }}
                    >
                      xx
                    </Link>
                    xx
                  </Trans>
                </div>
              </div>
            </div>
            {!modalOpen && (
              <Button
                loading={loading}
                color="primary"
                variant="contained"
                disabled={loading || !formik.isValid || !formik.dirty}
                type="submit"
                className={styles.saveButton}
              >
                {t('platform.saveChanges')}
              </Button>
            )}
          </Paper>
          {modalOpen && (
            <div className={styles.bottomSection} style={{ backgroundColor: palette.background.assignmentItem }}>
              <div
                className={styles.buttonsSection}
                style={{
                  flexDirection: `${tabletOrLess ? 'column' : ''}`,
                  padding: `${tabletOrLess ? '0 0 1rem 0' : ''}`,
                }}
              >
                <SendSaveButton
                  name="submitButton"
                  type="submit"
                  disabled={loading || !formik.isValid || !formik.dirty}
                  label={t('profile.save_continue')}
                  data-cy="submit-button"
                  className={styles.saveButton}
                  margin={`${tabletOrLess ? '1.6rem 0 0 0' : ''}`}
                />
                <div className={styles.dontShareLink}>
                  <a data-cy="dont-share-link" href="#" onClick={(e) => { e.preventDefault(); setNoShare(); }}>
                    {t('profile.dont_share')}
                  </a>
                </div>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
