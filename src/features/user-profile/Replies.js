import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { Avatar, useTheme } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faReply } from '@fortawesome/free-solid-svg-icons';
import styles from './MyQuestions.module.css';
import { useLocale } from '../../hooks/useLocale';
import HelpfulButtons from './HelpfulButtons';
import ReplyToExpert from './ReplyToExpert';
import ReplySection from './ReplySection';

const Replies = ({ questionAnswer, parent, refetchQuestions, setNewChild, isLastChild }) => {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const grayColor = palette.primary.greyPurple;
  const { toLocalDate } = useLocale();
  const [replyOpen, setReplyOpen] = useState(false);
  const { expert, type } = questionAnswer;

  const onReplyClick = () => {
    setReplyOpen(!replyOpen);
  };

  const closeReplySection = () => {
    setReplyOpen(false);
  };

  return (
    <>
      <div className={`${styles.questions}`}>
        <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.question')}>
          {type === 'question' ? (t('qa.question_label')) : (
            <FontAwesomeIcon
              icon={faReply}
              style={{
                color: '#818099',
                fontSize: '1rem',
              }}
            />
          )}
        </p>
        <p>{ questionAnswer.question }</p>
      </div>
      {/* answer by user */}
      {questionAnswer.answer && (
        <>
          <div className={styles.questions}>
            <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.answer')}>
              {t('qa.answer_label')}
            </p>
            <div style={{ backgroundColor: palette.background.paper, borderRadius: '0.5rem', padding: '0.5rem' }}>
              <div className={styles.userInfo}>
                <Avatar
                  alt={expert?.firstName}
                  src={questionAnswer?.expert?.avatarPath && questionAnswer?.expert?.avatarPath}
                />
                <p style={{ alignItems: 'center', display: 'flex', fontWeight: '600', padding: '0.2rem' }}>
                  {`${expert?.firstName} ${expert?.lastName}`}
                  <span style={{ fontWeight: '400', fontSize: '0.8rem', paddingTop: '0.2rem' }}>
                    {questionAnswer?.expert?.role && `, ${questionAnswer?.expert?.role}`}
                  </span>
                </p>
                <p className={styles.indicationOfTime}>
                  {`${moment(toLocalDate(questionAnswer.promiseDate)).fromNow(true)}`} ago
                </p>
              </div>
              <div className={styles.answered}>{ questionAnswer.answer }</div>
              <hr className={styles.dividerLine} />
              <div className={styles.replyContainer}>
                {isLastChild &&
                      (
                      <ReplyToExpert
                        expertFirstName={expert?.firstName}
                        onReplyClick={onReplyClick}
                        replyOpen={replyOpen}
                      />
                      )}
                <HelpfulButtons
                  initialAnswerHelpful={questionAnswer?.answerHelpful}
                  questionAnswerId={questionAnswer?.id}
                />
              </div>
            </div>
          </div>
          {replyOpen &&
          (
          <ReplySection
            questionAnswerId={parent?.id}
            closeReplySection={closeReplySection}
            refetchQuestions={refetchQuestions}
            setNewChild={setNewChild}
          />
          )}
        </>
      )}

      {/* question hasn’t been answered yet. */}
      {!questionAnswer.answer && questionAnswer.status !== 'support' && type === 'question' && (
        <div className={styles.awaitingResponse} style={{ backgroundColor: palette.background.paper }}>
          <div>{t('qa.not_answered_response')}</div>
        </div>
      )}
      {!questionAnswer.answer && questionAnswer.status === 'support' && type === 'question' && (
        <div className={styles.awaitingResponse} style={{ backgroundColor: palette.background.paper }}>
          <div>{t('qa.support_response')}</div>
        </div>
      )}
    </>
  );
};

export default Replies;
