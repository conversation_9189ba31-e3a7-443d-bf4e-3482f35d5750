import React, { useEffect, useRef } from 'react';
import { Paper, Avatar, useTheme } from '@mui/material';

import { useTranslation } from 'react-i18next';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { get } from 'lodash';
import moment from 'moment';
import AskAnExpertIcon from '../../images/ask_an_expert.svg';
import ChevronIcon from '../../icons/Chevron';
import styles from './ExpertsQA.module.css';
import GetQuestionQuery from './GetQuestionQuery.graphql';
import GetSimilarQuestionsQuery from './GetSimilarQuestionsQuery.graphql';
import { useLocale } from '../../hooks/useLocale';
import { pushToExpertQA } from '../navigation/Routes/AppRoutes';
import { useUser } from '../../hooks/useUser';
import { useMenuDrawer } from '../../hooks/useDrawer';

export default function ExpertsQA() {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { openDrawer } = useMenuDrawer();
  const user = useUser();
  const { params: { questionId } } = useRouteMatch();
  const grayColor = palette.primary.greyPurple;
  const borderDarkGray = palette.border.darkGrey;
  const history = useHistory();
  const { toLocalDate } = useLocale();
  const { loading, data } = useQuery(GetQuestionQuery,
    { variables: { id: questionId && questionId } });

  const getQuestion = get(data, 'getQuestion', []);
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);

  const { data: recommendedQuestions, refetch } = useQuery(GetSimilarQuestionsQuery,
    { variables: { searchText: (data && getQuestion.question) || '' } });

  const initialFocusRef = useRef(null);
  useEffect(() => {
    if (initialFocusRef && initialFocusRef.current) {
      initialFocusRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (getQuestion.question) {
      refetch();
    }
  }, [getQuestion.question, refetch]);

  const getSimilarQuestions = get(recommendedQuestions, 'getSimilarQuestions', []);

  const navigateToMyQuestions = () => {
    localStorage.setItem('myProfileTab', 'questions');
    history.push('/profile');
  };

  if (loading) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <Paper className={styles.info} sx={{ backgroundColor: 'background.default' }}>
          <div className={styles.formContainer}>
            <div
              // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
              tabIndex={0}
              className={styles.Header}
              ref={initialFocusRef}
            >
              {t('qa.expertsQA.expert_answers_Header')}
            </div>
            <div
              className={styles.title}
              style={{ display: 'flex', padding: '0.5rem 0rem', cursor: 'pointer' }}
              onClick={() => navigateToMyQuestions()}
              onKeyDown={(e) => {
                if (e.code === 'Enter' || e.code === 'Space') {
                  e.preventDefault();
                  navigateToMyQuestions();
                }
              }}
              role="button"
              tabIndex={0}
              aria-label={t('profile.back_to_my_questions')}
            >
              <div className={styles.chevron}>
                <ChevronIcon direction="left" />
              </div>
              <div>{t('profile.my_questions')}</div>
            </div>

            <h2
              className={styles.textMessageH2}
            >
              {t('qa.expertsQA.expert_answer_title')}
            </h2>
            {data && (
            <div
              // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
              tabIndex={0}
              className={styles.layout}
              style={{ border: borderDarkGray }}
            >
              <div className={`${styles.questions}`}>
                <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.question')}>
                  {t('qa.question_label')}
                </p>
                <p>{getQuestion.question}</p>
              </div>
              {/* answer by user */}
              <div className={styles.questions}>
                <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.answer')}>
                  {t('qa.answer_label')}
                </p>
                {getQuestion.answer && (
                <div style={{ backgroundColor: palette.background.paper, borderRadius: '0.5rem', padding: '0.5rem' }}>
                  <div className={styles.userInfo}>
                    <Avatar
                      alt={getQuestion.firstName}
                      src={getQuestion?.expert?.avatarPath && getQuestion?.expert?.avatarPath}
                    />
                    <p style={{ alignItems: 'center', display: 'flex', fontWeight: '600', padding: '0.2rem' }}>
                      {`${getQuestion?.expert?.firstName} ${getQuestion?.expert?.lastName}`}
                      <span style={{ fontWeight: '400', fontSize: '0.8rem', paddingTop: '0.2rem' }}>
                        {getQuestion?.expert?.role && `, ${getQuestion?.expert?.role}`}
                      </span>
                    </p>
                    <p className={styles.indicationOfTime}>
                      {`${moment(toLocalDate(getQuestion.promiseDate)).fromNow(true)}`} ago
                    </p>
                  </div>
                  <div className={styles.answered}>{ getQuestion.answer }</div>
                </div>
                )}
                {/* question hasn’t been answered yet. */}
                {!getQuestion.answer && (
                <div className={styles.awaitingResponse} style={{ backgroundColor: palette.background.paper }}>
                  <div>{t('qa.not_answered_response')}</div>
                </div>
                )}
              </div>
            </div>
            )}

            {/* Related Questions */}
            {getSimilarQuestions.length > 0 && (
            <>
              <div className={styles.title}>{t('qa.related_question')}</div>
              {getSimilarQuestions.map(({ question, id }) => (
                <div
                  className={`${styles.layout}`}
                  style={{ borderBottom: borderDarkGray, borderRadius: '0rem' }}
                  key={`related_questions_${id}`}
                  onClick={() => {
                    history.push(pushToExpertQA({ questionId: id }));
                  }}
                  onKeyDown={(e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      e.preventDefault();
                      history.push(pushToExpertQA({ questionId: id }));
                    }
                  }}
                  role="button"
                  tabIndex={0}
                >
                  <div className={styles.questions}>
                    <p className={styles.textQA} style={{ color: grayColor }} aria-label={t('qa.question')}>
                      {t('qa.question_label')}
                    </p>
                    <p className={styles.lineClamp}>{question}</p>
                  </div>
                </div>
              ))}
            </>
            )}
            {!!askExpertEnabled && (
            <div
              className={styles.askExpert}
              onClick={() => openDrawer('expert')}
              onKeyDown={(e) => {
                if (e.code === 'Enter' || e.code === 'Space') {
                  e.preventDefault();
                  openDrawer('expert');
                }
              }}
              role="button"
              tabIndex={0}
            >
              <div className={styles.iconContainer}>
                <AskAnExpertIcon aria-hidden />
              </div>
              <div className={styles.text}>
                <p>{t('qa.ask_question')}</p>
              </div>
              <div className={styles.chevron}>
                <ChevronIcon direction="right" />
              </div>
            </div>
            )}
          </div>
        </Paper>
      </div>
    </div>
  );
}
