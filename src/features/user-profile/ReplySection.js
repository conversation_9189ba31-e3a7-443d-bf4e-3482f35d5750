import React from 'react';
import { Formik, Field, Form } from 'formik';
import { useMutation } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { SEND_REPLY } from './Mutations';
import { ReplySectionContainer, SubmitButton, ButtonContainer, PrivacyNote, ErrorCard } from './HelpfulButtonsStyles';

const ReplySection = ({ questionAnswerId, closeReplySection, refetchQuestions, setNewChild }) => {
  const [createReply, { loading, error }] = useMutation(SEND_REPLY);
  const { t } = useTranslation();

  return (
    <ReplySectionContainer>
      {t('sideDrawer.askExpert.enter_your_reply')}
      <Formik
        initialValues={{ reply: '' }}
        onSubmit={async (values, { setSubmitting, resetForm }) => {
          const response = await createReply({ variables: { questionAnswerId, reply: values.reply } });
          resetForm();
          setSubmitting(false);
          closeReplySection();
          refetchQuestions();
          setNewChild(response?.data?.createReply);
        }}
      >
        {({ isSubmitting, values }) => (
          <Form>
            <div>
              <Field
                as="textarea"
                name="reply"
                id="reply"
                rows="5"
                style={{
                  width: '100%',
                  padding: '8px',
                  color: '#40405d',
                  fontFamily: 'SourceSansPro-Regular, Source Sans Pro, sans-serif',
                }}
              />
            </div>
            <PrivacyNote>
              {t('sideDrawer.askExpert.disclaimer')}
            </PrivacyNote>
            <ButtonContainer>
              <SubmitButton type="submit" disabled={isSubmitting || loading || !values?.reply?.trim()}>
                {isSubmitting || loading ? t('platform.submitting') : t('platform.submitButton')}
              </SubmitButton>
            </ButtonContainer>
          </Form>
        )}
      </Formik>
      {error && <ErrorCard>{error?.message}</ErrorCard>}
    </ReplySectionContainer>
  );
};

export default ReplySection;
