.container {
  justify-content: center;
  align-items: center;
  margin-top: 28px;
  width: 100%;
  height: 100%;
}

.wrapper {
  height: 100%;
  margin: auto;
  display: flex;
  flex-direction: column;
}

.footerContainer {
  display: flex;
  margin-top: auto;
  height: 3rem;
}

.tabHeading {
  margin-left: -7px;
  margin-right: 10%;
  font-weight: 400;
  display: flex;
  padding: 7px;
  justify-content: flex-start;
  text-transform: capitalize;
  font-size: 1.1rem;
}

.tabHeadingSmallMobile {
  margin-left: -8px;
  margin-right: 0;
  font-weight: 400;
  display: flex;
  padding: 7px;
  justify-content: center;
  text-transform: capitalize;
  font-size: 1.1rem;
}

.tabHeading:hover {
  font-weight: bold;
}

.info {
  padding: 0px 25px 10px;
  border-radius: 0;
}

.infoSection {
  padding-top: 1rem;
}

.infoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.bottomLine {
  padding-top: 1.5rem;
}

.infoTitle { 
  font-weight: 600;
  font-size: 1.3rem;
}

.employeeInfoSection {
  width: 95%;
  font-weight: 600;
}

.employeeTitle { 
  font-weight: 600;
}

.employeeInfo { 
  padding: 2px;
  font-size: medium; 
}

.formContainer {
  width: 100%;
}

.form {
  display: flex;
  flex-direction: column;
}

.submit {
  margin-top: 35px !important;
  width: 100%;
}

.button {
  margin-top: 1.1rem;
  padding: 0.45rem 2.4rem;
  font-size: 1rem;
  text-transform: none;
  font-weight: 600;
}

.successCard {
  padding: 15px;
}

.themeContainer {
  display: flex;
  align-items: center;
}

.themeContainer h3 {
  margin-right: 1rem;
}

.textField {
  width: 20rem;
}

.labelHeader {
   font-weight: 600;
   padding: 0.2rem 0rem;
   font-size: 1rem;
}

.errorCard {
  width: 20rem;
  padding: 4px;
  font-size: 0.8rem;
}

.successCard {
  height: 2rem;
  display: flex;
  align-content: center;
  align-items: center;
  font-size: 0.9rem; 
}

.changePasswordTitle { 
  font-weight: 600;
  font-size: 1.3rem;
  padding: 1rem 0rem;
}

.dataField {
  padding-bottom: 1rem;
}

.toggle {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-weight: 600;
  margin-left: -10px;
}

.optionalMessage {
  text-align: left;
  font-style: italic;
  font-size: .9rem;
}

.protectingPrivacyHeader {
  padding-top: 1rem;
  font-weight: bold;
  font-size: .9rem;
}

.protectingPrivacyMessage {
  padding: .2rem 0 1rem;
  font-weight: normal;
  font-size: .9rem;
}

.protectingPrivacyMessage a, a:visited {
  color: inherit !important;
  padding-bottom: 1rem;
}

.saveButton {
  width: max-content;
  padding: .4rem 1.6rem;
  margin-top: 1rem !important;
  margin-right: 2rem !important;
}

