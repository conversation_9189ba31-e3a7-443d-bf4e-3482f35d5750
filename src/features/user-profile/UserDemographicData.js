/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { Dialog, useTheme } from '@mui/material';

import { useTranslation } from 'react-i18next';
import { useUser } from '../../hooks/useUser';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import styles from './UserDemographicData.module.css';
import UserDemographicDataForm from './UserDemographicDataForm';

export default function UserDemographicData() {
  const user = useUser();
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isMobile } = useResponsiveMode();

  const enableDemographics = get(user, 'accounts[0].enableAccountDemographicsFeature', false);
  const demographicFields = get(user, 'accounts[0].demographicFields', false);
  const demographicsRequested = get(user, 'demographicDataRequested', false);

  useEffect(() => {
    if (enableDemographics && demographicFields && !demographicsRequested && !open) {
      setOpen(true);
    } else if (open) {
      setOpen(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const handleClose = () => {
    setOpen(false);
  };

  if (!open) {
    return null;
  }

  return (
    <Dialog
      open={open}
      PaperProps={{
        style: {
          borderRadius: '1rem',
          padding: 0,
          height: '95%',
          width: isMobile ? '94%' : '60%',
          maxWidth: '94%',
          margin: 0,
          position: 'absolute',
          backgroundColor: palette.background.default,
          overflowY: 'hidden',
        },
      }}
    >
      <div className={styles.container}>
        <div className={styles.paddedSection}>
          <div className={styles.heading}>{t('profile.about_you')}</div>
          <hr className={styles.headingLine} style={{ backgroundColor: palette.primary.lightGreyShade }} />
        </div>
        <UserDemographicDataForm modalOpen={open} handleClose={handleClose} />
      </div>
    </Dialog>
  );
}
