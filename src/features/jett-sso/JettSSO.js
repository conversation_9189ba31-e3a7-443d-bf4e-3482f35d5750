import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { removeIsCompletedItemView, removeIsPreviewModeEnable } from '../../services/api/authentication';
import { pushToHome } from '../navigation/Routes/AppRoutes';

function JettSSO() {
  const [singleSigninVerified, setSingleSigninVerified] = useState(false);
  const { search } = useLocation();
  const { verifyJettSSO } = useAuth();
  const queryParams = new URLSearchParams(search);

  useEffect(() => {
    async function singleSigninVerification() {
      const token = queryParams.get('token');
      const email = queryParams.get('email');
      const employeeId = queryParams.get('employeeId');
      const forwardTo = queryParams.get('forwardTo');
      const endDate = queryParams.get('endDate') ? `&endDate=${queryParams.get('endDate')}` : '';

      await verifyJettSSO(token, email, employeeId);
      setSingleSigninVerified(true);

      if (forwardTo) {
        return window.location.replace(`${forwardTo}${endDate}`);
      }
      return window.location.replace(pushToHome());
    }
    if (!singleSigninVerified) {
      removeIsPreviewModeEnable();
      removeIsCompletedItemView();
      singleSigninVerification();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div />
  );
}

export default JettSSO;
