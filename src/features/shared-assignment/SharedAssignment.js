import React, { useEffect } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { useMutation } from '@apollo/client';
import CreateSharedAssignmentMutation from './CreateSharedAssignmentMutation.graphql';

import { pushToHome } from '../navigation/Routes/AppRoutes';
import { useUser } from '../../hooks/useUser';

import styles from './SharedAssignment.module.css';

function Shared() {
  const { params: routeParams } = useRouteMatch();
  const history = useHistory();
  const user = useUser();
  const [ssoAdminError, setSsoAdminError] = React.useState('');

  const [createSharedAssignment] = useMutation(CreateSharedAssignmentMutation);

  useEffect(() => {
    async function SharedLaunch() {
      if (user && user.adminAccountId) {
        setSsoAdminError('System admins cannot create Shared With Me assignments on a client account.');
      } else {
        const programId = routeParams.programId;
        const lessonId = routeParams.lessonId;
        if (programId) {
          await createSharedAssignment({ variables: { programId } });
        } else if (lessonId) {
          await createSharedAssignment({ variables: { lessonId } });
        }
        history.push(pushToHome());
      }
    }
    SharedLaunch();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className={ssoAdminError ? styles.adminWarn : ''}>
      {ssoAdminError}
    </div>
  );
}

export default Shared;
