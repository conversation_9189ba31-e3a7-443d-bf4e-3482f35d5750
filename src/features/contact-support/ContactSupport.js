import React, { useState, useEffect, useRef } from 'react';
import * as yup from 'yup';
import { useFormik } from 'formik';
import Dropzone from 'react-dropzone';
import { useTranslation } from 'react-i18next';
import { TextField, Paper, InputLabel, Box, useTheme } from '@mui/material';
import PaperClipIcon from '../../images/paperclip.png';
import CloseButtonIcon from '../../images/close-button.svg';
import Chevron from '../../icons/Chevron';
import { useNumberFormat } from '../../hooks/useNumberFormat';
import CancelButton from '../../components/Button/CancelButton';
import SendSaveButton from '../../components/Button/SendSaveButton';
import { useUser } from '../../hooks/useUser';
import { createZendeskTicket } from '../../services/api/zendesk';

import styles from './ContactSupport.module.css';

export default function ContactSupport({ onClose }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const user = useUser();
  const { formatFileSize } = useNumberFormat();
  const [messageSent, setMessageSent] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState([]);
  const [status, setStatus] = useState({ loading: false, error: false });
  const [focusedAfterSentDone, setFocusedAfterSentDone] = useState(false);
  const feedbackRef = useRef(null);

  const { loading, error } = status;

  const clickContactSupport = () => {
    onClose();
  };

  const validationSchema = yup.object({
    email: yup
      .string()
      .email(t('platform.enter_valid_email'))
      .required(t('platform.email_required')),
    subject: yup
      .string()
      .required(t('supportMenu.subject_required')),
    message: yup
      .string()
      .required(t('supportMenu.message_required')),
  });

  const formik = useFormik({
    initialValues: {
      name: `${user.firstName} ${user.lastName}`,
      email: `${user.email}`,
      subject: '',
      message: '',
    },
    validationSchema,
    onSubmit,
  });

  const updateDroppedFiles = (droppedFiles) => {
    setAcceptedFiles(droppedFiles);
    formik.setFieldValue('files', droppedFiles);
  };

  const removeFile = (fileIndex) => {
    const newFiles = [...acceptedFiles];
    newFiles.splice(fileIndex, 1);
    formik.setFieldValue('files', newFiles);
    setAcceptedFiles(newFiles);
  };

  useEffect(() => {
    if (!focusedAfterSentDone && messageSent && feedbackRef && feedbackRef.current) {
      feedbackRef.current.focus();
      setFocusedAfterSentDone(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messageSent]);

  const displayFiles = acceptedFiles.map((file, i) => (
    <div
      className={styles.fileDisplay}
      style={{ border: palette.border.completedAssignmentItems, backgroundColor: palette.background.default }}
      key={file.path}
    >
      <div className={styles.fileDisplayItem}>{file.path}</div>
      <div className={styles.fileDisplayItem}>{formatFileSize(file.size)}</div>
      <div className={styles.removeFileButton} type="button" onClick={() => removeFile(i)}>
        <CloseButtonIcon className={styles.closeIcon} />
      </div>
    </div>
  ));

  async function onSubmit({ name, email, subject, message, files }) {
    try {
      setStatus({ loading: true, error: false });
      const result = await createZendeskTicket({ name, email, subject, message, files });
      if (result) {
        setMessageSent(true);
        setStatus({ loading: false, error: false });
      }
    } catch (e) {
      setMessageSent(true);
      setStatus({ loading: false, error: true });
    }
  }

  if (messageSent) {
    return (
      <Paper
        tabIndex="0"
        ref={feedbackRef}
        className={styles.container}
        sx={{ bgcolor: 'background.default' }}
        aria-live="polite"
        aria-atomic="false"
        aria-relevant="additions text"
      >
        <div className={styles.drawerSectionNoBorder} onClick={onClose}>
          <div className={styles.backIcon}>
            <Chevron {...{ direction: 'left', width: '12px', height: '12px' }} />
          </div>
          <div className={styles.supportHeading}>
            <p className={styles.messageSentText}>{t('supportMenu.message_sent')}</p>
          </div>
        </div>
        <Paper className={styles.formContainer} style={{ backgroundColor: palette.background.default }}>
          {!error && (
            <div>
              <p className={styles.messageSentText}>{t('supportMenu.thanks_reaching_out')}</p>
              <p className={styles.teamReviewText}>{t('supportMenu.team_will_review')}</p>
            </div>
          )}
          {error && (
            <div>
              <p className={styles.messageSentText}>{t('supportMenu.sorry_message_error')}</p>
              <p className={styles.teamReviewText}>{t('supportMenu.contact_via_email')}</p>
            </div>
          )}
          <Box display="flex" justifyContent="center" mt={1.5}>
            <SendSaveButton
              name="confirmButton"
              type="button"
              label={t('sideDrawer.got_it')}
              data-cy="contactSupportConfirm"
              onClick={onClose}
            />
          </Box>
        </Paper>
      </Paper>
    );
  }

  return (
    <Paper
      className={styles.container}
      sx={{ bgcolor: 'background.default' }}
    >
      <div
        // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
        tabIndex={0}
        role="button"
        id="contact-support-initial-focus-element"
        className={styles.drawerSectionNoBorder}
        onClick={() => clickContactSupport()}
        onKeyDown={
          (e) => {
            if (e.code === 'Enter' || e.code === 'Space') {
              e.preventDefault();
              clickContactSupport();
            }
          }
        }
      >
        <div className={styles.backIcon}>
          <Chevron {...{ direction: 'left', width: '12px', height: '12px' }} />
        </div>
        <div className={styles.supportHeading}>
          <p>{t('supportMenu.contact_support')}</p>
        </div>
      </div>
      <span
        className={styles.indicationText}
        style={{ color: palette.primary.lightBlackOpacity60 }}
      >{t('supportMenu.indication_text')}
      </span>
      <div className={styles.formContainer}>
        <Box
          className={styles.error}
          sx={{
            color: palette.primary.errorText,
          }}
        >
          {error && t('platform.requestError')}
        </Box>
        <form onSubmit={formik.handleSubmit}>
          <div className={styles.formFieldWrapper}>
            <InputLabel htmlFor="name" className={styles.label}>
              {t('supportMenu.your_name')} ({t('supportMenu.optional')})
            </InputLabel>
            <TextField
              id="name"
              key="name"
              name="name"
              type="text"
              onChange={formik.handleChange}
              value={formik.values.name}
              error={formik.touched.name && Boolean(formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
              className={styles.textField}
              style={{ backgroundColor: palette.background.default }}
              autoComplete="name"
            />
          </div>
          <div className={styles.formFieldWrapper}>
            <InputLabel htmlFor="email" className={styles.label}>{t('supportMenu.email')}</InputLabel>
            <TextField
              id="email"
              key="email"
              name="email"
              type="text"
              onChange={formik.handleChange}
              value={formik.values.email}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
              className={styles.textField}
              style={{ backgroundColor: palette.background.default }}
              aria-describedby="email-helper-text"
              autoComplete="email"
            />
          </div>
          <div className={styles.formFieldWrapper}>
            <InputLabel htmlFor="subject" className={styles.label}>{t('supportMenu.subject')}</InputLabel>
            <TextField
              id="subject"
              key="subject"
              name="subject"
              type="text"
              onChange={formik.handleChange}
              value={formik.values.subject}
              error={formik.touched.subject && Boolean(formik.errors.subject)}
              helperText={formik.touched.name && formik.errors.subject}
              className={styles.textField}
              style={{ backgroundColor: palette.background.default }}
              aria-describedby="subject-helper-text"
            />
          </div>
          <div className={styles.formFieldWrapper}>
            <InputLabel htmlFor="message" className={styles.label}>{t('supportMenu.message')}</InputLabel>
            <TextField
              id="message"
              key="message"
              name="message"
              type="text"
              multiline
              rows={4}
              onChange={formik.handleChange}
              value={formik.values.message}
              error={formik.touched.message && Boolean(formik.errors.message)}
              helperText={formik.touched.message && formik.errors.message}
              className={styles.textField}
              style={{ backgroundColor: palette.background.default }}
              aria-describedby="message-helper-text"
            />
          </div>
          <div className={styles.formFieldWrapper}>
            <InputLabel htmlFor="files" className={styles.label}>
              {t('supportMenu.attachments')} ({t('supportMenu.browser_window')})
            </InputLabel>
            {acceptedFiles && acceptedFiles.length > 0 && (
              <div>
                {displayFiles}
              </div>
            )}
            <Dropzone onDrop={updateDroppedFiles} id="files">
              {({ getRootProps, getInputProps }) => (
                <div>
                  <div
                    {...getRootProps()}
                    className={styles.dropzoneArea}
                    style={{ borderColor: palette.background.darkGrey }}
                  >
                    <input {...getInputProps()} id="files" name="files" />
                    <img src={PaperClipIcon} alt="" className={styles.paperclipIcon} />
                    <p>{t('supportMenu.add_5_files')}</p>
                  </div>
                </div>
              )}
            </Dropzone>
          </div>

          <div className={styles.buttonContainer}>
            <CancelButton
              disableRipple
              onClick={() => clickContactSupport()}
              data-cy="cancelSupportTicket"
            />
            <SendSaveButton
              disableRipple
              type="submit"
              disabled={loading}
              data-cy="submitSupportTicket"
              label={t('sideDrawer.send')}
            />
          </div>
        </form>
      </div>
    </Paper>
  );
}
