.container {
  border: solid 1px;
  margin-bottom: 1rem;
}

.formContainer {
  width: 100%;
  padding: 1rem;
}

.drawerSectionNoBorder {
  width: 100%;
  cursor: pointer;
  padding: .5rem;
  display: flex;
  align-items: center;
}

.drawerSectionNoBorder p {
  margin: 0;
  margin-right: 0.5rem;
  font-size: .9rem;
  font-weight: 600;
}

.backIcon {
  display: flex;
  padding-left: 0.5rem;
  flex-grow: 1;
}

.error {
  font-style: italic;
  font-size: .9rem;
}

.supportHeading {
  display: flex;
  flex-grow: 1;
}

.messageSentText {
  font-weight: bold !important;
  font-size: 1rem;
}

.teamReviewText {
  font-weight: normal !important;
  font-size: .8rem;
}

.label {
  font-weight: normal !important;
  font-size: .9rem !important;
  white-space: inherit !important;
}

.formFieldWrapper {
  width: 100%;
  padding-bottom: 1rem;
}

.textField {
  width: 100%;
}

.dropzoneArea {
  display: flex;
  justify-content: center;
  align-items: center;
  border: dashed 1px;
  border-radius: 3px;
  height: 5rem;
  width: 100%;
  font-size: .9rem;
}

.fileDisplay {
  position: relative;
  display: flex;
  flex-flow: wrap;
  justify-content: left;
  align-items: flex-start;
  border: solid 1px;
  height: 3rem;
  width: 100%;
  font-size: .8rem;
  margin-bottom: .5rem;
  padding: .3rem 0 0 .4rem;
  border-radius: 3px;
}

.fileDisplayItem {
  display: flex;
  flex: 0 0 100%;
  flex-direction: column;
  width: 100%;
}

.removeFileButton {
  display: block;
  position: absolute;
  top: 4px;
  right: 4px;
}

.paperclipIcon {
  height: 1.2rem;
  padding-right: .5rem;
}

.closeIcon {
  height: 1rem;
}

.buttonContainer {
  padding: 0.5rem 0;
  display: flex;
  justify-content: center;
  gap: 0.5rem
}

.submitButtonContainer {
  width: auto !important;
}

.submitButton {
  padding: 0.3rem 1rem !important;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.indicationText {
  padding-left: 1rem;
  font-size: 0.8rem;
}