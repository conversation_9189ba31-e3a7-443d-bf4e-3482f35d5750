import React, { useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { Paper, useTheme } from '@mui/material';
import { useTranslation, Trans } from 'react-i18next';
import { get } from 'lodash';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import UpdateUserMutation from '../user-profile/UpdateUserMutation.graphql';
import Button from '../../components/Button/Button';

import { concedeControl } from '../../services/api/scormRusticiAPI';

import styles from './Banner.module.css';
import { useAuth } from '../../hooks/useAuth';
import { removeIsPreviewModeEnable } from '../../services/api/authentication';
import { useUser } from '../../hooks/useUser';
import { canAccessContentConfiguration, canEditOwnAccount } from '../../services/api/permissions';
import { clearUserClosedRightPanel }
  from '../../services/layoutSettings';

export default function Banner({ name, bannerHeight, id }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { getDevice } = useResponsiveMode();
  const { clearTokens, authedNavigateToAI } = useAuth();
  const device = getDevice();
  const user = useUser();
  const permissions = get(user, 'permissions', true);
  const isLearner = get(user, 'accounts[0].accountUsers.roleId') === 2;
  const [updateUser] = useMutation(UpdateUserMutation);
  const { updateUserData } = useAuth();

  /* const names = ['preview', 'scormCloseExit'];
  if (!names.includes(name)) {
    return null;
  } */

  const bgColors = { preview: palette.primary.lightBlue, scormCloseExit: palette.background.secondary };
  const colors = { preview: palette.primary.darkLightPurple, scormCloseExit: palette.primary.dark };
  const fontSizes = { smallMobile: 0.75, mobile: 0.85, tablet: 0.9 };
  const padding = { smallMobile: '4rem', mobile: '3rem' };

  const bgcolor = bgColors[name];
  const textColor = colors[name];
  const fontSize = fontSizes[device] || '1';
  const paddingRight = padding[device] || '2rem';

  const updateAdminLanguage = async () => {
    await updateUser({ variables: { language: 'en' } });
    updateUserData({ language: 'en' });
  };

  const saveExit = async () => {
    concedeControl();
    clearUserClosedRightPanel();
    await clearTokens();
    window.close();
  };

  useEffect(() => {
    const handleSaveBeforeWindowClose = () => {
      saveExit();
    };

    window.addEventListener('beforeunload', handleSaveBeforeWindowClose);
    return () => {
      // Cleanup: remove the event listener when the component unmounts
      window.removeEventListener('beforeunload', handleSaveBeforeWindowClose);
    };
  });

  const closePreview = async () => {
    removeIsPreviewModeEnable();
    updateAdminLanguage();
    const jettForwardTo = localStorage.getItem('forwardToRoute') || null;
    localStorage.removeItem('forwardToRoute');
    if (jettForwardTo && user && canEditOwnAccount(permissions) && canAccessContentConfiguration(permissions)) {
      await authedNavigateToAI(user, 'manage/content-library');
      return false;
    }
    if (isLearner && jettForwardTo) {
      await clearTokens();
    }
    // eslint-disable-next-line no-restricted-globals
    open(location, '_self').close();
  };

  return (
    <Paper
      className={name === 'scormCloseExit' ? styles.scormContainer : styles.container}
      sx={{ bgcolor, borderRadius: 0, height: `${bannerHeight}rem` }}
      id={id || ''}
    >
      <div
        className={styles.leftSection}
        style={{ paddingRight }}
      >
        <div
          className={styles.bodyText}
          style={{ fontSize: `${fontSize}rem`, lineHeight: `${fontSize * 1.2}rem`, color: `${textColor}` }}
        >
          {name === 'scormCloseExit' && (
            <Trans i18nKey="scorm.scorm_label_text" />
          )}
          {name === 'preview' && (
            <>
              <div className={styles.headingText}>{t('preview.preview_heading')}</div>
              <div>{t('preview.preview_text')}</div>
            </>
          )}
        </div>
      </div>
      <div className={styles.rightSection}>
        {name === 'scormCloseExit' && (
          <Button
            onClick={() => saveExit()}
            className={styles.closeButton}
            containerStyle={styles.closeButtonContainer}
            style={{
              backgroundColor: palette.button.downloadCertificate.backgroundHoverColor,
              color: palette.primary.white,
            }}
            sx={{
              borderRadius: '1.5rem',
              '&:focus': {
                outline: `solid ${palette.primary.white} 2px`,
                border: `solid ${palette.card.backgroundColor} 3px`,
              },
            }}
            variant="contained"
            data-cy="closeScormLabel"
          >
            {t('scorm.save_exit')}
          </Button>
        )}
        {name === 'preview' && (
          <Button
            onClick={() => closePreview()}
            className={styles.closeButton}
            containerStyle={styles.closeButtonContainer}
            style={{ backgroundColor: palette.primary.mainPurple, color: palette.primary.white }}
            sx={{
              borderRadius: '1.5rem',
              '&:focus': {
                outline: `solid ${palette.primary.white} 2px`,
                border: `solid ${palette.card.backgroundColor} 3px`,
              },
            }}
            variant="contained"
            data-cy="closePreviewLabel"
          >
            {t('preview.close_preview')}
          </Button>
        )}
      </div>
    </Paper>
  );
}
