.container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 6rem;
  padding: 1.25rem 1rem;
  /* padding: 0 0.8rem 0 1rem; */
  /* overflow: hidden; */
}

.scormContainer {
  position: fixed;
  top: 0px;
  z-index: 3;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 0.8rem 0 1rem;
  overflow: hidden;  
}

.leftSection {
  width: 83%;
  flex-grow: 4;
}

.rightSection {
  width: 17%;
  display: flex;
  align-self: center;
  justify-content: right;
  padding-right: 0.28rem;
}

.headingText {
  font-weight: bold;
  justify-content: left;
  padding: 0 0 0.2rem 0;
  margin: 0;
}

.bodyText {
  font-weight: normal;
  justify-content: left;
  align-self: center;
}

.closeButtonContainer {
  width: auto !important;
}

.closeButton {
  padding: 0.2rem .8rem !important;
  text-transform: none !important;
  font-size: 0.8rem !important;
  font-weight: bold !important;
  white-space: nowrap;
}