import React, { useState, useEffect, useRef } from 'react';
import * as yup from 'yup';
import { useFormik } from 'formik';
import { useMutation } from '@apollo/client';
import { useTheme, Paper, Checkbox, Typography, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Chevron from '../../icons/Chevron';
import { useUser } from '../../hooks/useUser';
import AskExpertMutation from './AskExpertMutation.graphql';
import SendSaveButton from '../../components/Button/SendSaveButton';
import CancelButton from '../../components/Button/CancelButton';
import FormikTextField from '../form/FormikTextField';
import { useActiveAssignment } from '../../hooks/useActiveAssignment';
import styles from './AskExpert.module.css';

const CONFIRM_PHASE = 0;
const SENT_PHASE = 1;

export default function AskExpertConfirm({ onClose, expertQuestion, clearQuestion }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const [formPhase, setFormPhase] = useState(CONFIRM_PHASE);
  const user = useUser();
  const [focusedAfterSentDone, setFocusedAfterSentDone] = useState(false);
  const feedbackRef = useRef(null);
  const initialFocusRef = useRef(null);
  const { activeAssignmentInfo } = useActiveAssignment();
  const programId = activeAssignmentInfo?.programId;
  const lessonId = activeAssignmentInfo?.lessonId;

  const validationSchema = yup.object({
    email: yup
      .string()
      .email(t('platform.enter_valid_email'))
      .when('notify',
        (notify, schema) => {
          if (notify) {
            return schema.required(t('platform.email_required'));
          }
          return schema;
        }),
  });

  const isValidEmailAddress = (email) => {
    return (/^[A-Z0-9!$&*-=`|~#%‘+/?_{}']+@[A-Z0-9.-]+\.[A-Z]{2,63}$/i.test(email));
  };

  const getUserEmail = (user) => {
    if (user?.email && user?.email !== null) {
      return user.email;
    }
    if (user?.scormId && isValidEmailAddress(user?.scormId)) {
      return user.scormId;
    }
    return '';
  };

  const formik = useFormik({
    initialValues: {
      email: getUserEmail(user),
      notify: true,
    },
    validationSchema,
    onSubmit,
  });

  const [askExpert, { loading, error }] = useMutation(AskExpertMutation, {
    onCompleted: () => {
      formik.resetForm();
      setFormPhase(SENT_PHASE);
      clearQuestion();
    },
  });

  async function onSubmit({ email, notify }) {
    askExpert({ variables: { question: expertQuestion, notifyUser: notify, notifyEmail: email, programId, lessonId } });
  }

  useEffect(() => {
    if (formPhase === CONFIRM_PHASE) {
      initialFocusRef.current.focus();
    }
  }, [formPhase]);

  useEffect(() => {
    if (!focusedAfterSentDone && formPhase === SENT_PHASE && feedbackRef && feedbackRef.current) {
      feedbackRef.current.focus();
      setFocusedAfterSentDone(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedAfterSentDone, formPhase]);

  return (
    <Paper
      className={styles.container}
      sx={{ bgcolor: 'background.default' }}
    >
      {formPhase === CONFIRM_PHASE && (
        <>
          <div
            className={styles.drawerSectionNoBorder}
            onClick={onClose}
          >
            <div className={styles.backIcon}>
              <Chevron {...{ direction: 'left', width: '12px', height: '12px' }} />
            </div>
            <div className={styles.expertHeading}>
              <h2 className={styles.askExpertH2}>{t('sideDrawer.askExpert.sendQuestionHeading')}</h2>
            </div>
            <div className={styles.backIcon}>&nbsp;</div>
          </div>
          <div className={styles.formContainer} style={{ backgroundColor: palette.background.default }}>
            {/* eslint-disable-next-line max-len */}
            <Typography
              tabIndex="0"
              ref={initialFocusRef}
              id="confirmPrompt"
              component="h3"
              sx={{ fontWeight: 600, lineHeight: 1.25 }}
              gutterBottom
            >
              {t('sideDrawer.askExpert.confirmPrompt')}
            </Typography>
            <div style={{ height: '.2rem' }} />
            <Typography sx={{ fontSize: '0.9rem' }} gutterBottom>
              {t('sideDrawer.askExpert.answeredByEmtrain')} {t('sideDrawer.askExpert.confirmationText')}
            </Typography>
            <Box
              className={styles.error}
              sx={{
                color: palette.primary.errorText,
              }}
            >
              {error && t('platform.requestError')}
            </Box>
            <form onSubmit={formik.handleSubmit}>
              {!formik.values.email && formik.values.notify && (
              <>
                <div style={{ height: '1rem' }} />
                <Typography
                  className={styles.emailLabel}
                  style={{ color: palette.primary.white, backgroundColor: palette.primary.main }}
                >
                  {t('sideDrawer.askExpert.enter_email')}
                </Typography>
              </>
              )}
              <div style={{ height: '1rem' }} />
              <FormikTextField
                autoFocus
                required
                name="email"
                id="email"
                // key="email"
                value={formik.values.email}
                formik={formik}
                className={styles.textField}
                labelText={t('sideDrawer.askExpert.email')}
                labelClassName={`${styles.label}`}
              />
              <div style={{ height: '1rem' }} />
              <div style={{ width: '100%', display: 'flex' }}>
                <div>
                  <Checkbox
                    id="notify"
                    key="notify"
                    name="notify"
                    value={formik.values.notify}
                    checked={formik.values.notify}
                    sx={{ '& .MuiSvgIcon-root': { fontSize: 24 }, padding: 0 }}
                    onChange={formik.handleChange}
                    // inputProps={{ 'aria-labelledby': 'notify-label' }}
                  />
                </div>
                <div style={{ marginLeft: '.6rem' }}>
                  <Typography
                    id="notify-label"
                    className={styles.label}
                  >
                    <label htmlFor="notify">
                      {t('sideDrawer.askExpert.notify_me')}
                    </label>
                  </Typography>
                </div>
              </div>
              <div style={{ height: '1rem' }} />
              <Box display="flex" justifyContent="space-between" mt={1.5}>
                <CancelButton
                  name="cancelConfirm"
                  type="button"
                  disabled={loading}
                  label={t('sideDrawer.cancel')}
                  data-cy="askExpertCancelConfirm"
                  onClick={onClose}
                />
                <SendSaveButton
                  name="submit"
                  type="submit"
                  disabled={loading || (!!formik.errors.email) || (!formik.values.email && formik.values.notify)}
                  label={t('sideDrawer.send')}
                  data-cy="askExpertSubmit"
                />
              </Box>
            </form>
          </div>
        </>
      )}
      {formPhase === SENT_PHASE && (
        <>
          <div
            className={styles.drawerSectionNoBorder}
            onClick={onClose}
            style={{ backgroundColor: palette.background.default }}
          >
            <div className={styles.backIcon}>
              <Chevron {...{ direction: 'left', width: '12px', height: '12px' }} />
            </div>
            <div className={styles.expertHeading}>
              <h2>{t('sideDrawer.askExpert.questionSentHeading')}</h2>
            </div>
            <div className={styles.backIcon}>&nbsp;</div>
          </div>
          <div
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex="0"
            ref={feedbackRef}
            className={styles.formContainer}
            style={{ backgroundColor: palette.background.default }}
            aria-relevant="additions text"
          >
            <Typography component="h3" sx={{ fontWeight: 600, lineHeight: 1.25 }} gutterBottom>
              {t('sideDrawer.askExpert.question_sent')}
            </Typography>
            <Typography sx={{ fontSize: '0.9rem' }} gutterBottom>
              {t('sideDrawer.askExpert.estimated_time_answer')}
            </Typography>
            <Typography sx={{ fontSize: '0.9rem' }} gutterBottom>
              {t('sideDrawer.askExpert.response_notification')}
            </Typography>
            <Box display="flex" justifyContent="center" mt={1.5}>
              <SendSaveButton
                name="confirmButton"
                type="button"
                label={t('sideDrawer.got_it')}
                data-cy="askExpertConfirm"
                onClick={onClose}
              />
            </Box>
          </div>
        </>
      )}
    </Paper>
  );
}
