.container {
  border: solid 1px;
  margin-bottom: 1rem;
}

.backIcon {
  display: flex;
  padding-left: 0.5rem;
}

.drawerSectionNoBorder {
  width: 100%;
  cursor: pointer;
  padding: .5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.drawerSectionNoBorder p {
  margin: 0;
  margin: 0 0.5rem 0 0.5rem;
  font-size: .9rem;
  font-weight: 600;
}

.formContainer {
  width: 100%;
  padding: 1rem;
}

.expertHeading {
  display: flex;
}

.emailLabel {
  font-weight: bold !important;
  font-size: .8rem !important;
  padding: .7rem;
  border-radius: 5px;
}

.label {
  font-weight: normal !important;
  font-size: .9rem !important;
}

.textField {
  width: 100%;
}

textarea {
  width: 100%;
  resize: none;
  border-radius: 0.25rem;
  padding: 0.75rem;
  font-size: 0.875rem;
  outline: none !important;
  :focus { 
    outline: none !important;
  }
  font-family: Source Sans Pro, sans-serif;
}

h2.askExpertH2 {
  font-size: 1rem;
  margin: 0;
  font-weight: 600;  
}

.error {
  font-style: italic;
  font-size: .9rem;
}

.blink {
  animation: fadeInOut 1.5s linear forwards;
}
 
@keyframes fadeInOut{
  0%,100% { opacity: 1; }
  50% { opacity: 1; background-color: #B7CEFF; }
}  