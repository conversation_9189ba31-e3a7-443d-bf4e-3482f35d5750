import React, { useEffect } from 'react';
import * as yup from 'yup';
import { useFormik } from 'formik';
import { Paper, Typography, TextareaAutosize, Box, styled } from '@mui/material';
import { useTranslation } from 'react-i18next';
import SendSaveButton from '../../components/Button/SendSaveButton';
import styles from './AskExpert.module.css';

const StyledTextArea = styled(({ isError, ...props }) => <TextareaAutosize {...props} />)(({ theme, isError }) => ({
  width: '100%',
  resize: 'none',
  borderRadius: '0.25rem',
  padding: '0.75rem',
  fontSize: '0.875rem',
  border: `1px solid ${isError ? theme.palette.primary.errorText : theme.palette.primary.greyPurple}`,
  fontFamily: theme.typography.fontFamily,
  color: theme.palette.primary.main,
  backgroundColor: theme.palette.background.default,
  '&:hover': {
    border: `1px solid ${isError ? theme.palette.primary.errorText : theme.palette.primary.main}`,
  },
  '&:focus-visible': {
    outline: 'none',
  },
  '::placeholder': {
    fontStyle: 'italic',
    color: theme.palette.primary.darkLightPurple,
  },
}));

export default function AskExpertQuestion({ initialQuestion, onSetQuestion, drawerState, id }) {
  const { t } = useTranslation();

  useEffect(() => {
    if (drawerState === 'expert') {
      const focusElement = document.getElementById('question');
      if (focusElement) {
        focusElement.focus();
      }
    }
  }, [drawerState]);

  const validationSchema = yup.object({
    question: yup.string().required(t('sideDrawer.askExpert.missingQuestionError')),
  });

  const formik = useFormik({
    initialValues: {
      question: initialQuestion,
    },
    validationSchema,
    onSubmit,
  });

  async function onSubmit({ question }) {
    onSetQuestion(question);
  }

  return (
    <Paper
      sx={{ bgcolor: 'background.default', px: 2, pb: 2 }}
      className={(drawerState && drawerState === 'expert') ? `${styles.blink}` : ''}
      id={id}
      aria-labelledby="menuAskExpert-button"
    >
      <Typography id="ask-expert-send-prompt" component="h3" sx={{ fontWeight: 600, lineHeight: 1.25 }} gutterBottom>
        {t('sideDrawer.askExpert.headerText')}
      </Typography>
      <Typography id="ask-expert-response-prompt" sx={{ fontSize: '0.9rem' }} gutterBottom>
        {t('sideDrawer.askExpert.responsePrompt')}
      </Typography>
      <div style={{ height: '.2rem' }} />
      <form onSubmit={formik.handleSubmit}>
        <label htmlFor="question" style={{ fontSize: '0.9rem' }}>
          {/* {t('sideDrawer.askExpert.question')} */}
          {t('sideDrawer.askExpert.askPrompt')}
        </label>
        <StyledTextArea
          id="question"
          name="question"
          key="question"
          inputMode="text"
          minRows={4}
          maxRows={10}
          // placeholder={t('sideDrawer.askExpert.askPrompt')}
          data-cy="ask-expert"
          value={formik.values.question}
          onChange={formik.handleChange}
          isError={formik.touched.question && Boolean(formik.errors.question)}
          aria-describedby="ask-expert-send-prompt ask-expert-response-prompt"
        />
        {
          formik.touched.question
          && Boolean(formik.errors.question)
          && <Typography sx={{ fontSize: '0.75rem' }} color="red">{formik.errors.question}</Typography>
        }
        <Box display="flex" justifyContent="flex-end" mt={1.5}>
          <SendSaveButton
            name="submitInitialPhase"
            type="submit"
            disabled={!formik.values.question}
            label={t('sideDrawer.submit')}
            data-cy="askExpertSubmitInitialPhase"
          />
        </Box>
      </form>
    </Paper>
  );
}
