/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React, { useRef, useEffect } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useTheme, Paper } from '@mui/material';
import styles from './CompletedItemsIntro.module.css';

import { useAccessibilityHelper } from '../../hooks/useAccessibilityHelper';

export default function CompletedItemsIntro({ itemCount, setCompletedItems }) {
  const { palette } = useTheme();
  const { stripHTMLForReader } = useAccessibilityHelper();
  const { t } = useTranslation();

  const initialFocusRef = useRef(null);

  useEffect(() => {
    initialFocusRef.current.focus();
  });

  return (
    <div className={styles.container}>
      <Paper
        className={styles.contentBox}
        tabIndex="0"
        ref={initialFocusRef}
        sx={{
          border: 'solid 1px #cbccd9',
          '&:focus': {
            border: `2px solid ${palette.card.backgroundColor}`,
          },
        }}
      >
        {itemCount === 0 && (
          <Paper
            data-cy="complete_todo_prompt"
            aria-label={stripHTMLForReader(t('completedItems.completeTodoPrompt'))}
            sx={{
              flexGrow: 1,
              border: '2px solid transparent',
              backgroundColor: 'white',
            }}
          >
            {/* Once you complete an assignment from your <1><strong>To Do</strong></1> list you'll be able to review it here. */}
            <Trans i18nKey="completedItems.completeTodoPrompt">
              xx
              {/* eslint-disable-next-line max-len */}
              <a href="#" style={{ textDecoration: 'none', color: palette.text.primary }} onClick={(e) => { e.preventDefault(); setCompletedItems(false); }}>
                xx
              </a>
              xx
            </Trans>
          </Paper>
        )}
        {itemCount !== 0 && (
          <Paper
            data-cy="select_completed_prompt"
            aria-label={stripHTMLForReader(t('completedItems.selectCompletedList'))}
            sx={{
              flexGrow: 1,
              border: '2px solid transparent',
              backgroundColor: 'white',
            }}
          >
            {/* Select an item from the <strong>Completed</strong> list to review content or access a completion certificate. */}
            <Trans i18nKey="completedItems.selectCompletedList" />
          </Paper>
        )}
      </Paper>
    </div>
  );
}
