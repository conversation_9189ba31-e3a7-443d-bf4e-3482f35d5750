import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { useLocation, useRouteMatch, useHistory } from 'react-router-dom';
import { useMutation } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../hooks/useAuth';
import CreateScormAssignmentMutation from './CreateScormAssignmentMutation.graphql';
import { pushToHome, pushToMicroLesson, pushToProgram, scormExit } from '../navigation/Routes/AppRoutes';
import { getSuspendData, setSuspendData } from '../../services/api/scormRusticiAPI';

function Scorm() {
  const [scormVerified, setScormVerified] = useState(false);
  const { params: routeParams } = useRouteMatch();
  const { t } = useTranslation();
  const { search } = useLocation();
  const history = useHistory();
  const queryParams = new URLSearchParams(search);
  const { clearTokens, verifyScorm } = useAuth();

  const [createScormAssignment] = useMutation(CreateScormAssignmentMutation);

  function scormError(code, value) {
    const contentType = routeParams?.programId ? 'Program' : 'Lesson';
    const contentID = routeParams?.programId || routeParams?.lessonId;
    let message = '';
    if (code === 401 && value.includes('This account is deactivated')) {
      message = t('scorm-exit.account_deactivate');
    } else if (code === 401 && value === 'Invalid Integration key') {
      message = t('scorm-exit.scorm_deactivate');
    } else if (code === 401 && value.includes('Scorm user not found suspendData')) {
      const parts = value.split(' ');
      const resourceId = parts[parts.indexOf('suspendData:') + 1] || null;
      const scormId = parts[parts.indexOf('scormId:') + 1] || null;
      message = t('scorm-exit.assignment_msg', { contentType, contentID, resourceId, scormId });
    } else if (code > 401 && code < 500) {
      message = t('scorm-exit.bad_request', { code });
    } else if (code >= 500 && code < 600) {
      message = t('scorm-exit.bad_gateway', { code });
    } else {
      message = t('scorm-exit.bad_request', { code });
    }
    const encodedMessage = encodeURIComponent(message);
    return history.push(scormExit({ message: encodedMessage }));
  }

  useEffect(() => {
    async function scormVerification() {
      try {
        await clearTokens();
        const suspendData = getSuspendData();
        const programId = routeParams.programId;
        const lessonId = routeParams.lessonId;
        const apiKey = queryParams.get('API_KEY');
        const integrationKey = queryParams.get('INT_KEY');
        const enforceSequence = queryParams.get('enforceSequence');
        const scormProgramId = await verifyScorm({ programId, lessonId, apiKey, integrationKey, enforceSequence });
        if (!suspendData && scormProgramId) {
          setSuspendData(scormProgramId);
        }
        setScormVerified(true);
      } catch (e) {
        const code = e.response.status;
        const value = e.response.data;
        await scormError(code, value);
      }
    }
    if (!scormVerified) {
      scormVerification();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function findIncompleteScormProgramLessonIdToOpen(assignmentItem) {
    const lessons = get(assignmentItem.data.createScormAssignment[0].content, 'lessons');
    if (lessons && lessons.length > 0) {
      const lesson = lessons.find(({ status }) => status !== 'completed');
      if (lesson) {
        return lesson.uid;
      }
      return lessons[0].uid;

      /* // if there are no lessons that aren't completed, find the next one in sequence, or the first one.
      const currentLessonIndex = assignmentItem.content.lessons.findIndex(({ uid }) => uid === lessonId);
      // default to next and we can find a next lesson, then return it.
      if (defaultToNext && currentLessonIndex !== -1 &&
        currentLessonIndex < assignmentItem.content.lessons.length - 1) {
        return assignmentItem.content.lessons[currentLessonIndex + 1].uid;
      }
      // if !defaultToNext, return the current lesson. This handles the reload case.
      if (!defaultToNext && currentLessonIndex !== -1) {
        return lessonId;
      }
      return assignmentItem.content.lessons[0].uid; */
    }
    return null;
  }

  useEffect(() => {
    async function scormLaunch() {
      if (scormVerified) {
        const programId = routeParams.programId;
        const lessonId = routeParams.lessonId;
        if (programId) {
          const programAssignment = await createScormAssignment({ variables: { programId } });

          const assignmentId = programAssignment &&
            programAssignment.data && get(programAssignment.data, 'createScormAssignment[0].id');

          if (assignmentId) {
            const lessonIdToOpen = findIncompleteScormProgramLessonIdToOpen(programAssignment);
            if (lessonIdToOpen) {
              history.push(pushToProgram(
                { assignmentId, programId, lessonId: lessonIdToOpen },
              ));
            }
          } else {
            history.push(pushToHome());
          }

          // history.push(pushToProgram(1, programId, 1));

          // B.C. Tried updating the AssignmentQuery with the data returned from createScormAssignment, but it's not working.
          // If we can figure out a way to do this correct we could elimate the calls to the AssignmentQuery in the scorm
          // case in LearnLayout. Will come back to this at some point during BETA.
          // update: async (cache, { data: { createScormAssignment: assignmentItems } }) => {
          //   let assignmentListData = cache.readQuery({
          //     query: AssignmentQuery,
          //     variables: { completed: false },
          //   });
          //   if (assignmentListData === null) {
          //     assignmentListData = { getAssignmentList: assignmentItems };
          //   } else {
          //     assignmentListData.getAssignmentList = [...assignmentListData.getAssignmentList, ...assignmentItems];
          //   }
          //   await cache.writeQuery({ query: AssignmentQuery, variables: { completed: false } }, assignmentListData);
          // history.push(pushToHome());
        } else if (lessonId) {
          // orig
          // await createScormAssignment({ variables: { lessonId } });

          const createdLessonAssignmentResult = await createScormAssignment({ variables: { lessonId } });
          const assignmentId = createdLessonAssignmentResult &&
            createdLessonAssignmentResult.data && get(createdLessonAssignmentResult.data, 'createScormAssignment[0].id');
          if (assignmentId) {
            history.push(pushToMicroLesson(
              { assignmentId, lessonId },
            ));
          } else {
            history.push(pushToHome());
          }
        }
      }
    }
    scormLaunch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scormVerified]);

  return (
    <div />
  );
}

export default Scorm;
