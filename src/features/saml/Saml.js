import React, { useState, useEffect } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { setByPassSSOSetting } from '../../services/api/authentication';

function Saml() {
  const { params: routeParams, url } = useRouteMatch();
  const [, path] = url.split('/');
  const [samlVerified, setSamlVerified] = useState(false);
  const { verifySaml } = useAuth();
  const history = useHistory();

  useEffect(() => {
    async function samlVerification() {
      const token = routeParams.token;
      await verifySaml(token);
      setSamlVerified(true);
    }
    if (!samlVerified && path !== 'account-admin') {
      setByPassSSOSetting(false);
      samlVerification();
    } else {
      setSamlVerified(false);
      setByPassSSOSetting(true);
      history.push('/login');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [path]);

  return (
    <div />
  );
}

export default Saml;
