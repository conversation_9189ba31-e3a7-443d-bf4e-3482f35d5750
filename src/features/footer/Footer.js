import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import { getCurrentAccount } from '../../services/api/currentAccount';

import styles from './Footer.module.css';

export default function Footer({ lessonIsLoaded, privacyFooter = false }) {
  const [currentAccount, setCurrentAccount] = useState(null);

  useEffect(() => {
    async function fetchCurrentAccount() {
      const account = await getCurrentAccount();
      setCurrentAccount(account);
    }
    fetchCurrentAccount();
  }, []);

  const privacyPolicyLink = (currentAccount && currentAccount.gdprPolicyUrl) || 'https://emtrain.com/emtrain-privacy-policy/';

  const { t } = useTranslation();
  const { isMobile } = useResponsiveMode();
  const container = privacyFooter ? `${styles.container} ${styles.privacyContainer}` : styles.container;
  const link = privacyFooter ? `${styles.link}  ${styles.linkText}` : styles.link;
  return (
    <footer className={`${!lessonIsLoaded ? styles.outerContainerNoLesson : ''}`}>
      <div className={container}>
        {!privacyFooter && (
        <span>
          &copy;
          { ` ${new Date().getFullYear()} Emtrain `}
          {!isMobile && '|'}
        </span>
        )}
        {isMobile && <div className={styles.break} /> }
        <ul className={styles.ulContainer}>
          <li>
            <Link
              className={link}
              target="_blank"
              to={{ pathname: privacyPolicyLink }}
            >
              {t('footer.privacyPolicy')}
            </Link>
            |
          </li>
          <li>
            <Link className={link} target="_blank" to={{ pathname: 'https://emtrain.com/terms-of-service/' }}>
              {t('footer.serviceTerms')}
            </Link>
            |
          </li>
          <li>
            <Link className={link} target="_blank" to={{ pathname: 'https://emtrain.com/accessibility/' }}>
              {t('footer.accessibility')}
            </Link>
          </li>
        </ul>
      </div>
    </footer>
  );
}
