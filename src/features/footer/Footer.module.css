.container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  font-size: .89rem;
  line-height: 1rem;
  padding: 1rem 0 1rem 0;
}

.outerContainerNoLesson {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

a.link {
  color: inherit;
  text-decoration: underline;
  font-weight: normal !important;
  font-size: .89rem;
  margin: 0 .2rem;
}

.break {
  flex-basis: 100%;
  height: 0;
}

.ulContainer {
  display: contents;
}

li {
  display: inherit;
}

.privacyContainer {
  font-size: .8rem !important;
  margin-top: .5rem;
  padding: 1rem 0rem 0rem 0rem !important;
}

a.linkText {
  font-size: .8rem !important;
}