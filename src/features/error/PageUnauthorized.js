import React from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material';
import { pushToHome } from '../navigation/Routes/AppRoutes';
import triangleIcon from '../../images/triangle-icon.png';
import emtrainLogo from '../../images/emtrain-logo.png';
import styles from './PageUnauthorized.module.css';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import Button from '../../components/Button/Button';
import { concedeControl } from '../../services/api/scormRusticiAPI';
import { clearUserClosedRightPanel } from '../../services/layoutSettings';
import { useAuth } from '../../hooks/useAuth';

export default function PageUnauthorized() {
  const history = useHistory();
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { clearTokens } = useAuth();
  const { isSmallMobile, isMobile } = useResponsiveMode();
  const backToHome = () => {
    history.push(pushToHome());
  };

  const scormExit = async () => {
    concedeControl();
    clearUserClosedRightPanel();
    await clearTokens();
    window.close();
  };
  const { url, params: { message } } = useRouteMatch();
  const [, path] = url.split('/');
  const isScormPath = (path === 'scorm-exit');
  const decodedMsg = decodeURIComponent(message);

  return (
    <div
      className={styles.mainContainer}
      style={{ color: palette.primary.darkLightPurple,
        backgroundColor: palette.background.ghostWhite,
        backgroundSize: `${(isSmallMobile || isMobile) ? 'contain' : ''}`,
      }}
    >
      <div
        className={styles.container}
        style={{ width: `${isSmallMobile ? '18rem' : '36rem'}`,
          background: palette.background.ghostWhite,
          border: palette.border.greyShade,
          filter: palette.border.dropShadow }}
      >
        <img
          src={triangleIcon}
          alt="infoIcon"
          style={{ width: `${isSmallMobile ? '2.5rem' : '3.5rem'}` }}
        />

        <span className={`${isSmallMobile ? styles.mobileTitle : styles.title}`}>
          {/* eslint-disable-next-line max-len, no-nested-ternary */}
          {isScormPath ? decodedMsg : path === 'logged-out' ? t('logged-out.error_message') : t('401-error.error_message')}
        </span>
        <Button
          type="button"
          className={styles.backToHomeButton}
          onClick={() => (isScormPath ? scormExit() : backToHome())}
          sx={{
            color: palette.button.login.color,
            fontSize: !isSmallMobile && '1.1rem !important',
            backgroundColor: palette.button.login.backgroundColor,
            '&:hover': {
              color: palette.button.login.hoverColor,
              backgroundColor: palette.button.login.hoverBackgroundColor,
            },
            '&:disabled': {
              color: palette.button.login.disabledColor,
              backgroundColor: palette.button.login.disabledBackgroundColor,
            },
            '&:focus': {
              border: `solid ${palette.card.backgroundColor} 3px`,
            } }}
        >
          {/* eslint-disable-next-line max-len, no-nested-ternary */}
          {isScormPath ? t('scorm-exit.exit') : path === 'logged-out' ? t('logged-out.home') : t('404-error.home')}
        </Button>
        <img
          src={emtrainLogo}
          alt="emtrainLogo"
          className={styles.emtrainLogo}
          style={{ height: `${isSmallMobile ? '0.9rem' : '1.3rem'}` }}
        />
      </div>
    </div>
  );
}
