.mainContainer {
  background-image: url('../../images/shapes.png');
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: auto;
  width: 100%;
  height: inherit;
  display: flex;
  justify-content: center;
  align-items: center;
}

.container {
  text-align: center;
  overflow: hidden;
  border-radius: 1rem;
  display: flex;
  flex-direction: column;
  padding: 2rem 1rem 1rem;
  justify-content: center;
  align-items: center;
}

.emtrainLogo {
  float: right;
  align-self: flex-end;
  margin-top: 0.5rem;
}

.title {
  text-align: center;
  font-weight: 600;
  line-height: 1.1;
  padding: 1.5rem 0 1.5rem;
  font-size: 1.7rem;
} 

.mobileTitle {
  text-align: center;
  font-weight: 600;
  line-height: 1.1;
  padding: 0.5rem 1rem;
  font-size: 1.2rem;
}

.backToHomeButton {
  padding: 0.2rem 1rem !important;
  font-weight: 600 !important;
  text-transform: none !important;
  border-radius: 100px !important;
}