.filterContainer {
  display: flex;
  flex-direction: row;
  padding: 0.5rem 0.75rem;
  align-items: center;
  justify-content: space-between;
  height: 4.5rem;
}

.container {
  position: relative;
  display: flex;
  flex: 1;
  min-width: 20rem !important;
  padding: 0;
  overflow: hidden;
  /* border: 4px solid red; */
}

.listContainer {
  display: flex;
  position: absolute;
  top: 4.5rem; /* match height of .filterContainer (abover) */
  left: 0;
  bottom: 0;
  right: 0;
  flex: 1;
  flex-direction: column;
  align-items: center;
  padding: 0.4rem 0 0 1.25rem;
  /* border: 4px solid lightblue; */
}

.scrollingListContainer {
  display: flex;
  flex-direction: column;
  position: absolute;
  height: 100vh;
  top: 0;
  right: 0;
  width: 100%;
  padding: 0px 1.25rem 5rem 1.25rem;
  overflow-y: auto;
  /* border: 2px solid red; */
}

.buttonDefault {
  margin: 0.25rem 0.25rem 0.25rem 0.4em !important;
  border-radius: 30px !important;
  background: transparent !important;
  font-size: 1.2em !important;
  line-height: 1.5 !important;
  text-transform: none !important;
  font-weight: normal !important;
  color: #B2B4CA !important;
  padding: 0 1em !important;
  border: 1px solid #65667C !important;
  height: 2.2rem !important;
  min-height: 2.2rem !important;
}

.buttonDefault:hover {
  margin: 0.25rem 0.25rem 0.25rem 0.4em !important;
  font-size: 1.2em !important;
  text-transform: none !important;
  font-weight: normal !important;
  color: #FFFFFF !important;
  border-radius: 30px !important;
}

.buttonSelected, .buttonSelected:hover {
  margin: 0.25rem 0.25rem 0.25rem 0.4em !important;
  font-size: 1.2em !important;
  text-transform: none !important;
  font-weight: bold !important;
  color: #FFFFFF !important;
  border-radius: 30px !important;
  background: rgba(255, 255, 255, .3) !important;
  padding: 0 1em !important;
  height: 2.2rem !important;
  min-height: 2.2rem !important;
}

.spacer {
  clear: both;
  height: .8rem;
}

.verticalLine {
  height: 2rem;
  border-left: solid 1px;
  border-color: rgba(111, 116, 143, 1);
  margin: 0.5rem 0.5rem;
}

.buttonsSection {
  display: flex;
}

.closeButtonContainer {
  display: flex;
  cursor: pointer;
  padding: 0.5rem 0.75rem 0 0;
}

.closeXMargin {
  margin-top: -0.5rem;
}

.link {
  cursor: pointer;
}

.assignmentsMessage {
  display: flex;
  align-self: flex-start;
  color: white;
  margin-bottom: 0.4rem;
  h1 {
    font-weight: normal;
    font-size: 1rem;
  }
}

.paddingDiv {
  width: 100%;
  padding-bottom: 2rem;
}

.sharedWithMeMessage {
  width: 100%;
  text-align: left;
  padding: .6rem 0;
  h1 {
    font-weight: normal;
    font-size: 1rem;
  }
}

.buttonContainer {
  padding-top: 1.4rem;
  display: flex;
  justify-content: center;
  gap: 0.5rem
}

.assignmentTab {
  margin-left: 0.4em !important;
  font-size: 1.2em !important;
  text-transform: none !important;
  font-weight: bold !important;
  color: #FFFFFF !important;
  padding: 0 0.2em !important;
  height: 2.2rem !important;
}
 
.assignmentTabSection {
  display: flex;
}

.skipLinkButton {
  position: absolute;
  left: -100em;
  top: -100em;
  text-transform: none !important;
  color: #FFFFFF !important;
  background: transparent !important;
  &:focus {
    position: static;
    margin: 0 0 1rem 1rem;
    border-radius: 30px !important;
    font-size: 1.2em !important;
    line-height: 1.5 !important;
    padding: 0 1em !important;
    height: 2.2rem !important;
    min-height: 2.2rem !important;
    z-index: 999999999;
  }
}
