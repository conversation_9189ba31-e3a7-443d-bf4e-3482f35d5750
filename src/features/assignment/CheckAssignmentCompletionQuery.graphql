
#import "./AssignmentFragment.graphql"

query CheckAssignmentCompletionQuery($assignmentUserLessonId: ID!, $lessonId: ID!, $enrollmentId: ID!, $scormProgramId: ID, $assignmentProgramId: ID, $assignmentLessonId: ID, $scormEnforceSequence: Boolean) {
  checkAssignmentCompletion(assignmentUserLessonId: $assignmentUserLessonId,
    lessonId: $lessonId,
    enrollmentId: $enrollmentId,
    scormProgramId: $scormProgramId,
    assignmentProgramId: $assignmentProgramId,
    assignmentLessonId: $assignmentLessonId,
    scormEnforceSequence: $scormEnforceSequence) {
    ...AssignmentFragment
  }
}
