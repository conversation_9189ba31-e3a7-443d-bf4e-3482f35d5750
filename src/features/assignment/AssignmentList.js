import React, { useState, Fragment, useEffect, useRef } from 'react';
import { useApolloClient, useMutation } from '@apollo/client';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { Button, Container, useTheme } from '@mui/material';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import { get, isEmpty } from 'lodash';
import CloseXMarkIcon from '../../images/close-x-mark-16.svg';
import { useAssignmentsDrawer } from '../../hooks/useDrawer';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import { usePreviewMode } from '../../hooks/usePreviewMode';
import { useUser } from '../../hooks/useUser';
import { pushToProgram, pushToMicroLesson, pushToHome } from '../navigation/Routes/AppRoutes';
import MicroLesson from './MicroLesson/MicroLesson';
import Program from './Program/Program';
import AssignmentFragment from './AssignmentFragment.graphql';
import CloseSharedAssignmentMutation from './CloseSharedAssignmentMutation.graphql';
import Dialog from '../../components/Modal/Dialog';
import styles from './AssignmentList.module.css';
import CancelButton from '../../components/Button/CancelButton';
import SendSaveButton from '../../components/Button/SendSaveButton';
import CardResponsiveText from '../../components/CardResponsiveText/CardResponsiveText';

export default function AssignmentList({
  assignmentListData, completedItems, handleBreadCrumb,
  onSetCompletedItems, findProgramLessonIdToOpen,
  setShowCompletionCertificate, timerPaused, loadedRouteParams,
  setScrollToAssignment, scrollToAssignment, updateLastViewedAssignment }) {
  const listContainerRef = useRef(null);
  const { t } = useTranslation();
  const [openProgram, setOpenProgram] = useState({});
  const [openProgramTimer, setOpenProgramTimer] = useState({});
  const [urlLoadedAssignmentDone, setUrlLoadedAssignmentDone] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [showJumpButton, setShowJumpButton] = useState(false);
  const client = useApolloClient();
  const history = useHistory();
  const { path, params: routeParams } = useRouteMatch();
  const { closeDrawer } = useAssignmentsDrawer();
  const { isAssignmentsDrawerView, isSmallMobile } = useResponsiveMode();
  const { previewMode } = usePreviewMode();
  const user = useUser();
  const isContentReviewer = get(user, 'accounts[0].accountUsers.roleId') === 7;
  const { palette } = useTheme();
  const [sharedModalData, setSharedModalData] = useState(false);
  const [assignmentUserLessonId, setAssignmentUserLessonId] = useState(null);

  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: '0.7rem',
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.customScrollbar.thumbColor,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.customScrollbar.background,
      },
    },
  });
  const scrollbarStyleClass = useStyles();

  // For scorm programs with an enforced sequence, set a single lesson
  // (the next one in the sequence) to enabled. Set all other user
  // lessons in the program to not enabled. Leave the program enabled.
  // It's assumed the passed in data structure is an array with 1 element, which is the program
  // We need to return the same structure.
  const enforceScormSequence = (assignment) => {
    const enforceSequence = assignment?.content?.scormEnforceSequence;
    const { content } = assignment;

    if (!enforceSequence) {
      const updatedAssignment = { ...assignment, content: { ...content }, enabled: true };
      if (assignment?.content?.lessons) {
        const updatedLessons = assignment?.content?.lessons.map((item) => {
          const updatedItem = { ...item, enabled: true };
          return updatedItem;
        });

        updatedAssignment.content.lessons = updatedLessons;
      }

      return [updatedAssignment];
    }

    let nextSequencedItemFound = false;

    const updatedAssignment = { ...assignment, content: { ...content }, enabled: true };
    if (assignment?.content?.lessons) {
      const updatedLessons = assignment?.content?.lessons.map((item) => {
        const enableProgramItem = !nextSequencedItemFound;
        const updatedItem = { ...item, enabled: enableProgramItem };

        if (enableProgramItem && item.status !== 'completed') {
          nextSequencedItemFound = true;
        }

        return updatedItem;
      });

      updatedAssignment.content.lessons = updatedLessons;
    }

    return [updatedAssignment];
  };

  // For campaigns with an enforced sequence, set a single user assignment
  // item (the next one in the sequence) to enabled. Set all other user
  // assignment items in the campaign to not enabled. Set a program to
  // enabled if any of its items is enabled.
  const enforceSequence = (assignments) => {
    let currentCampaignId;
    let nextSequencedItemFound;

    const assignmentsList = assignments.map((assignment) => {
      const { content } = assignment;
      let enableItem = !nextSequencedItemFound || assignment?.status === 'completed';

      // If sequencing is not enforced, everything should be enabled
      if (!assignment?.campaign?.sequenced) {
        const updatedAssignment = { ...assignment, content: { ...content }, enabled: true };

        if (assignment?.content?.lessons) {
          const updatedLessons = assignment?.content?.lessons.map((item) => {
            const updatedItem = { ...item, enabled: true };
            return updatedItem;
          });

          updatedAssignment.content.lessons = updatedLessons;
        }

        return updatedAssignment;
      }

      // Otherwise, let's build the sequencing
      if (currentCampaignId !== assignment.campaign.id) {
        currentCampaignId = assignment.campaign.id;
        nextSequencedItemFound = false;
      }

      if (assignment.position === 1) {
        enableItem = true;
      }

      const updatedAssignment = { ...assignment, content: { ...content }, enabled: enableItem };

      if (enableItem && assignment.status !== 'completed' && assignment.type !== 'program') {
        nextSequencedItemFound = true;
      }

      if (assignment?.content?.lessons) {
        let enableProgram = enableItem;

        const updatedLessons = assignment?.content?.lessons.map((item) => {
          const enableProgramItem = !nextSequencedItemFound;
          const updatedItem = { ...item, enabled: enableProgramItem };

          if (enableProgramItem && item.status !== 'completed') {
            nextSequencedItemFound = true;
            enableProgram = true;
          }

          return updatedItem;
        });

        updatedAssignment.content.lessons = updatedLessons;
        updatedAssignment.enabled = enableProgram;
      }
      return updatedAssignment;
    });

    // Specific ordering for workday users
    if (user.isWorkdayCCL && !previewMode) {
      // Get the current assignment ID from sessionStorage (assuming we haven't just launched)
      let launchedAssignmentId = sessionStorage.getItem('launchedAssignmentId');

      // If we're launching from an external link, get the assignment and set our sessionStoage value
      if (path.includes('external')) {
        const assignment = assignmentsList.find(({ id }) => id === routeParams?.assignmentId);
        launchedAssignmentId = assignment?.id;
        if (launchedAssignmentId) {
          sessionStorage.setItem('launchedAssignmentId', launchedAssignmentId);
        }
      }

      // If we have a launched assignment, we want it at the top of the list
      if (launchedAssignmentId) {
        // First, sort all items by createdAt in descending order
        assignmentsList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        // Then, move the launcehd assignment to the beginning of the list
        assignmentsList.sort((a, b) => {
          if (a.id === launchedAssignmentId) return -1;
          if (b.id === launchedAssignmentId) return 1;
          return 0;
        });
      } else {
        // If no launched assignment (i.e. workday learner used regular login flow), just sort by date
        assignmentsList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      }
      return assignmentsList;
    }

    return assignmentsList;
  };

  const assignmentItems = get(assignmentListData, 'getAssignmentList', [])
    .filter(({ assignmentType }) => assignmentType !== 'shared-closed');

  let updatedAssignments = assignmentItems;
  if (updatedAssignments.length !== 0) {
    updatedAssignments = user.scorm ? enforceScormSequence(assignmentItems[0]) : enforceSequence(assignmentItems);
  }

  const unSharedAssignmentItems = get(assignmentListData, 'getAssignmentList', [])
    .filter(({ assignmentType }) => assignmentType !== 'shared-closed' && assignmentType !== 'shared');

  const [closeSharedAssignment] = useMutation(CloseSharedAssignmentMutation);
  let sharedLabelDisplayed = false;
  function sharedLabelDone() {
    if (!sharedLabelDisplayed) {
      sharedLabelDisplayed = true;
      return false;
    }
    return true;
  }

  async function sharedModalDialog() {
    await closeSharedAssignment({
      update: (cache) => {
        cache.modify({
          id: `AssignmentItem:${assignmentUserLessonId}`,
          fields: {
            assignmentType(existingType) {
              return `${existingType}-closed`;
            },
          },
          /* broadcast: false // Include this to prevent automatic query refresh */
        });
      },
      variables: { assignmentUserLessonId },
    },
    );
    setSharedModalData(false);
    setAssignmentUserLessonId(null);
    history.push(pushToHome());
  }

  function closeBaseAssignment(e, assignmentId) {
    e.preventDefault();
    e.stopPropagation();
    setAssignmentUserLessonId(assignmentId);
    setSharedModalData(true);
  }

  function selectProgram(id, lessonId) {
    if (id) {
      if (openProgram[id]) {
        return;
      }
      setOpenProgram({
        [id]: !openProgram[id],
      });
      if (!openProgramTimer[id]) {
        setOpenProgramTimer({
          [id]: !openProgramTimer[id],
        });
      }
      // if the routeParams match the newly selected program, just return.
      const idArray = id.split('-');
      if (routeParams.assignmentId === idArray[0] &&
        routeParams.programId === idArray[1] &&
        routeParams.lessonId === lessonId) {
        return;
      }
      // when we toggle a new program, open the first lesson that isn't completed.
      const programAssignmentItem =
        client.readFragment({ id: `AssignmentItem:${idArray[0]}`, fragment: AssignmentFragment });

      // Update the last viewed assignment in the cache
      if (updateLastViewedAssignment && idArray[0]) {
        updateLastViewedAssignment(idArray[0]);
      }

      let lessonIdToOpen = null;
      if (lessonId) {
        lessonIdToOpen = lessonId;
      } else if (!urlLoadedAssignmentDone && loadedRouteParams &&
        loadedRouteParams.programId === idArray[1] && loadedRouteParams.lessonId) {
        lessonIdToOpen = loadedRouteParams.lessonId;
      } else {
        lessonIdToOpen = findProgramLessonIdToOpen(programAssignmentItem);
      }
      if (!urlLoadedAssignmentDone) {
        setUrlLoadedAssignmentDone(true);
      }
      if (lessonIdToOpen) {
        history.push(pushToProgram({ assignmentId: idArray[0], programId: idArray[1], lessonId: lessonIdToOpen }));
      }
    } else {
      setOpenProgram({});
      if (isAssignmentsDrawerView) {
        closeDrawer();
      }
    }
  }

  function selectProgramTimer(id) {
    setOpenProgramTimer({
      ...openProgramTimer,
      [id]: !openProgramTimer[id],
    });
  }

  useEffect(() => {
    if (scrollToAssignment && listContainerRef && listContainerRef.current) {
      listContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
      setScrollToAssignment(false);
    }
  }, [setScrollToAssignment, scrollToAssignment]);

  // this effect ensures the open program is set in all situations.
  useEffect(() => {
    if (!routeParams || !routeParams.assignmentId) {
      if (!isEmpty(openProgram)) {
        setOpenProgram({});
      }
      return;
    }
    if (updatedAssignments) {
      const assignment = updatedAssignments.find(({ id }) => id === routeParams.assignmentId);
      if (assignment && routeParams.programId) {
        if (!openProgram || !{}.hasOwnProperty.call(openProgram, `${assignment.id}-${assignment.content.uid}`)) {
          selectProgram(
            `${assignment.id}-${assignment.content.uid}`,
            routeParams && routeParams.lessonId ? routeParams.lessonId : null);
        }
      }
      if (!assignment && !routeParams.programId) {
        setOpenProgram({});
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [routeParams, updatedAssignments, assignmentItems]);

  useEffect(() => {
    if (routeParams && routeParams.assignmentId) {
      setShowJumpButton(true);
    } else {
      setShowJumpButton(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [routeParams]);

  function focusOnJumpElement() {
    const jumpElement = document.getElementById('lesson-card-focus-element');
    if (jumpElement) {
      jumpElement.focus();
    }
  }

  return (
    <Container
      className={`${styles.container} ${scrollbarStyleClass.scrollBar}`}
      sx={{ bgcolor: 'background.assignmentList', width: isSmallMobile ? '100vw !important' : '23.58rem !important' }}
      id="assignment-list"
      // aria-orientation="vertical"
    >
      <div className={styles.filterContainer}>
        {!user.scorm && !previewMode && !isContentReviewer && (
          <div>
            {showJumpButton && !user.scorm && !previewMode && !isContentReviewer && ( // showJumpButton
              <Button
                disableRipple
                className={styles.skipLinkButton}
                onClick={focusOnJumpElement}
                data-cy="skip_to_main_content"
                sx={{
                  border: `2px solid ${palette.card.backgroundColor} !important`,
                  '&:focus': {
                    border: `2px solid ${palette.card.backgroundColor} !important`,
                  },
                  outline: '1px solid white !important',
                }}
              >
                {t('assignmentList.skip_to_manin_content')}
              </Button>
            )}
            <Tabs
              value={tabValue}
              selectionFollowsFocus
              className={styles.buttonsSection}
              TabIndicatorProps={{ style: { backgroundColor: 'transparent' } }}
              aria-label="assignment-list-tabs"
            >
              <Tab
                onClick={() => { onSetCompletedItems(false); setTabValue(0); }}
                className={completedItems ? styles.buttonDefault : styles.buttonSelected}
                data-cy="todo"
                index={0}
                value={tabValue}
                role="tab"
                aria-controls="todo-items-tabpanel"
                aria-selected={!completedItems}
                id="todo-items-tab"
                label={t('assignmentList.todo')}
                aria-label={t('assignmentList.todo')}
                disableRipple
                sx={{
                  '&:focus': {
                    outline: '2px solid white !important',
                    border: `2px solid ${palette.card.backgroundColor} !important`,
                  },
                }}
              />
              <Tab
                onClick={() => { onSetCompletedItems(true); setTabValue(1); }}
                className={completedItems ? styles.buttonSelected : styles.buttonDefault}
                data-cy="completed"
                index={1}
                value={tabValue}
                role="tab"
                aria-controls="completed-items-tabpanel"
                aria-selected={completedItems}
                id="completed-items-tab"
                label={t('assignmentList.completed')}
                aria-label={t('assignmentList.completed')}
                disableRipple
                sx={{
                  '&:focus': {
                    outline: '2px solid white !important',
                    border: `2px solid ${palette.card.backgroundColor} !important`,
                  },
                }}
              />
            </Tabs>
          </div>
        )}
        {previewMode && !user.scorm && !isContentReviewer && (
          <div className={styles.buttonsSection}>
            <Button
              className={styles.buttonSelected}
              disableRipple
              sx={{
                '&:focus': {
                  outline: '2px solid white !important',
                  border: `2px solid ${palette.card.backgroundColor} !important`,
                },
              }}
            >
              {t('preview.preview')}
            </Button>
          </div>
        )}
        {!previewMode && !user.scorm && isContentReviewer && (
          <div className={styles.buttonsSection}>
            <Button
              className={styles.buttonSelected}
            >
              {t('assignmentList.review')}
            </Button>
          </div>
        )}
        {user.scorm && !previewMode && !isContentReviewer && (
          <div className={styles.assignmentTabSection}>
            <div className={`${styles.assignmentTab}`}>
              {t('assignmentList.assignment_capitalized')}
            </div>
          </div>
        )}
        {isAssignmentsDrawerView && (
          <div className={styles.closeButtonContainer} onClick={() => closeDrawer()}>
            <CloseXMarkIcon
              aria-label={t('assignmentList.closeAssignmentList')}
              tabIndex="0"
              role="button"
              className={styles.closeXMargin}
              onKeyDown={
                (e) => {
                  if (e.code === 'Enter' || e.code === 'Space') {
                    e.preventDefault();
                    closeDrawer();
                  }
                }
              }
            />
          </div>
        )}
      </div>
      {(previewMode || isContentReviewer) && (
        <div className={styles.spacer} />
      )}
      <div className={styles.listContainer}>
        <div
          className={styles.scrollingListContainer}
          ref={listContainerRef}
          role="tabpanel"
          aria-labelledby={completedItems ? 'completed-items-tab' : 'todo-items-tab'}
          id={completedItems ? 'completed-items-tabpanel' : 'todo-items-tabpanel'}
          value={tabValue}
          index={completedItems ? 1 : 0}
        >
          {!user.isWorkdayCCL && completedItems && (!updatedAssignments || updatedAssignments.length === 0) && (
            <div className={styles.assignmentsMessage} data-cy="no_completed_assignments">
              <h1>
                {t('completedItems.noCompletedAssignments')}
              </h1>
            </div>
          )}
          {!user.isWorkdayCCL && !completedItems && (!updatedAssignments || unSharedAssignmentItems.length === 0) && (
            <div className={styles.assignmentsMessage} data-cy="no_todo_assignments">
              <h1>
                { !isContentReviewer ? t('welcome.noTodoAssignments') : t('welcome.noItemsToReview') }
              </h1>
            </div>
          )}
          {!user.isWorkdayCCL && !user.scorm && !previewMode && !isContentReviewer && updatedAssignments && unSharedAssignmentItems.length > 0 && (
            <>
              <div className={styles.assignmentsMessage} style={{ color: palette.primary.light }}>
                <h1>
                  {`${!updatedAssignments || unSharedAssignmentItems.length === 0 ? '0' : unSharedAssignmentItems.length} ` +
                  `${completedItems ? t('assignmentList.completed') : t('assignmentList.current')} ` +
                  `${unSharedAssignmentItems.length === 1 ?
                    t('assignmentList.assignment') : t('assignmentList.assignments')}`}
                </h1>
              </div>
            </>
          )}
          {updatedAssignments.map(({ id, type, assignmentType, content, dueDate, enabled }, i) => (
            <Fragment key={id}>
              {!user.isWorkdayCCL && assignmentType === 'shared' && !sharedLabelDone() && !completedItems && (
                <div className={styles.sharedWithMeMessage} style={{ color: palette.primary.white }}>
                  <h1>
                    {t('assignmentList.shared_with_me')}
                  </h1>
                </div>
              )}
              {/* eslint-disable-next-line no-nested-ternary */}
              {assignmentType === 'shared-closed'
                ? (<div />) : (
                  type === 'program'
                    ? (
                      <Program
                        assignmentId={id}
                        content={content}
                        dueDate={dueDate}
                        assignmentEnabled={enabled}
                        keyId={i}
                        isOpen={openProgram[`${id}-${content.uid}`]}
                        isProgramTimerOpen={!!openProgramTimer[`${id}-${content.uid}`]}
                        onProgramClick={(event) => {
                          if (event.target && event.target.id && (event.target.id.startsWith('completion_click_'))) {
                            setShowCompletionCertificate(true);
                          } else if (!openProgram[`${id}-${content.uid}`]) {
                            setShowCompletionCertificate(false);
                          }
                          if (routeParams.assignmentId === id && routeParams.programId === content.uid) {
                            return;
                          }
                          // Update the last viewed assignment in the cache
                          if (updateLastViewedAssignment) {
                            updateLastViewedAssignment(id);
                          }
                          selectProgram(`${id}-${content.uid}`);
                        }}
                        onProgramTimerClick={() => selectProgramTimer(`${id}-${content.uid}`)}
                        timerPaused={timerPaused}
                        onLessonClick={(event, lesson, vars) => {
                          event.preventDefault();
                          if (routeParams.programId === content.uid && routeParams.lessonId === lesson.uid) {
                            return;
                          }
                          setShowCompletionCertificate(false);
                          handleBreadCrumb({ program: content, lesson });
                          if (isAssignmentsDrawerView) { closeDrawer(); }
                          // Update the last viewed assignment in the cache
                          if (updateLastViewedAssignment) {
                            updateLastViewedAssignment(id);
                          }
                          history.push(pushToProgram(vars));
                        }}
                        completedItems={completedItems}
                        setShowCompletionCertificate={setShowCompletionCertificate}
                        assignmentType={assignmentType}
                        closeBaseAssignment={closeBaseAssignment}
                      />
                    ) : (
                      <MicroLesson
                        assignmentId={id}
                        content={content}
                        dueDate={dueDate}
                        assignmentEnabled={enabled}
                        keyId={i}
                        onClick={(event, vars) => {
                          event.preventDefault();
                          setShowCompletionCertificate(false);
                          selectProgram();
                          handleBreadCrumb({ lesson: content });
                          // Update the last viewed assignment in the cache
                          if (updateLastViewedAssignment) {
                            updateLastViewedAssignment(id);
                          }
                          history.push(pushToMicroLesson(vars));
                        }}
                        completedItems={completedItems}
                        assignmentType={assignmentType}
                        closeBaseAssignment={closeBaseAssignment}
                      />
                    )
                )}
            </Fragment>
          ))}
          <Dialog
            open={!!sharedModalData}
            onClose={() => { setSharedModalData(false); }}
            sharedAssignmentDialog
          >
            <CardResponsiveText>
              <div style={{ marginTop: '2rem' }}>
                {t('assignmentList.close_shared_assignment')}
              </div>
            </CardResponsiveText>
            <div className={styles.buttonContainer}>
              <CancelButton
                onClick={() => { setSharedModalData(false); }}
                data-cy="sharedAssignmentCancel"
              />
              <SendSaveButton
                onClick={() => { sharedModalDialog(); }}
                data-cy="sharedAssignmentOk"
                label={t('sideDrawer.ok')}
              />
            </div>
          </Dialog>
          <div className={styles.paddingDiv} />
        </div>
      </div>
    </Container>
  );
}
