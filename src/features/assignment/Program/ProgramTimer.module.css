.container {
  cursor: pointer;
  margin-top: 0.75rem;
  padding: 0.75rem;
}

.header, .minuteTextContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.closedHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.minuteTextContainer {
  margin-top: 0.5rem;
}

.titleContainer {
  display: flex;
  align-items: center;
  width: 95%;
}

.timeRequirementHeader {
  margin: 0;
  padding-left: 0.25rem;
  font-size: 1rem;
  /* font-weight: bold; */
  font-size: .9rem;
}

.timeRequirementText {
  font-size: .9rem;
}

.clockIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.minsRemaining {
  padding-left: 0.5rem;
  font-size: .9rem;
}

.minimumDescription {
  padding-right: 1rem;
  font-size: .9rem;
  line-height: 1.1rem;;
  min-width: 100%;
  width: 0;
}

.progressBarContainer {
  width: 100%;
  height: 1.5rem;
  overflow: hidden;
}

.progressBar {
  position: relative;
  border-radius: 5px;
  height: 100%;
  transition: all 0.25s ease-in-out;
}

.completedText {
  /* font-weight: bold; */
  font-size: .9rem;
}

.requiredText {
  font-weight: normal;
  font-size: .9rem;
}

.pausedSection {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  /* font-weight: bold; */
  font-size: .8rem;
  line-height: 1.4rem;
}

.timerSummary {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.pausedSectionMinimized {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  /* font-weight: bold; */
  font-size: .8rem;
  line-height: 1.4rem;
  align-self: flex-end;
  border-radius: .6rem;
  padding: 0 1rem 0 1rem;
}

.timeRequirementMet {
  border-top: solid 1px lightgray;
  padding: 0.5rem .05rem 0 0.5rem;
  margin-top: 0.5rem;
}

.timeRequirementMetIcon {
  margin-right: 0.25rem;
}

.timerLeftSection {
  flex-grow: 2;
}
