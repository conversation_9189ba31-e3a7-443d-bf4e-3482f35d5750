.item {
  margin-bottom: 1rem;
  width: 100%;
  border-radius: 8px;
  position: relative;
}

.itemDisabled {
  margin-bottom: 1rem;
  width: 100%;
  border-radius: 8px;
  position: relative;
  background-color: rgba(78%,78.8%,86.7%,50%);
}

.pinkTab {
  width: 1.4rem;
  position: absolute;
  top: 0;
  right: 0;
}

.programLessons {
  padding: 0 .7rem 0.75rem .7rem !important;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.paddingCompletedActive {
  width: 100%;
  height: .8rem;
}

.programTile {
  padding-top: 0.75rem;
  position: relative;
}

.programTileOpen {
  padding-top: 0.75rem;
}

.programTitle {
  word-break: break-word;
  padding-right: 1rem;;
}

.programTitle > h3 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.2rem;
  word-break: break-word;
}

.progressIcon {
  width: 1rem;
  height: 1rem;
}

.hide {
  display: none;
}

.link {
  cursor: pointer;
}

.image {
  border-radius: 3px;
  margin-right: 1rem;
  max-height: 6rem;
  max-width: 6rem;
}

.programLesson {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.programLessonTitle {
  padding-left: 0.4rem;
  margin: 0.5rem 0;
  font-size: .9rem;
  line-height: 1.1rem;
  margin-inline-start: 0; /*dd tag styles */
}

.programLessonTitleDisabled {
  padding-left: 0.4rem;
  font-size: .9rem;
  line-height: 1.1rem;
  margin-inline-start: 0; /*dd tag styles */
  color: rgba(34, 36, 66, 60%) !important;
}

.programLessons, .programHeader {
  padding: 0 0.75rem;
}

.programHeader {
  display: flex;
  cursor: pointer;
}

.programHeader h3, p {
  margin: 0;
}

.dueDate {
  margin-top: 0.5rem;
  font-size: .85rem;
  line-height: 1.1rem;
  margin-bottom: 0.5rem;
}

.disabled {
  color: rgba(34, 36, 66, 70%);
}

.horizontalLine {
  opacity: 30%;
  height: 1px;
  border: none;
}

.progressBar {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  width: 100%;
  padding: 0 1rem .5rem 1rem;
  cursor: pointer;
}

.progressBarCompletedActive {
  border-radius: 0 !important;
  padding: 0.5rem 1rem .3rem 1rem;
}

.progressBar p {
  margin: 0;
  font-weight: 600;
  font-size: .9rem;
}

.lessonContainer {
  display: flex;
  align-items: top;
  width: 100%;
  border-radius: 8px;
  transition: all 0.5s ease;
  padding: 0.25rem .5rem;
  margin-inline-start: auto; /*dd tag styles */
}

.lessonContainerDisabled {
  display: flex;
  align-items: top;
  width: 100%;
  border-radius: 8px;
  transition: all 0.5s ease;
  padding: 0.25rem .5rem;
  margin-inline-start: auto; /*dd tag styles */
}

.tooltip {
  position: relative;
  display: table;
  width: 100%;
}

.tooltipProgram {
  padding-top: 0.75rem;
  padding-bottom: 0.5rem;
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  background-color: #FCFCFC;
  color: #40405D;
  text-align: left;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #C7C9DD;
  line-height: 16px;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  top: 125%;
  display: flex;
  align-items: center;
  z-index: 1;
  margin: 0 15px;
}

.tooltipProgram .tooltiptextProgram {
  visibility: hidden;
  background-color: #FCFCFC;
  color: #40405D;
  text-align: left;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #C7C9DD;
  line-height: 16px;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  width: 80%;
  display: flex;
  align-items: center;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
  visibility: visible;
}

.tooltipProgram:hover .tooltiptextProgram {
  visibility: visible;
}

.lessonContainer dd {
  margin: 0;
}

.activeLesson {
  background: #41415c;
  color: white;
  transition: all 0.5s ease-in-out;
}

.completedLessonsContainer {
  text-align: right;
}

.completedLessonsContainer p {
  font-weight: normal;
}

.timeNotMet {
  display: flex;
  align-items: center;
}

.timeNotMet p {
  margin-left: 0.15rem;
  font-weight: bold;
}

.completedItemRow {
  align-items: center;
  padding: 0 1rem 0.4rem 1rem;
}

.completedItemLabel {
  text-align: center;
  font-size: 0.9rem;
  font-weight: bold;
  padding: 0 0.5rem 0 0.5rem;
  line-height: 1.6rem;
}

.completedItemCert {
  display: inline-flex;
  padding-top: .4rem;
  gap: .3rem;
  justify-content: left;
  align-items: center;
  cursor: pointer;
}

.completedItemCertText {
  font-size: .8rem;
  font-weight: normal;
}

.completedItemCertText:hover {
  font-weight: 600;
}

.rightArrowDiv {
  margin-bottom: .1rem;
}

.rightArrow {
  width: .4rem;
}

.break {
  flex-basis: 100%;
  width: 100%;
}

.completedItemName {
  display: inline-flex;
  font-size: 1rem;
  line-height: 1.2rem;
  font-weight: bold;
  padding-bottom: .5rem;
}

.lessonsList {
  margin-block-start: 0em;
  margin-block-end: 0em;
  padding-inline-start: initial;
}

.lessonTitle {
  margin-block-start: 0em;
  margin-block-end: 0em;
}

.closedSharedAssignmentButton {
  position: absolute;
  top: -1rem;
  right: -1rem;
  cursor: pointer;
  z-index: 10;
}

.closedSharedAssignmentImage {
  width: 1.4rem;
}
