/* eslint-disable max-len */
import React from 'react';
import { Paper, Box, Button, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import ClockIcon from '@mui/icons-material/AccessTime';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBan } from '@fortawesome/free-solid-svg-icons';
import { get } from 'lodash';
import { useRouteMatch } from 'react-router-dom';
import { IN_PROGRESS, OPEN, COMPLETED } from '../../../hooks/useAssignment';
import { useLocale } from '../../../hooks/useLocale';
import { useUser } from '../../../hooks/useUser';
import { usePreviewMode } from '../../../hooks/usePreviewMode';
import ProgramTimer from './ProgramTimer';
import getCompletedTimeInMinutes from './getCompletedTimeInMinutes';
import PlayCircle from '../../../icons/PlayCircle';
import Circle from '../../../icons/Circle';
import CheckCircle from '../../../icons/CheckCircle';
import CertificateIcon from '../../../icons/Certificate';
import styles from './Program.module.css';
import ProgramThumbnail from '../../../images/program_thumbnail.png';
import rightArrow from '../../../images/right-arrow.png';
import PinkTab from '../../../images/pink-tab.png';
import CloseSharedIcon from '../../../images/x-in-circle.png';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import { useAssignmentsDrawer } from '../../../hooks/useDrawer';

function getProgLessonLabelledby(lesson, isActive, completedItems, labelledby) {
  const { status } = lesson;
  const statusText = status === COMPLETED ? 'completed' : 'incomplete';
  const inProgressText = isActive ? 'inprogress-' : '';
  const text = completedItems && isActive
    ? `inprogress-completed-${labelledby}`
    : `${inProgressText}${statusText}-${labelledby}`;
  return text;
}

function getProgramLessonStatusIcon(t, lesson, isActive, completedItems, labelledby) {
  const { status, enabled } = lesson;
  if (completedItems && isActive) {
    return <PlayCircle aria-hidden aria-labelledby={getProgLessonLabelledby(lesson, isActive, completedItems, labelledby)} />;
  }
  switch (status) {
    case IN_PROGRESS:
    case OPEN:
      return isActive ?
        <PlayCircle aria-hidden aria-labelledby={getProgLessonLabelledby(lesson, isActive, completedItems, labelledby)} />
        : <Circle color={!enabled ? '#9696b7' : 'default'} aria-hidden aria-labelledby={getProgLessonLabelledby(lesson, isActive, completedItems, labelledby)} />;
    case COMPLETED:
      return isActive ?
        <PlayCircle aria-hidden aria-labelledby={getProgLessonLabelledby(lesson, isActive, completedItems, labelledby)} />
        : <CheckCircle aria-hidden aria-labelledby={getProgLessonLabelledby(lesson, isActive, completedItems, labelledby)} />;
    default:
      return <CheckCircle aria-hidden aria-labelledby={getProgLessonLabelledby(lesson, isActive, completedItems, labelledby)} />;
  }
}

function determineClass(isOpen, assignmentEnabled) {
  if (!assignmentEnabled) {
    return styles.tooltipProgram;
  }
  if (isOpen) {
    return styles.programTileOpen;
  }
  return styles.programTile;
}

export default function Program(props) {
  const {
    assignmentId,
    content,
    assignmentEnabled,
    dueDate,
    onProgramClick,
    onLessonClick,
    isOpen,
    isProgramTimerOpen,
    onProgramTimerClick,
    completedItems,
    setShowCompletionCertificate,
    timerPaused,
    keyId,
    assignmentType,
    closeBaseAssignment,
  } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { params: routeParams } = useRouteMatch();
  const { toLocalDate } = useLocale();
  const user = useUser();
  const { previewMode } = usePreviewMode();
  const { closeDrawer } = useAssignmentsDrawer();
  const { isAssignmentsDrawerView } = useResponsiveMode();
  const isContentReviewer = get(user, 'accounts[0].accountUsers.roleId') === 7;

  const { lessons, minTimeInMinutes, title, status, uid } = content;

  const timeRequirementMet = getCompletedTimeInMinutes(
    { elapsedTime: content.elapsedTimeInSeconds, minTimeInMinutes }) >= minTimeInMinutes;
  const programIsComplete = status === COMPLETED;
  const todoItemCompleted = !completedItems &&
    (content.status === COMPLETED || (timeRequirementMet && content.percentComplete === 100));

  const totalEstimatedMinutes = lessons.reduce((currentTotal, lesson) => {
    return currentTotal + lesson.estimatedMinutes;
  }, 0);

  const showBottomBanner = completedItems ||
    (!isOpen && (content.status === IN_PROGRESS || content.status === COMPLETED));
  const completedLessons = get(content, 'lessons', [])
    .filter(({ status: lessonStatus }) => lessonStatus === COMPLETED)
    .length;

  const progressText = content.status === IN_PROGRESS ? t('assignmentList.in_progress') : t('assignmentList.completed');
  // eslint-disable-next-line max-len
  const completedLessonsText = `${completedLessons} ${t('platform.of')} ${content.lessons.length} ${t('assignmentList.lessons_completed')}`;
  const isActive = routeParams.assignmentId === assignmentId && routeParams.programId === content.uid;

  const filePath = content.file || ProgramThumbnail;

  const isAllLessonsCompleted = Boolean(completedLessons === content.lessons.length);

  return (
    <Paper
      className={`${assignmentEnabled ? styles.item : styles.itemDisabled}`}
      sx={{
        bgcolor:
          // eslint-disable-next-line max-len, no-nested-ternary
          `${isActive ? 'background.assignmentItem' : 'background.secondary'}`,
      }}
      key={assignmentId}
    >
      {assignmentType === 'shared' && !todoItemCompleted && !completedItems && assignmentEnabled && (
        <Button onClick={(e) => closeBaseAssignment(e, assignmentId)} className={styles.closedSharedAssignmentButton}>
          <img src={CloseSharedIcon} alt="" className={styles.closedSharedAssignmentImage} />
        </Button>
      )}
      {(todoItemCompleted || completedItems) && assignmentEnabled && (
        <img src={PinkTab} className={styles.pinkTab} alt="" />
      )}
      <div
        className={determineClass(isOpen, assignmentEnabled)}
        onClick={assignmentEnabled ? (e) => onProgramClick(e) : undefined}
        onKeyDown={
          (e) => {
            if (e.code === 'Enter') {
              e.preventDefault();
              onProgramClick(e);
            }
          }
        }
        role="button"
        tabIndex="0"
      >
        {!assignmentEnabled && (
        <span className={styles.tooltiptextProgram}>
          <FontAwesomeIcon
            icon={faBan}
            size="2x"
            style={{
              marginRight: '0.4rem',
              transform: 'rotate(90deg)',
              color: '#818099',
            }}
          />
          {`${t('assignmentList.enforce_sequence_message')}`}
        </span>
        )}
        <div
          className={styles.programHeader}
          data-cy={`programAssignment-${keyId}`}
        >
          <div>
            <img src={filePath} className={styles.image} alt="" />
          </div>
          <div style={{ color: palette.primary.dark }}>
            <div className={`${styles.programTitle} ${!assignmentEnabled ? styles.disabled : ''}`}>
              <h3 data-cy={`program-title ${keyId}`}>{title}</h3>
            </div>
            {!todoItemCompleted && !completedItems && (
              <div className={`${styles.dueDate} ${!assignmentEnabled ? styles.disabled : ''}`}>
                {!!minTimeInMinutes && (
                  <p data-cy="lessons-count">
                    {`${t('assignmentList.required_time')} ${minTimeInMinutes} ${t('timer.minutes_abbrev')}`}
                  </p>
                )}
                {!minTimeInMinutes && totalEstimatedMinutes !== 0 && !user.scorm && (
                  <p data-cy="lessons-count">
                    {`${t('assignmentList.estimated_time')} ${totalEstimatedMinutes} ${t('timer.minutes_abbrev')}`}
                  </p>
                )}
                {!minTimeInMinutes && totalEstimatedMinutes === 0 && (
                  <p data-cy="lessons-count">{`${lessons.length} ${t('assignmentList.lessons')}`}</p>
                )}
                {!user.scorm && !previewMode && !isContentReviewer && !(assignmentType === 'shared') && !user.isWorkdayCCL && (
                  <p data-cy="assignment-due_date">{`${t('assignmentList.due_date')} ${toLocalDate(dueDate)}`}</p>
                )}
              </div>
            )}
            {(todoItemCompleted || completedItems) && content.hasCertificate && (
              <div
                id="completion_click_1"
                className={styles.completedItemCert}
                onClick={() => { setShowCompletionCertificate(true); if (isAssignmentsDrawerView) { closeDrawer(); } }}
                role="button"
                tabIndex="0"
              >
                <div id="completion_click_2" className={styles.completedItemCertIcon}>
                  <CertificateIcon id="completion_click_3" />
                </div>
                <div
                  id="completion_click_4"
                  className={styles.completedItemCertText}
                  data-cy="completion_certificate"
                  role="button"
                  tabIndex="0"
                >
                  {t('lessonCards.view_certificate')}
                </div>
                <div id="completion_click_5" className={styles.rightArrowDiv}>
                  <img
                    id="completion_click_6"
                    src={rightArrow}
                    className={styles.rightArrow}
                    alt=""
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        {content.status !== OPEN && (!completedItems || !isActive) && (
          <div><hr className={styles.horizontalLine} style={{ backgroundColor: palette.primary.greyPurple }} /></div>
        )}
        {todoItemCompleted && !isContentReviewer && !previewMode && (
          <div>
            <div className={styles.completedItemRow}>
              <Paper
                className={styles.completedItemLabel}
                sx={{ bgcolor: 'background.darkPink', color: 'primary.dark' }}
                data-cy="program-completed"
              >
                {t('assignmentList.completed')}
              </Paper>
            </div>
          </div>
        )}
        {(isContentReviewer || previewMode) && programIsComplete && (
          <Box
            className={`${styles.progressBar} ${isActive ? styles.progressBarCompletedActive : ''}`}
            sx={{ bgcolor: isActive ? 'background.secondary' : '', color: 'primary.dark' }}
          >
            <p data-cy={`${progressText}`}>{`${progressText} ${toLocalDate(content.completionDate) || ''}`}</p>
          </Box>
        )}
        {!todoItemCompleted && showBottomBanner && (
          <Box
            className={`${styles.progressBar} ${completedItems && isActive ? styles.progressBarCompletedActive : ''} ${!assignmentEnabled ? styles.disabled : ''}`}
            sx={{ bgcolor: completedItems && isActive ? 'background.secondary' : '', color: 'primary.dark' }}
          >
            <p data-cy={`${progressText}`}>{`${progressText} ${toLocalDate(content.completionDate) || ''}`}</p>
            <div className={styles.completedLessonsContainer}>
              {!completedItems && !isAllLessonsCompleted && (
                <p data-cy="no_of_lessons_completed">{completedLessonsText}</p>
              )}
              {
                content.percentComplete === 100 && !!minTimeInMinutes && !timeRequirementMet
                  ? (
                    <div className={styles.timeNotMet}><ClockIcon style={{ fontSize: '1.2rem' }} />
                      <p data-cy="time_requirement">{`${t('timer.time_requirement')} ${t('timer.not_met')}`}</p>
                    </div>
                  )
                  : null
              }
            </div>
          </Box>
        )}
        {(!todoItemCompleted || user.scorm || isContentReviewer || previewMode) && assignmentEnabled && (
          <div className={`${styles.programLessons} ${isOpen ? '' : styles.hide}`}>
            {(completedItems || user.scorm || isContentReviewer || previewMode) && isActive && (
              <div className={styles.paddingCompletedActive} />
            )}
            <ul className={styles.lessonsList}>
              {lessons.map((lesson, index) => (
                <li key={`${uid}-${lesson.uid}`} style={{ display: 'unset' }}>
                  {lesson.enabled && (
                    <a
                      href="#"
                      className={styles.programLesson}
                      onClick={
                      (e) => onLessonClick(e, lesson, { assignmentId, programId: content.uid, lessonId: lesson.uid })
                    }
                      data-cy={`programAssignment-${keyId}-lesson-${index}`}
                      aria-current="page"
                      style={{ pointerEvents: isActive && routeParams.lessonId === lesson.uid ? 'none' : '' }}
                    >
                      {/* eslint-disable-next-line max-len */}
                      <dl className={`${styles.lessonContainer} ${routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId ? styles.activeLesson : ''} ${styles.lessonTitle}`}>
                        {/* eslint-disable-next-line max-len */}
                        <dt>
                          {getProgramLessonStatusIcon(t, lesson, routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId, completedItems, `lesson-${keyId}-${index}-${lesson.uid}`)}
                        </dt>
                        <dd
                          className={styles.programLessonTitle}
                          style={{ color:
                      // eslint-disable-next-line max-len
                      `${routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId ? '' : palette.primary.dark}`,
                          }}
                          data-cy={`lesson-title-${index}`}
                          id={getProgLessonLabelledby(lesson, routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId, completedItems, `lesson-${keyId}-${index}-${lesson.uid}`)}
                        >
                          {lesson.title}
                        </dd>
                      </dl>
                    </a>
                  )}
                  {!lesson.enabled && (
                    <span className={styles.tooltip}>
                      <span className={styles.tooltiptext}>
                        <FontAwesomeIcon
                          icon={faBan}
                          size="2x"
                          style={{
                            marginRight: '0.4rem',
                            transform: 'rotate(90deg)',
                            color: '#818099',
                          }}
                        />
                        {`${t('assignmentList.enforce_sequence_message')}`}
                      </span>
                      <dl className={`${styles.lessonContainerDisabled} ${routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId ? styles.activeLesson : ''} ${styles.lessonTitle}`}>
                        <dt>
                          {getProgramLessonStatusIcon(t, lesson, routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId, completedItems, `lesson-${keyId}-${index}-${lesson.uid}`)}
                        </dt>
                        <dd
                          className={styles.programLessonTitleDisabled}
                          style={{ color:
                        `${routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId ? '' : palette.primary.dark}`,
                          }}
                          data-cy={`lesson-title-${index}`}
                          id={getProgLessonLabelledby(lesson, routeParams.lessonId === lesson.uid && routeParams.assignmentId === assignmentId, completedItems, `lesson-${keyId}-${index}-${lesson.uid}`)}
                        >
                          {lesson.title}
                        </dd>
                      </dl>
                    </span>
                  )}
                </li>
              ))}
            </ul>
            {!!minTimeInMinutes && isOpen && assignmentEnabled && (
              <ProgramTimer
                isOpen={isProgramTimerOpen}
                onClick={onProgramTimerClick}
                minTimeInMinutes={minTimeInMinutes}
                elapsedTime={content.elapsedTimeInSeconds}
                programIsComplete={programIsComplete}
                timeRequirementMet={timeRequirementMet}
                timerPaused={timerPaused}
              />
            )}
          </div>
        )}
      </div>
    </Paper>
  );
}
