/* eslint-disable max-len */
import React from 'react';
import { Paper, useTheme } from '@mui/material';
import { Trans, useTranslation } from 'react-i18next';

import CheckCircle from '../../../icons/CheckCircle';
import ChevronDownIcon from '../../../images/chevron-down-solid.svg';
import ChevronUpIcon from '../../../images/chevron-up-solid.svg';

import styles from './ProgramTimer.module.css';
import getCompletedTimeInMinutes from './getCompletedTimeInMinutes';
import ClockIcon from '../../../icons/Clock';
import PauseCircleIcon from '../../../icons/PauseCircle';

export default function ProgramTimer(props) {
  const { palette } = useTheme();
  const {
    isOpen,
    onClick,
    elapsedTime,
    timeRequirementMet,
    programIsComplete,
    minTimeInMinutes,
    timerPaused,
    contentArea = false,
  } = props;
  const { t } = useTranslation();

  const completedMins = getCompletedTimeInMinutes({ elapsedTime, minTimeInMinutes });
  const barPercentage = Math.min(100, Math.round(100 * (elapsedTime / (minTimeInMinutes * 60))));
  const minsRemaining = completedMins >= minTimeInMinutes ? 0 : minTimeInMinutes - completedMins;

  if (timeRequirementMet && programIsComplete) {
    return (
      <div className={styles.timeRequirementMet}>
        <div className={styles.titleContainer}>
          <CheckCircle className={styles.timeRequirementMetIcon} />
          <p className={styles.timeRequirementText} data-cy="timer_requirement_met">
            {/* eslint-disable-next-line max-len */}
            {`${t('timer.time_requirement')} ${t('timer.met')} (${minTimeInMinutes} ${t('timer.of')} ${minTimeInMinutes} ${t('timer.minutes_abbrev')})`}
          </p>
        </div>
      </div>
    );
  }

  // Timer Paused Component
  const TimerPaused = () => (
    <div
      className={styles.pausedSectionMinimized}
      style={{ background: palette.background.paused, color: palette.primary.dark }}
    >
      <PauseCircleIcon />
      <span data-cy="paused"><strong>{t('timer.paused')}</strong></span>
    </div>
  );

  // Minutes Remaining Text
  const minsRemainingText = (isAbbrev) => {
    const abbreviatedTextKey = minsRemaining === 1 ? 'timer.min_remaining' : 'timer.mins_remaining';
    const nonAbbreviatedTextKey = minsRemaining === 1 ? 'timer.minute_remaining' : 'timer.minutes_remaining';
    return (
      `${minsRemaining} ${t(isAbbrev ? abbreviatedTextKey : nonAbbreviatedTextKey)}`
    );
  };

  // Minutes Remaining Message
  const minsRemainingMessageEnd = (isFullMessage) => {
    const minutesPart = `${minTimeInMinutes}`;
    const endMessagePart = isFullMessage ? t('timer.in_addition_spend_extended') : t('timer.minutes_on_course');
    return ` ${minutesPart} ${endMessagePart}`;
  };

  // Timer Summary Component
  const TimerSummary = ({ isTimerPaused, isContentArea = false }) => (
    <>
      {timeRequirementMet ?
        (
          <div className={styles.timerLeftSection} />
        ) :
        (
          <div className={styles.timerLeftSection}>
            <span>
              <p className={styles.minsRemaining}>
                {isContentArea ? `(${minsRemainingText(false)})` : minsRemainingText(true)}
              </p>
            </span>
          </div>
        )}
      {isTimerPaused && (<TimerPaused />)}
    </>
  );

  if (!isOpen) {
    return (
      <Paper
        className={styles.container}
        onClick={onClick}
        sx={{
          bgcolor: contentArea ? 'background.assignmentList' : 'background.secondary',
          color: contentArea ? 'primary.contrastText' : 'primary.dark',
        }}
        tabIndex="0"
        role="button"
        onKeyDown={
          (e) => {
            if (e.code === 'Enter' || e.code === 'Space') {
              e.preventDefault();
              onClick();
            }
          }
        }
        aria-controls="time-requirement-info"
        aria-expanded={isOpen}
      >
        <div id="time-requirement-info">
          <div className={styles.closedHeader}>
            <div className={styles.titleContainer}>
              {
                timeRequirementMet
                  ? <CheckCircle className={styles.clockIcon} />
                  : <ClockIcon isSolidIcon={contentArea} />
              }
              <p className={styles.timeRequirementHeader} data-cy="timer_met">{`${t('timer.time_requirement')} ${timeRequirementMet ? t('timer.met') : t('timer.not_met')}`}</p>
              {contentArea && <TimerSummary isContentArea={contentArea} isTimerPaused={timerPaused} />}
            </div>
            <ChevronDownIcon />
          </div>
          {!contentArea && (
          <div className={styles.timerSummary}>
            <TimerSummary isTimerPaused={timerPaused} />
          </div>
          )}
        </div>
      </Paper>
    );
  }

  return (
    <Paper
      className={styles.container}
      onClick={onClick}
      sx={{
        bgcolor: contentArea ? 'background.assignmentList' : 'background.secondary',
        color: contentArea ? 'primary.contrastText' : 'primary.dark',
      }}
      data-cy="time-requirement"
      tabIndex="0"
      role="button"
      onKeyDown={
        (e) => {
          if (e.code === 'Enter' || e.code === 'Space') {
            e.preventDefault();
            onClick();
          }
        }
      }
      aria-controls="time-requirement-info"
      aria-expanded={isOpen}
    >
      <div id="time-requirement-info">
        <div className={styles.header}>
          <div className={styles.titleContainer}>
            <ClockIcon isSolidIcon={contentArea} />
            <p className={styles.timeRequirementHeader} data-cy="time_requirement"><strong>{t('timer.time_requirement')}</strong></p>
          </div>
          <ChevronUpIcon />
        </div>
        <div className={styles.minimumDescription}>
          <span data-cy="spend_time_content">
            <Trans i18nKey="timer.in_addition_spend" />
            {minsRemainingMessageEnd(contentArea)}
            {!contentArea && (
              <div>
                <br />
                <Trans i18nKey="timer.not_time_limit" />
              </div>
            )}
          </span>
        </div>
        <div className={styles.minuteTextContainer}>
          <p className={styles.completedText} data-cy="completed_time"><strong>{`Completed: ${completedMins} min`}</strong></p>
          <p className={styles.requiredText} data-cy="required_time">{`Required: ${minTimeInMinutes} min`}</p>
        </div>
        <Paper className={styles.progressBarContainer} sx={{ bgcolor: 'background.default' }}>
          {(!timerPaused || timeRequirementMet) && (
            <div
              className={styles.progressBar}
              style={{ width: `${barPercentage}%`, background: palette.background.progressBar, border: palette.border.lightGreenShade }}
              data-cy="progress"
            />
          )}
          {timerPaused && !timeRequirementMet && (
            <div
              className={styles.progressBar}
              style={{ width: '100%', background: palette.background.paused, color: palette.primary.dark }}
            >
              <div className={styles.pausedSection}>
                <PauseCircleIcon />
                <span data-cy="timer_paused"><strong>{t('timer.paused')}</strong></span>
              </div>
            </div>
          )}
        </Paper>
      </div>
    </Paper>
  );
}
