#import "./AssignmentFragment.graphql"

query AssignmentQuery($completed: Boolean!, $scormProgramId: ID, $assignmentProgramId: ID, $assignmentLessonId: ID, $scormEnforceSequence: Boolean) {
  getAssignmentList(completed: $completed, scormProgramId: $scormProgramId, assignmentProgramId: $assignmentProgramId, assignmentLessonId: $assignmentLessonId, scormEnforceSequence: $scormEnforceSequence) {
    ...AssignmentFragment
  }
}
