.item {
  margin-bottom: 1rem;
  width: 100%;
  border-radius: 8px;
  position: relative;
}

.itemDisabled {
  margin-bottom: 1rem;
  width: 100%;
  border-radius: 8px;
  position: relative;
  background-color: rgba(78%,78.8%,86.7%,50%);
}

.pinkTab {
  width: 1.4rem;
  position: absolute;
  top: 0;
  right: 0;
}

.lessonTile {
  width: 100%;
  display: flex;
}

.disabled {
  color: rgba(34, 36, 66, 70%);
}

.microlesson {
  padding: 0.75rem;
  cursor: pointer;
  text-decoration: none;
  display: flex;
  position: relative;
}

.microlesson a, a:visited, a:hover, a:active {
  color: inherit;
}

.microlesson h3 {
  margin: 0;
  font-weight: 600;
}

.microlessonTitle {
  word-break: break-word;
  padding-right: 1rem;;
}

.microlessonTitle > h3 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.2rem;
  word-break: break-word;
}

.microlessonTitle > h3 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.2rem;
  word-break: break-word;
}

.microlessonDescription {
  margin-top: 0.5rem;
}

.microlessonDescription p {
  margin: 0;
  font-size: .85rem;
  line-height: 1.1rem;
}

.image {
  border-radius: 3px;
  margin-right: 1rem;
  max-height: 6rem;
  max-width: 6rem;
}

.activeLessonContainer {
  display: flex;
  gap: .6rem;
  border-radius: 8px;
  width: 100%;
  transition: all 0.5s ease-in-out;
  padding: 0.2rem .6rem;
}

.activeLessonContainer p {
  margin: 0;
  font-weight: 600;
  font-size: .9rem;
}

.progressBar {
  position: absolute;
  bottom: 0;
  left: 0;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  width: 100%;
  padding: 0.5rem 1rem;
  border-top: 1px solid;
}

.progressBar p {
  margin: 0;
  font-weight: 600;
  font-size: .9rem;
}

.extraPadding {
  padding-bottom: 3rem;
}

.completedItemLabel {
  text-align: center;
  font-size: 0.9rem;
  font-weight: bold;
  padding: 0 0.5rem 0 0.5rem;
  line-height: 1.6rem;
}

.break {
  flex-basis: 100%;
  width: 100%;
}

.completedItemName {
  display: inline-flex;
  font-size: 1rem;
  line-height: 1.2rem;
  font-weight: bold;
  padding-bottom: .5rem;
}

.closedSharedAssignmentButton {
  position: absolute;
  top: -1rem;
  right: -1rem;
  cursor: pointer;
  z-index: 10;
}

.closedSharedAssignmentImage {
  width: 1.4rem;
}

.tooltip {
  padding-top: 0.75rem;
  padding-bottom: 0.5rem;
}

.tooltip .tooltiptext {
  visibility: hidden;
  background-color: #fff;
  color: #40405D;
  text-align: left;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #bcbcd1;
  line-height: 16px;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  width: 80%;
  display: flex;
  align-items: center;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
  visibility: visible;
}