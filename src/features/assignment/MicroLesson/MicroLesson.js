import React from 'react';
import { get } from 'lodash';
import { Paper, Button, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useRouteMatch } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBan } from '@fortawesome/free-solid-svg-icons';
import { useLocale } from '../../../hooks/useLocale';
import PlayCircle from '../../../icons/PlayCircle';
import { COMPLETED, IN_PROGRESS } from '../../../hooks/useAssignment';
import { useUser } from '../../../hooks/useUser';
import { usePreviewMode } from '../../../hooks/usePreviewMode';
import MicrolessonThumbnail from '../../../images/microlesson_thumbnail.png';
import PinkTab from '../../../images/pink-tab.png';
import CloseSharedIcon from '../../../images/x-in-circle.png';

import styles from './MicroLesson.module.css';

export default function MicroLesson(props) {
  const { assignmentId, content, onClick, dueDate, completedItems, keyId, assignmentType, closeBaseAssignment, assignmentEnabled } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { params: routeParams } = useRouteMatch();
  const { toLocalDate } = useLocale();
  const user = useUser();
  const { previewMode } = usePreviewMode();
  const isContentReviewer = get(user, 'accounts[0].accountUsers.roleId') === 7;

  const showBottomBanner = content.status === IN_PROGRESS || content.status === COMPLETED;
  const isActive =
    routeParams.assignmentId === assignmentId && routeParams.lessonId === content.uid && !routeParams.programId;
  const todoItemCompleted = !completedItems && content.status === COMPLETED;

  const filePath = content.file || MicrolessonThumbnail;

  return (
    <Paper
      className={`${assignmentEnabled ? styles.item : styles.itemDisabled}`}
      sx={{
        bgcolor:
          // eslint-disable-next-line max-len, no-nested-ternary
          `${isActive ? 'background.assignmentItem' : 'background.secondary'}`,
      }}
      key={assignmentId}
    >
      {assignmentType === 'shared' && !todoItemCompleted && !completedItems && assignmentEnabled && (
        <Button onClick={(e) => closeBaseAssignment(e, assignmentId)} className={styles.closedSharedAssignmentButton}>
          <img src={CloseSharedIcon} alt="" className={styles.closedSharedAssignmentImage} />
        </Button>
      )}
      {(todoItemCompleted || completedItems) && assignmentEnabled && (
        <img src={PinkTab} className={styles.pinkTab} alt="" />
      )}
      <span className={!assignmentEnabled ? styles.tooltip : ''}>
        {!assignmentEnabled && (
        <span className={styles.tooltiptext}>
          <FontAwesomeIcon
            icon={faBan}
            size="2x"
            style={{
              marginRight: '0.4rem',
              transform: 'rotate(90deg)',
              color: '#818099',
            }}
          />
          {`${t('assignmentList.enforce_sequence_message')}`}
        </span>
        )}

        <a
          href="#"
          onClick={assignmentEnabled ? (e) => { onClick(e, { assignmentId, lessonId: content.uid }); } : undefined}
          style={{ color: palette.primary.main, pointerEvents: isActive ? 'none' : '' }}
          className={`${styles.microlesson} ${showBottomBanner ? styles.extraPadding : ''} ${!assignmentEnabled ? styles.tooltip : ''}`}
          data-cy={`microLessonAssignment-${keyId}`}
        >
          <div className={styles.lessonTile}>
            <div>
              <img src={filePath} className={styles.image} alt="" />
            </div>
            <div style={{ color: palette.primary.dark }}>
              <div className={`${styles.microlessonTitle} ${!assignmentEnabled ? styles.disabled : ''}`}>
                <h3 data-cy="microlesson-title">{content.title}</h3>
              </div>
              {!todoItemCompleted && !completedItems && (
              <div className={`${styles.microlessonDescription} ${!assignmentEnabled ? styles.disabled : ''}`}>
                {content.estimatedMinutes !== 0 && !user.scorm && (
                <p data-cy="assignment-microlesson">
                  {`${t('assignmentList.estimated_time')} ${content.estimatedMinutes} ${t('timer.minutes_abbrev')}`}
                </p>
                )}
                {content.estimatedMinutes === 0 && (
                  <p data-cy="assignment-microlesson">
                    {t('assignmentList.microlesson')}
                  </p>
                )}
                {!user.scorm && !previewMode && !isContentReviewer && !(assignmentType === 'shared') && !user.isWorkdayCCL && (
                  <p data-cy="assignment-due_date">{`${t('assignmentList.due_date')} ${toLocalDate(dueDate)}`}</p>
                )}
              </div>
              )}
            </div>
          </div>
          {showBottomBanner && (
          <div
            className={styles.progressBar}
            style={{
              borderColor: palette.card.borderColor,
              padding: isActive ? '0.5rem .4rem' : '',
              color: palette.primary.dark,
            }}
          >
            {todoItemCompleted && (
            <Paper
              className={styles.completedItemLabel}
              sx={{ bgcolor: 'background.darkPink', color: 'primary.dark' }}
              data-cy="assignment-complete"
            >
              {t('assignmentList.completed')}
            </Paper>
            )}
            {!todoItemCompleted && isActive && assignmentEnabled && (
            <div
              className={styles.activeLessonContainer}
              style={{ background: palette.primary.darkLightPurple, color: palette.primary.contrastText }}
            >
              <div>
                <PlayCircle aria-hidden />
              </div>
              <div>
                <p data-cy="assignment-status">{content.status === IN_PROGRESS ?
                  // eslint-disable-next-line max-len
                  t('assignmentList.in_progress') : `${t('assignmentList.completed')} ${toLocalDate(content.completionDate)}`}
                </p>
              </div>
            </div>
            )}
            {!todoItemCompleted && !isActive && (
            <p data-cy="assignment-status">{content.status === IN_PROGRESS ?
              // eslint-disable-next-line max-len
              t('assignmentList.in_progress') : `${t('assignmentList.completed')} ${toLocalDate(content.completionDate)}`}
            </p>
            )}
          </div>
          )}
        </a>
      </span>
    </Paper>
  );
}
