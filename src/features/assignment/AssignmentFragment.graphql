fragment AssignmentFragment on AssignmentItem {
  id
  type
  assignmentType
  dueDate
  campaignId
  position
  status
  createdAt
  updatedAt
  campaign {
    id
    name
    sequenced
  }
  baseAssignmentId
  isLastViewedCard
  lastViewedLessonId
  content {
    ... on ProgramItem {
      uid: id
      title
      status
      enrollmentId
      completionDate
      completedMessage
      hasCertificate
      downloadInstructions
      sourceLifecycle
      scormEnforceSequence
      minTimeInMinutes
      elapsedTimeInSeconds
      percentComplete
      file
      lessons {
        uid: id
        title
        status
        enrollmentId
        assignmentId
        sequence
        groupAssignment {
          id
          groupId
          campaign {
            id
            sequenced
            name
          }
        }
        sourceLifecycle
        estimatedMinutes
        language
      }
    }
    ... on LessonItem {
      uid: id
      title
      status
      enrollmentId
      completionDate
      sourceLifecycle
      percentComplete
      minCardTimeInSeconds
      estimatedMinutes
      file
      language
    }
  }
}
