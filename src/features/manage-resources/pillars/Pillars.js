import React from 'react';
import { InputAdornment, TextField, useTheme } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import IconButton from '@mui/material/IconButton';
import InfoIcon from '../../../images/info-icon.png';
import { useQuery } from '@apollo/client';
import { get } from 'lodash';
import { styled } from '@mui/material/styles';
import styles from './Pillars.module.css';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import GetPillarsQuery from './GetPillarsQuery.graphql';
import { colors } from '../../../theme/colors';

const CustomTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '25px',
    height: 36,
    '& fieldset': {
      borderColor: 'lightgrey',
    },
    '&:hover fieldset': {
      borderColor: 'grey',
    },
  },
});

export default function Pillars(props) {
  const { isDesktop, isLargeDesktop, isLaptop, isTablet } = useResponsiveMode();
  const { palette } = useTheme();
  const { pillarId, setPillarId, searchQuery, setSearchQuery, debouncedSearch, msg } = props;
  const { lightCylindricalBlue, brightPink, mainPurple, lightBlueGrey } = colors;

  const pillarColors = {
    Respect: lightCylindricalBlue,
    Inclusion: brightPink,
    Ethics: mainPurple,
  };

  const { data: getPillars } = useQuery(GetPillarsQuery, { variables: { limit: 3, offset: 0 } });
  const pillars = get(getPillars, 'getPillars.data', []);

  function handlePillarSelect(value) {
    setPillarId(parseInt(value));
    setSearchQuery('');
  }

  const handleSearchChange = (event) => {
    let searchText = event.target.value;
    debouncedSearch(searchText);
    setSearchQuery(searchText);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  const screenOuter = isLargeDesktop ? `${styles.outerLargeScreen}` :
    isDesktop ? `${styles.outerDesktopScreen}` : isLaptop ? `${styles.outerSmallScreen}` : '';
  const outerPillarsContainer = isTablet ? styles.outerPillarsSmallContainer : styles.outerPillarsContainer
  
  return (
    <div className={`${outerPillarsContainer} ${screenOuter}`}>
      <div className={styles.container}>
        <div className={styles.pillarsSelection}>
          <div
            onClick={() => handlePillarSelect(null)}
            className={styles.pillarsText}
            style={{ borderBottom: !pillarId && `solid 2px ${palette.primary.main}`, fontWeight: (!pillarId) && 600 }}
          >All
          </div>
        </div>
        <ul className={styles.ulContainer}>
          {pillars.map(({ id, name }) => (
            <li key={`${name}`}>
              <span className={styles.divide} style={{ border: `1px solid ${lightBlueGrey}` }} />
              <div
                className={styles.pillarsSelection}
                onClick={() => handlePillarSelect(id)}
              >
                <div
                  className={styles.pillarsText}
                  style={{
                    fontWeight: (id == pillarId) && 600,
                    borderBottom: (id == pillarId) && `solid 2px ${palette.primary.main}`,
                    color: pillarColors[name]
                  }}
                >{name}
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
      <div className={styles.searchBox}>
        <CustomTextField
          variant="outlined"
          placeholder="Search..."
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{ width: isLargeDesktop ? 500 : isDesktop ? 400 : isLaptop ? 350 : 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <IconButton
                  onClick={handleClearSearch}
                  aria-label="clear search"
                  edge="end"
                  size="small"
                >
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
        {searchQuery && searchQuery.length < 3 && msg && (
          <div className={styles.searchMsg}>
          <img
            src={InfoIcon}
            alt="infoIcon"
            className={styles.info}
          />
          <span className={styles.msg}>Please enter 3 or more characters to complete your search.</span>
          </div>
        )}
      </div>
    </div>
  );
}
