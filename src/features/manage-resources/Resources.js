/* eslint-disable max-len */
import React from 'react';
import { Box, Typography, Icon, Divider, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { colors } from '../../theme/colors';
import styles from './ResourceContainer.module.css';
import { downloadResourceFile } from '../../services/api/completionCert';

const ResourceItem = ({ id, resourceType, socialCapitalPillars, type, manageLibraryTitle, description, file, link }) => {
  const { lightCylindricalBlue, brightPink, mainPurple, ghostWhite, darkPurple } = colors;
  const pillarColors = {
    Respect: lightCylindricalBlue,
    Inclusion: brightPink,
    Ethics: mainPurple,
  };

  const handleResourceFileORLink = async (rType, url) => {
    if (rType === 'link') {
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      await downloadResourceFile(url);
    }
  };

  return (
    <>
      <Box
        onClick={() => handleResourceFileORLink(type, ((file && file.path) || link))}
        key={`resource-${resourceType}-${id}`}
        sx={{
          cursor: 'pointer',
          color: darkPurple,
          padding: '1rem',
          '&:hover': {
            backgroundColor: ghostWhite,
          },
        }}
      >
        <Typography component="span" color="#818099" sx={{ fontSize: '0.875rem', fontWeight: 600 }} mb={0.5}>
          {resourceType.toUpperCase()}
          {socialCapitalPillars.map(({ name }, index) => (
            <span key={`pillar ${name}`}>
              {index < socialCapitalPillars.length && <span className={styles.separator}>|</span>}
              <span style={{ color: pillarColors[name] }}>{name.toUpperCase()}</span>
            </span>
          ))}
        </Typography>
        <Box display="flex" alignItems="center" mb={1}>
          <Typography
            sx={{ fontWeight: 600, fontSize: '1.125rem', '&:hover': { fontWeight: 'bold' } }}
          >{manageLibraryTitle}
            <Icon className={type === 'file' ? `${styles.downloadIcon}` : `${styles.linkIcon}`} />
          </Typography>
        </Box>
        <Typography variant="body2">
          {description}
        </Typography>
      </Box>
      <Divider />
    </>
  );
};

const ResourceList = (props) => {
  const { palette } = useTheme();
  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: 0,
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.scrollbar.thumbColor,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.scrollbar.background,
      },
    },
  });

  const scrollbarStyleClass = useStyles();
  const { resourceAssets, listContainerRef, handleScroll } = props;

  return (
    <div
      onScroll={handleScroll}
      ref={listContainerRef}
      className={`${styles.infiniteScroll} ${scrollbarStyleClass.scrollBar}`}
    >
      <Box>
        {resourceAssets && resourceAssets.map((resource) => (
          <ResourceItem key={`resourceAssets -${resource.id}`} {...resource} />
        ))}
      </Box>
    </div>
  );
};

export default ResourceList;
