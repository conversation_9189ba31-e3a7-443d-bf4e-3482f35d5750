.resourceContainer {
  border: 0;
  border-radius: 0;
  padding-bottom: 2rem;
  display: grid;
  padding: .5rem;
  height: 89vh;
}

.container {
  border: 0;
  display: grid;
  grid-template-columns: 3fr 1fr;
}

.resourceHeader {
  display: grid;
  padding-left: 1.375rem;
  span.items {
     font-size: 0.875rem;
     margin-left: 0.5rem;
    }
}

.resourceTypesBox {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0.3rem 0.3rem 0.5rem 0rem;
  gap: 0.5rem;
}

.resourceText {
  font-weight: 600;
  font-size: 0.9rem;
  height: 1.688rem;
}

.clearIcon {
  font-size: 1rem !important;
  margin: 0.063rem 0.5rem 0 -0.563rem !important;
  color: inherit !important;
}

a.link {
  color: inherit;
  text-decoration: underline !important;
  font-weight: normal !important;
  font-size: .8rem;
  margin: 0.2rem;
}

.downloadIcon {
  background-image: url('../../images/download_icon.png');
  background-repeat: no-repeat;
  height: 0.975rem;
  margin-left: 0.5rem;
  margin-top: -0.1rem;
  cursor: pointer;
}

.downloadIcon:hover {
  background-image: url('../../images/hover_download_icon.png');
}

.linkIcon {
  background-image: url('../../images/link_icon.png');
  background-repeat: no-repeat;
  height: 0.975rem;
  margin-left: 0.5rem;
  margin-bottom: -0.1em;
  cursor: pointer;
}

.linkIcon:hover {
  background-image: url('../../images/hover_link_icon.png');
}

.infiniteScroll {
  padding-left: 0.625rem;
  overflow-y: auto;
  scrollbar-width: none;
  &::-webkit-scrollbar-track {
     border-radius: 10px;
  }
 }

.separator {
  margin: 0 4px;
}



