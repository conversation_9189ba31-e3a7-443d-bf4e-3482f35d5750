import React, { useEffect, useRef, useState } from 'react';
import { Box, Chip, Link, Paper, Typography, useTheme } from '@mui/material';
import { useQuery } from '@apollo/client';
import { get } from 'lodash';
import debounce from 'lodash.debounce';
import Pillars from './pillars/Pillars';
import styles from './ResourceContainer.module.css';
import ResourceTypes from './resourceTypes/ResourceTypes';
import GetPillarsQuery from './pillars/GetPillarsQuery.graphql';
import GetResourceAssetsQuery from './GetResourceAssetsQuery.graphql';
import ResourceList from './Resources';
import ClearIcon from '@mui/icons-material/Clear';
import NoResults from './NoResult';

function ResourceContainer() {
  const { palette } = useTheme();
  const [pillarId, setPillarId] = useState(null);
  const [msg, showMsg] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [resourceTypes, setResourceTypes] = useState([]);
  const [clearResourceType, setClearResourceType] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const listContainerRef = useRef(null);
  let searchText = searchQuery.length >= 3 ? searchQuery : '';
  let resourceType = resourceTypes.join(', ');

  const { data: getPillars } = useQuery(GetPillarsQuery, { variables: { limit: 3, offset: 0 } });
  const pillars = get(getPillars, 'getPillars.data', []);

  const { loading, data: getResourceAssets, fetchMore, refetch } = useQuery(GetResourceAssetsQuery,{
    variables: { offset: 0, limit: 15, pillarId, searchText, resourceType },
    notifyOnNetworkStatusChange: true,
  });
  const mrlList = get(getResourceAssets , 'getResourceAssets.data', []);
  const total = get(getResourceAssets, 'getResourceAssets.total', 0);

  const loadResources = () => {
    if (!hasMore || loading) return;
    fetchMore({ variables: { offset: page * 15, limit: 15, pillarId, searchText, resourceTypes } });
    if(total === mrlList.length){
      setHasMore(false);
    } else {
      setPage(page + 1);
    }
  };

  async function loadSearch(searchQuery){
    showMsg(false);
    if(searchQuery.length >= 3){
      setPillarId(null);
      setResourceTypes([]);
      await refetch();
    } else if(searchQuery.length !== 0) {
      showMsg(true);
    }
  }

  const [debouncedSearch] = useState(() => debounce(loadSearch, 500), [searchQuery]);
  useEffect(async() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  async function reloadResources(){
    setHasMore(true);
    setPage(1);
    await refetch();
  }

  useEffect(() => {
    setResourceTypes([]);
    reloadResources();
  }, [pillarId]);

  useEffect(() => {
    if(searchQuery.length === 0 || (searchQuery.length === 2 && searchText === '')){
      reloadResources();
    }
  }, [searchQuery]);

  useEffect(() => {
      reloadResources();
  }, [resourceTypes]);

  const handleScroll = () => {
    if (listContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = listContainerRef.current;
      if (scrollTop + clientHeight > (scrollHeight - 1)) {
        loadResources();
      }
    }
  };

  const handleClearResourceType = (value) => {
    setClearResourceType(value)
  };

  const handleClearAll = () => {
    setClearResourceType(null);
    setResourceTypes([]);
  };

  const resourceHeader = searchQuery.length >= 3 ? `Results for “${searchQuery}”` : `${!pillarId ? 'All' : `${pillars[pillarId-1].name}`} Resources`

  return (
    <>
      <div style={{ background: palette.background.default, marginTop: '-4rem', padding: '0.65rem 1rem' }}>
        <Pillars
          pillarId={pillarId}
          setPillarId={setPillarId}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          debouncedSearch={debouncedSearch}
          msg={msg}
        />
      </div>
      <div style={{ background: palette.background.default }} className={styles.resourceHeader} >
          <Typography 
          data-cy='resource-header'
          sx={{ fontSize: '1.5rem',
            marginTop: '0.5em',
            color: palette.primary.darkBlackOpacity87 
          }}
          >{resourceHeader}
          <span className={styles.items} data-cy='count'>{`(${total} ${total === 1 ? 'item' : 'items'})`}</span>
          </Typography>
          {resourceTypes.length > 0 && 
          <Box className={styles.resourceTypesBox}>
          {resourceTypes.map(item => 
          <Chip
            className={styles.resourceText}
            style={{ background: palette.markerBox.lightGrey }}
            key={`${item}`}
            label={item}
            data-cy={`${item}`}
            onDelete={() => handleClearResourceType(item)}
            deleteIcon={<ClearIcon className={styles.clearIcon} />}
          />)}
          <Link
              href='#'
              className={styles.link}
              onClick={handleClearAll}
              data-cy='clear'
            >
              Clear all
          </Link>
        </Box>}
        <div style={{ borderBottom: `${palette.border.lightGrayishShade}` }} />
      </div>
      <div className={styles.container}>
        <Paper className={styles.resourceContainer} sx={{ backgroundColor: 'background.default' }}>
          {total > 0 && (
          <ResourceList 
            resourceAssets={mrlList}
            handleScroll={handleScroll} 
            listContainerRef={listContainerRef} 
          />)}
          {(searchQuery.length >= 3 && total === 0) && (<NoResults searchText={searchText} />)}
        </Paper>
        <ResourceTypes
          pillarId={pillarId}
          searchText={searchText}
          resourceType={resourceTypes}
          setResourceTypes={setResourceTypes}
          clearResourceType={clearResourceType}
         />
      </div>
    </>
  );
}
export default ResourceContainer;
