import React, { useEffect, useState } from 'react';
import { get, omit } from 'lodash';
import PropTypes from 'prop-types';
import { Box, Checkbox, FormControlLabel, useTheme } from '@mui/material';
import styles from './ResourceTypes.module.css';
import { useQuery } from '@apollo/client';
import GetResourceTypesQuery from './GetResourceTypesQuery.graphql';
import { colors } from '../../../theme/colors';

const initialCheckboxState = {
  'Course Guides': false,
  'Course Summaries': false,
  'Infographics': false,
  'Transcripts': false,
  'Activity Sheets': false,
  'Tip Sheets': false,
  'Quick Links': false,
  'Templates': false,
  'Research Reports': false,
  'Emtrain Culture Skills Model': false
}

function ResourceTypes({ pillarId, searchText, resourceType, setResourceTypes, clearResourceType }) {
  const { data: getResourceTypeFilters } = useQuery(GetResourceTypesQuery,
    { variables: { pillarId: parseInt(pillarId), searchText: searchText } });
  const resourceTypes = get(getResourceTypeFilters, 'getResourceTypeFilters.data', []);
  const [checkboxFilter, setCheckboxFilter] = useState(initialCheckboxState);
  const { palette } = useTheme();
  const { cadetGrey } = colors;

  useEffect(() => {
    if(resourceType.length === 0) {
      setCheckboxFilter(initialCheckboxState);
    }
  },[resourceType])

  useEffect(() => {
    if(clearResourceType) {
      const updatedCheckboxFilter = {
        ...omit(checkboxFilter, '__typename'),
        [clearResourceType]: false,
      };
      const filteredResourceTypes = resourceType.filter(item => item !== clearResourceType);
      setResourceTypes(filteredResourceTypes)
      setCheckboxFilter(updatedCheckboxFilter);
    }
  },[clearResourceType])

  function handleCheckboxChange({ key, value }) {
    const updatedCheckboxFilter = {
      ...omit(checkboxFilter, '__typename'),
      [key]: value,
    };
    let selectedResourceTypes = [];
    if (resourceType.includes(key)) {
      selectedResourceTypes = resourceType.filter(item => item !== key);
    } else {
      selectedResourceTypes = [...resourceType, key]
    }
    setResourceTypes(selectedResourceTypes)
    setCheckboxFilter(updatedCheckboxFilter);
  }

  return (
    <Box
      className={styles.boxStyle}
      sx={{
        bgcolor: 'background.default',
        border: palette.border.lightGrayishShade,
        borderRadius: '4px',
      }}
    >
      <div className={styles.title} style={{ color: palette.primary.text }}>Resource Type</div>
      {resourceTypes.map((item, i) => {
        const key = item.resourceType;
        return (
          <Box key={`ResourceType-${i}`}>
            <FormControlLabel
              className={styles.checkboxItemText}
              sx={{
                '& .MuiFormControlLabel-label': { fontSize: '0.9rem' },
                '& .Mui-disabled': { color: `${cadetGrey} !important`} 
              }}
              data-cy={`${key}`}
              control={
              <Checkbox
              sx={{ 
                padding: '0.2rem',
                '& .MuiSvgIcon-root': { width: '1rem' },
                ':hover': { backgroundColor: 'unset'}
               }}
              checked={get(checkboxFilter, key, false)}
              onClick={() => { handleCheckboxChange({ key, value: !checkboxFilter[key] }); }}
              name={item.resourceType} />
              }
              label={`${item.resourceType} (${item.resourceTypeCount})`}
              disabled={item.resourceTypeCount === 0}
            />
          </Box>
        );
      })}
    </Box>
  );
}

ResourceTypes.propTypes = {
  pillarId: PropTypes.number,
  resourceType: PropTypes.array.isRequired,
  setResourceTypes: PropTypes.func.isRequired,
  searchText: PropTypes.string.isRequired,
  clearResourceType: PropTypes.string,
};

export default ResourceTypes;
