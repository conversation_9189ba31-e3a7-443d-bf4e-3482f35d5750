query GetResourceAssets($limit: Int, $offset: Int, $searchText: String, $pillarId: Int, $resourceType: String) {
  getResourceAssets(
    limit: $limit
    offset: $offset
    searchText: $searchText
    resourceType: $resourceType
    pillarId: $pillarId
  ) {
    total
    limit
    data {
      id
      title
      description
      type
      link
      resourceType
      publishToManageLibrary
      manageLibraryTitle
      audience
      createdAt
      file {
        path
        __typename
      }
      socialCapitalPillars {
        name
        description
        id
        __typename
      }
      __typename
    }
    __typename
  }
}
