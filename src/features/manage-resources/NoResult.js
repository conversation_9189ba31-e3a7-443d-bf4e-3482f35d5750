import React from 'react';
import { Typography, <PERSON>, Link } from '@mui/material';
import { colors } from '../../theme/colors';

const NoResults = ({ searchText }) => {
  const { castingSeaBlue, darkByzantinePurple } = colors;
  return (
    <Box sx={{ padding: '.5rem 1rem' }}>
      <Typography variant="h6" sx={{ fontSize: '1.188rem', fontWeight: 600, color: darkByzantinePurple }}>
        Sorry, we couldn’t find any matches for “{searchText}”
      </Typography>
      <Typography variant="body1" mt={1}>
        Please make sure that the words are spelled correctly. You might also try different or more general search terms.
      </Typography>
      <Typography variant="body1" mt={1}>
        If you’re still unable to find what you’re looking for,&nbsp;
        <Link target="_blank" href="https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820">
          <span style={{ color: castingSeaBlue, textDecoration: 'none' }}>contact your Client Success Team</span>
        </Link>
        . We’ll be happy to help!
      </Typography>
    </Box>
  );
};

export default NoResults;
