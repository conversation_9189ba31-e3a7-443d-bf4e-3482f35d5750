import React from 'react';
import { get } from 'lodash';
import { Paper, Button, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useAssignmentsDrawer, useMenuDrawer } from '../../hooks/useDrawer';
import { useUser } from '../../hooks/useUser';
import { usePreviewMode } from '../../hooks/usePreviewMode';
import EmtrainWhiteLogo from '../../images/emtrain-white-logo.svg';
import HamburgerIcon from '../../icons/Hamburger';

import styles from './MobileHeader.module.css';

export default function MobileHeader({ completedItems, onSetCompletedItems, manageView = false }) {
  const { openDrawer } = useAssignmentsDrawer();
  const { openDrawer: openMenuDrawer } = useMenuDrawer();
  const { t } = useTranslation();
  const { palette } = useTheme();
  const user = useUser();
  const isContentReviewer = get(user, 'accounts[0].accountUsers.roleId') === 7;
  const { previewMode } = usePreviewMode();

  const customLogoSrc = get(user, 'accounts[0].customLogoSrc');
  const customDarkLogoSrc = get(user, 'accounts[0].customDarkLogoSrc');
  const customLogoUrl = customLogoSrc && !customDarkLogoSrc ? customLogoSrc : customDarkLogoSrc || null;

  return (
    <Paper
      className={styles.container}
      sx={{ bgcolor: 'background.assignmentList', borderRadius: 0 }}
    >
      <div className={styles.headerWrapper}>
        <div className={styles.filterContainer}>
          {!manageView && !user.scorm && !previewMode && !isContentReviewer && (
            <>
              <Button
                onClick={() => { openDrawer(); onSetCompletedItems(false); }}
                className={completedItems ? styles.buttonDefault : styles.buttonSelected}
                disableRipple
                sx={{ color: 'primary.contrastText',
                  whiteSpace: 'nowrap',
                  marginRight: '0.25rem',
                  '&:focus': {
                    outline: '2px solid white !important',
                    border: `2px solid ${palette.card.backgroundColor} !important`,
                  },
                }}
                data-cy="todo"
              >
                {t('assignmentList.todo')}
              </Button>
              <Button
                onClick={() => { openDrawer(); onSetCompletedItems(true); }}
                className={completedItems ? styles.buttonSelected : styles.buttonDefault}
                disableRipple
                sx={{ color: 'primary.contrastText',
                  whiteSpace: 'nowrap',
                  '&:focus': {
                    outline: '2px solid white !important',
                    border: `2px solid ${palette.card.backgroundColor} !important`,
                  },
                }}
                data-cy="completed"
              >
                {t('assignmentList.completed')}
              </Button>
            </>
          )}
          {manageView && (
            <div>
              <Button
                onClick={() => { openDrawer(); }}
                sx={{ color: 'primary.contrastText', whiteSpace: 'nowrap', marginRight: '0.25rem' }}
                data-cy="menu"
              >
                <HamburgerIcon {...{ color: palette.primary.white }} />
              </Button>
            </div>
          )}
          {previewMode && !user.scorm && !isContentReviewer && (
            <Button
              onClick={() => { openDrawer(); }}
              className={styles.buttonSelected}
              disableRipple
              sx={{ color: 'primary.contrastText',
                whiteSpace: 'nowrap',
                marginRight: '0.25rem',
                '&:focus': {
                  outline: '2px solid white !important',
                  border: `2px solid ${palette.card.backgroundColor} !important`,
                },
              }}
              data-cy="todo"
            >
              {t('preview.preview')}
            </Button>
          )}
          {!previewMode && !user.scorm && isContentReviewer && (
            <Button
              onClick={() => { openDrawer(); }}
              className={styles.buttonSelected}
              sx={{ color: 'primary.contrastText',
                whiteSpace: 'nowrap',
                marginRight: '0.25rem',
                '&:focus': {
                  outline: '2px solid white !important',
                  border: `2px solid ${palette.card.backgroundColor} !important`,
                },
              }}
              disableRipple
              data-cy="todo"
            >
              {t('assignmentList.review')}
            </Button>
          )}
          {user.scorm && !previewMode && !isContentReviewer && (
            <Button
              onClick={() => { openDrawer(); }}
              className={styles.buttonSelected}
              disableRipple
              sx={{ color: 'primary.contrastText',
                whiteSpace: 'nowrap',
                marginRight: '0.25rem',
                '&:focus': {
                  outline: '2px solid white !important',
                  border: `2px solid ${palette.card.backgroundColor} !important`,
                },
              }}
              data-cy="todo"
            >
              {t('assignmentList.assignment_capitalized')}
            </Button>
          )}
        </div>
        <div className={styles.buttonContainer}>
          <Button
            onClick={() => { return previewMode ? null : openMenuDrawer(); }}
            disableRipple
            sx={{ '&:focus': {
              outline: '2px solid white !important',
              border: `2px solid ${palette.card.backgroundColor} !important`,
            } }}
          >
            {!customLogoUrl && (
              <EmtrainWhiteLogo
                tabIndex="0"
                role="img"
                aria-label={t('platform.emtrain_logo')}
                className={styles.emtrainLogo}
              />
            )}
            {!previewMode && (
              <HamburgerIcon {...{ color: palette.primary.white }} />
            )}
          </Button>
        </div>
      </div>
    </Paper>
  );
}
