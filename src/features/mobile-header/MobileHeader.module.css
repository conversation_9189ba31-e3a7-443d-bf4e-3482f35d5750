.container {
  display: block;
  width: 100%;
  height: 4.2rem;
}

.headerWrapper {
  display: flex;
  justify-content: space-between;
}

.filterContainer {
  display: flex;
  padding: 0.75rem 0 0 0.85rem;
  align-items: center;
}

.filterContainer button {
  text-transform: none;
  font-size: 1rem;
  font-weight: normal;
}

.buttonContainer {
  display: flex;
  padding: 0.67rem 0.25rem 0 0;
}

.buttonDefault {
  border-radius: 30px !important;
  border: 1px solid transparent !important;
  background: transparent !important;
  text-transform: none !important;
  font-weight: normal !important;
  color: #B2B4CA !important;
  padding: 0 .6rem !important;
  border-radius: 20px !important;
  border: 1px solid #65667C !important;
}

.buttonDefault:hover {
  text-transform: none !important;
  font-weight: normal !important;
  color: #FFFFFF !important;
  border-radius: 20px !important;
  border: 1px solid rgba(252, 252, 252, 0.75) !important;
}

.buttonSelected, .buttonSelected:hover {
  text-transform: none !important;
  font-weight: bold !important;
  color: #FFFFFF !important;
  border-radius: 20px !important;
  border: 1px solid transparent !important;
  background: rgba(255, 255, 255, .3) !important;
  padding: 0 .6rem !important;
}

.logoContainer {
  margin-top: 0.43rem;
  margin-right: -1rem;
}

.emtrainLogo {
  height: 1.75rem;
  margin-bottom: 0.313rem;
  width: 5.938rem;
}

.customLogo {
  object-fit: contain;
  height: 2rem;
  margin: 0.2rem 0.29rem 0.2rem 0.35rem;
}


