import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useTheme } from '@mui/material';
import styles from './PrivacyText.module.css';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';

export default function PrivacyText({ currentAccount }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isSmallMobile } = useResponsiveMode();
  const privacyPolicyLink = (currentAccount && currentAccount.gdprPolicyUrl) || 'https://emtrain.com/emtrain-privacy-policy/';
  return (
    <div
      className={isSmallMobile ? styles.privacyTextMobile : styles.privacyText}
      style={{ color: palette.text.primary }}
    >
      <Trans
        i18nKey={t('privacy.privacy_note')}
        components={{
          link1: <Link
            className="privacyTextLink"
            target="_blank"
            to={{ pathname: 'https://emtrain.com/terms-of-service/' }}
            sx={{ color: palette.primary.darkLightPurple }}
          />,
          link2: <Link
            className="privacyTextLink"
            target="_blank"
            to={{ pathname: privacyPolicyLink }}
            sx={{ color: palette.primary.darkLightPurple }}
          />,
        }}
      />
    </div>
  );
}
