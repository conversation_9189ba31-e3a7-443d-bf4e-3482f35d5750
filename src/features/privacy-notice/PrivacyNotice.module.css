.container {
  position:absolute;
  bottom: 1.6rem;
  right:0;
  margin: .8rem;
  padding: 1rem;
  width: 18rem;
  border-radius: .4rem;
  z-index: 9999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.headingText {
  font-weight: bold;
  font-size: 1rem;
}

.bodyText {
  font-weight: normal;
  font-size: .9rem;
  flex-basis: 100%;
  margin-top: .4rem;
  overflow-y: auto;
}

.bodyText a, a:visited, a:hover, a:active {
  color: inherit !important;
}

.break {
  height: 1rem;
}

.closeButtonOuter {
  padding-top: 2rem;
}

.closeButtonContainer {
  width: auto !important;
}

.closeButton {
  padding: 0.2rem 1.5rem !important;
  text-transform: none !important;
  font-size: .8rem !important;
  font-weight: bold !important;
}

.closeButtonImage {
  height: .9rem;
  width: .9rem;
}

.closeModal {
  top: -0.7em;
  right: -2rem;
  float: right;
}

.mobileCloseModal {
  top: -0.5rem;
  right: -0.5rem;
  float: right;
}

.mobileContainer {
  border-radius: 0.2rem;
  padding: 0.5rem;
}