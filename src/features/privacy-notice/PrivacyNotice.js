import React from 'react';
import { Dialog, useTheme, IconButton } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useMutation } from '@apollo/client';
import { useTranslation, Trans } from 'react-i18next';
import { Close as CloseIcon } from '@mui/icons-material';
import SendConsentRemovedEventMutation from './SendConsentRemovedEventMutation.graphql';

import styles from './PrivacyNotice.module.css';
import Footer from '../footer/Footer';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';

export default function PrivacyNotice({ privacyViewed, setPrivacyViewed }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isSmallMobile, isMobile, isTablet } = useResponsiveMode();
  const [sendConsentCardRemovedEvent] = useMutation(SendConsentRemovedEventMutation, { ignoreResults: true });

  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: '0.4rem',
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.background.darkGrey,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.background.default,
      },
    },
  });
  const scrollbarStyleClass = useStyles();
  const contentContainer = isSmallMobile ? `${styles.bodyText} ${styles.mobileContainer} ${scrollbarStyleClass.scrollBar}` : `${styles.bodyText}`;

  const updatePolicyViewed = () => {
    sendConsentCardRemovedEvent();
    setPrivacyViewed(true);
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);
  };

  if (privacyViewed) {
    return null;
  }

  return (
    <Dialog
      open
      onClose={() => updatePolicyViewed()}
      PaperProps={{
        style: {
          top: !(isSmallMobile || isMobile) && '2rem',
          margin: 0,
          borderRadius: '1rem',
          padding: isSmallMobile ? '1rem' : '2rem 3rem 2rem 2rem',
          maxWidth: (isSmallMobile || isMobile || isTablet) ? '90vw' : '50vw',
          position: 'absolute',
          backgroundColor: palette.background.lightShadeBlue,
          overflowY: 'hidden',
          color: palette.primary.dark,
          filter: palette.border.dropShadow,
        },
      }}
    >
      <div className={styles.headingText}>
        {t('privacy.privacy_header')}
        <IconButton
          aria-label={t('platform.close')}
          onClick={() => updatePolicyViewed()}
          className={isSmallMobile ? styles.mobileCloseModal : styles.closeModal}
          sx={{
            color: palette.primary.main,
            padding: 0,
            '& .MuiTouchRipple-root': { position: 'initial' },
            '&:focus': {
              outline: `solid ${palette.primary.white} 2px`,
              border: `solid ${palette.card.backgroundColor} 3px`,
              borderRadius: '2px',
              width: '23px',
              height: '23px',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </div>
      <div className={contentContainer} style={{ border: isSmallMobile ? palette.border.darkGrey : '' }}>
        <Trans i18nKey="privacy.privacy_note_1" data-cy="privacy-policy-message_1" />
        <div className={styles.break} />
        <Trans i18nKey="privacy.privacy_note_2" data-cy="privacy-policy-message_2" />
        <div className={styles.break} />
        <Trans i18nKey="privacy.privacy_note_3" data-cy="privacy-policy-message_3" />
        <div className={styles.break} />
        <Trans i18nKey="privacy.privacy_note_4" data-cy="privacy-policy-message_4" />
        <div className={styles.break} />
      </div>
      <Footer privacyFooter />
    </Dialog>
  );
}
