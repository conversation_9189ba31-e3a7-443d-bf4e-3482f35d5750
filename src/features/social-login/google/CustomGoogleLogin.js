import React from 'react';
import axios from 'axios';
import { useApolloClient, useMutation } from '@apollo/client';
import { useGoogleLogin } from '@react-oauth/google';
import { useHistory } from 'react-router-dom';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import SendSignupEventMutation from '../../authentication/SendSignupEventMutation.graphql';
import SendSigninEventMutation from '../../authentication/SendSigninEventMutation.graphql';
import { useAuth } from '../../../hooks/useAuth';
import GoogleLogo from '../../../images/google.svg';
import styles from './googleLogin.module.css';
import { GOOGLE_BASE_URL } from '../../../config/constants';

const CustomGoogleLogin = ({setStatus}) => {
  const { googleLogin } = useAuth();
  const client = useApolloClient();
  const history = useHistory();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const [sendSignupEvent] = useMutation(SendSignupEventMutation, { ignoreResults: true });
  const [sendSigninEvent] = useMutation(SendSigninEventMutation, { ignoreResults: true });
  const onSuccess = async (response) => {
    setStatus({ loading: true, error: false });
    try {
      const userData = await googleLogin(response.name, response.email);
      if (userData.isNewUser) {
        // signup
        sendSignupEvent();
      } else {
        // signin
        sendSigninEvent();
      }
      await client.clearStore();
      history.push(appRoutes.HOME);
    } catch (e) {
      let aOuthErrorMessage = e.response.data;
      if (e.response && e.response.data && e.response.data.error === 'Login OAuth error') {
        if (e.response.data.userOAuth === 'google') {
          aOuthErrorMessage = t('signUp.googleSignInError');
        } else if (e.response.data.userOAuth === 'linkedin') {
          aOuthErrorMessage = t('signUp.linkedinSignInError');
        }
      } else if (e.response && e.response.data === 'Signup error') {
        aOuthErrorMessage = t('signUp.linkedInSelfSignupError');
      }
      setStatus({ loading: false, error: true, errorMessage: aOuthErrorMessage, errorCode: 409 });
    }
  };
  const googleOAuthLogin = useGoogleLogin({
    onSuccess: (codeResponse) => {
      axios
        .get(`${GOOGLE_BASE_URL}?access_token=${codeResponse.access_token}`, {
          headers: {
            Authorization: `Bearer ${codeResponse.access_token}`,
            Accept: 'application/json',
          },
        })
        .then((res) => {
          onSuccess(res.data);
        })
        .catch((err) => {
          setStatus({ loading: false, error: true, errorMessage: err.message, errorCode: 409 });
        });
    },
    onError: (err) => {
      setStatus({ loading: false, error: true, errorMessage: err.message, errorCode: 409 });
    },
  });
  const buttonDesign = {
    border: palette.border.darkGrey,
    color: palette.primary.darkLightPurple,
  };

  return (
    <div>
      <button type="button" className={styles.googleButton} onClick={() => googleOAuthLogin()} style={buttonDesign}>
        <GoogleLogo className={styles.googleLogo} />
        {t('signUp.loginWithGoogle')}
      </button>
    </div>
  );
};

export default CustomGoogleLogin;
