import React from 'react';
import { useTheme } from '@mui/material';
import styles from './customOrDesign.module.css';

export function CustomOrDesign({ type }) {
  const { palette } = useTheme();
  const divDesign = {};
  return (
    <div className={type === 'signup' ? styles.signUpOr : styles.signInOr} style={divDesign}>
      <div style={{ border: palette.border.main }} />
      <span style={{ padding: '0rem 0.5rem' }}> OR </span>
      <div style={{ border: palette.border.main }} />
    </div>
  );
}
