import React from 'react';
import PropTypes from 'prop-types';
import { GoogleOAuthProvider } from '@react-oauth/google';
import CustomGoogleLogin from './google/CustomGoogleLogin';
import CustomLinkedInLogin from './linkedIn/CustomLinkedInLogin';
import CustomLinkedInCallback from './linkedIn/CustomLinkedInCallback';
import CustomMicrosoftLogin from './microsoft/CustomMicrosoftLogin';
import { CustomOrDesign } from './CustomOrDesign';

function SocialLoginContainer({ currentAccount, type, setStatus }) {
  if (!currentAccount) {
    return null;
  }
  const { googleClientId, linkedinClientId, selfSignup, microsoftClientId, microsoftTenantId } = currentAccount;
  return (
    <>
      {type === 'login' && (linkedinClientId || googleClientId) && (<CustomOrDesign type={type} />)}
      {googleClientId && (
        <GoogleOAuthProvider
          clientId={googleClientId}
        >
          <CustomGoogleLogin type={type} setStatus={setStatus} />
        </GoogleOAuthProvider>
      )}
      <CustomLinkedInCallback />
      {selfSignup && linkedinClientId && (
        <CustomLinkedInLogin clientId={linkedinClientId} setStatus={setStatus}/>
      )}
      {microsoftClientId && microsoftTenantId && (
        <CustomMicrosoftLogin clientId={microsoftClientId} tenantId={microsoftTenantId} setStatus={setStatus}/>
      )}
      {type === 'signup' && (linkedinClientId || googleClientId || microsoftClientId) && (<CustomOrDesign type={type} />)}
    </>
  );
}

SocialLoginContainer.propTypes = {
  currentAccount: PropTypes.object.isRequired,
  type: PropTypes.string.isRequired,
};

export default SocialLoginContainer;
