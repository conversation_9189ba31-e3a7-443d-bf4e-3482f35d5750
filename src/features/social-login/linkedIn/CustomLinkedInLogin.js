import React from 'react';
import { useApolloClient, useMutation } from '@apollo/client';
import { useLinkedIn } from 'react-linkedin-login-oauth2';
import { useHistory } from 'react-router-dom';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import SendSignupEventMutation from '../../authentication/SendSignupEventMutation.graphql';
import SendSigninEventMutation from '../../authentication/SendSigninEventMutation.graphql';
import { useAuth } from '../../../hooks/useAuth';
import LinkedinLogo from '../../../images/linkedin.svg';
import styles from './linkedInLogin.module.css';

const redirectUri = `${window.location.origin}/linkedin`;
function CustomLinkedInLogin({ clientId, setStatus }) {
  const { linkedinLogin } = useAuth();
  const client = useApolloClient();
  const history = useHistory();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const [sendSignupEvent] = useMutation(SendSignupEventMutation, { ignoreResults: true });
  const [sendSigninEvent] = useMutation(SendSigninEventMutation, { ignoreResults: true });
  const { linkedInLogin } = useLinkedIn({
    clientId,
    redirectUri,
    onSuccess: async (code) => {
      setStatus({ loading: true, error: false, errorMessage: null });
      try {
        const userData = await linkedinLogin(code, redirectUri);
        if (userData.isNewUser) {
          sendSignupEvent();
        } else {
          sendSigninEvent();
        }
        await client.clearStore();
        history.push(appRoutes.HOME);
      } catch (e) {
        let oAuthErrorMessage = e.response.data;
        if (e.response && e.response.data && e.response.data.error === 'Login OAuth error') {
          if (e.response.data.userOAuth === 'google') {
            oAuthErrorMessage = t('signUp.googleSignInError');
          } else if (e.response.data.userOAuth === 'linkedin') {
            oAuthErrorMessage = t('signUp.linkedinSignInError');
          }
        } else if (e.response && e.response.data === 'Signup error') {
          oAuthErrorMessage = t('signUp.linkedInSelfSignupError');
        }
        setStatus({ loading: false, error: true, errorMessage: oAuthErrorMessage, errorCode: 409 });
      }
    },
    scope: 'openid profile email',
    onError: (err) => {
      setStatus({ loading: false, error: true, errorMessage: err.errorMessage, errorCode: 409 });
    },
  });

  const buttonDesign = {
    border: palette.border.darkGrey,
    color: palette.primary.darkLightPurple,
  };
  return (
    <div className={styles.rowGap}>
      <button type="button" className={styles.linkedInButton} onClick={() => linkedInLogin()} style={buttonDesign}>
        <LinkedinLogo className={styles.LinkedInLogo} />
        {t('signUp.loginWithLinkedIn')}
      </button>
    </div>
  );
}

export default CustomLinkedInLogin;
