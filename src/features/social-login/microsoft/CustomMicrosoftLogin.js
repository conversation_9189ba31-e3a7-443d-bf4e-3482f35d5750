import React from "react";
import { useApolloClient, useMutation } from '@apollo/client';
import { useHistory } from 'react-router-dom';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import SendSignupEventMutation from '../../authentication/SendSignupEventMutation.graphql';
import SendSigninEventMutation from '../../authentication/SendSigninEventMutation.graphql';
import { useAuth } from '../../../hooks/useAuth';
import MicrosoftLogo from '../../../images/microsoft.svg';
import styles from './microsoftLogin.module.css';
import { PublicClientApplication } from '@azure/msal-browser';

const CustomMicrosoftLogin = ({ clientId, tenantId, setStatus }) => {
  const { microsoftLogin } = useAuth();
  const client = useApolloClient();
  const history = useHistory();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const [sendSignupEvent] = useMutation(SendSignupEventMutation, { ignoreResults: true });
  const [sendSigninEvent] = useMutation(SendSigninEventMutation, { ignoreResults: true });
  const config = {
    auth: {
      clientId,
      authority: `https://login.microsoftonline.com/${tenantId}`,
    }
  };
  const myMsal = new PublicClientApplication(config);
  const loginRequest = {
    scopes: ["user.read"],
    prompt: 'select_account',
  }
  const signInClickHandler = () => {
    myMsal
      .loginPopup(loginRequest)
      .then(async (response) => {
        try {
          const userData = await microsoftLogin(response.accessToken);
          if (userData.isNewUser) {
            sendSignupEvent();
          } else {
            sendSigninEvent();
          }
          await client.clearStore();
          history.push(appRoutes.HOME);
        } catch (e) {
          let aOuthErrorMessage = e.response.data;
          if (e.response && e.response.data && e.response.data.error === 'Login OAuth error') {
            if (e.response.data.userOAuth === 'google') {
              aOuthErrorMessage = t('signUp.googleSignInError');
            } else if (e.response.data.userOAuth === 'linkedin') {
              aOuthErrorMessage = t('signUp.linkedinSignInError');
            } else if (e.response.data.userOAuth === 'microsoft') {
              aOuthErrorMessage = t('signUp.microsoftSignInError');
            }
          } else if (e.response && e.response.data === 'Signup error') {
            aOuthErrorMessage = t('signUp.microsoftSelfSignupError');
          }
          setStatus({ loading: false, error: true, errorMessage: aOuthErrorMessage, errorCode: 409 });
        }
      })
    }
  const buttonDesign = {
    border: palette.border.darkGrey,
    color: palette.primary.darkLightPurple,
    };
  return (
    <div className={styles.rowGap}>
      <button type="button" className={styles.microsoftButton} onClick={() => signInClickHandler()} style={buttonDesign}>
        <MicrosoftLogo className={styles.microsoftLogo} />
        {t('signUp.loginWithMicrosoft')}
      </button>
    </div>
  );
};

export default CustomMicrosoftLogin;