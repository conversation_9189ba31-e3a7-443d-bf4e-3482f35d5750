/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* TODO:
  Re-factor the update the user having seen the message
  Remove the above when the video part is implemented
  Add the video part
  Move the strings into the translation infrastructure
  Write tests
*/
import React, { useEffect, useRef } from 'react';
import { useMutation } from '@apollo/client';
import { get } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';
import { styled, Typography, useTheme, Paper } from '@mui/material';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import Video from '../../components/Video/Video';
import { useAssignmentsDrawer } from '../../hooks/useDrawer';
import { useUser } from '../../hooks/useUser';
import UpdateUserMutation from '../user-profile/UpdateUserMutation.graphql';
import styles from './Welcome.module.css';

const StyledTypography = styled(Typography)({
  fontSize: '1em',
  lineHeight: '1.3',
});

export default function Welcome({ itemCount, loginRedirectDone, onSetCompletedItems }) {
  const [updateUser] = useMutation(UpdateUserMutation);
  const user = useUser();
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);
  const { palette } = useTheme();
  const { t } = useTranslation();

  const screenReaderFocusRef = useRef(null);

  const { openDrawer } = useAssignmentsDrawer();
  const displayWelcomeVideo = get(user, 'accounts[0].welcomeVideoEnabled', true);

  // Notify the server the welcome message has been displayed.  The client message is not removed until
  // the user clicks on an assignment or refreshes the page.
  useEffect(() => {
    if (!user.displayedWelcome) {
      updateUser({ variables: { displayedWelcome: true } });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (screenReaderFocusRef && screenReaderFocusRef.current) {
      screenReaderFocusRef.current.focus();
    }
  }, []);

  // Using Trans here instead of useTranslation to enable certain html tags
  const TopWelcomeMessage = () => {
    if (itemCount > 0) {
      return (
        <>
          <h3>
            <Trans i18nKey="welcome.topWelcomeTitle" />
          </h3>
          <StyledTypography gutterBottom>
            <Trans i18nKey="welcome.topAssignments" />
            {displayWelcomeVideo && (
              <span>
                &nbsp;
                <Trans i18nKey="welcome.topAssignmentsVideo" />
              </span>
            )}
          </StyledTypography>
        </>
      );
    }

    return (
      <>
        <h3>
          <Trans i18nKey="welcome.topWelcomeTitle" />
        </h3>
        <StyledTypography gutterBottom>
          <Trans i18nKey="welcome.topNoAssignments">
            xx
            {/* eslint-disable-next-line max-len */}
            <a href="#" style={{ textDecoration: 'none', color: palette.text.primary }} onClick={(e) => { e.preventDefault(); onSetCompletedItems(true); openDrawer(); }}>
              xx
            </a>
            xx
          </Trans>
        </StyledTypography>
        {displayWelcomeVideo && (
          <StyledTypography gutterBottom>
            <Trans i18nKey="welcome.topShortVideo" />
          </StyledTypography>
        )}
      </>
    );
  };

  const GettingStartedMessage = () => {
    return (
      <>
        <StyledTypography gutterBottom>
          <Trans i18nKey="welcome.gettingStarted">
            xx
            {/* eslint-disable-next-line max-len */}
            <a href="https://answers-support.emtrain.com/hc/en-us/articles/12581822787853-Emtrain-AI-Hosted-/" style={{ textDecoration: 'none', color: palette.text.primary }} target="_blank" rel="noreferrer">
              xx
            </a>
            xx
          </Trans>
          <a
            href="https://answers-support.emtrain.com/hc/en-us/articles/12581822787853-Emtrain-AI-Hosted-/"
            style={{ textDecoration: 'none', color: palette.text.primary }}
            target="_blank"
            rel="noreferrer"
          >
            <OpenInNewIcon className={styles.openNew} />
          </a>
        </StyledTypography>
      </>
    );
  };

  const BottomWelcomeMessage = () => {
    if (itemCount > 0) {
      return (
        <>
          <span className={styles.messageHeader}>
            <Trans i18nKey="welcome.bottomHurry" />
          </span>
          <StyledTypography>
            <Trans i18nKey="welcome.bottomNoProblem" />
          </StyledTypography>
        </>
      );
    }

    return null;
  };

  const WelcomeBackMessage = () => {
    if (itemCount === 0 && loginRedirectDone) {
      return (
        <>
          <h3 data-cy="welcome-back-msg">
            <Trans i18nKey="welcome.topWelcomeBackTitle" />
          </h3>
          <StyledTypography data-cy="no-assignments-now-msg">
            <Trans i18nKey="welcome.noAssignmentsNow">
              xx
              {/* eslint-disable-next-line max-len */}
              <a href="#" style={{ textDecoration: 'none', color: palette.text.primary }} onClick={(e) => { e.preventDefault(); onSetCompletedItems(true); openDrawer(); }}>
                xx
              </a>
              xx
            </Trans>
          </StyledTypography>
        </>
      );
    }
    return (
      <StyledTypography>
        <Trans i18nKey="welcome.assignmentsCompleted" data-cy="assignments-completed-msg">
          xx
          {/* eslint-disable-next-line max-len */}
          <a href="#" style={{ textDecoration: 'none' }} onClick={(e) => { e.preventDefault(); onSetCompletedItems(true); openDrawer(); }}>xx</a>
          xx
        </Trans>
      </StyledTypography>
    );
  };

  return (
    <div className={styles.container}>
      <Paper
        className={styles.wrapper}
        tabIndex={0}
        id="welcome-message-focus-element"
        data-cy="welcome-message-focus-element"
        ref={screenReaderFocusRef}
        sx={{
          '&:focus': {
            border: `2px solid ${palette.card.backgroundColor}`,
          },
        }}
      >
        {user.displayedWelcome ? <WelcomeBackMessage /> : (
          <>
            <div id="top-welcome-msg" data-cy="top-welcome-msg">
              <TopWelcomeMessage />
            </div>
            {displayWelcomeVideo && (
              <>
                <div className={styles.videoWrapper} data-cy="video-content">
                  {askExpertEnabled && !user.scorm && (
                    <Video
                      title={t('platform.welcome_video_description')}
                      videoId="1765"
                    />
                  )}
                  {!askExpertEnabled && !user.scorm && (
                    <Video
                      title={t('platform.welcome_video_description')}
                      videoId="1821"
                    />
                  )}
                  {askExpertEnabled && user.scorm && (
                    <Video
                      title={t('platform.welcome_video_description')}
                      videoId="1822"
                    />
                  )}
                  {!askExpertEnabled && user.scorm && (
                    <Video
                      title={t('platform.welcome_video_description')}
                      videoId="1823"
                    />
                  )}
                </div>
                <div id="bottom-welcome-msg" data-cy="bottom-welcome-msg">
                  <BottomWelcomeMessage />
                </div>
              </>
            )}
            {!displayWelcomeVideo && (
              <>
                <div className={styles.gettingStartedMessage} data-cy="getting-started-msg">
                  <GettingStartedMessage />
                </div>
              </>
            )}
          </>
        )}
      </Paper>
    </div>
  );
}
