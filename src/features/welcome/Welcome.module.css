.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.wrapper {
  margin-top: 1.9rem;
  width: 90%;
  max-width: 42rem;
  border: solid 1px #cbccd9;
  border-radius: 5px;
  padding: 1.75rem;
  font-size: 1.2rem;
  z-index: 1;
  background-color: #fcfcfc;
}

.videoWrapper {
  margin: 1rem 0;
}

.container h3 {
  margin: 0;
  font-size: 1.75rem;
}

.container h4 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: bold;
}

.messageHeader {
  font-weight: bold;
}

.gettingStartedMessage {
  margin-top: 1.6rem;
}

.openNew {
  position: relative;
  /* Adjust these values accordingly */
  top: .2rem;
  left: .4rem;
}