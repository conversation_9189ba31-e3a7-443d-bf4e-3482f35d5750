/* eslint-disable max-len */
import * as React from 'react';
import { styled } from '@mui/material/styles';
import { makeStyles } from '@mui/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import { useTheme } from '@mui/material';
import PropTypes from 'prop-types';
import styles from './VersionTooltip.module.css';
import { useContentLibrary } from '../../../../hooks/useContentLibrary';

function VersionDropdown({ versionsCount, allVersions, isPWHUS }) {
  const { palette } = useTheme();
  const [open, setOpen] = React.useState(false);
  const { filters } = useContentLibrary();
  const handleTooltipClose = () => {
    setOpen(false);
  };

  const handleTooltipOpen = () => {
    if (!isPWHUS) {
      setOpen(true);
    }
  };

  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: 5,
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.scrollbar.thumbColor,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.scrollbar.background,
      },
    },
  });
  const scrollbarStyleClass = useStyles();

  const VersionsTooltip = styled(({ className, ...props }) => (
    <Tooltip
      {...props}
      PopperProps={{
        className: `no-propagate ${className ?? ''}`,
      }}
      arrow
      classes={{ popper: className }}
      placement="bottom-start"
    />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: palette.background.ghostWhite,
      color: palette.primary.darkByzantinePurple,
      fontSize: theme.typography.pxToRem(12),
      border: '1px solid #818099',
      borderRadius: 6,
    },
    [`&.${tooltipClasses.popper}[data-popper-placement*="bottom"] .${tooltipClasses.tooltip}`]: {
      marginTop: '5px',
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: palette.background.ghostWhite,
      '&::before': {
        border: '1px solid #818099',
      },
    },
  }));

  if (allVersions.length === 0) {
    return null;
  }

  const versionsData = () => {
    const sortedVersions = allVersions || [];
    return sortedVersions.map((item) => {
      const { id, audience, title: versionName } = item;
      const isAudienceChecked = filters?.audience.includes(audience);
      return (<Typography key={id} sx={{ fontWeight: isAudienceChecked ? 'bold' : 400 }}>{versionName}</Typography>);
    });
  };

  return (
    <ClickAwayListener onClickAway={handleTooltipClose}>
      <div>
        <VersionsTooltip
          onClose={handleTooltipClose}
          open={open}
          disableFocusListener
          disableHoverListener
          disableTouchListener
          title={(
            <div className={`${scrollbarStyleClass.scrollBar} ${styles.dropdownScroll}`}>
              {versionsData()}
            </div>
            )}
        >
          <Typography
            variant="body2"
            className={styles.version}
            sx={{ color: palette.primary.main }}
            onClick={handleTooltipOpen}
          >
            {`${versionsCount} ${versionsCount === 1 ? 'Version' : 'Versions'}`}
            <ArrowDropDownIcon fontSize="small" className={styles.arrowIcon} />
          </Typography>
        </VersionsTooltip>
      </div>
    </ClickAwayListener>
  );
}

VersionDropdown.propTypes = {
  versionsCount: PropTypes.number,
  allVersions: PropTypes.array.isRequired,
  isPWHUS: PropTypes.bool.isRequired,
};

export default VersionDropdown;
