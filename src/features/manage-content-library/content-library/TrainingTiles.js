/* eslint-disable max-len */
import React from 'react';
import { Card, CardMedia, CardContent, Typography, Chip, Box,
  CardActions, Grid, Button, useTheme } from '@mui/material';
import { useHistory } from 'react-router-dom';
import { capitalize } from 'lodash';
import ProgramThumbnail from '../../../images/program_thumbnail.png';
import MicrolessonThumbnail from '../../../images/microlesson_thumbnail.png';
import Global from '../../../images/global.png';
import Canada from '../../../images/canada.png';
import US from '../../../images/US.png';
import India from '../../../images/india.png';
import { colors } from '../../../theme/colors';
import styles from './TrainingTiles.module.css';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import VersionDropdown from './VersionDropdown/VersionsTooltip';
import { pushToContentLibraryDetails } from '../../navigation/Routes/AppRoutes';
import { getListingLessonsORPrograms, sortedVersionsData } from '../../../hooks/useSortContentVersions';

const TrainingCard = (props) => {
  const { lightCylindricalBlue, brightPink, mainPurple } = colors;
  const { palette } = useTheme();
  const history = useHistory();
  const { isDesktop, isLargeDesktop, isXLLargeDesktop, middleScreen } = useResponsiveMode();
  const pillarColors = {
    Respect: lightCylindricalBlue,
    Inclusion: brightPink,
    Ethics: mainPurple,
  };

  const Countries = {
    International: Global,
    'All Countries': Global,
    Canada,
    India,
    'United States of America': US,
  };

  const { filePath, title, type, instructionalType, description, lessons, id, mclContentId, isCustom,
    programs, countryName, pillars, listType, catalogItems, edition, audience, part, stateCode, isCustomTag } = props;

  const listingLessonsORPrograms = listType === 'listing' && getListingLessonsORPrograms(catalogItems);

  const cProgramsLessons = (lessons || programs) || [];
  const catalogLessonsORPrograms = listType === 'catalog' && cProgramsLessons.map((item) => ({
    ...item,
    type,
    edition,
    audience,
    part,
    stateCode,
  }));

  const versions = (listType === 'catalog' ? (catalogLessonsORPrograms.length) : (listingLessonsORPrograms.length)) || 0;
  // eslint-disable-next-line no-nested-ternary
  const contentType = type && (['program', 'course'].includes(type) ? 'Course' :
    type === 'lesson' ? 'Course Lesson' : capitalize(type));
  const lessonsORPrograms = (listType === 'catalog' ? (catalogLessonsORPrograms) : (listingLessonsORPrograms)) || [];
  const catalogThumbnail = instructionalType === 'course' ? ProgramThumbnail : MicrolessonThumbnail;
  const listingThumbnail = ['program', 'course'].includes(type) ? ProgramThumbnail : MicrolessonThumbnail;
  const file = filePath || (listType === 'catalog' ? (lessonsORPrograms[0]?.filePath || catalogThumbnail) : listingThumbnail);
  const isPWHUS = listType === 'listing' && mclContentId === 21;

  const getTileWidth = () => {
    if (middleScreen) return styles.middleWidth;
    if (isXLLargeDesktop && isDesktop) return styles.smallWidth;
    if (isLargeDesktop || isDesktop) return styles.largeWidth;
    return styles.smallWidth;
  };

  const tileWidth = getTileWidth();

  const handleNavigateToLibraryDetails = (mclType, listingID, contentID) => {
    if (mclType === 'listing') {
      history.push(pushToContentLibraryDetails(listingID, contentID));
    }
  };

  const sortedVersions = sortedVersionsData(lessonsORPrograms, isCustomTag);

  return (
    <Grid item>
      <Card
        className={`${styles.cardBox} ${tileWidth}`}
        sx={{ boxShadow: 2, '&:hover': { backgroundColor: palette.background.ghostWhite } }}
        onClick={(e) => {
          if (e.target.closest('.no-propagate')) return;
          handleNavigateToLibraryDetails(listType, mclContentId, sortedVersions[0]?.id);
        }}
      >
        <CardMedia
          component="img"
          image={file}
          alt={title}
          className={styles.cardImage}
        />
        <CardContent
          sx={{ padding: '0.5rem 0.75rem' }}
        >
          <Typography variant="h6" className={styles.title} sx={{ color: palette.primary.darkByzantinePurple }}>
            {title}
          </Typography>
          <Typography variant="body2" className={styles.contentType} sx={{ color: palette.primary.main }}>
            {listType === 'catalog' ? capitalize(instructionalType) : contentType}
          </Typography>
          <Typography
            variant="body2"
            className={styles.contentDescription}
            sx={{ color: palette.primary.darkByzantinePurple, WebkitLineClamp: pillars && pillars.length > 1 ? 2 : 4 }}
          >
            {description}
          </Typography>
        </CardContent>
        {listType && (
        <CardActions className={styles.cardBottom}>
          <Box className={`no-propagate ${styles.bottomSection1}`}>
            {!isPWHUS && (
            <VersionDropdown
              versionsCount={versions}
              allVersions={sortedVersions}
              isPWHUS={isPWHUS}
            />
            )}
            {isCustomTag && (
            <Button
              className={styles.customTag}
              sx={{ backgroundColor: palette.background.lightShadeGrey,
                color: palette.primary.darkLightPurple }}
            >Custom
            </Button>
            )}
          </Box>
          {pillars && (
          <Box className={styles.bottomSection2}>
            {countryName && (<img src={Countries[countryName]} alt="country" width={24} height={24} />)}
            <Box className={styles.pillarsList}>
              {pillars.map(({ name }) => (
                <Chip
                  key={`pillar-${name}`}
                  label={name}
                  className={styles.pillar}
                  sx={{
                    borderColor: pillarColors[name],
                    color: palette.primary.darkByzantinePurple,
                  }}
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>
          )}
        </CardActions>
        )}
        {!listType && (
        <CardActions className={styles.cardBottom}>
          <Box className={styles.bottomSection2}>
            <Typography variant="body2" sx={{ fontSize: 13, fontWeight: 400, color: palette.primary.main }}>
              {`#${mclContentId || id}`}
            </Typography>
            {isCustom && (
            <Button
              className={styles.customTag}
              sx={{ backgroundColor: palette.primary.darkLightPurple,
                color: palette.primary.white,
                '&:hover': {
                  backgroundColor: palette.primary.darkLightPurple,
                } }}
            >Custom
            </Button>
            )}
          </Box>
        </CardActions>
        )}
      </Card>
    </Grid>
  );
};

export default TrainingCard;
