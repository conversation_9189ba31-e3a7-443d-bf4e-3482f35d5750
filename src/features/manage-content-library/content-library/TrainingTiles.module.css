.cardBox {
  height: 100%;
  border-radius: 18.7px;
  display: grid;
  grid-template-rows: 145px;
  background-color: #ffffff;
}

.middleWidth {
  min-width: 260px;
  max-width: 333px;
}

.smallWidth {
  min-width: 260px;
  max-width: 305px;
}

.largeWidth {
  min-width: 260px;
  max-width: 305px;
}
.cardImage {
  height: 145px;
}

.title {
  font-size: 19px;
  font-weight: 700;
  line-height: 22px; 
  text-overflow: ellipsis;
  line-break: anywhere;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.contentType {
  font-size: 13px;
  font-weight: 400;
  line-height: 22px;
  margin-bottom: 8px;
}

.contentDescription {
  font-size: 13px;
  font-weight: 400;
  line-height: 19px;
  margin-bottom: 8px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-break: anywhere;
}

.customTag {
  font-weight: 700;
  font-size: 13px !important;
  width: 53px !important;
  height: 20px !important;
  border-radius: 4px; 
  text-transform: none !important;
}

.cardBottom {
  padding: 0px 12px 16px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: flex-end;
}

.bottomSection1 {
  display: flex;
  align-items: center;
}

.bottomSection2 {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  margin-left: 0px !important;
}

.pillarsList{
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.pillar {
  height: 24px;
  border-radius: 18.82px;
  font-size: 12px;
  font-weight: 600;
  border-width: 1.44px;
  span {
    padding: 0px 10px;
  }
}