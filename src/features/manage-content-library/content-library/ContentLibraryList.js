/* eslint-disable max-len */
import React from 'react';
import { Grid, Box, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import styles from '../ContentLibraryContainer.module.css';
import { colors } from '../../../theme/colors';
import TrainingCard from './TrainingTiles';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import SpinnerGif from '../../../images/spinner.gif';
import { useContentLibrary } from '../../../hooks/useContentLibrary';

const ContentLibrary = (props) => {
  const { darkPurple } = colors;
  const { palette } = useTheme();
  const { tilesLoading } = useContentLibrary();
  const { isDesktop, isLargeDesktop, isXLLargeDesktop, middleScreen } = useResponsiveMode();

  const DEFAULT_SPINNER_WIDTH = 50;
  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: 0,
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.scrollbar.thumbColor,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.scrollbar.background,
      },
    },
  });
  const scrollbarStyleClass = useStyles();
  const { listContainerRef, handleScroll, trainings } = props;

  const getMidGap = () => {
    if (middleScreen) return 4.5;
    return isLargeDesktop ? 3.25 : 2.5;
  };

  const getGridTemplateColumn = () => {
    if (middleScreen) return styles.gridTemplate3;
    if ((isXLLargeDesktop && isDesktop) || (isDesktop && isLargeDesktop)) return styles.gridTemplate4;
    return styles.gridTemplate3;
  };

  const midGap = getMidGap();
  const gridTemplateColumn = getGridTemplateColumn();

  return (
    <div
      onScroll={handleScroll}
      ref={listContainerRef}
      className={`${styles.infiniteScroll} ${scrollbarStyleClass.scrollBar}`}
    >
      <Box
        sx={{
          cursor: 'pointer',
          color: darkPurple,
          padding: '1.25rem',
        }}
      >
        {tilesLoading && (
        <div className={styles.loadSpinner}>
          <img src={SpinnerGif} width={DEFAULT_SPINNER_WIDTH} aria-label="tilesLoading" />
        </div>
        )}
        <Grid container columnGap={midGap} rowGap={2.5} justifyContent="left" className={gridTemplateColumn}>
          {trainings.map((training) => (
            <TrainingCard key={`training_tiles-${training.id}-${training.title}`} {...training} />))}
        </Grid>
      </Box>
    </div>
  );
};

export default ContentLibrary;
