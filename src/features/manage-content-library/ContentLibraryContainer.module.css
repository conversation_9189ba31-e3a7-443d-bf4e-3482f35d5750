.contentLibraryContainer {
  border: 0;
  border-radius: 0;
  padding-bottom: 2rem;
  display: grid;
}

.container {
  border: 0;
  display: grid;
  grid-template-columns: 77.5% 22.5%;
  height: 98% !important;
}

.gridTemplate3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.gridTemplate4 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.header {
  display: grid;
  padding-left: 1.375rem;
  span.items {
     font-size: 0.875rem;
     margin-left: 0.5rem;
    }
}

.mclTypes {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0.3rem 0.3rem 0.5rem 0rem;
  gap: 0.5rem;
}

.clearIcon {
  font-size: 1rem !important;
  margin: 0.063rem 0.5rem 0 -0.563rem !important;
  color: inherit !important;
}

a.link {
  color: inherit;
  text-decoration: underline !important;
  font-weight: normal !important;
  font-size: .8rem;
  margin: 0.2rem;
}

.infiniteScroll {
  height: 100vh;
  overflow-y: auto;
  scrollbar-width: none;
  &::-webkit-scrollbar-track {
     border-radius: 10px;
  }
 }

.separator {
  margin: 0 4px;
}

.loadSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 45%;
  top: 50%;
}

.sortBySelect {
  width: 11.125rem;
  height: 1.875rem;
  text-align: center;
  padding: 0.2rem 0.3rem 0.2rem 0.5rem;
  font-size: 0.875rem;
}

.topSection {
  display: grid;
  align-items: center;
  justify-content: space-between;
  grid-template-columns: 85% 15%;
  padding-right: 1.375rem;
}

.pillSelection {
  display: grid;
  align-items: center;
  justify-content: space-between;
  grid-template-columns: 85% 15%;
}

.sortByText {
  font-size: 0.875rem;
  font-weight: 600;
}

.pillFiltersBox {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0.3rem 0.3rem 0.5rem 0rem;
  gap: 0.5rem;
}

.pillText {
  font-weight: 600;
  font-size: 0.9rem;
  height: 1.688rem;
}

.clearIcon {
  font-size: 1rem !important;
  margin: 0.063rem 0.5rem 0 -0.563rem !important;
  color: inherit !important;
}
