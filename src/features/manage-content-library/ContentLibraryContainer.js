/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { InputAdornment, MenuItem, Paper, Select, styled, Typography, useTheme,
  OutlinedInput, Chip, Box, Link } from '@mui/material';
import { useQuery } from '@apollo/client';
import { get } from 'lodash';
import debounce from 'lodash.debounce';
import ClearIcon from '@mui/icons-material/Clear';
import Pillars from './pillars/Pillars';
import styles from './ContentLibraryContainer.module.css';
import GetPillarsQuery from './pillars/GetPillarsQuery.graphql';
import NoResults from './NoResult';
import ContentLibrary from './content-library/ContentLibraryList';
import { useContentLibrary } from '../../hooks/useContentLibrary';
import SideBarFilters from './sidebar-filters/SideBarFilters';

const CustomMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '0.875rem',
  padding: '5px 10px',
  color: theme.palette.text.primary,
}));

function ContentLibraryContainer() {
  const { palette } = useTheme();
  const { contentTrainingTiles, tiles, clearTrainingTiles, filters,
    contentFilters, tilesLoading } = useContentLibrary();
  const [msg, showMsg] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const listContainerRef = useRef(null);
  const { pillarId, title, contentId, searchType, sortBy, trainingType, location,
    audience, concepts, indicators, isCustomized, selectedFilterPills } = filters;
  const searchText = ((searchType === 'Keyword' && searchQuery.length >= 3) ||
   (searchType === 'Content ID' && searchQuery.length >= 2)) ? searchQuery : '';

  const { data: getPillarsData } = useQuery(GetPillarsQuery, { variables: { limit: 3, offset: 0 } });
  const pillars = get(getPillarsData, 'getPillars.data', []);

  const loadSearch = useCallback(
    async (query, queryType) => {
      showMsg(false);
      const queryLength = query.length;
      if ((queryType === 'Content ID' && queryLength >= 2 && !Number.isNaN(Number(query)))
         || (queryType === 'Keyword' && queryLength >= 3)) {
        contentFilters(queryType, query);
      } else if (query.length !== 0) {
        showMsg(true);
      }
    }, [searchType]);
  const debouncedSearch = useCallback(debounce(loadSearch, 800), [loadSearch]);
  const getTrainingTiles = get(tiles, 'data', []);
  const total = get(tiles, 'total', 0);

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const loadContentTiles = async () => {
    if (!hasMore) return;
    await contentTrainingTiles({ skip: page * 15, limit: 15, ...filters });
    if (total === getTrainingTiles.length) {
      setHasMore(false);
    } else {
      setPage(page + 1);
    }
  };

  const handleScroll = () => {
    if (listContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = listContainerRef.current;
      if (scrollTop + clientHeight > (scrollHeight - 1) && !(total === getTrainingTiles.length)) {
        loadContentTiles();
      }
    }
  };

  const normalizedFilters = [
    ...trainingType.sort(),
    ...location.sort(),
    ...audience.sort(),
    ...concepts.sort(),
    ...indicators.sort(),
  ].join('|');

  useEffect(() => {
    const fetchTrainingTiles = async () => {
      setHasMore(true);
      setPage(1);
      clearTrainingTiles();
      await contentTrainingTiles({ skip: 0, limit: 15, ...filters });
    };
    fetchTrainingTiles();
  }, [title, contentId, pillarId, sortBy, isCustomized, normalizedFilters]);

  const handleSortBy = async (event) => {
    const sortByValue = event.target.value;
    await contentFilters('sortBy', sortByValue);
  };

  const handleClearFilters = (obj) => {
    let value = false;
    if (obj?.type !== 'isCustomized') {
      value = filters[obj?.type].filter((item) => item !== obj.key);
    }
    contentFilters(obj?.type, value, obj);
  };

  const handleClearAllFilters = () => {
    contentFilters('clearAll');
  };

  useEffect(() => {
    if (searchQuery.length === 0 || ((searchType === 'Keyword' && searchQuery.length === 2 && searchText === '') ||
     (searchType === 'Content ID' && searchQuery.length === 1 && searchText === ''))) {
      contentFilters();
    }
  }, [searchQuery, searchText]);

  // eslint-disable-next-line no-nested-ternary
  const libraryHeader = ((searchType === 'Content ID' && searchQuery.length >= 2 && !Number.isNaN(Number(searchQuery))) ||
  (searchType === 'Keyword' && searchQuery.length >= 3))
    ? `Results for “${searchQuery}”`
    : !pillarId ? 'All Trainings' : `${pillars[pillarId - 1]?.name} Trainings`;

  const isNoResults = !tilesLoading && ((searchType === 'Content ID' && searchQuery.length >= 2) ||
  (searchType === 'Keyword' && searchQuery.length >= 3) || (selectedFilterPills.length > 0)) && total === 0;

  return (
    <>
      <div style={{ background: palette.background.default, marginTop: '-4rem', padding: '0.65rem 1rem' }}>
        <Pillars
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          debouncedSearch={debouncedSearch}
          msg={msg}
        />
      </div>
      <div style={{ background: palette.background.default }} className={styles.header}>
        <div className={selectedFilterPills.length === 0 ? styles.topSection : ''}>
          <Typography
            data-cy="library-header"
            sx={{
              fontSize: '1.5rem',
              marginTop: '0.5em',
              color: palette.primary.darkBlackOpacity87,
            }}
          >
            {libraryHeader}
            <span className={styles.items} data-cy="count">{`(${total} ${total === 1 ? 'title' : 'titles'})`}</span>
          </Typography>
          <Box className={styles.pillSelection}>
            {selectedFilterPills.length > 0 && (
            <Box className={styles.pillFiltersBox}>
              {selectedFilterPills.map((item) => (
                <Chip
                  className={styles.pillText}
                  style={{ background: palette.markerBox.lightGrey }}
                  key={`${item.value}`}
                  label={item.value}
                  data-cy={`${item.value}`}
                  onDelete={() => handleClearFilters(item)}
                  deleteIcon={<ClearIcon className={styles.clearIcon} />}
                />
              ))}
              <Link
                href="#"
                className={styles.link}
                onClick={handleClearAllFilters}
                data-cy="clear_all"
              >
                Clear all
              </Link>
            </Box>
            )}
            <Select
              value={sortBy}
              onChange={handleSortBy}
              className={styles.sortBySelect}
              input={(
                <OutlinedInput
                  startAdornment={(
                    <InputAdornment position="start">
                      <Typography
                        variant="body2"
                        className={styles.sortByText}
                        sx={{ color: palette.primary.main }}
                      >
                        Sort By:
                      </Typography>
                    </InputAdornment>
                    )}
                />
                )}
              sx={{
                '& .MuiOutlinedInput-input': {
                  textAlign: 'left',
                },
                '& .MuiInputBase-input.MuiInput-input': {
                  paddingRight: '10px',
                },
                '& .MuiInputBase-input:focus': {
                  backgroundColor: 'transparent',
                },
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    marginTop: '-10px !important',
                    '& .Mui-selected': {
                      fontWeight: 'bold',
                    },
                    '& ul': {
                      background: palette.background.white,
                    },
                  },
                },
              }}
            >
              <CustomMenuItem value="popular">Most Popular</CustomMenuItem>
              <CustomMenuItem value="alphabetical">Alphabetical</CustomMenuItem>
              <CustomMenuItem value="newest">Newest</CustomMenuItem>
            </Select>
          </Box>
        </div>
        <div style={{ borderBottom: palette.border.lightGrayishShade }} />
      </div>
      <div className={styles.container}>
        <Paper
          className={styles.contentLibraryContainer}
          sx={{ backgroundColor: 'background.default', height: '100vh' }}
        >
          {isNoResults && <NoResults />}
          <ContentLibrary
            listContainerRef={listContainerRef}
            handleScroll={handleScroll}
            trainings={getTrainingTiles}
          />
        </Paper>
        <SideBarFilters />
      </div>
    </>
  );
}

export default ContentLibraryContainer;
