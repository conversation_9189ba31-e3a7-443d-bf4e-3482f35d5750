.container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  font-size: .89rem;
  line-height: 1rem;
  padding: 1rem 0 1rem 0;
  margin: 0 auto;
  overflow: hidden;
  min-width: 20rem;
}

.outerPillarsContainer {
  display: grid;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.outerPillarsSmallContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.outerSmallScreen {
  grid-template-columns: 0.875fr 1fr;
  margin: 0rem 3rem 0rem 13.5rem;
}

.outerDesktopScreen {
  grid-template-columns: 1fr 1fr;
  margin: 0rem 6rem 0rem 14rem;
}

.outerLargeScreen {
  grid-template-columns: 1fr 1fr;
  margin: 0rem 6rem 0rem 13.5rem;
}

.break {
  flex-basis: 100%;
  height: 0;
}

.ulContainer {
  display: contents;
}

li {
  display: inherit;
}

.pillarsSelection {
  width: auto;
  font-weight: 500;
  padding: 0rem 0.5rem;
  cursor: pointer;
}

.pillarsText {
  margin: 0.313rem;
  padding-bottom: 0.313rem;
  font-size: 1.125rem;
}

.pillarsSelection:hover {
  font-weight: 600;
}

.divide {
  width: 0.063rem;
  height: 1.375rem;
  opacity: 0.45;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
}

.searchBox {
  display: flex;
  flex-direction: column;
  margin-top: 0.75rem;
  height: 3.25rem;
  width: fit-content;
}

.searchMsg {
  display: flex;
  font-size: 0.813rem;
  max-width: 23rem;
  /* justify-content: center; */
  align-items: center;
  padding: 0.3rem 0rem;
  padding-left: 1rem;
  img.info {
    width: 0.813rem;
  }

  span.msg {
    margin-left: .2rem;
  }
}

.searchTypeSelect {
  width: 100px;
  margin-right: -13px;
  text-align: center;
  padding: 0.2rem 0.3rem 0.2rem 0.5rem;
  font-size: 0.875rem;
  border-bottom-right-radius: 20px;
  border-top-right-radius: 20px;
}

.menuItem {
  font-size: 0.875rem;
  padding: 5px 10px;
}
