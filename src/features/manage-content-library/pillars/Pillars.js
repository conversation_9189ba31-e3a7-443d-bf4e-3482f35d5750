/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
import React from 'react';
import { InputAdornment, TextField, useTheme, Select, MenuItem, IconButton } from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { useQuery } from '@apollo/client';
import { get } from 'lodash';
import { styled } from '@mui/material/styles';
import InfoIcon from '../../../images/info-icon.png';
import styles from './Pillars.module.css';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import GetPillarsQuery from './GetPillarsQuery.graphql';
import { colors } from '../../../theme/colors';
import { useContentLibrary } from '../../../hooks/useContentLibrary';

const CustomTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '25px',
    height: 36,
    '& fieldset': {
      borderColor: 'lightgrey',
    },
    '&:hover fieldset': {
      borderColor: 'grey',
    },
  },
});

const SearchTypeSelect = ({ value, onChange }) => {
  const { palette } = useTheme();

  return (
    <Select
      value={value}
      onChange={onChange}
      variant="standard"
      disableUnderline
      sx={{
        background: palette.background.ghostWhite,
        '& .MuiSelect-icon': {
          right: '0.5rem',
        },
        '& .MuiInputBase-input.MuiInput-input': {
          paddingRight: '10px',
        },
        '& .MuiInputBase-input:focus': {
          backgroundColor: palette.background.ghostWhite,
        },
      }}
      MenuProps={{
        PaperProps: {
          sx: {
            marginTop: '5px !important',
            '& .Mui-selected': {
              fontWeight: 'bold',
            },
            '& ul': {
              background: palette.background.white,
            },
          },
        },
      }}
      className={styles.searchTypeSelect}
      IconComponent={ArrowDropDownIcon}
    >
      <MenuItem value="Keyword" className={styles.menuItem}>Keyword</MenuItem>
      <MenuItem value="Content ID" className={styles.menuItem}>Content ID</MenuItem>
    </Select>
  );
};

const PillarsList = ({ pillars, selectedPillarId, onSelect, palette }) => {
  const { lightCylindricalBlue, brightPink, mainPurple, lightBlueGrey } = colors;
  const pillarColors = {
    Respect: lightCylindricalBlue,
    Inclusion: brightPink,
    Ethics: mainPurple,
  };

  return (
    <ul className={styles.ulContainer}>
      <li>
        <div className={styles.pillarsSelection} onClick={() => onSelect(null)}>
          <div
            className={styles.pillarsText}
            style={{
              borderBottom: !selectedPillarId && `solid 2px ${palette.primary.main}`,
              fontWeight: !selectedPillarId && 600,
            }}
          >
            All
          </div>
        </div>
      </li>
      {pillars.map(({ id, name }) => (
        <li key={name}>
          <span className={styles.divide} style={{ border: `1px solid ${lightBlueGrey}` }} />
          <div className={styles.pillarsSelection} onClick={() => onSelect(id)}>
            <div
              className={styles.pillarsText}
              style={{
                fontWeight: Number(id) === selectedPillarId && 600,
                borderBottom: Number(id) === selectedPillarId && `solid 2px ${palette.primary.main}`,
                color: pillarColors[name],
              }}
            >
              {name}
            </div>
          </div>
        </li>
      ))}
    </ul>
  );
};

export default function Pillars({
  searchQuery,
  setSearchQuery,
  debouncedSearch,
  msg,
}) {
  const { isDesktop, isLargeDesktop, isLaptop, isTablet } = useResponsiveMode();
  const { filters: { searchType, pillarId }, contentFilters } = useContentLibrary();
  const { palette } = useTheme();

  const { data: getPillars } = useQuery(GetPillarsQuery, { variables: { limit: 3, offset: 0 } });
  const pillars = get(getPillars, 'getPillars.data', []);

  const handlePillarSelect = (value) => {
    contentFilters('pillar', parseInt(value, 10));
    setSearchQuery('');
  };

  const handleSearchChange = (event) => {
    const searchText = event.target.value;
    debouncedSearch(searchText, searchType);
    setSearchQuery(searchText);
  };

  const handleClearSearch = () => {
    contentFilters('search', searchType);
    setSearchQuery('');
  };

  const handleSearchTypeChange = (event) => {
    const type = event.target.value;
    contentFilters('search', type);
    setSearchQuery('');
  };

  const outerStyles = `${isTablet ? styles.outerPillarsSmallContainer : styles.outerPillarsContainer} ${
    isLargeDesktop ? styles.outerLargeScreen : isDesktop ? styles.outerDesktopScreen : isLaptop ? styles.outerSmallScreen : ''
  }`;

  const keywordMsg = (searchType === 'Keyword' && searchQuery.length < 3) ? 'Please enter 3 or more characters to complete your search.' : false;
  const contentIdMsg = (searchType === 'Content ID' && searchQuery.length < 2) ? 'Please enter a complete content ID number to search'
    : (searchType === 'Content ID' && Number.isNaN(Number(searchQuery))) ? 'Content ID search only supports numbers.' : false;

  return (
    <div className={outerStyles}>
      <div className={styles.container}>
        <PillarsList
          pillars={pillars}
          selectedPillarId={pillarId}
          onSelect={handlePillarSelect}
          palette={palette}
        />
      </div>
      <div className={styles.searchBox}>
        <CustomTextField
          variant="outlined"
          placeholder="Search..."
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{ width: isLargeDesktop ? 428 : isDesktop ? 316 : 272 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                {(searchQuery) && (
                  <IconButton
                    onClick={handleClearSearch}
                    aria-label="clear search"
                    edge="end"
                    size="small"
                  >
                    <ClearIcon />
                  </IconButton>
                )}
                <SearchTypeSelect value={searchType} onChange={handleSearchTypeChange} />
              </InputAdornment>
            ),
          }}
        />
        {searchQuery && msg && (keywordMsg || contentIdMsg) && (
          <div className={styles.searchMsg}>
            <img src={InfoIcon} alt="infoIcon" className={styles.info} />
            <span className={styles.msg}>{searchType === 'Keyword' ? keywordMsg : contentIdMsg }</span>
          </div>
        )}
      </div>
    </div>
  );
}
