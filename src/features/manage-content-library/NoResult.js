import React from 'react';
import { Typo<PERSON>, <PERSON>, Link } from '@mui/material';
import { colors } from '../../theme/colors';
import { useContentLibrary } from '../../hooks/useContentLibrary';

const NoResults = () => {
  const { castingSeaBlue, darkByzantinePurple, darkCharcoal } = colors;
  const { filters: { searchType, selectedFilterPills } } = useContentLibrary();
  const searchWarning = searchType === 'Keyword' ? 'your search.' : 'the Content ID you entered.';
  const searchMsg = searchType === 'Keyword' ?
    'Please make sure that the words are spelled correctly. You might also try different or more general search terms.'
    : 'Please make sure that you entered the correct Content ID number.';
  const warning = selectedFilterPills.length > 0 ? 'your filter selections.' : searchWarning;
  const msg = selectedFilterPills.length > 0 ? 'Please clear one or more of the filters above.' : searchMsg;

  return (
    <Box sx={{ padding: '.5rem 1.5rem' }}>
      <Typography variant="h6" sx={{ fontSize: '1.188rem', fontWeight: 600, color: darkByzantinePurple }}>
        Sorry, we couldn’t find any matches for {warning}
      </Typography>
      <Typography variant="body1" mt={1} sx={{ color: darkCharcoal }}>
        {msg}
      </Typography>
      <Typography variant="body1" mt={1} sx={{ color: darkCharcoal }}>
        If you’re still unable to find what you’re looking for,&nbsp;
        <Link target="_blank" href="https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820">
          <span style={{ color: castingSeaBlue, textDecoration: 'none' }}>contact your Client Success Team</span>
        </Link>
        . We’ll be happy to help!
      </Typography>
    </Box>
  );
};

export default NoResults;
