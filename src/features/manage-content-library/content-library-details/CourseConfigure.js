/* eslint-disable max-len */
import React, { useEffect, useMemo, useState } from 'react';
import { Box, Button, Divider, InputLabel, Stack, TextareaAutosize, TextField, Typography, useTheme } from '@mui/material';
import { useFormik } from 'formik';
import { get } from 'lodash';
import * as yup from 'yup';
import { Visibility } from '@mui/icons-material';
import { Link } from 'react-router-dom';
import ReactQuill, { Quill } from 'react-quill';
import { getContentPolicies, patchContentConfiguration } from '../../../services/api/contentLibrary';
import styles from './ContentLibraryDetailsContainer.module.css';
import ChevronIcon from '../../../icons/Chevron';
import SendSaveButton from '../../../components/Button/SendSaveButton';
import Switch from '../../../components/Switch/Switch';
import CancelButton from '../../../components/Button/CancelButton';
import { pushToContentPreview } from '../../navigation/Routes/AppRoutes';
import { useUser } from '../../../hooks/useUser';
import { colors } from '../../../theme/colors';
import '../../../../node_modules/react-quill/dist/quill.snow.css';

const toolbarOptions = [
  ['bold', 'italic', 'underline'],
  ['link'],
  ['clean'],
];

const modules = {
  toolbar: toolbarOptions,
};

const formats = [
  'bold', 'italic', 'underline', 'link',
];

Quill.debug('error');

export default function CourseConfigure({ contentData, handleToggleBtn }) {
  const { palette } = useTheme();
  const user = useUser();
  const name = `${user?.firstName} ${user?.lastName}`;
  const email = `${user?.email}`;
  const accountName = get(user, 'accounts[0].name');
  const { castingSeaBlue } = colors;
  const { id, type } = contentData[0] || {};
  const [data, setData] = useState({});
  const fetchContentDetails = async () => {
    const response = await getContentPolicies(id, type);
    if (response) setData(response);
  };
  useEffect(() => {
    fetchContentDetails();
  }, [id]);

  const labelStyle = {
    color: palette.primary.darkLightPurple,
    fontSize: '0.9rem',
  };

  const initialValues = useMemo(() => ({
    hasLastCardMessage: data.hasLastCardMessage || false,
    completedMessage: data.completedMessage || null,
    hasCertificate: data.hasCertificate || (!data?.isAccountProgramModified),
    downloadInstructions: data.downloadInstructions || null,
    certificateText: data.certificateText || null,
    isTimed: data.isTimed,
    enableTimeRequirements: !!(!data.isTimed && data.minTimeInMinutes),
    minTimeInMinutes: data.minTimeInMinutes,
  }), [data]);

  const formik = useFormik({
    enableReinitialize: true,
    initialValues,
    validationSchema: yup.object({
      enableTimeRequirements: yup.boolean(),
      minTimeInMinutes: yup.lazy((_, context) => {
        const { enableTimeRequirements } = context.parent;
        return enableTimeRequirements
          ? yup
            .mixed()
            .nullable()
            .required('Please set a minimum time or set the toggle to “No”')
            .test(
              'is-valid-integer',
              'Please enter a number',
              (value) => value !== null &&
              value !== '' &&
              /^[0-9]+$/.test(String(value)),
            )
            .test(
              'max-length',
              'Value must be less than 8 digits.',
              (value) => String(value).length < 8,
            )
          : yup.mixed().notRequired();
      }),
    }),
    onSubmit,
  });

  async function handleChange(e) {
    await formik.setFieldValue('enableTimeRequirements', e);
    await formik.setFieldValue('minTimeInMinutes', data.minTimeInMinutes);
    await formik.validateForm();
  }

  async function onSubmit(values) {
    const minTime = values.enableTimeRequirements && values.minTimeInMinutes !== ''
      ? values.minTimeInMinutes
      : null;

    const rearrangedData = {
      ...values,
      minTimeInMinutes: minTime,
    };
    const res = await patchContentConfiguration(id, type, rearrangedData);
    setData(res);
    formik.resetForm();
  }

  const onCancel = () => {
    formik.resetForm();
  };

  const handleNavigateToPreview = (contentType, contentID) => {
    return pushToContentPreview(contentType, contentID);
  };

  const unlockEmtrainURL = () => {
    const topicOrQuestion = 'I would like to set a minimum time that learners must spend on a SCORM course.';
    const encodedQueryParams = `Your Company Name=${encodeURIComponent(accountName)}&Your Name=${encodeURIComponent(name)}&Your Email Address=${encodeURIComponent(email)}&Agenda Topics / Questions=${encodeURIComponent(topicOrQuestion)}`;
    const unlockAnalyticsURL = `https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820?${encodedQueryParams}`;
    return unlockAnalyticsURL;
  };

  return (
    <Box p={3} sx={{ color: palette.primary.darkByzantinePurple }}>
      <Box className={styles.breadcrumb} onClick={() => handleToggleBtn()}>
        <ChevronIcon
          key="icon"
          direction="left"
          width="0.8rem"
          height="0.8rem"
          color={palette.primary.main}
          style={{ marginRight: '0.25rem' }}
          role="img"
          aria-label="chevron right"
          aria-hidden={false}
        />
        <strong>Back</strong>
      </Box>
      <Box>
        <form onSubmit={formik.handleSubmit}>
          <Box className={styles.configureBox}>
            <Typography fontSize="1.5rem" fontWeight="600">Course Configure Options</Typography>
            <Divider />
            <Typography fontSize="1.188rem" fontWeight="600">Course Completion</Typography>
            <InputLabel style={labelStyle}>
              Include a message on the last card of the course?
            </InputLabel>
            <div className={styles.toggle}>
              <Switch
                id="hasLastCardMessage"
                name="hasLastCardMessage"
                size="small"
                inputProps={{ 'data-cy': 'hasLastCardMessage' }}
                checked={formik.values.hasLastCardMessage}
                onChange={formik.handleChange}
                tabIndex={0}
                sx={{
                  fontFamily: 'Source Sans Pro,sans-serif',
                  border: formik.values.hasLastCardMessage ? `0.5px solid ${palette.primary.white}` : '',
                  '& .MuiSwitch-switchBase': { padding: formik.values.hasLastCardMessage ? '1.5px' : '2px' },
                }}
              />
              <Typography fontWeight="600">{formik.values.hasLastCardMessage ? 'Yes' : 'No'}</Typography>
            </div>
            {formik.values.hasLastCardMessage && (
            <>
              <InputLabel style={labelStyle}>
                Text on the last card of the course (optional)
              </InputLabel>
              <ReactQuill
                style={{ marginBottom: '0.5rem' }}
                theme="snow"
                value={formik.values.completedMessage}
                onChange={(value) => formik.setFieldValue('completedMessage', value)}
                modules={modules}
                formats={formats}
              />
            </>
            )}
            <Box className={styles.previewSection}>
              <InputLabel style={labelStyle}>
                Include downloadable Completion Certificate at the end of the course?
              </InputLabel>
              {formik.values.hasCertificate && (
              <Button
                variant="text"
                startIcon={<Visibility />}
                className={styles.actionButton}
                disableRipple
                onClick={() => window.open(handleNavigateToPreview(type, id))}
              >
                Preview
              </Button>
              )}
            </Box>
            <div className={styles.toggle}>
              <Switch
                id="hasCertificate"
                name="hasCertificate"
                size="small"
                role="switch"
                inputProps={{ 'data-cy': 'hasCertificate' }}
                checked={formik.values.hasCertificate}
                onChange={formik.handleChange}
                tabIndex={0}
                sx={{
                  fontFamily: 'Source Sans Pro,sans-serif',
                  border: formik.values.hasCertificate ? `0.5px solid ${palette.primary.white}` : '',
                  '& .MuiSwitch-switchBase': { padding: formik.values.hasCertificate ? '1.5px' : '2px' },
                }}
              />
              <Typography fontWeight="600">{formik.values.hasCertificate ? 'Yes' : 'No'}</Typography>
            </div>
            {formik.values.hasCertificate && (
            <>
              <InputLabel style={labelStyle}>
                Text on the <strong>Completion Certificate Card</strong> (optional)
              </InputLabel>
              <TextareaAutosize
                id="downloadInstructions"
                name="downloadInstructions"
                type="textarea"
                style={{ color: palette.text.primary, marginBottom: '0.5rem' }}
                minRows={2}
                maxRows={3}
                role="switch"
                onChange={formik.handleChange}
                value={formik.values.downloadInstructions ?? ''}
                onBlur={formik.handleBlur}
                data-cy="downloadInstructions"
              />
              <InputLabel style={labelStyle}>
                Text on the <strong>Completion Certificate</strong> (optional)
              </InputLabel>
              <TextareaAutosize
                id="certificateText"
                name="certificateText"
                type="textarea"
                style={{ color: palette.text.primary }}
                minRows={2}
                maxRows={3}
                role="switch"
                onChange={formik.handleChange}
                value={formik.values.certificateText ?? ''}
                onBlur={formik.handleBlur}
                data-cy="certificateText"
              />
            </>
            )}
            <Divider sx={{ marginTop: '1rem' }} />
            <Typography fontSize="1.188rem" fontWeight="600">Minimum Time Requirement</Typography>
            {!formik.values.isTimed && !user.isScormAccount && (
            <>
              <InputLabel style={labelStyle}>
                You have the option to set the minimum time that learners must spend on this course.
              </InputLabel>
              <InputLabel style={labelStyle}>
                Enable time requirements?
              </InputLabel>
              <div className={styles.toggle}>
                <Switch
                  id="enableTimeRequirements"
                  name="enableTimeRequirements"
                  size="small"
                  role="switch"
                  inputProps={{ 'data-cy': 'enableTimeRequirements' }}
                  checked={formik.values.enableTimeRequirements}
                  onChange={(_, e) => handleChange(e)}
                  tabIndex={0}
                  sx={{
                    fontFamily: 'Source Sans Pro,sans-serif',
                    border: formik.values.enableTimeRequirements ? `0.5px solid ${palette.primary.white}` : '',
                    '& .MuiSwitch-switchBase': { padding: formik.values.enableTimeRequirements ? '1.5px' : '2px' },
                  }}
                />
                <Typography fontWeight="600">{formik.values.enableTimeRequirements ? 'Yes' : 'No'}</Typography>
              </div>
            </>
            )}
            {((formik.values.enableTimeRequirements && !formik.values.isTimed) || formik.values.isTimed) && (
            <>
              <Typography style={labelStyle}>
                {formik.values.isTimed ? 'This course has a preset time requirement and cannot be changed.' : 'Minimum time requirement' }
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <TextField
                  id="minTimeInMinutes"
                  name="minTimeInMinutes"
                  type="text"
                  onChange={formik.handleChange}
                  value={formik.values.minTimeInMinutes ?? ''}
                  onBlur={formik.handleBlur}
                  data-cy="minTimeInMinutes"
                  error={formik.touched.minTimeInMinutes && Boolean(formik.errors.minTimeInMinutes)}
                  InputProps={{
                    inputProps: { min: 0, style: { MozAppearance: 'textfield', padding: '4px', textAlign: 'center' } },
                    sx: {
                      '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
                        WebkitAppearance: 'none',
                        margin: 0,
                      },
                    },
                    style: { width: '42px', height: '34px' },
                  }}
                  disabled={formik.values.isTimed}
                />
                <Typography variant="body2" fontSize="1rem">minutes</Typography>
              </Stack>
              {formik.touched.minTimeInMinutes && Boolean(formik.errors.minTimeInMinutes) && (
                <Typography color="#d32f2f" fontSize="0.75rem">{formik.errors.minTimeInMinutes}</Typography>
              )}
            </>
            )}
            {user.isScormAccount && !formik.values.isTimed && (
            <Typography variant="body1" mt={1}>
              If you’d like to set a minimum time that learners must spend on this course, please&nbsp;
              <Link target="_blank" to={{ pathname: unlockEmtrainURL() }}>
                <span style={{ color: castingSeaBlue, textDecoration: 'none' }}>
                  contact your Client Success Manager.
                </span>
              </Link>
            </Typography>
            )}
            <Divider sx={{ marginTop: '1rem' }} />
            <Box display="flex" marginTop="1rem" justifyContent="flex-start" gap={1}>
              <CancelButton
                data-cy="Cancel"
                disabled={!formik.dirty}
                onClick={() => onCancel()}
              />
              <SendSaveButton
                name="save button"
                type="submit"
                disabled={!formik.dirty}
                label="save changes"
                data-cy="save-button"
              />
            </Box>
          </Box>
        </form>
      </Box>
    </Box>
  );
}
