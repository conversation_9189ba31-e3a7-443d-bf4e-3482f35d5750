/* eslint-disable max-len */
import React, { useState, useEffect } from 'react';
import { Box, FormControl, MenuItem, Select, Typography, List, ListItem, Link, useTheme } from '@mui/material';
import { Link as NavLink } from 'react-router-dom';
import { get } from 'lodash';
import ExpandableRecommendations from './ExpandableRecommendations';
import trainingData from './trainings.json';
import styles from './ContentLibraryDetailsContainer.module.css';
import BulbBg from '../../../images/bulb_backgrond.svg';
import BulbIcon from '../../../images/bulb.svg';
import { colors } from '../../../theme/colors';
import { useUser } from '../../../hooks/useUser';
import { getContentDetails } from '../../../services/api/contentLibrary';
import { getListingLessonsORPrograms, sortedVersionsData } from '../../../hooks/useSortContentVersions';
import { pushToContentLibraryDetails } from '../../navigation/Routes/AppRoutes';
import { patchAccountRecommendations } from '../../../services/api/accounts';
import { useAuth } from '../../../hooks/useAuth';

const regions = ['ca', 'ch', 'ct', 'ny'];
const regionLabel = {
  ca: 'California',
  ch: 'Chicago, IL',
  ct: 'Connecticut',
  ny: 'New York',
};

const getStyles = (value, recommendState) => {
  const styleMap = {
    Y: { color: '#6FBE55', background: 'rgba(111, 190, 85, 0.15)' },
    N: { color: '#818099', background: 'rgba(129, 128, 153, 0.15)' },
    default: { color: (recommendState !== '') && '#ED1E63', background: 'transparent' },
  };

  const { color, background } = styleMap[value] || styleMap.default;

  return {
    border: `1px solid ${color}`,
    backgroundColor: background,
    borderRadius: 2,
    minWidth: 90,
    height: '30px',
  };
};

const PwhUsRecommendations = ({ scrollToBottom, expandValue, setExpandValue }) => {
  const user = useUser();
  const { getUserInfoByID } = useAuth();
  const initialState = regions.reduce((acc, region) => ({ ...acc, [region]: '' }), {});
  const logMeInAccount = get(user, 'accounts[1]', false);
  let accountId = get(user, 'accounts.0.id');
  let trainingRecommenders = get(user, 'accounts.0.trainingRecommender');
  if (logMeInAccount && logMeInAccount.id) {
    accountId = get(user, 'accounts.1.id');
    trainingRecommenders = get(user, 'accounts.1.trainingRecommender');
  }
  const selectedTrainingRecommenders = trainingRecommenders || initialState;
  const [state, setState] = useState(selectedTrainingRecommenders);
  const [open, setOpen] = useState(expandValue);
  const [contentDetails, setContentDetails] = useState({});
  const { palette } = useTheme();
  const { castingSeaBlue } = colors;

  const fetchContentDetails = async () => {
    const response = await getContentDetails('listing', 2);
    if (response) setContentDetails(response);
  };

  useEffect(() => {
    fetchContentDetails();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setOpen(expandValue);
    if (expandValue) {
      setTimeout(() => {
        if (scrollToBottom) {
          scrollToBottom();
        }
      }, 100);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expandValue]);

  const { catalogItems, isCustomTag, isSpecialPwhUs } = contentDetails;
  const listingLessonsORPrograms = getListingLessonsORPrograms(catalogItems);
  const activeSortedVersions = sortedVersionsData(listingLessonsORPrograms, isCustomTag, false, isSpecialPwhUs, false);
  const { id } = activeSortedVersions[0] || [];

  const handleChange = (region) => async (event) => {
    const trainingRecommender = { ...state, [region]: event.target.value === 'Select' ? '' : event.target.value };
    setState(trainingRecommender);
    await patchAccountRecommendations(accountId, { trainingRecommender }).then(() => getUserInfoByID(user?.id, user?.adminAccountId));
    if (state) {
      setTimeout(() => {
        if (scrollToBottom) {
          scrollToBottom();
        }
      }, 100);
    }
  };

  const handleToggleBtn = (expand) => {
    setOpen(!(expand));
    setExpandValue(!(expand));
    if (!expand) {
      // Only scroll when expanding or all states selected
      setTimeout(() => {
        if (scrollToBottom) {
          scrollToBottom();
        }
      }, 100);
    }
  };

  const onNavigateToByStandard = () => {
    if (id) {
      setOpen(false);
      setState(initialState);
    }
  };

  const recommendState = `${state.ca}${state.ch}${state.ct}${state.ny}`;
  const data = trainingData[recommendState] || null;
  const path = pushToContentLibraryDetails('listing', 2, id); // ByStander Training

  const message = `Use the versions dropdown to explore all of our course options, including shorter versions for non-managers, untimed versions, and prior editions you’ve ${user.isScormAccount ? 'downloaded' : 'deployed'} in the past.`;

  return (
    <ExpandableRecommendations
      key="recommendation"
      isOpen={open}
      onClick={() => handleToggleBtn(open)}
      title="We’ll help you choose the right harassment training for your team."
      data-cy="recommendation"
    >
      <Box className={styles.dropdownSection}>
        <Typography fontWeight="600">Do you have employees in…</Typography>
        {regions.map((region) => (
          <FormControl key={region} sx={{ display: 'flex', flexDirection: 'row', gap: '0.5rem', alignItems: 'center' }}>
            <label htmlFor={`${region}-label`} style={{ fontWeight: '600' }}>{regionLabel[region]}?</label>
            <Select
              labelId={`${region}-label`}
              name={`${region}-label`}
              value={state[region] === '' ? 'Select' : state[region]}
              placeholder="Select"
              onChange={handleChange(region)}
              displayEmpty
              notched
              sx={{
                background: palette.background.white,
                '& .MuiSelect-select': { padding: '0.25rem' },
                height: '1.75rem',
                ...getStyles(state[region], recommendState),
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    '& ul': {
                      background: palette.background.white,
                      '& li': {
                        fontSize: '0.875rem',
                        padding: '0.313rem 0.625rem',
                      },
                    },
                  },
                },
              }}
            >
              <MenuItem value="Select">Select</MenuItem>
              <MenuItem value="Y">Yes</MenuItem>
              <MenuItem value="N">No</MenuItem>
            </Select>
          </FormControl>
        ))}
      </Box>
      {!data?.requirements && (
      <Box
        gap={2}
        className={styles.recommendedBottomSection}
        sx={{ background: palette.background.default }}
      >
        <Box
          sx={{
            position: 'relative',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 50,
            height: 50,
          }}
        >
          <BulbBg
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: 'fit-content',
              height: 'fit-content',
            }}
          />
          <BulbIcon
            style={{
              zIndex: 1,
              position: 'relative',
              left: 17.5,
              top: 5,
            }}
          />
        </Box>
        <Typography sx={{ padding: '0rem 2rem' }}>
          Our harassment course is designed to meet legal training standards across the United States,
          but some states and cities have more stringent requirements than others. Answer the questions above
          and we’ll recommend the right course versions to ensure your company is fully compliant, regardless of
          where your employees are located.
        </Typography>
      </Box>
      )}
      {data?.requirements.length > 0 && (
      <Box className={styles.recommenderSection} sx={{ background: palette.background.default }}>
        <Box>
          <Typography variant="h6" sx={{ fontSize: '1.188rem', fontWeight: '600' }} gutterBottom>
            Your Recommended Trainings
          </Typography>
          <List sx={{ padding: '0rem 1rem' }}>
            {data?.requirements.map((req) => (
              <ListItem key={`${req?.audience}-type`} sx={{ display: 'list-item !important', listStyleType: 'disc', p: 0 }}>
                <Typography variant="body1">
                  {req.audience}&nbsp;
                  {req?.requirement === 'Bystander' ? (
                    <NavLink to={id ? path : '#'}>
                      <span onClick={() => onNavigateToByStandard()} style={{ color: castingSeaBlue, textDecoration: 'none' }}>{req?.requirement}</span>
                    </NavLink>
                  ) : (
                    <strong>{req?.requirement}</strong>
                  )}
                  {req?.duration && (<strong>{` (${req?.duration}),`}</strong>)} {req?.frequency}
                </Typography>
              </ListItem>
            ))}
          </List>
          <Typography variant="body2" sx={{ mt: 2, fontSize: '0.9rem' }}>
            {data?.note}
          </Typography>
          <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic', fontSize: '0.8rem' }}>
            *We create sequential “A” and “B” versions of this course every two years to keep it current and engaging. We recommend deploying the “A” and “B” versions on alternating years to always provide your employees with a fresh learning experience
          </Typography>
        </Box>
        <span className={styles.bottomDivide} style={{ border: palette.border.main }} />
        <Box>
          <Typography className={styles.recommenderHeader}>Looking for something different?</Typography>
          <Typography className={styles.recommenderText}>{message}</Typography>
          <Typography className={styles.recommenderHeader} sx={{ marginTop: '1rem' }}>Still have questions?</Typography>
          <Typography className={styles.recommenderText}>Read about&nbsp;
            <Link target="_blank" href="https://emtrain.com/harassment-requirements/">
              <span style={{ color: castingSeaBlue, textDecoration: 'none' }}>US Harassment Training Requirements</span>
            </Link>
            &nbsp;or&nbsp;
            <Link target="_blank" href="https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820">
              <span style={{ color: castingSeaBlue, textDecoration: 'none' }}>Contact your Client Success Team</span>
            </Link>
            &nbsp;for additional assistance.
          </Typography>
        </Box>
      </Box>
      )}
    </ExpandableRecommendations>
  );
};

export default PwhUsRecommendations;
