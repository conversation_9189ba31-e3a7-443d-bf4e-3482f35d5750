.mainContainer {
  min-height: 100vh;
  margin-top: -3.5rem;
  padding: 1rem 3rem;
}

.contentDetailsContainer {
  border: 1px solid grey;
  border-radius: .5rem;
  padding-bottom: 2rem;
  margin-top: 1rem;
  display: grid;
}

.innerContainer {
  display: flex;
  flex-direction: column;
}

.breadcrumb {
  cursor: pointer;
  width: fit-content;
}

.contentDetailsSection {
  display: grid;
  gap: 1rem;
  grid-template-columns: 80% 2% 16%;
  padding: 1rem 2rem
}

.title {
  display: flex; 
  align-items: center; 
  gap: 0.5rem
}

.cardImage {
  width: 258px;
  height: 155px;
  border-radius: 6px;
  box-sizing: border-box;
}

.customTag {
  font-weight: 700;
  font-size: 13px !important;
  width: 53px !important;
  height: 20px !important;
  border-radius: 4px;
  text-transform: none !important;
}

.bottomSection1 {
  display: flex;
  align-items: center;
}

.bottomSection2 {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  margin-left: 0px !important;
}

.pillarsList {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.pillar {
  height: 24px;
  border-radius: 18.82px;
  font-size: 12px;
  font-weight: 600;
  border-width: 1.44px;
  span {
    padding: 0px 10px;
  }
}

.label {
  font-weight: 600;
}

.header {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1rem;
  flex-wrap: wrap;
}

.divide {
  width: 0.063rem;
  height: 1.375rem;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
}

.bottomDivide {
  width: 0.063rem;
  height: auto;
  opacity: 0.45;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
  margin: 0rem 1rem;
}

.selectField {
  width: 85%;
  height: 2.313rem;
  border-radius: 0.25rem;
}