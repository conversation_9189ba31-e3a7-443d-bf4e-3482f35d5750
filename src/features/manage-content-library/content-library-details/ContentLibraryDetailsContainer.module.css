.mainContainer {
  min-height: 100vh;
  margin-top: -3.5rem;
  padding: 1rem 3rem;
}

.contentDetailsContainer {
  border: 1px solid grey;
  border-radius: .5rem;
  padding-bottom: 2rem;
  margin-top: 1rem;
  display: grid;
}

.innerContainer {
  display: flex;
  flex-direction: column;
}

.breadcrumb {
  cursor: pointer;
  width: fit-content;
}

.contentDetailsSection {
  display: grid;
  gap: 1rem;
  grid-template-columns: 80% 2% 16%;
  padding: 1rem 2rem
}

.courseLessonTitle {
  padding: 1rem 0rem 0rem 0rem;
  width: 80%;
}

.courseDetailsSection {
  display: grid;
  gap: 1rem;
  grid-template-columns: 80% 2% 16%;
  padding: 0rem;
}

.title {
  display: flex; 
  align-items: center; 
  gap: 0.5rem
}

.lessonDescription {
  display: flex; 
  align-items: center; 
  gap: 0.5rem;
  margin: 1rem 0rem;
}

.cardImage {
  width: 258px;
  height: 155px;
  border-radius: 6px;
  box-sizing: border-box;
}

.customTag {
  font-weight: 700;
  font-size: 13px !important;
  width: 53px !important;
  height: 20px !important;
  border-radius: 4px;
  text-transform: none !important;
}

.bottomSection1 {
  display: flex;
  align-items: center;
}

.bottomSection2 {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  margin-left: 0px !important;
}

.pillarsList {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.pillar {
  height: 24px;
  border-radius: 18.82px;
  font-size: 12px;
  font-weight: 600;
  border-width: 1.44px;
  span {
    padding: 0px 10px;
  }
}

.label {
  font-weight: 600;
}

.header {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1rem;
  flex-wrap: wrap;
}

.divide {
  width: 0.063rem;
  height: 1.375rem;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
}

.bottomDivide {
  width: 0.063rem;
  height: auto;
  opacity: 0.45;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
  margin: 0rem 1rem;
}

.selectField {
  width: 85%;
  height: 2.313rem;
  border-radius: 0.25rem;
}

.policyIcon {
  width: 1rem;
  height: 1.2rem;
}

.actionButton{
  color: #166fd7;
  font-weight: bold !important;
  text-transform: capitalize !important;
  background-color: unset !important;
  padding: 0.2rem 0.2rem !important;
  font-size: 0.975rem !important;
}

.configureBox {
  display: flex;
  gap: 0.5rem;
  flex-direction: column;
}

.toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.previewSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 1.2rem;
  margin-top: 0.5rem;
}

.spanCardDetails {
  font-size: 19px;
  font-style: normal;
  color: #222444;
  line-height: 20px;
}

.policyFormTitle {
  padding-top: 1px;
  padding-bottom: 4px;
  font-size: 15px;
  line-height: 20px;
}

.saveSaveButtonBox {
  display: flex;
  padding-top: 16px;
  padding-bottom: 16px;
  position: relative;
  gap: 12px;
}

.policyFormDivider {
  width: 100%;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  margin-top: 8px;
  margin-bottom: 8px;
}

.policyUploadText {
  flex: 1;
  font-weight: 700;
  font-size: 19px;
}

.policyUploadContainer {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-bottom: 10px;
}

.policyUploadTextStyle {
  font-size: 15px;
  line-height: 20px;
  font-weight: 400;
}

.remText {
  font-size: 0.82rem;
  font-weight: 400;
}

.flexAlignZeroGap {
  display: flex;
  align-items: center;
  gap: 0;
}

.flexBoxDashed {
  flex: 1;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-height: 40px;
  max-width: 550px;
}

.centeredText {
  width: 100%;
  text-align: center;
  color: #6b7280;
}

.fullWidthSemiBoldText {
  width: 100%;
  font-weight: 600;
  color: #6b7280;
}

.ellipsisText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90%;
}

.dashedFlexBox {
  flex: 1;
  max-width: 625px;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 12px;
  padding-right: 0;
}

.centeredBox {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  padding-left: 32px;
  padding-right: 32px;
}

.confirmText {
  color: #212121;
  font-size: 24px;
  font-weight: 400;
}

.dialogActions {
  position: absolute;
  bottom: 40px;
  left: 0;
  right: 0;
  justify-content: center;
  gap: 8px;
  display: flex;
}

.relativeBox {
  position: relative;
  width: 559px;
  height: 228px;
  padding: 0;
  padding-bottom: 80px;
}

.flexContainer {
  display: flex;
  position: relative;
  gap: 12px;
  padding-top: 16px;
  padding-bottom: 16px;
}

.lessonCourses {
  border-bottom: 1px solid #cbccd9;
  width: fit-content;
  line-height: 1.7;
}