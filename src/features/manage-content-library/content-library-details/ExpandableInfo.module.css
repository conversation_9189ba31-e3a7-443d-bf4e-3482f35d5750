.expandInfoHeader {
  padding: 0.3rem 0rem;
  display: flex;
  cursor: pointer;
  text-transform: none;
  margin: 0;
  font-weight: 600;
  width: fit-content;
  font-size: 0.9rem;
}

.expandInfoHeader :hover {
  font-weight: bold !important;
}

.text {
  margin-right: 0.5rem;
}

.text p {
  margin: 0;
  font-weight: 600;
}

h2.expandableInfoH2 {
  font-size: 1rem;
  margin: 0;
  font-weight: 600;  
}

.chevron {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.chevronIcon {
  height: 0.8rem;
  width: 0.8rem;
}

.expandRecommendationHeader {
  padding: 0.6rem 1.5rem;
  display: flex;
  cursor: pointer;
  text-transform: none;
  margin: 0;
  font-weight: 600;
  justify-content: space-between;
  border: 1px solid #cbccd9;
  border-start-start-radius: 4px;
  border-start-end-radius: 4px;
}

.Header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0;
}