import React, { useState } from 'react';
import { Box, Button, Stack } from '@mui/material';
import { Visibility } from '@mui/icons-material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShare, faWrench, faDownload } from '@fortawesome/free-solid-svg-icons';
import { get } from 'lodash';
import Policy from '../../../images/policy_icon.png';
import PolicyInfo from '../../../images/policy_info_icon.svg';
import styles from './ContentLibraryDetailsContainer.module.css';
import { APP_URL } from '../../../config/constants';
import Tooltip from '../../../components/Tooltip/Tooltip';
import { useUser } from '../../../hooks/useUser';
import { pushToContentPreview } from '../../navigation/Routes/AppRoutes';
import { colors } from '../../../theme/colors';
import { handleScormPIFDownload } from '../../../services/api/contentLibrary';

export default function ContentActions({ contentData, handleToggleBtn }) {
  const user = useUser();
  const [btnState, setBtnState] = useState({
    shareBtn: false,
    downloadBtn: false,
  });
  const buttonSx = {
    '&.Mui-disabled': {
      color: colors.blue,
      cursor: 'pointer',
      opacity: 0.5,
      pointerEvents: 'visible',
    },
  };
  const accountType = get(user, 'accounts.0.accountType');
  const { id, type, instructionalType, title, hasPolicy, lessons, pendingPolicyConfiguration } = contentData[0] || {};
  // checking for inactive content items - prior(deployed/downloaded)
  const isDisable = title?.includes('Edited') ?? false;
  const shareLink = type === 'lesson'
    ? `${APP_URL}/share/microlesson/${id}`
    : `${APP_URL}/share/program/${id}`;

  const programsPolicyCount = lessons?.reduce((sum, item) => {
    return sum + (item.pendingPolicyConfiguration || 0);
  }, 0);

  const hasPolicyTrue = type === 'lesson' ? hasPolicy === 1 : (lessons?.some((lesson) => lesson.hasPolicy)) ?? false;
  const hasPolicyCount = type === 'lesson' ? pendingPolicyConfiguration : programsPolicyCount;

  // eslint-disable-next-line max-len
  const policyInfoWarning = `Before ${user.isScormAccount ? 'downloading' : 'deploying'} this ${instructionalType}, you must provide ${hasPolicyCount} ${hasPolicyCount > 1 ? 'policies' : 'policy'}.`;

  const copyToClipboard = (shareUrl) => {
    let copyField = null;
    copyField = document.createElement('textarea');
    copyField.innerText = shareUrl;
    document.body.appendChild(copyField);
    copyField.select();
    document.execCommand('copy');
    copyField.remove();
  };

  const showTooltip = (key, link) => {
    if (link) copyToClipboard(link);
    setBtnState((prev) => ({ ...prev, [key]: true }));
    setTimeout(() => setBtnState((prev) => ({ ...prev, [key]: false })), 2000);
  };

  const handleNavigateToPreview = (contentType, contentID) => {
    return pushToContentPreview(contentType, contentID);
  };

  const handleScormPIFClick = async (contentType, contentID) => {
    const finalUrl = `/${contentType}s/${contentID}/scormDownload`;
    const accountId = get(user, 'accounts.0.id');
    if (hasPolicyTrue && hasPolicyCount > 0) {
      // eslint-disable-next-line max-len
      const isConfirmed = window.confirm('A required policy has not yet been configured for this content. You must configure the policy here prior to deployment in your LMS. Are you sure you want to download this content now?');
      if (isConfirmed) {
        await handleScormPIFDownload(finalUrl, accountId, 'scormDownload.csv');
      }
    } else {
      await handleScormPIFDownload(finalUrl, accountId, 'scormDownload.csv');
    }
  };

  return (
    <Stack alignItems="flex-start" justifyContent="center" minWidth={140}>
      {hasPolicyTrue && (
      <Box>
        <Button
          variant="text"
          className={styles.actionButton}
          startIcon={<img id="policy" src={Policy} className={styles.policyIcon} alt="Policy" />}
          disableRipple
          onClick={() => handleToggleBtn('policyBtn')}
        >
          Policies
        </Button>
        {hasPolicyCount > 0 && (
        <Tooltip
          title={policyInfoWarning}
          role="tooltip"
          placement="bottom-end"
          marginTop="1px"
          maxWidth="10rem"
        >
          <Button
            variant="img"
            tabIndex={0}
            sx={{ padding: 0 }}
            disableRipple
          ><PolicyInfo />
          </Button>
        </Tooltip>
        )}
      </Box>
      )}
      <Button
        variant="text"
        startIcon={<Visibility />}
        className={styles.actionButton}
        disableRipple
        onClick={() => window.open(handleNavigateToPreview(type, id))}
      >
        Preview
      </Button>

      {type === 'program' && ['customer', 'internal'].includes(accountType) && (
        <Button
          variant="text"
          tabIndex={0}
          startIcon={<FontAwesomeIcon icon={faWrench} width="16" />}
          className={styles.actionButton}
          disableRipple
          onClick={() => handleToggleBtn('configureBtn')}
        >
          Configure
        </Button>
      )}

      {!user.isScormAccount && (
        <Tooltip
          role="tooltip"
          placement="bottom-start"
          title={isDisable ? 'This content is out-of-date and cannot be shared.' : 'Link Copied'}
          open={btnState.shareBtn}
        >
          <Button
            startIcon={<FontAwesomeIcon icon={faShare} width="16" />}
            className={styles.actionButton}
            disableRipple
            disabled={isDisable}
            tabIndex={0}
            onClick={() => !isDisable && showTooltip('shareBtn', shareLink)}
            onMouseOver={() => isDisable && showTooltip('shareBtn')}
            sx={buttonSx}
          >
            Share
          </Button>
        </Tooltip>
      )}

      {user.isScormAccount && (
        <Tooltip
          open={btnState.downloadBtn}
          placement="bottom-start"
          title="This content is out-of-date and cannot be downloaded."
        >
          <Button
            variant="text"
            startIcon={<FontAwesomeIcon icon={faDownload} width="16" />}
            className={styles.actionButton}
            disableRipple
            disabled={isDisable}
            tabIndex={0}
            onMouseOver={() => isDisable && showTooltip('downloadBtn')}
            onClick={() => handleScormPIFClick(type, id)}
            sx={buttonSx}
          >
            Download
          </Button>
        </Tooltip>
      )}
    </Stack>
  );
}
