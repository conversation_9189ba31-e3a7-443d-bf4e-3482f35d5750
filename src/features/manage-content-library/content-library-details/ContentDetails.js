/* eslint-disable max-len */
import React, { useState } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Button,
  TableHead,
  useTheme,
  InputLabel,
  TextField,
  styled,
  Divider,
} from '@mui/material';
import { useFormik } from 'formik';
import { useHistory, useRouteMatch } from 'react-router-dom';
import moment from 'moment';
import styles from './ContentLibraryDetailsContainer.module.css';
import SendSaveButton from '../../../components/Button/SendSaveButton';
import PenToSquare from '../../../images/penToSquare.svg';
import { pushToContentLibraryDetails } from '../../navigation/Routes/AppRoutes';
import { patchContentConfiguration } from '../../../services/api/contentLibrary';
import ExpandableInfo from './ExpandableInfo';
import { colors } from '../../../theme/colors';
import ContentVersionDropDown from './ContentVersionDropDown';

export default function ContentDetails({ lessonsORPrograms, contentType, versions, listingDetails,
  activeSortedVersions, inActiveSortedVersions, onSaveUpdate }) {
  const { palette } = useTheme();
  const { params: { listingId, contentId } } = useRouteMatch();
  const history = useHistory();
  const [edit, setEdit] = useState(false);
  const [btnState, setBtnState] = useState({});

  const audienceType = {
    all: 'All',
    employee: 'Non-Manager',
    manager: 'Manager',
  };

  const CustomTableCell = styled(TableCell)(() => ({
    border: '1px solid rgba(224, 224, 224, 1)',
    textAlign: 'center',
    '&.MuiTableCell-body': {
      fontWeight: 'bold',
    },
  }));

  // eslint-disable-next-line no-nested-ternary
  const versionHeader = versions === 1 && contentType === 'Course'
    ? 'There is 1 version of this course.'
    : versions > 1
      ? `There are ${versions} versions of this ${contentType.toLowerCase()}:`
      : null;

  const contentData = [...activeSortedVersions, ...inActiveSortedVersions].filter((item) => item.id === Number(contentId));
  const { id, listingTitle: contentTitle, accountTitle: accountContentTitle, description, audience, stateName, build, version,
    edition, type, isCustomized, dateAdded, lessons, minTimeInMinutes, duration, requiredMinutes } = contentData[0] || [];
  const location = ['United States of America', 'Canada'].includes(listingDetails?.countryName) ? stateName : 'All';

  const getDuration = () => {
    if (type === 'program') {
      // eslint-disable-next-line no-nested-ternary
      return minTimeInMinutes
        ? `${minTimeInMinutes} min`
        : duration
          ? `${duration} min`
          : '';
    }
    return requiredMinutes ? `${requiredMinutes} min` : '';
  };

  const formik = useFormik({
    initialValues: {
      title: ((accountContentTitle || contentTitle) || ''),
    },
    onSubmit,
  });

  const handleSelect = async (selectedItem) => {
    formik.resetForm({ values: { title: (selectedItem.accountTitle || selectedItem.listingTitle) } });
    history.push(pushToContentLibraryDetails(listingId, selectedItem.id));
    setBtnState({});
    setEdit(false);
  };

  async function onSubmit({ title }) {
    await patchContentConfiguration(contentId, type, title).then(() => onSaveUpdate());
    setEdit(false);
  }

  const handleToggleBtn = (key) => {
    setBtnState((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <Paper className={styles.contentDetailsContainer} sx={{ backgroundColor: 'background.default' }}>
      <div className={styles.header} style={{ background: palette.background.ribbon }}>
        <div className={styles.innerContainer}>
          <InputLabel
            className={styles.label}
            style={{ color: palette.primary.white }}
            htmlFor="contents"
          >{versionHeader}
          </InputLabel>
          {versions > 1 && (
          <ContentVersionDropDown
            activeSortedVersions={activeSortedVersions}
            inActiveSortedVersions={inActiveSortedVersions}
            contentData={contentData}
            handleSelect={handleSelect}
          />
          )}
        </div>
      </div>
      <Box className={styles.contentDetailsSection}>
        <Box>
          <form onSubmit={formik.handleSubmit}>
            <Box className={styles.title}>
              <InputLabel style={{ color: palette.primary.darkLightPurple, fontSize: '0.875rem' }}>
                Title for Learners:{' '}
              </InputLabel>
              {!edit && (
                <>
                  <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                    {formik.values.title}
                  </InputLabel>
                  <PenToSquare width="15" height="15" cursor="pointer" onClick={() => { setEdit(true); }} />
                </>
              )}
              {edit && formik.values.title && (
                <>
                  <TextField
                    id="title"
                    name="title"
                    type="text"
                    onChange={formik.handleChange}
                    value={formik.values.title}
                    onBlur={formik.handleBlur}
                    error={formik.touched.title && Boolean(formik.errors.title)}
                    data-cy="title"
                    helperText={formik.touched.title && formik.errors.title}
                    size="small"
                    InputProps={{ style: { borderRadius: '8px', width: '600px', height: '36px' } }}
                  />
                  <SendSaveButton
                    name="save button"
                    type="submit"
                    disabled={!formik.isValid || !formik.dirty}
                    label="save"
                    data-cy="save-button"
                  // className={styles.saveButton}
                  />
                </>
              )}
            </Box>
            {isCustomized === 1 && (
              <Button
                style={{ float: 'right', marginTop: '-20px' }}
                className={styles.customTag}
                sx={{
                  backgroundColor: palette.primary.darkLightPurple,
                  color: palette.primary.white,
                  '&:hover': {
                    backgroundColor: palette.primary.darkLightPurple,
                  },
                }}
              >Custom
              </Button>
            )}
          </form>
          <Typography mb={2}>{description}</Typography>

          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <CustomTableCell>Audience</CustomTableCell>
                  <CustomTableCell>Location</CustomTableCell>
                  <CustomTableCell>Duration</CustomTableCell>
                  <CustomTableCell>ID</CustomTableCell>
                  <CustomTableCell>Edition</CustomTableCell>
                  <CustomTableCell>Date Added</CustomTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <CustomTableCell>{audienceType[audience]}</CustomTableCell>
                  <CustomTableCell>{location}</CustomTableCell>
                  <CustomTableCell>{getDuration()}</CustomTableCell>
                  <CustomTableCell>{id}</CustomTableCell>
                  <CustomTableCell>{`${edition || '0'}.${version || '0'}.${build || '0'}`}</CustomTableCell>
                  <CustomTableCell>{dateAdded ? moment(dateAdded).format('MM/DD/YYYY') : ''}</CustomTableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
        <span className={styles.bottomDivide} style={{ border: palette.border.main }} />
      </Box>

      <Box sx={{ padding: '0rem 2rem' }}>
        {lessons && (
          <ExpandableInfo
            key={`Lessons in this ${lessonsORPrograms.length > 1 ? 'version' : 'course'}`}
            isOpen={btnState.expandLessons || false}
            onClick={() => handleToggleBtn('expandLessons')}
            title={`Lessons in this ${lessonsORPrograms.length > 1 ? 'version' : 'course'}`}
            data-cy="Lessons"
          >
            <Divider sx={{ my: 1 }} />
            {lessons.map((lesson, index) => (
              <LessonItem
                key={lesson.id}
                title={lesson.title}
                id={lesson.id}
                time={lesson.requiredMinutes}
                description={lesson.description}
                lessons={lessons}
                index={index}
              />
            ),
            )}
          </ExpandableInfo>
        )}
      </Box>
    </Paper>
  );
}

function LessonItem({ id, title, description, time }) {
  const { castingSeaBlue } = colors;

  return (
    <>
      <Box display="flex" key={id} justifyContent="space-between" style={{ padding: '0rem 1rem' }}>
        <Box pr={2}>
          <Typography variant="subtitle2" fontWeight="bold">
            <span style={{ color: castingSeaBlue }}>{title}</span> – #{id}
          </Typography>
          <Typography variant="body2">
            {description}
          </Typography>
        </Box>
        <Typography variant="body2" minWidth={50}>
          {`${time || '0'} min`}
        </Typography>
      </Box>
      <Divider sx={{ my: 1 }} />
    </>
  );
}
