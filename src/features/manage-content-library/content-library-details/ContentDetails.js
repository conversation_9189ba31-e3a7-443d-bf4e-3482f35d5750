/* eslint-disable max-len */
import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Button,
  TableHead,
  useTheme,
  InputLabel,
  TextField,
  styled,
  Divider,
} from '@mui/material';
import { useFormik } from 'formik';
import { useHistory, useRouteMatch } from 'react-router-dom';
import moment from 'moment';
import styles from './ContentLibraryDetailsContainer.module.css';
import SendSaveButton from '../../../components/Button/SendSaveButton';
import PenToSquare from '../../../images/penToSquare.svg';
import { pushToContentLibraryDetails,
  pushToCourseConfigure,
  pushToCourseLessonDetails,
  pushToPolicies } from '../../navigation/Routes/AppRoutes';
import { getContentDetails, patchContentConfiguration } from '../../../services/api/contentLibrary';
import ExpandableInfo from './ExpandableInfo';
import { colors } from '../../../theme/colors';
import ContentVersionDropDown from './ContentVersionDropDown';
import ContentActions from './ContentActions';
import Policies from './Policies';
import CourseConfigure from './CourseConfigure';
import LessonInCourses from './LessonCourses';
import PwhHeaderLogo from '../../../images/pwh-mcl-help-logo.svg';

export default function ContentDetails({ lessonsORPrograms, contentType, versions, contentDetails,
  activeSortedVersions, inActiveSortedVersions, onSaveUpdate, mclType, isPWHUS = false, handleRecommendedState }) {
  const { palette } = useTheme();
  const { params: { mclID, contentId }, url } = useRouteMatch();
  const history = useHistory();
  const [edit, setEdit] = useState(false);
  const [btnState, setBtnState] = useState({});
  const [courseLessonDetails, setCourseLessonDetails] = useState({});

  const audienceType = {
    all: 'All',
    employee: 'Non-Manager',
    manager: 'Manager',
  };

  const CustomTableCell = styled(TableCell)(() => ({
    border: '1px solid rgba(224, 224, 224, 1)',
    textAlign: 'center',
    '&.MuiTableCell-body': {
      fontWeight: 'bold',
    },
  }));

  // eslint-disable-next-line no-nested-ternary
  const versionHeader = isPWHUS
    ? 'Explore our course versions:'
    : versions === 1 && contentType === 'Course'
      ? 'There is 1 version of this course.'
      : versions > 1
        ? `There are ${versions} versions of this ${contentType}:`
        : null;
  const contentData = [...activeSortedVersions, ...inActiveSortedVersions].filter((item) => item.id === Number(contentId));
  const { id, listingTitle: contentTitle, accountTitle: accountContentTitle, description, audience, stateName, build, version,
    edition, type, isCustomized, dateAdded, lessons, minTimeInMinutes, duration, requiredMinutes, isCustomTag, listType } = contentData[0] || [];
  const location = ['United States of America', 'Canada'].includes(contentDetails?.countryName) ? stateName : 'All';
  const isCustomTagEnabled = (isCustomized === 1 || (isCustomTag && (listType === 'catalog'))) || false;

  const fetchCourseLessonDetails = async () => {
    if (contentType === 'Course Lesson') {
      const response = await getContentDetails('lesson', contentId);
      if (response) setCourseLessonDetails({ ...response, type: 'lesson' });
    }
  };

  useEffect(() => {
    fetchCourseLessonDetails();
  }, [contentId]);

  const { isCustomized: isCustomizedLesson, programs: lessonCourses } = courseLessonDetails;

  const getDuration = () => {
    if (type === 'program') {
      // eslint-disable-next-line no-nested-ternary
      return minTimeInMinutes !== null
        ? `${minTimeInMinutes} min`
        : duration !== null
          ? `${duration} min`
          : '';
    }
    return requiredMinutes ? `${requiredMinutes} min` : '';
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      title: ((accountContentTitle || contentTitle) || ''),
    },
    onSubmit,
  });

  const handleSelect = async (selectedItem) => {
    // eslint-disable-next-line no-shadow
    const { type, hasPolicy, lessons } = selectedItem;
    const hasPolicyTrue = type === 'lesson' ? hasPolicy === 1 : (lessons?.some((lesson) => lesson.hasPolicy)) ?? false;

    formik.resetForm({ values: { title: (selectedItem.accountTitle || selectedItem.listingTitle) } });
    if (btnState.configureBtn || url.includes('courseConfigure')) {
      history.push(pushToCourseConfigure(mclType, mclID, selectedItem.id));
      // history.push(pushToCourseConfigure(listingId, selectedItem.id));
    } else if ((btnState.policyBtn || url.includes('policies')) && hasPolicyTrue) {
      history.push(pushToPolicies(mclType, mclID, selectedItem.id));
    } else {
      history.push(pushToContentLibraryDetails(mclType, mclID, selectedItem.id));
      setBtnState({});
      setEdit(false);
    }
  };

  async function onSubmit(values) {
    await patchContentConfiguration(contentId, type, values).then(() => onSaveUpdate());
    setEdit(false);
  }

  const handleToggleBtn = (key) => {
    if (key) {
      setBtnState((prev) => ({ ...prev, [key]: !prev[key] }));
      if (key === 'configureBtn' || url.includes('courseConfigure')) {
        history.push(pushToCourseConfigure(mclType, mclID, contentId));
      }
      if (key === 'policyBtn' || url.includes('policies')) {
        history.push(pushToPolicies(mclType, mclID, contentId));
      }
    } else {
      setBtnState({});
      history.push(pushToContentLibraryDetails(mclType, mclID, contentId));
    }
  };

  return (
    <Paper className={styles.contentDetailsContainer} sx={{ backgroundColor: 'background.default' }}>
      <div className={styles.header} style={{ background: palette.background.ribbon }}>
        <div className={styles.innerContainer}>
          <div className={styles.headerTopRow}>
            <InputLabel
              className={styles.label}
              style={{ color: palette.primary.white, ...(isPWHUS ? { marginBottom: '0.5rem' } : {}) }}
              htmlFor="contents"
            >{versionHeader}
            </InputLabel>
          </div>
          {versions > 1 && (
            <ContentVersionDropDown
              activeSortedVersions={activeSortedVersions}
              inActiveSortedVersions={inActiveSortedVersions}
              contentData={contentData}
              handleSelect={handleSelect}
              isPWHUS={isPWHUS}
              selectedContentId={contentId}
            />
          )}
        </div>
        {isPWHUS && (
          <Box
            className={styles.helpBox}
            onClick={() => handleRecommendedState()}
          >
            <PwhHeaderLogo style={{ width: 30, height: 30, marginTop: '3px' }} />
            <Box sx={{ lineHeight: 1.2, fontWeight: 'bold' }}>
              <div>Need help choosing</div>
              <div>the right version?</div>
            </Box>
          </Box>
        )}
      </div>
      {!(url.includes('courseConfigure') || url.includes('policies')) && (
        <>
          <Box className={styles.contentDetailsSection}>
            <Box>
              <form onSubmit={formik.handleSubmit}>
                <Box className={styles.title}>
                  <InputLabel style={{ color: palette.primary.darkLightPurple, fontSize: '0.875rem' }}>
                    Title for Learners:{' '}
                  </InputLabel>
                  {!edit && (
                    <>
                      <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                        {formik.values.title}
                      </InputLabel>
                      <PenToSquare width="15" height="15" cursor="pointer" onClick={() => { setEdit(true); }} />
                    </>
                  )}
                  {edit && formik.values.title && (
                    <>
                      <TextField
                        id="title"
                        name="title"
                        type="text"
                        onChange={formik.handleChange}
                        value={formik.values.title}
                        onBlur={formik.handleBlur}
                        error={formik.touched.title && Boolean(formik.errors.title)}
                        data-cy="title"
                        helperText={formik.touched.title && formik.errors.title}
                        size="small"
                        InputProps={{ style: { borderRadius: '8px', width: '600px', height: '36px' } }}
                      />
                      <SendSaveButton
                        name="save button"
                        type="submit"
                        disabled={!formik.isValid || !formik.dirty}
                        label="save"
                        data-cy="save-button"
                      />
                    </>
                  )}
                </Box>
                {isCustomTagEnabled && (
                  <Button
                    style={{ float: 'right', marginTop: '-20px' }}
                    className={styles.customTag}
                    sx={{
                      backgroundColor: palette.primary.darkLightPurple,
                      color: palette.primary.white,
                      '&:hover': {
                        backgroundColor: palette.primary.darkLightPurple,
                      },
                    }}
                  >Custom
                  </Button>
                )}
              </form>
              <Typography mb={2}>{description}</Typography>

              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <CustomTableCell>Audience</CustomTableCell>
                      <CustomTableCell>Location</CustomTableCell>
                      <CustomTableCell>Duration</CustomTableCell>
                      <CustomTableCell>ID</CustomTableCell>
                      <CustomTableCell>Edition</CustomTableCell>
                      <CustomTableCell>Date Added</CustomTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <CustomTableCell>{audienceType[audience]}</CustomTableCell>
                      <CustomTableCell>{location}</CustomTableCell>
                      <CustomTableCell>{getDuration()}</CustomTableCell>
                      <CustomTableCell>{id}</CustomTableCell>
                      <CustomTableCell>{`${edition || '0'}.${version || '0'}.${build || '0'}`}</CustomTableCell>
                      <CustomTableCell>{dateAdded ? moment(dateAdded).format('MM/DD/YYYY') : ''}</CustomTableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
            <span className={styles.bottomDivide} style={{ border: palette.border.main }} />
            <ContentActions
              contentData={contentData}
              handleToggleBtn={handleToggleBtn}
            />
          </Box>
          <Box sx={{ padding: '0rem 2rem' }}>
            {lessons && (
              <ExpandableInfo
                key={`Lessons in this ${lessonsORPrograms.length > 1 ? 'version' : 'course'}`}
                isOpen={btnState.expandLessons || false}
                onClick={() => handleToggleBtn('expandLessons')}
                title={`Lessons in this ${lessonsORPrograms.length > 1 ? 'version' : 'course'}`}
                data-cy="Lessons"
              >
                <Divider sx={{ my: 1 }} />
                {lessons.map((lesson, index) => (
                  <LessonItem
                    key={lesson.id}
                    title={lesson?.accountLessonTitle || lesson?.title}
                    id={lesson.id}
                    time={lesson.requiredMinutes}
                    description={lesson.description}
                    lessons={lessons}
                    index={index}
                    mclType={mclType}
                    mclID={mclID}
                    contentId={contentId}
                  />
                ),
                )}
              </ExpandableInfo>
            )}
            {(contentType === 'Course Lesson') && (
              <LessonInCourses
                lessonCourses={lessonCourses}
                isCustomized={isCustomizedLesson}
              />
            )}
          </Box>
        </>
      )}
      {(btnState?.policyBtn || url.includes('policies')) && (
        <Policies
          contentData={contentData}
          handleToggleBtn={handleToggleBtn}
        />
      )}
      {(btnState?.configureBtn || url.includes('courseConfigure')) && (
      <CourseConfigure
        contentData={contentData}
        handleToggleBtn={handleToggleBtn}
      />
      )}
    </Paper>
  );
}

function LessonItem({ id, title, description, time, mclType, mclID, contentId }) {
  const { castingSeaBlue } = colors;
  const history = useHistory();

  return (
    <>
      <Box display="flex" key={id} justifyContent="space-between" style={{ padding: '0rem 1rem' }}>
        <Box pr={2}>
          <Typography variant="subtitle2" fontWeight="bold" onClick={() => { history.push(pushToCourseLessonDetails(mclType, mclID, contentId, id)); }}>
            <span style={{ color: castingSeaBlue, cursor: 'pointer' }}>{title}</span> – #{id}
          </Typography>
          <Typography variant="body2">
            {description}
          </Typography>
        </Box>
        <Typography variant="body2" minWidth={50}>
          {`${time || '0'} min`}
        </Typography>
      </Box>
      <Divider sx={{ my: 1 }} />
    </>
  );
}
