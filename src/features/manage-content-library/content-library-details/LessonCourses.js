import React from 'react';
import {
  Box,
  Typography,
  Stack,
} from '@mui/material';
import { useHistory } from 'react-router-dom';
import styles from './ContentLibraryDetailsContainer.module.css';
import { getCatalogPrograms, getListingPrograms, groupedListingORCatalogPrograms,
  sortByClientSpecific,
  sortedVersionsData } from '../../../hooks/useSortContentVersions';
import { colors } from '../../../theme/colors';
import { pushToContentLibraryDetails } from '../../navigation/Routes/AppRoutes';

const LessonInCourses = ({ lessonCourses, isCustomized }) => {
  const { castingSeaBlue } = colors;
  const history = useHistory();
  const catalogPrograms = sortedVersionsData(getCatalogPrograms(lessonCourses?.catalog || []), isCustomized);
  const listingPrograms = sortedVersionsData(getListingPrograms(lessonCourses?.listings || []), isCustomized);

  const groupedCatalogPrograms = sortByClientSpecific(groupedListingORCatalogPrograms(catalogPrograms) || []);
  const groupedListingPrograms = sortByClientSpecific(groupedListingORCatalogPrograms(listingPrograms) || []);

  const groupedListingANDCatalogPrograms = [...groupedCatalogPrograms, ...groupedListingPrograms];

  if (groupedListingANDCatalogPrograms.length === 0) {
    return null;
  }

  return (
    <Box sx={{ paddingTop: '2rem' }}>
      <Typography variant="body1" fontWeight="500" gutterBottom className={styles.lessonCourses}>
        This lesson is included in these course(s):
      </Typography>
      <Stack spacing={2}>
        {groupedListingANDCatalogPrograms.map((course) => (
          <Box key={course.id}>
            <Typography variant="subtitle1" fontWeight="600" sx={{ mb: 0.5 }}>
              {course.title}
            </Typography>
            <Stack spacing={0.5}>
              {course.programs.map(({ id, name }) => (
                <Typography
                  key={id}
                  variant="subtitle2"
                  fontWeight="500"
                  width="fit-content"
                  onClick={() => { history.push(pushToContentLibraryDetails(course.mclType, course.id, id)); }}
                >
                  <span style={{ color: castingSeaBlue, cursor: 'pointer' }}>{name}</span>
                </Typography>
              ))}
            </Stack>
          </Box>
        ))}
      </Stack>
    </Box>
  );
};

export default LessonInCourses;
