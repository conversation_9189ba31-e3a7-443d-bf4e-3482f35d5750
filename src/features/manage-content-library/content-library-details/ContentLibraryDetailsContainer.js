/* eslint-disable max-len */
import React from 'react';
import { useRouteMatch } from 'react-router-dom';
import CourseLessonDetails from './CourseLessonsDetails';
import ContentLibraryDetails from './ContentLibraryListDetails';

function ContentLibraryDetailsContainer() {
  const { params: { lessonId }, url } = useRouteMatch();
  const subdomains = url.split('/');
  const isLesson = subdomains.includes('lesson');

  if (isLesson && lessonId) {
    return (
      <CourseLessonDetails />
    );
  }
  return (
    <ContentLibraryDetails />
  );
}

export default ContentLibraryDetailsContainer;
