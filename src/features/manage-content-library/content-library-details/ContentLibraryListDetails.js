/* eslint-disable max-len */
import React, { useEffect, useState } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { Box, Typography, Chip, Grid, CardMedia, useTheme } from '@mui/material';
import { capitalize } from 'lodash';
import MicrolessonThumbnail from '../../../images/microlesson_thumbnail.png';
import ProgramThumbnail from '../../../images/program_thumbnail.png';
import Global from '../../../images/global.png';
import Canada from '../../../images/canada.png';
import US from '../../../images/US.png';
import India from '../../../images/india.png';
import ChevronIcon from '../../../icons/Chevron';
import styles from './ContentLibraryDetailsContainer.module.css';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import { colors } from '../../../theme/colors';
import { getContentDetails } from '../../../services/api/contentLibrary';
import ExpandableInfo from './ExpandableInfo';
import ContentDetails from './ContentDetails';
import { getListingLessonsORPrograms, sortedVersionsData } from '../../../hooks/useSortContentVersions';
import { useUser } from '../../../hooks/useUser';

function ContentLibraryDetails() {
  const { params: { mclType, mclID }, url } = useRouteMatch();
  const user = useUser();
  const { lightCylindricalBlue, brightPink, mainPurple } = colors;
  const { palette } = useTheme();
  const history = useHistory();
  const [contentDetails, setContentDetails] = useState({});
  const [btnState, setBtnState] = useState({});
  const { CONTENT_LIBRARY } = appRoutes;
  const fetchContentDetails = async () => {
    const response = await getContentDetails(mclType, mclID);
    if (response) setContentDetails(response);
  };

  useEffect(() => {
    fetchContentDetails();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mclID, mclType, url]);

  const pillarColors = {
    Respect: lightCylindricalBlue,
    Inclusion: brightPink,
    Ethics: mainPurple,
  };

  const Countries = {
    International: Global,
    'All Countries': Global,
    Canada,
    India,
    'United States of America': US,
  };

  const handleToggleBtn = (key) => {
    setBtnState((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const { filePath, title, type, instructionalType, description, lessons, concepts, indicators, isCustomTag, isClientSpecific,
    programs, countryName, pillars, listType, catalogItems, inActiveCatalogItems, edition: catalogItemEdition, audience, part, stateCode, stateName } = contentDetails;

  const listingLessonsORPrograms = listType === 'listing' && getListingLessonsORPrograms(catalogItems);
  const inActiveCatalogItemsLessonsORPrograms = listType === 'listing' && getListingLessonsORPrograms(inActiveCatalogItems, user.isScormAccount, true);
  const cProgramsLessons = (type === 'program' ? programs : lessons) || [];
  const catalogLessonsORPrograms = listType === 'catalog' && cProgramsLessons.map((item) => ({
    ...item,
    accountTitle: (item?.accountLessonTitle || item?.accountProgramName),
    dateAdded: (item?.lessonMappedDate || item?.programMappedDate),
    title: item?.name,
    type,
    edition: item?.edition,
    audience,
    part,
    stateCode,
    stateName,
    isCustomTag,
    listType,
    isClientSpecific,
  }));

  const versions = (listType === 'catalog' ? (catalogLessonsORPrograms.length) : (listingLessonsORPrograms.length)) || 0;
  // eslint-disable-next-line no-nested-ternary
  let contentType = type && (['program', 'course'].includes(type) ? 'Course' :
    type === 'lesson' ? 'Course Lesson' : capitalize(type));
  contentType = (listType === 'catalog' && instructionalType === 'diagnostic') ? capitalize(instructionalType) : contentType;
  const lessonsORPrograms = (listType === 'catalog' ? (catalogLessonsORPrograms) : (listingLessonsORPrograms)) || [];
  const inActiveLessonsORPrograms = (listType === 'listing' ? inActiveCatalogItemsLessonsORPrograms : []) || [];
  const catalogThumbnail = instructionalType === 'course' ? ProgramThumbnail : MicrolessonThumbnail;
  const listingThumbnail = ['program', 'course'].includes(type) ? ProgramThumbnail : MicrolessonThumbnail;
  const file = filePath || (listType === 'catalog' ? (lessonsORPrograms[0]?.filePath || catalogThumbnail) : listingThumbnail);
  // const isPWHUS = listType === 'listing' && mclID === 21;
  const listingEdition = catalogItems && Math.max(...catalogItems?.map((item) => item.edition));
  const catalogEdition = (listType === 'catalog' && instructionalType === 'course') ? catalogItemEdition : 0;
  const highestEdition = listType === 'catalog' ? catalogEdition : listingEdition;

  const conceptsArr = concepts?.map((c) => c.concept).sort() || [];
  const cultureSkillsArr = indicators?.map((p) => p.name).sort() || [];

  function getOrdinalSuffix(n) {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    if (n > 1) {
      return `${n + (s[(v - 20) % 10] || s[v] || s[0])} Edition`;
    }
  }
  const activeSortedVersions = sortedVersionsData(lessonsORPrograms, isCustomTag);
  const inActiveSortedVersions = sortedVersionsData(inActiveLessonsORPrograms, isCustomTag, true);

  return (
    <Box
      className={styles.mainContainer}
      style={{ background: palette.background.default, color: palette.primary.main }}
    >
      <Box className={styles.breadcrumb} onClick={() => history.push(CONTENT_LIBRARY)}>
        <ChevronIcon
          key="icon}"
          direction="left"
          width="0.8rem"
          height="0.8rem"
          color={palette.primary.main}
          style={{ marginRight: '0.25rem' }}
          role="img"
          aria-label="chevron right"
          aria-hidden={false}
        />
        <strong>Content Library</strong>
      </Box>
      <Grid container sx={{ padding: '1rem 0rem', columnGap: '1rem' }} alignItems="flex-start">
        <Grid>
          <CardMedia
            component="img"
            image={file}
            className={styles.cardImage}
            sx={{ width: '100%' }}
          />
        </Grid>
        <Grid item xs={12} md={8}>
          <Typography variant="title">{contentType}</Typography>
          <Box style={{ display: 'flex', gap: '0.5rem', alignItems: 'baseline' }}>
            <Typography fontWeight="bold" fontSize="1.75rem">{title}&nbsp;</Typography>
            {highestEdition > 1 && (
            <>
              <span className={styles.divide} style={{ border: palette.border.main }} />
              <Typography fontSize="0.875rem">&nbsp;{getOrdinalSuffix(Number(highestEdition))}</Typography>
            </>
            )}
          </Box>

          {pillars && (
          <Box className={styles.bottomSection2}>
            {countryName && (<img src={Countries[countryName]} alt="country" width={24} height={24} />)}
            <Box className={styles.pillarsList}>
              {pillars.map(({ name }) => (
                <Chip
                  key={`pillar-${name}`}
                  label={name}
                  className={styles.pillar}
                  sx={{
                    borderColor: pillarColors[name],
                    color: palette.primary.darkByzantinePurple,
                  }}
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>
          )}
        </Grid>
      </Grid>
      <Typography variant="body2">
        {description}
      </Typography>
      <ExpandableInfo
        key="More Info"
        isOpen={btnState.moreInfo || false}
        onClick={() => handleToggleBtn('moreInfo')}
        title="More Info"
        data-cy="More Info"
        type="More Info"
      >
        <Box>
          <Box style={{ display: 'flex', flexDirection: 'column', marginBottom: '.5rem' }}>
            <Typography fontWeight="bold">Concepts</Typography>
            <Typography variant="body2" fontWeight="500">
              {conceptsArr?.length ? conceptsArr.join(', ') : ''}
            </Typography>
          </Box>
          <Box style={{ display: 'flex', flexDirection: 'column', marginBottom: '.5rem' }}>
            <Typography variant="title" fontWeight="bold">Culture Skills</Typography>
            <Typography variant="body2" fontWeight="500">
              {cultureSkillsArr?.length ? cultureSkillsArr.join(', ') : ''}
            </Typography>
          </Box>
        </Box>
      </ExpandableInfo>
      {lessonsORPrograms.length >= 1 && (
        <ContentDetails
          lessonsORPrograms={lessonsORPrograms}
          contentType={contentType}
          mclType={listType}
          versions={versions}
          contentDetails={contentDetails}
          activeSortedVersions={activeSortedVersions}
          inActiveSortedVersions={inActiveSortedVersions}
          onSaveUpdate={fetchContentDetails}
        />
      )}
    </Box>
  );
}

export default ContentLibraryDetails;
