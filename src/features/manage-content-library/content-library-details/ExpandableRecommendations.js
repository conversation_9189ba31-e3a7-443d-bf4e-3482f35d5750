import { Paper, Typography } from '@mui/material';
import React from 'react';
import ChevronIcon from '../../../icons/Chevron';
import styles from './ExpandableInfo.module.css';
import PwhHeaderLogo from '../../../images/pwh-mcl-help-logo.svg';

export default function ExpandableRecommendations(props) {
  const { isOpen, onClick, title, children } = props;

  return (
    <Paper sx={{ marginTop: '1.5rem', border: '1px solid #cbccd9' }}>
      <h2 className={styles.expandableInfoH2}>
        <Typography
          component="div"
          onClick={onClick}
          className={`${styles.expandRecommendationHeader} accordion-trigger`}
          sx={{ backgroundColor: '#eef2fc !important' }}
          aria-expanded={isOpen}
          aria-disabled={isOpen}
          aria-controls={`${props['data-cy']}`}
          id={`${props['data-cy']}-button`}
          data-cy={props['data-cy']}
        >
          <Typography gutterBottom className={styles.Header}>
            <PwhHeaderLogo style={{ width: 28, height: 28 }} />
            {title}
          </Typography>
          <div aria-hidden className={styles.chevron}>
            <ChevronIcon className={styles.chevronIcon} direction={isOpen ? 'up' : 'down'} />
          </div>
        </Typography>
      </h2>
      {isOpen && children}
    </Paper>
  );
}
