/* eslint-disable max-len */
import React, { useEffect, useState } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Button,
  TableHead,
  useTheme,
  InputLabel,
  TextField,
  styled,
} from '@mui/material';
import moment from 'moment';
import { useFormik } from 'formik';
import styles from './ContentLibraryDetailsContainer.module.css';
import SendSaveButton from '../../../components/Button/SendSaveButton';
import PenToSquare from '../../../images/penToSquare.svg';
import ChevronIcon from '../../../icons/Chevron';
import { appRoutes, pushToContentLibraryDetails } from '../../navigation/Routes/AppRoutes';
import { getContentDetails, patchContentConfiguration } from '../../../services/api/contentLibrary';
import ContentActions from './ContentActions';
import LessonInCourses from './LessonCourses';
import Spinner from '../../../components/Spinner/Spinner';

function CourseLessonDetails() {
  const { params: { mclID, mclType, contentId, lessonId } } = useRouteMatch();
  const { palette } = useTheme();
  const history = useHistory();
  const [courseLessonDetails, setCourseLessonDetails] = useState({});
  const { CONTENT_LIBRARY } = appRoutes;
  const [edit, setEdit] = useState(false);
  const [loader, setLoader] = useState(true);

  const CustomTableCell = styled(TableCell)(() => ({
    border: '1px solid rgba(224, 224, 224, 1)',
    textAlign: 'center',
    '&.MuiTableCell-body': {
      fontWeight: 'bold',
    },
  }));

  const fetchCourseLessonDetails = async () => {
    const response = await getContentDetails('lesson', lessonId);
    setLoader(false);
    if (response) {
      setCourseLessonDetails({ ...response, type: 'lesson' });
    }
  };

  useEffect(() => {
    fetchCourseLessonDetails();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lessonId]);

  const { id, title, description, build, version, edition, resource,
    requiredMinutes, isCustomized, programs, accountLessons } = courseLessonDetails;
  const getDuration = () => {
    return requiredMinutes !== null ? `${requiredMinutes} min` : '';
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      title: (((accountLessons && accountLessons[0]?.title) || title) || ''),
    },
    onSubmit,
  });

  async function onSubmit(values) {
    await patchContentConfiguration(lessonId, 'lesson', values).then(() => fetchCourseLessonDetails());
    setEdit(false);
  }

  const onNavigationBack = () => {
    if (mclID && mclType) {
      history.push(pushToContentLibraryDetails(mclType, mclID, contentId));
    } else {
      history.push(CONTENT_LIBRARY);
    }
  };

  const breadcrumbList = (mclType === 'listing' ? programs?.listings : [(programs?.catalog[0]?.catalogItem || [])]) || [];
  const breadcrumb = mclType && mclID ? breadcrumbList.filter((obj) => obj.id === Number(mclID))[0]?.title : 'Content Library';

  if (loader) {
    return <Spinner customContainerHeight="90vh" />;
  }

  return (
    <Box
      className={styles.mainContainer}
      style={{ background: palette.background.default, color: palette.primary.main }}
    >
      <Box className={styles.breadcrumb} onClick={() => onNavigationBack()}>
        <ChevronIcon
          key="icon}"
          direction="left"
          width="0.8rem"
          height="0.8rem"
          color={palette.primary.main}
          style={{ marginRight: '0.25rem' }}
          role="img"
          aria-label="chevron right"
          aria-hidden={false}
        />
        <strong>{breadcrumb}</strong>
      </Box>
      <Box className={styles.courseLessonTitle}>
        <form onSubmit={formik.handleSubmit}>
          <Box className={styles.title}>
            <InputLabel style={{ color: palette.primary.darkLightPurple, fontSize: '0.875rem' }}>
              Lesson Title:{' '}
            </InputLabel>
            {!edit && (
              <>
                <InputLabel className={styles.label} style={{ color: palette.primary.darkLightPurple }}>
                  {formik.values.title}
                </InputLabel>
                <PenToSquare width="15" height="15" cursor="pointer" onClick={() => { setEdit(true); }} />
              </>
            )}
            {edit && formik.values.title && (
              <>
                <TextField
                  id="title"
                  name="title"
                  type="text"
                  onChange={formik.handleChange}
                  value={formik.values.title}
                  onBlur={formik.handleBlur}
                  error={formik.touched.title && Boolean(formik.errors.title)}
                  data-cy="title"
                  helperText={formik.touched.title && formik.errors.title}
                  size="small"
                  InputProps={{ style: { borderRadius: '8px', width: '600px', height: '36px' } }}
                />
                <SendSaveButton
                  name="save button"
                  type="submit"
                  disabled={!formik.isValid || !formik.dirty}
                  label="save"
                  data-cy="save-button"
                />
              </>
            )}
          </Box>
          {isCustomized && (
            <Button
              style={{ float: 'right', marginTop: '-20px' }}
              className={styles.customTag}
              sx={{
                backgroundColor: palette.primary.darkLightPurple,
                color: palette.primary.white,
                '&:hover': {
                  backgroundColor: palette.primary.darkLightPurple,
                },
              }}
            >Custom
            </Button>
          )}
        </form>
      </Box>
      <Box className={styles.courseDetailsSection}>
        <Box>
          <Box className={styles.lessonDescription}>
            <InputLabel style={{ color: palette.primary.darkLightPurple, fontSize: '0.875rem' }}>
              Lesson Description:{' '} {description}
            </InputLabel>
          </Box>
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <CustomTableCell>Duration</CustomTableCell>
                  <CustomTableCell>ID</CustomTableCell>
                  <CustomTableCell>Edition</CustomTableCell>
                  <CustomTableCell>Date Added</CustomTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <CustomTableCell>{getDuration()}</CustomTableCell>
                  <CustomTableCell>{id}</CustomTableCell>
                  <CustomTableCell>{`${edition || '0'}.${version || '0'}.${build || '0'}`}</CustomTableCell>
                  <CustomTableCell>{resource?.resourceBundles[0]?.createdAt ? moment(resource?.resourceBundles[0]?.createdAt).format('MM/DD/YYYY') : ''}</CustomTableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
        <span className={styles.bottomDivide} style={{ border: palette.border.main }} />
        <ContentActions
          contentData={[courseLessonDetails]}
        />
      </Box>
      <LessonInCourses
        lessonCourses={programs}
        isCustomized={isCustomized}
      />
    </Box>
  );
}

export default CourseLessonDetails;
