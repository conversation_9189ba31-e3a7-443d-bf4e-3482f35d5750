/* eslint-disable no-param-reassign */
/* eslint-disable array-callback-return */
/* eslint-disable max-len */
import React, { useEffect, useState } from 'react';

import { Box, Typography, useTheme } from '@mui/material';

import { getContentPolicies } from '../../../services/api/contentLibrary';
import PolicyCardDetails from '../../../components/PoliciesCards/PolicyCardDetails';
import ChevronIcon from '../../../icons/Chevron';
import PolicyInfo from '../../../images/policy_info_icon.svg';

export default function Policies({ contentData, handleToggleBtn }) {
  const { palette } = useTheme();
  const { id, type } = contentData[0] || {};
  const [data, setData] = useState({});
  const [programLessons, setProgramLessons] = useState([]);
  const fetchPolicyDetails = async () => {
    const response = await getContentPolicies(id, type);

    if (response) {
      if (type === 'program') {
        setProgramLessons(response?.lessons);
      } else {
        setData(response);
      }
    }
  };

  useEffect(() => {
    fetchPolicyDetails();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  function getUploadedFileName(path) {
    if (!path) return false;

    const match = path.match(/\/[0-9a-fA-F-]{36}_(.+)$/);
    const fileName = match ? match[1] : '';

    return fileName.endsWith('Policy-Placeholder.pdf');
  }

  // eslint-disable-next-line no-shadow
  function getPolicyLinkOrFilePath(sumOfConfiguredPolicie, policy) {
    // eslint-disable-next-line no-shadow
    const { id, list1, accountLessonCards = [], images = [] } = policy;
    if (accountLessonCards && accountLessonCards.length > 0) {
      const match = accountLessonCards.find((card) => card.lessonCardId === id);
      if (match) {
        if (match.policyType === 'link' && match.link.length) {
          // eslint-disable-next-line no-param-reassign
          sumOfConfiguredPolicie += 1;
        }

        if (match.policyType === 'file' && match?.fileId && match?.file?.path.length) {
          sumOfConfiguredPolicie += 1;
        }
      }
    } else if (
      list1 === 'View Document' &&
        Array.isArray(images) &&
        images.length > 0
    ) {
      const imageMatch = images.find((img) => img.fileableId === id);
      if (imageMatch?.path && imageMatch?.path.length && !getUploadedFileName(imageMatch.path)) {
        sumOfConfiguredPolicie += 1;
      }
    }

    return sumOfConfiguredPolicie;
  }

  function countConfiguredPolicies(policies = []) {
    let totalConfiguredPolicies = 0;
    policies.forEach((policy) => {
      totalConfiguredPolicies = getPolicyLinkOrFilePath(
        totalConfiguredPolicies,
        policy,
      );
    });
    return totalConfiguredPolicies;
  }

  const getPolicyCardsInfo = (pData) => {
    const totalConfiguredPolicies = countConfiguredPolicies(pData.policies);
    return `(${totalConfiguredPolicies}/${pData?.policies && pData?.policies.length} Added)`;
  };

  const getProgramPolicyCardsInfo = (pData) => {
    let totalPoliciesCards = 0;
    pData?.map((pItem) => {
      totalPoliciesCards += pItem?.policyLessonCards.length;
    });
    const totalConfiguredPolicies = countConfiguredPolicies(
      pData.flatMap((lessonData) => lessonData.policyLessonCards),
    );
    return `(${totalConfiguredPolicies}/${totalPoliciesCards} Added)`;
  };

  // eslint-disable-next-line no-shadow
  function isPolicyPresent(policy) {
    // eslint-disable-next-line no-shadow
    const { id, list1, cardType, accountLessonCards = [], images = [] } = policy;

    if (cardType !== 'policyAcknowledgement') return null;

    if (accountLessonCards && accountLessonCards.length > 0) {
      const match = accountLessonCards.find((card) => card.lessonCardId === id);
      if (match) {
        if (match.policyType === 'link' && match.link?.length) {
          return true;
        }

        if (match.policyType === 'file' && match.file?.path?.length) {
          return true;
        }

        return false;
      }
    } else {
      if (list1 === 'View Document') {
        const imageMatch = Array.isArray(images)
          ? images.find((img) => img.fileableId === id)
          : null;

        if (imageMatch?.path?.length && !getUploadedFileName(imageMatch.path)) {
          return true;
        }

        return false;
      }
      return false;
    }
  }

  return (
    <>
      {data && (
        <Box
          key={data?.id || ''}
          sx={{
            padding: '1rem 2rem',
          }}
        >

          <a
            href="#"
            onClick={(e) => {
              e.preventDefault(); // Prevent default anchor behavior
              handleToggleBtn();
            }}
            style={{
              cursor: 'pointer',
              fontSize: '19px',
              fontStyle: 'normal',
              color: '#222444', // Missing "#" in original color
              lineHeight: '16px',
              textDecoration: 'none',
              fontWeight: 'bold',
            }}
          >
            <ChevronIcon
              key="icon"
              direction="left"
              width="0.8rem"
              height="0.8rem"
              color={palette.primary.main}
              style={{ marginBottom: '2px' }}
              role="img"
              aria-label="chevron right"
              aria-hidden={false}
            />Back
          </a>
          <Typography
            fontSize="1.05rem"
            sx={{ pb: '10px', mt: '10px', color: '#222444', fontStyle: 'normal', paddingBottom: 2, lineHeight: '20px' }}
          >
            <Box component="span" fontWeight="bold">
              Policies
            </Box>{' '}
            <Box component="span" fontWeight="normal">
              {type === 'program' ? getProgramPolicyCardsInfo(programLessons) : getPolicyCardsInfo(data)}
            </Box>
          </Typography>
          {type &&
            type !== 'program' &&
            data?.policies?.map((item) => {
              return (
                <Box key={`lessonBox-${item.id}`} id={`lessonBox-${item.id}`} sx={{ pb: 2 }}>
                  <Box
                    sx={{
                      border: '1px solid #ccc',
                      borderRadius: 1,
                      overflow: 'hidden',
                      boxShadow: 0,
                    }}
                  >
                    <Box
                      sx={{
                        height: '45px',
                        backgroundColor: '#f2f2f5',
                        px: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Typography
                        component="span"
                        sx={{ fontSize: '17px', fontStyle: 'normal', color: '#222444', lineHeight: '20px' }}
                      >
                        Lesson:{' '}
                        <strong>
                          {' '}
                          {data?.title || ''} - #{data?.id || ''}{' '}
                        </strong>{' '}
                      </Typography>
                      {!isPolicyPresent(item) && <PolicyInfo />}
                    </Box>
                    <PolicyCardDetails
                      type={type}
                      key={`policyCard-${item.id}`}
                      cardData={item}
                      onSavePolicies={fetchPolicyDetails}
                      lessonId={data.id}
                      isPolicyPresent={isPolicyPresent(item)}
                    />
                  </Box>
                </Box>
              );
            })}
          {programLessons?.length > 0 &&
            programLessons.map((item) => (
              item.policyLessonCards?.map((card, index) => {
                return (
                  <Box
                    id={`programBox-${item.id}`}
                    key={`policyCardBox-${card.id || index}`}
                    sx={{
                      border: '1px solid #ccc',
                      borderRadius: 1,
                      overflow: 'hidden',
                      boxShadow: 0,
                      mb: 2,
                    }}
                  >
                    <Box
                      sx={{
                        height: '45px',
                        backgroundColor: '#f2f2f5',
                        px: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Typography
                        component="span"
                        sx={{ fontSize: '17px', fontStyle: 'normal', color: '#222444', lineHeight: '20px' }}
                      >
                        Lesson:{' '}
                        <strong>
                          {item?.title || ''} - #{item?.id || ''}
                        </strong>
                      </Typography>
                      {!isPolicyPresent(card) && <PolicyInfo />}
                    </Box>

                    <PolicyCardDetails
                      id={`policyCard-${card.id || index}`}
                      type={type}
                      key={`policyCard-${card.id || index}`}
                      cardData={card}
                      onSavePolicies={fetchPolicyDetails}
                      lessonId={item.id}
                      isPolicyPresent={isPolicyPresent(card)}
                    />
                  </Box>
                );
              })
            ))}
        </Box>
      )}
    </>
  );
}
