import React from 'react';
import {
  MenuItem,
  Select,
  Box,
  useTheme,
} from '@mui/material';
import { NestedMenuItem } from 'mui-nested-menu';
import { styled } from '@mui/material/styles';
import { useUser } from '../../../hooks/useUser';

export default function ContentVersionDropDown({ activeSortedVersions,
  inActiveSortedVersions, handleSelect, contentData, isPWHUS = false, selectedContentId }) {

  const CustomNestedMenuItem = styled(({ isSelected, ...props }) => {
    return <NestedMenuItem {...props} />;
  })(({ theme, isSelected }) => {

    return {
      '&:hover': {
        backgroundColor: `${theme.palette.background.highlighted} !important`,
      },
      backgroundColor: isSelected
        ? theme.palette.background.highlighted
        : 'transparent !important',
      borderTop: '0.5px solid rgba(0, 0, 0, 0.12)',
      '&.Mui-disabled': {
        height: '40px',
        opacity: '1 !important',
        backgroundColor: isSelected
          ? theme.palette.background.highlighted
          : 'transparent !important',
        padding: '0.3rem 0.2rem !important',
        borderTop: '0.5px solid grey',
        pointerEvents: 'hover',
      },
    };
  });
  

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [value, setValue] = React.useState(contentData[0] || {});
  const open = Boolean(anchorEl);
  const { palette } = useTheme();
  const user = useUser();
  const selectRef = React.useRef(null);

  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  // Enhanced click-away detection for left navigation and other areas
  React.useEffect(() => {
    if (!open) return;

    const handleClickOutside = (event) => {
      if (!event.target) return;

      const isInsideSelect = selectRef.current?.contains(event.target);
      const isInsideMenu =
        event.target.closest('.MuiPaper-root') ||
        event.target.closest('.MuiMenu-root') ||
        event.target.closest('.MuiSelect-root') ||
        event.target.closest('[role="listbox"]') ||
        event.target.closest('[role="option"]');

      if (!isInsideSelect && !isInsideMenu) {
        handleClose();
      }
    };

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    // Delay to avoid the opening click triggering close
    const rafId = requestAnimationFrame(() => {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    });

    return () => {
      cancelAnimationFrame(rafId);
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [open]);  

  const setSelectValue = (item) => {
    setValue(item);
    handleClose();
    handleSelect(item);
  };

  let extraCustomItems = [];
  let extraEditionItems = [];
  let mainDropdownItems = [];
  if (isPWHUS) {
    const assignedIds = new Set();

    mainDropdownItems = activeSortedVersions.filter(p => {
      if (p.isTimed === 1) {
        assignedIds.add(p.id);
        return true;
      }
      return false;
    });

    extraCustomItems = activeSortedVersions.filter(p => {
      if (p.isCustomized === 1 && !assignedIds.has(p.id)) {
        assignedIds.add(p.id);
        return true;
      }
      return false;
    });

    const getMoreVersions = () => {
      const nonCustomNonTimed = activeSortedVersions.filter(p =>
        p.isCustomized === 0 &&
        p.isTimed !== 1 &&
        !assignedIds.has(p.id)
      );

      const condensed = nonCustomNonTimed.filter(p => p.pwhUsOrder !== null);
      const nonCondensed = nonCustomNonTimed.filter(p => p.pwhUsOrder === null);

      return [...condensed, ...nonCondensed];
    };

    extraEditionItems = getMoreVersions();
  } else {
    mainDropdownItems = activeSortedVersions;
  }
  return (
    <Box ref={selectRef}>
      <Select
        open={open}
        value={value?.id || ''}
        onOpen={handleClick}
        onClose={handleClose}
        displayEmpty
        onChange={(e) => {
          const selectedId = Number(e.target.value);
          const allItems = [...mainDropdownItems, ...inActiveSortedVersions, ...extraCustomItems, ...extraEditionItems];
          const selectedItem = allItems.find((item) => item.id === selectedId);
          if (selectedItem) setSelectValue(selectedItem);
        }}
        renderValue={() => value?.title || 'Select'}
        sx={{
          height: '40px',
          minWidth: '389px',
          textAlign: 'left',
          background: 'white',
          justifyContent: 'space-between',
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              marginTop: '-0.5rem !important',
              maxHeight: '250px',
              overflowY: 'auto',
              background: 'white',
            },
          },
        }}
      >
        {mainDropdownItems.map((item) => (
          <MenuItem
            key={item.id}
            value={item.id}
            selected={value?.id === item?.id}
            sx={{
              fontWeight: value?.id === item?.id ? '600' : 'normal',
              backgroundColor: value?.id === item?.id ? `${palette.background.ghostWhite} !important` : 'inherit',
              '&:hover': { backgroundColor: `${palette.background.highlighted} !important` },
            }}
          >
            {item.title}
          </MenuItem>
        ))}
        {extraCustomItems.length > 0 && (
          <CustomNestedMenuItem
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: '250px',
                  overflowY: 'auto',
                  background: 'white',
                },
              } }}
            label={'More Custom Versions'}
            parentMenuOpen={open}
            isSelected = {extraCustomItems.filter(item => item.id === parseInt(selectedContentId)).length > 0}
          >
            {extraCustomItems.map((item) => (
              <MenuItem
                key={item.id}
                value={item.id}
                onClick={() => setSelectValue(item)}
                selected={value?.id === item.id}
                sx={{
                  fontWeight: value?.id === item.id ? '600' : 'normal',
                  backgroundColor: value?.id === item.id ? `${palette.background.ghostWhite} !important` : 'inherit',
                  '&:hover': { backgroundColor: `${palette.background.highlighted} !important` },
                }}
              >
                {item.title}
              </MenuItem>
            ))}
          </CustomNestedMenuItem>
        )}
        {extraEditionItems.length > 0 && (
          <CustomNestedMenuItem
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: '250px',
                  overflowY: 'auto',
                  background: 'white',
                },
              } }}
            label={extraCustomItems.length ? 'More Standard Versions' : 'More Versions'}
            parentMenuOpen={open}
            isSelected = {extraEditionItems.filter(item => item.id === parseInt(selectedContentId)).length > 0}
          >
            {extraEditionItems.map((item) => (
              <MenuItem
                key={item.id}
                value={item.id}
                onClick={() => setSelectValue(item)}
                selected={value?.id === item.id}
                sx={{
                  fontWeight: value?.id === item.id ? '600' : 'normal',
                  backgroundColor: value?.id === item.id ? `${palette.background.ghostWhite} !important` : 'inherit',
                  '&:hover': { backgroundColor: `${palette.background.highlighted} !important` },
                }}
              >
                {item.title}
              </MenuItem>
            ))}
          </CustomNestedMenuItem>
        )}
        {inActiveSortedVersions.length > 0 && (
          <CustomNestedMenuItem
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: '250px',
                  overflowY: 'auto',
                  background: 'white',
                },
              } }}
            label={`Prior ${user.isScormAccount ? 'Downloaded' : 'Deployed'} Editions`}
            parentMenuOpen={open}
            isSelected = {inActiveSortedVersions.filter(item => item.id === parseInt(selectedContentId)).length > 0}
          >
            {inActiveSortedVersions.map((item) => (
              <MenuItem
                key={item.id}
                value={item.id}
                onClick={() => setSelectValue(item)}
                selected={value?.id === item.id}
                sx={{
                  fontWeight: value?.id === item.id ? '600' : 'normal',
                  backgroundColor: value?.id === item.id ? `${palette.background.ghostWhite} !important` : 'inherit',
                  '&:hover': { backgroundColor: `${palette.background.highlighted} !important` },
                }}
              >
                {item.title}
              </MenuItem>
            ))}
          </CustomNestedMenuItem>
        )}
      </Select>
    </Box>
  );
}
