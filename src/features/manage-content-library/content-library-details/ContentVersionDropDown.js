import React from 'react';
import {
  MenuItem,
  Select,
  Box,
  useTheme,
} from '@mui/material';
import { NestedMenuItem } from 'mui-nested-menu';
import { styled } from '@mui/material/styles';
import { useUser } from '../../../hooks/useUser';

export default function ContentVersionDropDown({ activeSortedVersions,
  inActiveSortedVersions, handleSelect, contentData }) {
  const CustomNestedMenuItem = styled(({ title, ...props }) => <NestedMenuItem {...props} />)(({ theme, title }) => ({
    '&:hover': { backgroundColor: `${theme.palette.background.highlighted} !important` },
    backgroundColor: title.includes('Edited') ? theme.palette.background.highlighted : 'transparent !important',
    borderTop: '0.5px solid rgba(0, 0, 0, 0.12)',
    '&.Mui-disabled': {
      height: '40px',
      opacity: '1 !important',
      backgroundColor: title.includes('Edited') ? theme.palette.background.highlighted : 'transparent !important',
      padding: '0.3rem 0.2rem !important',
      borderTop: '0.5px solid grey',
      pointerEvents: 'hover',
    },
  }));

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [value, setValue] = React.useState(contentData[0] || {});
  const open = Boolean(anchorEl);
  const { palette } = useTheme();
  const user = useUser();

  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const setSelectValue = (item) => {
    setValue(item);
    handleClose();
    handleSelect(item);
  };

  return (
    <Box>
      <Select
        open={open}
        value={value?.id || ''}
        onOpen={handleClick}
        onClose={handleClose}
        displayEmpty
        onChange={(e) => {
          const selectedId = Number(e.target.value);
          const allItems = [...activeSortedVersions, ...inActiveSortedVersions];
          const selectedItem = allItems.find((item) => item.id === selectedId);
          if (selectedItem) setSelectValue(selectedItem);
        }}
        renderValue={() => value?.title || 'Select'}
        sx={{
          height: '40px',
          minWidth: '389px',
          textAlign: 'left',
          background: 'white',
          justifyContent: 'space-between',
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              marginTop: '-0.5rem !important',
              maxHeight: '250px',
              overflowY: 'auto',
              background: 'white',
            },
          },
        }}
      >
        {activeSortedVersions.map((item) => (
          <MenuItem
            key={item.id}
            value={item.id}
            selected={value?.id === item?.id}
            sx={{
              fontWeight: value?.id === item?.id ? '600' : 'normal',
              backgroundColor: value?.id === item?.id ? `${palette.background.ghostWhite} !important` : 'inherit',
              '&:hover': { backgroundColor: `${palette.background.highlighted} !important` },
            }}
          >
            {item.title}
          </MenuItem>
        ))}

        {inActiveSortedVersions.length > 0 && (
          <CustomNestedMenuItem
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: '250px',
                  overflowY: 'auto',
                  background: 'white',
                },
              } }}
            label={`Prior ${user.isScormAccount ? 'Downloaded' : 'Deployed'} Editions`}
            parentMenuOpen={open}
            title={contentData[0]?.title}
          >
            {inActiveSortedVersions.map((item) => (
              <MenuItem
                key={item.id}
                value={item.id}
                onClick={() => setSelectValue(item)}
                selected={value?.id === item.id}
                sx={{
                  fontWeight: value?.id === item.id ? '600' : 'normal',
                  backgroundColor: value?.id === item.id ? `${palette.background.ghostWhite} !important` : 'inherit',
                  '&:hover': { backgroundColor: `${palette.background.highlighted} !important` },
                }}
              >
                {item.title}
              </MenuItem>
            ))}
          </CustomNestedMenuItem>
        )}
      </Select>
    </Box>
  );
}
