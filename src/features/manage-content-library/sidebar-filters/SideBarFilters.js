import React, { useState } from 'react';
import { Switch, Typography, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import FilterListIcon from '@mui/icons-material/FilterList';
import { get } from 'lodash';
import ExpandableFilterCard from '../../../components/ExpandableFilterCard/ExpandableFilterCard';
import styles from './SideBarFilters.module.css';
import SocialCapitals from './SocialCapitals';
import { useContentLibrary } from '../../../hooks/useContentLibrary';
import SimpleFilters from './SimpleFilters';
import Concepts from './Concepts';

const initialBtnState = {
  trainingBtn: false,
  locationBtn: false,
  audienceBtn: false,
  indicatorBtn: false,
  conceptsBtn: false,
};

const trainingsData = [
  { key: 'course', value: 'Course' },
  { key: 'microlesson', value: 'Microlesson' },
  { key: 'diagnostic', value: 'Diagnostic' },
  { key: 'lesson', value: 'Course Lesson' },
];

const audienceData = [
  { key: 'all', value: 'All Employees' },
  { key: 'manager', value: 'Managers' },
  { key: 'employee', value: 'Non-Managers' },
];

const locationsData = [
  { key: 'All Countries', value: 'All Countries' },
  { key: 'United States of America', value: 'USA' },
  { key: 'Canada', value: 'Canada' },
  { key: 'India', value: 'India' },
  { key: 'International', value: 'International' },
];

export default function SideBarFilters() {
  const { palette } = useTheme();
  const [btnState, setBtnState] = useState(initialBtnState);
  const [isExpand, setIsExpand] = useState(false);
  const useStyles = makeStyles(() => ({
    scrollBar: {
      overflowY: 'auto',
      padding: '0 0.5rem 0.5rem 0',
      height: '98vh',
      '&::-webkit-scrollbar': { width: 0 },
      '&::-webkit-scrollbar-thumb': { backgroundColor: palette.background.secondary },
      '&::-webkit-scrollbar-track': { backgroundColor: palette.background.primary },
    },
  }));
  const classes = useStyles();
  const { tiles, contentFilters, filters: { audience, location, trainingType, isCustomized } } = useContentLibrary();
  const isCustomTrainings = get(tiles, 'isCustomTrainings', 0);

  const handleToggleBtn = (key) => {
    const newBtnState = { ...btnState, [key]: !btnState[key] };
    const IsExpandCollapse = Object.values(newBtnState).every((value) => value === true);
    setIsExpand(IsExpandCollapse);
    setBtnState(newBtnState);
  };

  const handleExpandORCollapse = () => {
    const expandBtnState = Object.keys(btnState).reduce((acc, key) => {
      acc[key] = !isExpand;
      return acc;
    }, {});
    setIsExpand((prev) => (!prev));
    setBtnState(expandBtnState);
  };

  const handleIsCustomized = () => {
    const selectedPillObject = { key: !isCustomized, type: 'isCustomized', value: 'Customized' };
    contentFilters('isCustomized', !isCustomized, selectedPillObject);
  };

  return (
    <div className={styles.container}>
      <div className={styles.expandFilters} onClick={() => handleExpandORCollapse()}>
        <FilterListIcon className={styles.iconStyle} />
        <Typography
          sx={{
            '&:hover': {
              fontWeight: 'bold',
            } }}
        >{!isExpand ? 'Expand' : 'Collapse'} all filters
        </Typography>
      </div>
      <div className={classes.scrollBar}>
        <div id="userMenuAccordionGroup">
          <ExpandableFilterCard
            isOpen={btnState.trainingBtn}
            onClick={() => handleToggleBtn('trainingBtn')}
            title="Training Type"
            data-cy="trainingType"
          >
            <SimpleFilters
              filterOptions={trainingsData}
              filteredData={trainingType}
              type="trainingType"
            />
          </ExpandableFilterCard>
          <ExpandableFilterCard
            isOpen={btnState.locationBtn}
            onClick={() => handleToggleBtn('locationBtn')}
            title="Location"
            data-cy="location"
          >
            <SimpleFilters
              filterOptions={locationsData}
              filteredData={location}
              type="location"
            />
          </ExpandableFilterCard>
          <ExpandableFilterCard
            isOpen={btnState.audienceBtn}
            onClick={() => handleToggleBtn('audienceBtn')}
            title="Audience"
            data-cy="audience"
          >
            <SimpleFilters
              filterOptions={audienceData}
              filteredData={audience}
              type="audience"
            />
          </ExpandableFilterCard>
          <ExpandableFilterCard
            isOpen={btnState.conceptsBtn}
            onClick={() => handleToggleBtn('conceptsBtn')}
            title="Concepts"
            data-cy="concepts"
          >
            <Concepts />
          </ExpandableFilterCard>
          <ExpandableFilterCard
            isOpen={btnState.indicatorBtn}
            onClick={() => handleToggleBtn('indicatorBtn')}
            title="Culture Skill"
            data-cy="socialIndicators"
          >
            <SocialCapitals />
          </ExpandableFilterCard>
          {isCustomTrainings > 0 && (
          <div className={styles.toggle}>
            <Typography>Show only customized trainings</Typography>
            <Switch
              size="small"
              inputProps={{ 'data-cy': 'customizedTrainings' }}
              checked={isCustomized}
              onClick={() => handleIsCustomized()}
              id="customized-switch"
              tabIndex={0}
            />
          </div>
          )}
        </div>
      </div>
    </div>
  );
}
