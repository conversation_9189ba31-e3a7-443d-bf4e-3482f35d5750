import React, { useEffect, useState, useMemo } from 'react';
import { Box, Checkbox, FormControlLabel, IconButton, InputAdornment,
  TextField, Typography, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import { makeStyles } from '@mui/styles';
import styles from './SideBarFilters.module.css';
import { colors } from '../../../theme/colors';
import { useContentLibrary } from '../../../hooks/useContentLibrary';
import { getConcepts } from '../../../services/api/contentLibrary';
import ExpandableConcepts from '../../../components/ExpandableFilterCard/ExpandableConcepts';

const CustomTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    height: 32,
    paddingRight: 4,
    '& fieldset': {
      borderColor: 'lightgrey',
    },
    '&:hover fieldset': {
      borderColor: 'grey',
    },
  },
});

function Concepts() {
  const { palette } = useTheme();
  const { contentFilters, filters: { concepts, contentId } } = useContentLibrary();
  const { cadetGrey } = colors;
  const isContentIDSearch = !!(contentId);

  const [conceptsData, setConceptsData] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [btnState, setBtnState] = useState({});

  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: 5,
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.scrollbar.thumbColor,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.scrollbar.background,
      },
    },
  });
  const scrollbarStyleClass = useStyles();

  useEffect(() => {
    async function fetchConcepts() {
      const response = await getConcepts();
      if (response) setConceptsData(response);
    }
    fetchConcepts();
  }, []);

  const filteredData = useMemo(() => {
    return Object.entries(conceptsData).reduce((acc, [skillArea, items]) => {
      const filteredItems = items.filter(({ value }) => (searchQuery.length === 1 ?
        value.toLowerCase().startsWith(searchQuery.toLowerCase())
        : value.toLowerCase().includes(searchQuery.toLowerCase())),
      );
      if (filteredItems.length > 0) {
        acc[skillArea] = filteredItems;
      }
      return acc;
    }, {});
  }, [conceptsData, searchQuery]);

  useEffect(() => {
    const updatedBtnState = Object.fromEntries(
      Object.entries(conceptsData).map(([skillArea, items]) => [
        skillArea,
        searchQuery.length >= 1
          ? items.some(({ value }) => (searchQuery.length === 1 ?
            value.toLowerCase().startsWith(searchQuery.toLowerCase())
            : value.toLowerCase().includes(searchQuery.toLowerCase())),
          )
          : false,
      ]),
    );
    setBtnState(updatedBtnState);
  }, [conceptsData, searchQuery]);

  const handleToggleBtn = (key) => {
    setBtnState((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleCheckboxChange = (key, value) => {
    const updatedConcepts = concepts.includes(key)
      ? concepts.filter((item) => item !== key)
      : [...concepts, key];
    const selectedPillObject = { key, type: 'concepts', value };
    contentFilters('concepts', updatedConcepts, selectedPillObject);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  return (
    <Box
      className={styles.boxStyle}
      sx={{
        bgcolor: 'background.default',
        borderRadius: '4px',
      }}
    >
      <CustomTextField
        sx={{ width: '100%' }}
        variant="outlined"
        placeholder="Search concepts..."
        value={searchQuery}
        onChange={handleSearchChange}
        InputProps={{
          endAdornment: searchQuery && (
            <InputAdornment position="end">
              <IconButton
                onClick={handleClearSearch}
                sx={{
                  '& .MuiSvgIcon-root': { width: '0.8em' },
                }}
                aria-label="clear search"
                edge="end"
                size="small"
              >
                <ClearIcon />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
      {Object.keys(filteredData).length === 0 && <Typography>No matching concepts found</Typography>}
      <Box
        className={`${scrollbarStyleClass.scrollBar} ${styles.conceptsBoxStyle}`}
        sx={{
          bgcolor: 'background.default',
          borderRadius: '4px',
        }}
      >
        {Object.entries(filteredData).map(([skillArea, items]) => (
          <ExpandableConcepts
            key={skillArea}
            isOpen={btnState[skillArea] || false}
            onClick={() => handleToggleBtn(skillArea)}
            title={skillArea}
            data-cy={skillArea}
            type="concepts"
          >
            {items.map(({ key, value }) => (
              <Box key={key}>
                <FormControlLabel
                  className={styles.checkboxItemText}
                  sx={{
                    '& .MuiFormControlLabel-label': { fontSize: '0.9rem' },
                    '& .Mui-disabled': { color: `${cadetGrey} !important` },
                  }}
                  data-cy={key}
                  control={(
                    <Checkbox
                      sx={{
                        padding: '0.2rem',
                        '& .MuiSvgIcon-root': { width: '1rem' },
                        ':hover': { backgroundColor: 'unset' },
                      }}
                      checked={concepts.includes(key)}
                      onChange={() => handleCheckboxChange(key, value)}
                      name={value}
                      disabled={isContentIDSearch}
                    />
                )}
                  label={value}
                />
              </Box>
            ))}
          </ExpandableConcepts>
        ))}
      </Box>
    </Box>
  );
}

export default Concepts;
