import React from 'react';
import { Box, Checkbox, FormControlLabel } from '@mui/material';
import PropTypes from 'prop-types';
import styles from './SideBarFilters.module.css';
import { colors } from '../../../theme/colors';
import { useContentLibrary } from '../../../hooks/useContentLibrary';

function SimpleFilters({ filterOptions, filteredData, type }) {
  const { contentFilters } = useContentLibrary();
  const { cadetGrey } = colors;

  function handleCheckboxChange({ key, value }) {
    let checkedData = [];
    if (filteredData.includes(key)) {
      checkedData = filteredData.filter((item) => item !== key);
    } else {
      checkedData = [...filteredData, key];
    }
    const selectedPillObject = { key, type, value };
    contentFilters(type, checkedData, selectedPillObject);
  }

  return (
    <Box
      className={styles.boxStyle}
      sx={{
        bgcolor: 'background.default',
        borderRadius: '4px',
      }}
    >
      {filterOptions.map((item) => {
        const key = item.key;
        const value = item.value;
        const isChecked = filteredData.filter((keyName) => keyName === key)?.[0] === key;
        return (
          <Box key={key}>
            <FormControlLabel
              className={styles.checkboxItemText}
              sx={{
                '& .MuiFormControlLabel-label': { fontSize: '0.9rem' },
                '& .Mui-disabled': { color: `${cadetGrey} !important` },
              }}
              data-cy={`${key}`}
              control={(
                <Checkbox
                  sx={{
                    padding: '0.2rem',
                    '& .MuiSvgIcon-root': { width: '1rem' },
                    ':hover': { backgroundColor: 'unset' },
                  }}
                  checked={isChecked}
                  onClick={() => { handleCheckboxChange({ key, value }); }}
                  name={item.value}
                />
            )}
              label={`${item.value}`}
            />
          </Box>
        );
      })}
    </Box>
  );
}

SimpleFilters.propTypes = {
  filterOptions: PropTypes.array.isRequired,
  filteredData: PropTypes.array.isRequired,
  type: PropTypes.string.isRequired,
};

export default SimpleFilters;
