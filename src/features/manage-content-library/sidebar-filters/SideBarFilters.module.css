.container {
  font-size: .89rem;
  line-height: 1rem;
  padding: 0.5rem;
  margin: 0 auto;
  overflow-y: hidden;
  width: 100%;
  display: inline-flex;
  flex-direction: column;
}


.boxStyle {
  margin: 0rem 0.8rem 0.4rem;;
  height: fit-content;
}

.conceptsBoxStyle {
  max-height: 26rem;
  overflow-y: auto;
}
    
.checkboxItemText {
  margin: 0;
  align-items: center;
}

.expandFilters {
  display: inline-flex;
  justify-content: flex-end;
  padding: 0.2rem 1rem;
  gap: 0.2rem;
  align-items: center;
  cursor: pointer;
  font-weight: bold;
}

.iconStyle {
  width: 0.8em;
  height: 0.8em;
}

.toggle {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}