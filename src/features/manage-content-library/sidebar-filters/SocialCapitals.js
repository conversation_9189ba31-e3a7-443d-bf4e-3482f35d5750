import React, { useEffect, useState } from 'react';
import { Box, Checkbox, Divider, FormControlLabel, Typography } from '@mui/material';
import styles from './SideBarFilters.module.css';
import { colors } from '../../../theme/colors';
import { useContentLibrary } from '../../../hooks/useContentLibrary';
import { getSocialIndicators } from '../../../services/api/contentLibrary';

function SocialCapitals() {
  const { contentFilters, filters: { indicators, contentId } } = useContentLibrary();
  const { lightCylindricalBlue, brightPink, mainPurple, cadetGrey } = colors;
  const isContentIDSearch = !!(contentId);
  const pillarColors = {
    Respect: lightCylindricalBlue,
    Inclusion: brightPink,
    Ethics: mainPurple,
    Belonging: '#f8a513',
  };
  const [socialCapitals, setSocialCapitals] = useState({});
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(async () => {
    const response = await getSocialIndicators();
    if (response) {
      setSocialCapitals(response);
    }
  }, []);

  function handleCheckboxChange({ key, value }) {
    let checkedIndicators = [];
    if (indicators.includes(key)) {
      checkedIndicators = indicators.filter((item) => item !== key);
    } else {
      checkedIndicators = [...indicators, key];
    }
    const selectedPillObject = { key, type: 'indicators', value };
    contentFilters('indicators', checkedIndicators, selectedPillObject);
  }

  if (Object.keys(socialCapitals).length === 0) {
    return null;
  }

  return (
    <Box
      className={styles.boxStyle}
      sx={{
        bgcolor: 'background.default',
        borderRadius: '4px',
      }}
    >
      {Object.entries(socialCapitals).map(([skillArea, items]) => {
        return (
          <React.Fragment key={skillArea}>
            <Typography sx={{ fontWeight: '600' }}>{skillArea}</Typography>
            <Divider sx={{ borderBottom: `3px solid ${pillarColors[skillArea]}` }} />
            {items.map((item) => {
              const key = item.key;
              const value = item.value;
              const isChecked = indicators.filter((type) => type === key)?.[0] === key;
              return (
                <Box key={key}>
                  <FormControlLabel
                    className={styles.checkboxItemText}
                    sx={{
                      '& .MuiFormControlLabel-label': { fontSize: '0.9rem' },
                      '& .Mui-disabled': { color: `${cadetGrey} !important` },
                    }}
                    data-cy={`${key}`}
                    control={(
                      <Checkbox
                        sx={{
                          padding: '0.2rem',
                          '& .MuiSvgIcon-root': { width: '1rem' },
                          ':hover': { backgroundColor: 'unset' },
                        }}
                        checked={isChecked}
                        onClick={() => { handleCheckboxChange({ key, value }); }}
                        name={item.value}
                        disabled={isContentIDSearch}
                      />
                  )}
                    label={`${item.value}`}
                  />
                </Box>
              );
            })}
          </React.Fragment>
        );
      },
      )}
    </Box>
  );
}

export default SocialCapitals;
