import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { But<PERSON>, Container, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { get } from 'lodash';
import * as LDClient from 'launchdarkly-js-client-sdk';
import DashboardIcon from '../../../images/dashboard.svg';
import CampaignsIcon from '../../../images/campaigns.svg';
import ReportsIcon from '../../../images/reports.svg';
import SiteConfigIcon from '../../../images/site config.svg';
import UsersIcon from '../../../images/users.svg';
import UsersGroupsIcon from '../../../images/groups.svg';
import LibraryIcon from '../../../images/content library.svg';
import AnalyticsIcon from '../../../images/analytics.svg';
import ResourcesIcon from '../../../images/resources.svg';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import styles from './CustomerAdminNavigation.module.css';
import { useUser } from '../../../hooks/useUser';
import {
  canAccessAnalytics, canAccessContentConfiguration, canAccessOwnReports, canAccessResources,
  canAccessSiteConfiguration, canCreateOwnGroups, canEditCampaigns, canEditOwnAccount,
  canReadOwnUsers, canViewAccountDashboard,
} from '../../../services/api/permissions';
import { useAuth } from '../../../hooks/useAuth';

let LaunchDarklyClient;
function CustomerAdminNavigation({ navSelection, setNavSelection, currentAccount }) {
  const location = useLocation();
  const { pathname } = location;
  const { authedNavigateToAI } = useAuth();
  const { isSmallMobile } = useResponsiveMode();
  const user = useUser();
  const { palette } = useTheme();
  const accounts = user?.adminAccountId ? get(user, 'accounts[1]', true) : get(user, 'accounts[0]', true);
  const permissions = get(user, 'permissions', true);
  const patriotBlue = palette.primary.patriotBlue;
  const [isMclFlagEnabled, setIsMclFlagEnabled] = useState(false);
  const [isAnalytics2FlagEnabled, setIsAnalytics2FlagEnabled] = useState(false);

  const useStyles = makeStyles({
    scrollBar: {
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: '0.7rem',
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        borderRadius: 10,
        backgroundColor: palette.customScrollbar.thumbColor,
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.customScrollbar.background,
      },
    },
  });
  const scrollbarStyleClass = useStyles();
  const keywords = ['catalog', 'listing', 'lesson'];
  const isMatch = keywords.some((keyword) => pathname.includes(keyword));

  useEffect(() => {
    if (pathname === '/manage') {
      setNavSelection('dashboard');
    } else if (pathname.includes('/campaigns')) {
      setNavSelection('campaigns');
    } else if (pathname.includes('/config') && !pathname.includes('/content-library')) {
      setNavSelection('config');
    } else if (pathname.includes('/content-library')) {
      setNavSelection('contentLibrary');
    } else if (pathname.includes('/new-content-library') && !isMatch) {
      setNavSelection('new-content-library');
    } else if (pathname.includes('/new-content-library') && isMatch) {
      setNavSelection(null);
    } else if (pathname.includes('/resources')) {
      setNavSelection('resources');
    } else if (pathname.includes('/users')) {
      setNavSelection('users');
    } else if (pathname.includes('/groups')) {
      setNavSelection('groups');
    } else if (pathname.includes('/insights') || pathname.includes('/reports')) {
      setNavSelection('insights');
    } else if (pathname.includes('/analytics') && !pathname.includes('/analytics2')) {
      setNavSelection('analytics2');
    } else if (pathname.includes('/analytics2')) {
      setNavSelection('analytics2');
    }
  }, [pathname, setNavSelection]);

  useEffect(() => {
    if (currentAccount && user && accounts) {
      const { launchDarklyKey } = currentAccount;
      const ldsContext = {
        kind: 'user',
        key: 'emtrain-context-key',
        email: user.email,
        accountId: accounts.id,
      };
      LaunchDarklyClient = LDClient.initialize(launchDarklyKey, ldsContext);
      LaunchDarklyClient.on('ready', () => {
        // LD Flags
        const mclFlag = LaunchDarklyClient.variation('manage-content-library', false);
        const analytics2Flag = LaunchDarklyClient.variation('manage-analytics2', false);
        if (mclFlag) {
          setIsMclFlagEnabled(mclFlag);
        }
        if (analytics2Flag) {
          setIsAnalytics2FlagEnabled(analytics2Flag);
        }
      });
    }
  }, [accounts, currentAccount, user]);

  const navigateToAI = async (e, forwardTo) => {
    e.preventDefault();
    authedNavigateToAI(user, forwardTo);
  };

  return (
    <Container
      className={`${styles.container} ${scrollbarStyleClass.scrollBar}`}
      sx={{ bgcolor: 'background.assignmentList',
        zIndex: 1301,
        width: isSmallMobile ? '70vw !important' : '10.5rem !important' }}
      role="navigation"
    >
      {user && (
        <>
          <div className={styles.navigationPanel} style={{ color: palette.background.manageMenu }}>
            {user && canViewAccountDashboard(permissions) && (
            <Link to="/manage" data-cy="dashboard">
              <Button
                onClick={(e) => navigateToAI(e, 'manage')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'dashboard' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <DashboardIcon className={styles.imageLogo} />
                <span className={styles.title}>Dashboard</span>
              </Button>
            </Link>
            )}
            {user && !user.adminSSOaccountIsScorm && canEditCampaigns(permissions) && accounts && !accounts.scorm && !user.isWorkdayCCL && (
            <Link to="/manage/campaigns" data-cy="campaigns">
              <Button
                onClick={(e) => navigateToAI(e, 'manage/campaigns')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'campaigns' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <CampaignsIcon className={styles.imageLogo} />
                <span className={styles.title}>Campaigns</span>
              </Button>
            </Link>
            )}
            {user && canAccessOwnReports(permissions) && (
            <Link to="/manage/insights" onClick={() => setNavSelection('insights')} data-cy="insights">
              <Button
                className={styles.navigationButton}
                onClick={(e) => navigateToAI(e, 'manage/insights')}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'insights' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <ReportsIcon className={styles.imageLogo} />
                <span className={styles.title}>Reports</span>
              </Button>
            </Link>
            )}
            {user && canEditOwnAccount(permissions) && canAccessSiteConfiguration(permissions) && (
            <Link to="/manage/config" data-cy="siteConfig">
              <Button
                onClick={(e) => navigateToAI(e, 'manage/config')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'config' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <SiteConfigIcon className={styles.imageLogo} />
                <span className={styles.title}>Site Config</span>
              </Button>
            </Link>
            )}
            {user && canEditOwnAccount(permissions) && canAccessContentConfiguration(permissions) && (
            <Link to="/manage/content-library" data-cy="contentLibrary">
              <Button
                onClick={(e) => navigateToAI(e, 'manage/content-library')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'contentLibrary' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <LibraryIcon className={styles.imageLogo} />
                <span className={styles.title}>Content Library</span>
              </Button>
            </Link>
            )}
            {user && canEditOwnAccount(permissions) && canAccessContentConfiguration(permissions)
            && isMclFlagEnabled && (
            <Link to="/manage/new-content-library" data-cy="new-content-library">
              <Button
                onClick={() => setNavSelection('new-content-library')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: (navSelection === 'new-content-library' || navSelection === null) && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <LibraryIcon className={styles.imageLogo} />
                <span className={styles.title}>New MCL</span>
              </Button>
            </Link>
            )}
            {user && canAccessResources(permissions) && (
            <Link to="/manage/resources" data-cy="resources">
              <Button
                onClick={() => setNavSelection('resources')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'resources' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <ResourcesIcon className={styles.imageLogo} />
                <span className={styles.title}>Resources</span>
              </Button>
            </Link>
            )}
            {user && canReadOwnUsers(permissions) && (
            <Link to="/manage/users" data-cy="users">
              <Button
                onClick={(e) => navigateToAI(e, 'manage/users')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'users' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <UsersIcon className={styles.imageLogo} />
                <span className={styles.title}>Users</span>
              </Button>
            </Link>
            )}
            {user && !user.adminSSOaccountIsScorm
            && canCreateOwnGroups(permissions) && accounts && !accounts.scorm && !user.isWorkdayCCL && (
            <Link to="/manage/groups" data-cy="groups">
              <Button
                onClick={(e) => navigateToAI(e, 'manage/groups')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'groups' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <UsersGroupsIcon className={styles.imageLogo} />
                <span className={styles.title}>Groups</span>
              </Button>
            </Link>
            )}
            {user && canAccessAnalytics(permissions) && !isAnalytics2FlagEnabled && (
            <Link to="/manage/analytics/overview" data-cy="analytics">
              <Button
                onClick={(e) => navigateToAI(e, 'manage/analytics/overview')}
                className={styles.navigationButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: navSelection === 'analytics' && palette.primary.main,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: patriotBlue,
                  },
                }}
              >
                <AnalyticsIcon className={styles.imageLogo} />
                <span className={styles.title}>Intelligence</span>
              </Button>
            </Link>
            )}
            {user && canAccessAnalytics(permissions) && isAnalytics2FlagEnabled && (
              <Link to="/manage/analytics/overview" data-cy="analytics">
                <Button
                  onClick={() => setNavSelection('analytics2')}
                  className={styles.navigationButton}
                  sx={{
                    color: palette.button.login.color,
                    backgroundColor: navSelection === 'analytics2' && palette.primary.main,
                    '&:hover': {
                      color: palette.button.login.hoverColor,
                      backgroundColor: patriotBlue,
                    },
                  }}
                >
                  <AnalyticsIcon className={styles.imageLogo} />
                  <span className={styles.title}>Intelligence</span>
                </Button>
              </Link>
            )}
          </div>
          <div className={styles.copyRights} style={{ color: palette.primary.white }}>
            ©&nbsp;Emtrain
            <br />
            All Rights Reserved
          </div>
        </>
      )}
    </Container>
  );
}

export default CustomerAdminNavigation;
