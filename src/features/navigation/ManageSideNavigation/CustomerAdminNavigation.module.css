.container {
  /* flex: 1; */
  /* flex: 0 0 11.25rem; */
  min-height: 100vh;
  min-width: 10.6rem !important;
  padding: 0 !important;
  overflow: hidden;
  display: grid;
  align-content: space-between;
}

.filterContainer {
  display: flex;
  padding: 0.3rem 0 0.5rem 0.75rem;
  align-items: center;
  justify-content: flex-end;
}

.navigationPanel {
  height: 100%;
}

.navigationButton {
    width: 100%;
    height: 2.75rem;
    padding-left: 1.25rem;
    margin-top: 0.75rem;
    border-radius: 0;
    cursor: pointer;
    border: none; 
    justify-content: flex-start;
    text-transform: capitalize;
}

.imageLogo {
  width: 1.625rem !important;
  height: 1.125rem !important;
  margin-right: 0.313rem !important;
}

.title {
  font-weight: 600;
  line-height: 1rem;
}

.copyRights {
  font-size: 0.75rem;
  padding: 0rem 0rem 1rem 1.3rem;
}
