import React, { useState, useEffect } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { get } from 'lodash';
import { useMutation } from '@apollo/client';
import { Box, Drawer as MaterialDrawer, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';

import { setJWPlayerLanguage } from '../../../services/jwPlayerSettings';
import { useAuth } from '../../../hooks/useAuth';
import { useUser } from '../../../hooks/useUser';
import { useMenuDrawer } from '../../../hooks/useDrawer';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import ExpandableCard from '../../../components/ExpandableCard/ExpandableCard';
import UserMenuContent from './UserMenuContent';
import AskExpertQuestion from '../../ask-expert/AskExpertQuestion';
import AskExpertConfirm from '../../ask-expert/AskExpertConfirm';
import ContactSupport from '../../contact-support/ContactSupport';
import SupportMenuContent from './SupportMenuContent';
import EmtrainStandardLogo from '../../../images/emtrain-standard-logo.svg';
import UserCircle from '../../../icons/UserCircle';
import AskAnExpertIcon from '../../../images/ask_an_expert.svg';
import lifeSaverIcon from '../../../images/life_raft.png';
import QuestionCircle from '../../../icons/QuestionCircle';

import styles from './SideDrawer.module.css';
import LanguageSelector from '../../../components/LanguageSelector/LanguageSelector';
import UpdateUserMutation from '../../user-profile/UpdateUserMutation.graphql';
import SendSignoutEventMutation from '../../user-profile/SendSignoutEventMutation.graphql';
import HamburgerIcon from '../../../icons/Hamburger';
import ChevronIcon from '../../../icons/Chevron';
import { setUserClosedRightPanel, getUserClosedRightPanel, clearUserClosedRightPanel }
  from '../../../services/layoutSettings';
import { pushToHome } from '../Routes/AppRoutes';
import { deleteCookie } from '../../../hooks/useCookies';

const UserCircleIcon = () => <UserCircle color="#40405D" width="25px" height="25px" />;
const QuestionCircleIcon = () => <QuestionCircle width="25px" height="25px" />;
const ContactSupportIcon = () => (
  <img
    src={lifeSaverIcon}
    alt="lifeSaverIcon"
    style={{ width: '25px',
      height: '25px',
      backgroundColor: 'transparent',
      borderRadius: '50%' }}
  />
);

const initialBtnState = {
  userBtn: true,
  expertBtn: false,
  expertConfirmBtn: false,
  supportBtn: false,
  languageBtn: false,
  contactSupportBtn: false,
};

export default function SideDrawer({
  bannerHeight, position, selectedLanguages, manageView = false, preview = false }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const history = useHistory();

  const { logout, updateUserData, authedNavigateToAI } = useAuth();
  const { isDesktop } = useResponsiveMode();

  const user = useUser();

  const [updateUser] = useMutation(UpdateUserMutation);
  const [sendSignoutEvent] = useMutation(SendSignoutEventMutation, { ignoreResults: true });

  const { isOpenDrawer, openDrawer, closeDrawer, drawerState } = useMenuDrawer();
  const [btnState, setBtnState] = useState(initialBtnState);
  const [expertQuestion, setExpertQuestion] = useState('');
  const [welcomeVideoOpen, setWelcomeVideoOpen] = useState(false);
  const [drawerVariant, setDrawerVariant] = useState('persistent');

  const videoClosedCaption = get(user, 'accounts[0].videoClosedCaption', false);
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);
  const customLogoSrc = get(user, 'accounts[0].customLogoSrc');
  const customDarkLogoSrc = get(user, 'accounts[0].customDarkLogoSrc');
  const accountType = get(user, 'accounts[0].accountType');
  const accountRoleId = get(user, 'accounts[0].accountUsers.roleId', 0);
  const canAccessAdminApp = accountType === 'admin' && accountRoleId === 6;
  // TODO: For now we are just determining this by role. However, we need to get the permissions check
  // down to the JETT client. We can determine how to do this a little bit later.
  const canAccessManageApp = accountRoleId === 3 || accountRoleId === 4 || accountRoleId === 5 ||
    accountRoleId === 6 || accountRoleId === 8 || accountRoleId >= 10;

  const customLogoUrl = customLogoSrc && !customDarkLogoSrc ? customLogoSrc : customDarkLogoSrc || null;
  // don't open drawer on browser window width change after the user clicked to close it
  const userClosedRightPanel = getUserClosedRightPanel();

  useEffect(() => {
    if (isDesktop && !welcomeVideoOpen && !manageView) {
      if (!userClosedRightPanel) {
        openDrawer();
      }
      setDrawerVariant('persistent'); // not in modal
      // we don't want to close the drawer if the welcome video
      // is currently playing in the side drawer
    } else if (!welcomeVideoOpen) {
      closeDrawer();
      setDrawerVariant('temporary'); // in modal
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDesktop, welcomeVideoOpen]);

  const setSideDrawerWelcomeVideoOpen = (videoDialogOpen) => {
    setWelcomeVideoOpen(videoDialogOpen);
  };

  const handleLogout = () => {
    closeDrawer();
    clearUserClosedRightPanel();
    sendSignoutEvent().then(() => logout(), deleteCookie());
  };

  const saveUserLanguage = async (language) => {
    setJWPlayerLanguage(language, videoClosedCaption);
    const languageEnum = language ? String(language).replace(/-/g, '_').toLowerCase() : null;
    await updateUser({ variables: { language: languageEnum } });
    updateUserData({ language: language.toLowerCase() });
  };

  const saveUserAudio = (audio) => {
    updateUser({ variables: { audio } });
    updateUserData({ audio });
  };

  const handleToggleBtn = (key) => {
    const newBtnState = { ...initialBtnState, [key]: !btnState[key] };
    if (key !== 'userBtn') {
      newBtnState.userBtn = false;
    }
    setBtnState(newBtnState);
    // return focus to the menu button when we close the menu, including when we close the menu by clicking the cancel button
    const buttonNames = {
      expertConfirmBtn: 'menuAskExpert',
      menuAskExpert: 'menuAskExpert',
      contactSupportBtn: 'menuSupport',
      languageBtn: 'language-button',
    };
    if (buttonNames[key] && btnState[key]) {
      setTimeout(() => {
        const menuFocusElement = document.getElementById(`${buttonNames[key]}-button`);
        if (menuFocusElement) {
          menuFocusElement.focus();
        }
      }, 100);
    }
  };

  const handleOpenSupportForm = () => {
    handleToggleBtn('contactSupportBtn');
    setTimeout(() => {
      const initialFocusElement = document.getElementById('contact-support-initial-focus-element');
      if (initialFocusElement) {
        initialFocusElement.focus();
      }
    }, 500);
  };

  const handleLanguageBtn = () => {
    handleToggleBtn('languageBtn');
    handleToggleBtn('userBtn');
    setTimeout(() => {
      const languagesButton = document.getElementById('language-button');
      if (languagesButton) {
        languagesButton.focus();
      }
    }, 500);
  };

  const handleContactSupport = () => {
    handleToggleBtn('contactSupportBtn');
    handleToggleBtn('supportBtn');
  };

  const handleAskExpert = () => {
    handleToggleBtn('expertConfirmBtn');
    handleToggleBtn('expertBtn');
  };

  const handleSetQuestion = (question) => {
    setExpertQuestion(question);
    handleToggleBtn('expertConfirmBtn');
  };

  const clearQuestion = () => {
    setExpertQuestion('');
    openDrawer('my_questions'); // for fetching my questions
  };

  const userCloseDrawer = () => {
    if (isDesktop) {
      setUserClosedRightPanel();
    }
    closeDrawer();
    setTimeout(() => {
      const el = document.getElementById('side-drawer-open-button');
      if (el) {
        el.focus();
      }
    }, 500);
  };

  const navigateToAI = async (e, forwardTo) => {
    e.preventDefault();
    authedNavigateToAI(user, forwardTo);
  };

  useEffect(() => {
    if (drawerState === 'expert') {
      setBtnState(() => ({ ...initialBtnState, userBtn: false, expertBtn: true }));
      setTimeout(() => {
        openDrawer('open');
      }, 1500);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawerState]);

  const useStyles = makeStyles({
    bannerTopMargin: {
      overflow: 'hidden',
      height: '100%',
      marginTop: bannerHeight ? `${bannerHeight}rem` : '0px',
    },
    scrollBar: {
      'overflow-y': 'auto',
      padding: '0 0.5rem 0.5rem 0',
      marginBottom: bannerHeight ? `${bannerHeight}rem` : '0',
      '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
        width: '0.5rem',
        borderRadius: '0.5rem !important',
      },
      '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
        backgroundColor: palette.background.secondary,
        borderRadius: '0.5rem !important',
      },
      '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
        backgroundColor: palette.background.primary,
        borderRadius: '0.5rem !important',
      },
    },
  });
  const classes = useStyles();

  return (
    <MaterialDrawer
      className={`${styles.drawerContainer}`}
      classes={{
        paper: classes.bannerTopMargin,
      }}
      BackdropProps={{
        invisible: true,
      }}
      anchor={position}
      open={isOpenDrawer()}
      onClose={closeDrawer}
      variant={drawerVariant}
      style={{ position: 'static', overflowX: 'hidden', width: `${isOpenDrawer() && !manageView ? '20rem' : '0'}` }}
    >
      <div id="side-drawer" className={styles.container}>
        <div
          id="side-drawer-close-button"
          aria-label={t('platform.emtrain_navigation_panel')}
          className={styles.logoContainer}
          style={{
            marginBottom: `${(canAccessAdminApp || canAccessManageApp) ? '.5rem' : ''}`,
            '&:focus': {
              border: `2px solid ${palette.card.backgroundColor} !important`,
            },
          }}
          onClick={userCloseDrawer}
          onKeyDown={(e) => {
            if (e.code === 'Enter' || e.code === 'Space') {
              e.preventDefault();
              userCloseDrawer();
            }
          }}
          role="button"
          tabIndex="0"
          aria-controls="side-drawer-open-button"
        >
          <Box display="flex" alignItems="center">
            <HamburgerIcon {...{ color: palette.primary.dark }} />
            <ChevronIcon {...{ color: palette.primary.dark, height: '16px', width: '9px', direction: 'right' }} />
          </Box>
          {(!customLogoUrl || manageView) && (
            <EmtrainStandardLogo
              role="img"
              aria-hidden
              alt={t('platform.emtrain_logo')}
              className={styles.logo}
            />
          )}
          {customLogoUrl && !manageView && (
            <img
              src={customLogoUrl}
              alt="LogoImage"
              className={styles.customLogo}
            />
          )}
        </div>
        {(canAccessAdminApp || canAccessManageApp) && !preview && (
          <div className={styles.appLinksSection}>
            {canAccessManageApp && !manageView && (
              <div className={styles.appLink}>
                <Link
                  className={styles.manageLink}
                  onClick={(e) => navigateToAI(e, 'manage')}
                  to="#"
                >
                  {t('sideDrawer.switch_manage')}
                </Link>
              </div>
            )}
            {canAccessAdminApp && (
              <div className={styles.appLink}>
                <Link
                  className={styles.manageLink}
                  onClick={(e) => navigateToAI(e, 'superadmin')}
                  to="#"
                >
                  {manageView ? 'Switch to Admin' : t('sideDrawer.switch_admin')}
                </Link>
              </div>
            )}
            {manageView && (
              <div className={styles.appLink}>
                <Link
                  className={styles.manageLink}
                  onClick={() => history.push(pushToHome())}
                  to="#"
                >
                  Switch to Learner
                </Link>
              </div>
            )}
          </div>
        )}
        <div className={classes.scrollBar}>
          <div id="userMenuAccordionGroup">
            {btnState.languageBtn ? (
            // eslint-disable-next-line max-len
              <LanguageSelector selectedLanguages={selectedLanguages} onClose={handleLanguageBtn} onSaveUserLanguage={saveUserLanguage} />
            ) : (
              <ExpandableCard
                isOpen={btnState.userBtn || manageView}
                onClick={() => handleToggleBtn('userBtn')}
                title={`${user.firstName} ${user.lastName}`}
                icon={UserCircleIcon}
                data-cy="menuUser"
                drawerState={drawerState}
                manageView={manageView}
                preview={preview}
              >
                <UserMenuContent
                  id="menuUser"
                  onLanguageClick={() => handleToggleBtn('languageBtn')}
                  onSaveUserAudio={saveUserAudio}
                  handleLogout={handleLogout}
                  manageView={manageView}
                  preview={preview}
                />
              </ExpandableCard>
            )}
            {askExpertEnabled && !manageView && !preview && (
              btnState.expertConfirmBtn ? (
                <AskExpertConfirm
                  onClose={handleAskExpert}
                  expertQuestion={expertQuestion}
                  clearQuestion={clearQuestion}
                />
              ) : (
                <ExpandableCard
                  isOpen={btnState.expertBtn}
                  onClick={() => handleToggleBtn('expertBtn')}
                  title={t('sideDrawer.expertTitle')}
                  icon={AskAnExpertIcon}
                  data-cy="menuAskExpert"
                  drawerState={drawerState}
                  isAskExpert
                >
                  <AskExpertQuestion
                    id="menuAskExpert"
                    initialQuestion={expertQuestion}
                    onSetQuestion={handleSetQuestion}
                    drawerState={drawerState}
                  />
                </ExpandableCard>
              )
            )}
            {!preview && (
              btnState.contactSupportBtn && !manageView ? (
                <ContactSupport onClose={handleContactSupport} />
              ) : (
                <ExpandableCard
                  isOpen={btnState.supportBtn || manageView}
                  onClick={() => handleToggleBtn('supportBtn')}
                  title={manageView ? 'Help & Resources' : t('sideDrawer.supportTitle')}
                  icon={QuestionCircleIcon}
                  data-cy="menuSupport"
                  drawerState={drawerState}
                  manageView={manageView}
                >
                  <SupportMenuContent
                    ariaLabelledby="menuSupport-button"
                    onContactSupportClick={handleOpenSupportForm}
                    setSideDrawerWelcomeVideoOpen={setSideDrawerWelcomeVideoOpen}
                    manageView={manageView}
                  />
                </ExpandableCard>
              ))}
            {manageView && (
              btnState.contactSupportBtn ? (
                <ContactSupport onClose={handleContactSupport} />
              )
                : (
                  <ExpandableCard
                    isOpen={btnState.contactSupportBtn}
                    // onClick={() => handleToggleBtn('contactSupportBtn')}
                    onClick={handleOpenSupportForm}
                    title="Contact Support"
                    icon={ContactSupportIcon}
                    data-cy="contactSupport"
                    drawerState={drawerState}
                    manageView
                    preview={preview}
                  />
                )
            )}
            {(customLogoSrc || customDarkLogoSrc) && !manageView && (
            <div className={styles.poweredBySection}>
              <div>{t('sideDrawer.powered_by')}</div>
              <div className={styles.logoSection}><EmtrainStandardLogo className={styles.logoSmall} /></div>
            </div>
            )}
          </div>
        </div>
        {manageView && (
        <div className={styles.emailLink}>
          <Link
            to={{ pathname: 'https://web.emtrain.com/OP-PreferenceCenterv3_Clients_LP-Preferences.html' }}
            target="_blank"
            className={styles.emailPreferenceLink}
            data-cy="Email Preferences"
            onClick={() => closeDrawer()}
          >
            Email Preferences
            {' '}
            &#187;
          </Link>
        </div>
        )}
      </div>
    </MaterialDrawer>
  );
}
