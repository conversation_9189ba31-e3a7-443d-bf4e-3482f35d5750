import React from 'react';
import { useTranslation } from 'react-i18next';
import { get } from 'lodash';
import { List, useTheme, ListItem, Typography, Button } from '@mui/material';
import { Link } from 'react-router-dom';
import ExternalLinkIcon from '../../../icons/ExternalLink';
import { useUser } from '../../../hooks/useUser';
import ChevronIcon from '../../../icons/Chevron';
import Video from '../../../components/Video/Video';
import './SupportMenuContent.module.css';
import { useMenuDrawer } from '../../../hooks/useDrawer';

export default function SupportMenuContent({ onContactSupportClick, setSideDrawerWelcomeVideoOpen,
  id, ariaLabelledby, manageView }) {
  const { t } = useTranslation();
  const { palette } = useTheme();
  const { closeDrawer } = useMenuDrawer();
  const user = useUser();
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);
  const faqLink = user?.scorm ?
    'https://answers-support.emtrain.com/hc/en-us/sections/**************-FAQs-Emtrain-AI-SCORM-Learning-Environment' :
    user?.isWorkdayCCL ?
      'https://answers-support.emtrain.com/hc/en-us/sections/**************-FAQs-Emtrain-Learning-Experience-for-Workday-CCL' :
      'https://answers-support.emtrain.com/hc/en-us/sections/**************-FAQs-Emtrain-AI-Hosted-Learning-Experience';

  const onClickSupportLink = () => {
    window.open(faqLink, '_blank');
  };

  const helpOptions = [
    {
      text: 'Getting Started',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/articles/************-Onboarding-to-Emtrain-AI' },
    {
      text: 'Site Config & Integrations',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/sections/************-Site-Config-Integrations' },
    {
      text: 'Users',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/articles/************-Step-3-Import-Update-Roster' },
    {
      text: 'User Groups',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/articles/360054616432-Step-4-Create-Training-Groups' },
    {
      text: 'Campaigns',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/articles/360055100571-Step-6-Campaign-Creation-Communication-Strategy' },
    {
      text: 'Content Library',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/articles/************-Step-5-Content-Configuration' },
    {
      text: 'Reports',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/articles/************-Step-8-Reporting' },
    {
      text: 'Analytics',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/sections/*************-Analytics' },
    {
      text: 'See All Help Topics',
      pathname: 'https://answers-support.emtrain.com/hc/en-us/categories/************-Account-Admins' },
  ];

  if (manageView) {
    return (
      <List disablePadding sx={{ width: '100%', padding: '0rem 1rem .5rem' }} id={id}>
        <li>
          <Typography component="h3" sx={{ fontSize: '0.875rem', fontWeight: 'bold' }} gutterBottom>
            {'I\'m looking for help with...'}
          </Typography>
        </li>
        {helpOptions.map((list, i) => {
          return (
            list.text !== 'See All Help Topics' ? (
              <li data-cy={`helpItem-${i}`} key={`helpItem-${i + 1}`}>
                <Link to={{ pathname: `${list.pathname}` }} target="_blank" className="helpSupportText">
                  <Typography
                    component="h3"
                    gutterBottom
                    sx={{ fontSize: '0.875rem', color: palette.primary.cyanBlue }}
                    onClick={() => closeDrawer()}
                  >
                    {list.text}
                  </Typography>
                </Link>
              </li>
            ) : (
              <li data-cy={`helpItem-${i}`} key={`helpItem-${i + 1}`}>
                <Link to={{ pathname: `${list.pathname}` }} target="_blank" className="helpSupportText">
                  <Typography
                    component="h3"
                    gutterBottom
                    sx={{ fontSize: '0.875rem', fontWeight: 600, color: palette.primary.cyanBlue }}
                    onClick={() => closeDrawer()}
                  >
                    {list.text}
                    {' '}
                    &#187;
                  </Typography>
                </Link>
              </li>
            )
          );
        },
        )}
      </List>
    );
  }

  return (
    <List disablePadding sx={{ width: '100%' }} id={id} aria-labelledby={ariaLabelledby}>
      {/* Video Thumbnail */}
      <ListItem sx={{ flexDirection: 'column', alignItems: 'flex-start' }}>
        <Typography component="h3" sx={{ fontSize: '0.875rem', fontWeight: 600 }} gutterBottom>
          {t('supportMenu.overviewTitle')}
        </Typography>
        {askExpertEnabled && !user.scorm && (
          <Video
            videoId="1765"
            title={t('platform.welcome_video_description')}
            playIconWH="2.5rem"
            setVideoDialogOpen={setSideDrawerWelcomeVideoOpen}
          />
        )}
        {!askExpertEnabled && !user.scorm && (
          <Video
            videoId="1821"
            title={t('platform.welcome_video_description')}
            playIconWH="2.5rem"
            setVideoDialogOpen={setSideDrawerWelcomeVideoOpen}
          />
        )}
        {askExpertEnabled && user.scorm && (
          <Video
            videoId="1822"
            title={t('platform.welcome_video_description')}
            playIconWH="2.5rem"
            setVideoDialogOpen={setSideDrawerWelcomeVideoOpen}
          />
        )}
        {!askExpertEnabled && user.scorm && (
          <Video
            videoId="1823"
            title={t('platform.welcome_video_description')}
            playIconWH="2.5rem"
            setVideoDialogOpen={setSideDrawerWelcomeVideoOpen}
          />
        )}
      </ListItem>
      <ListItem sx={{ borderTop: `1px solid ${palette.primary.lightGreyOpacity30}`, width: '100%', padding: 0 }}>
        <Button
          sx={{
            flexDirection: 'column',
            alignItems: 'flex-start',
            border: '0 !important',
            borderRadius: 0,
            margin: 0,
            width: '100% !important',
            fontSize: '0.875rem',
            textTransform: 'none',
            padding: '.5rem .5rem .5rem 1rem',
            cursor: 'pointer',
            fontFamily: 'SourceSansPro-Regular, Source Sans Pro, sans-serif',
            '&:hover': {
              backgroundColor: 'primary.greyShade',
              cursor: 'pointer',
            },
          }}
          onClick={onClickSupportLink}
          onKeyDown={
            (e) => {
              if (e.code === 'Enter' || e.code === 'Space') {
                e.preventDefault();
                onClickSupportLink();
              }
            }
          }
        >
          <Typography sx={{ fontSize: '0.875rem' }} gutterBottom>
            {t('supportMenu.need_help_assignment')}
          </Typography>
          <Typography sx={{ fontSize: '0.875rem', fontWeight: 600 }}>
            {t('supportMenu.check_faqs')}
            <ExternalLinkIcon styleProps={{ marginLeft: '0.25rem' }} />
          </Typography>
        </Button>
      </ListItem>
      <ListItem sx={{ borderTop: `1px solid ${palette.primary.lightGreyOpacity30}`, width: '100%', padding: 0 }}>
        <Button
          sx={{
            flexDirection: 'column',
            alignItems: 'flex-start',
            border: '0 !important',
            borderRadius: 0,
            margin: 0,
            width: '100% !important',
            fontSize: '0.875rem',
            textTransform: 'none',
            padding: '.5rem .5rem .5rem 1rem',
            cursor: 'pointer',
            fontFamily: 'SourceSansPro-Regular, Source Sans Pro, sans-serif',
            '&:hover': {
              backgroundColor: 'primary.greyShade',
              cursor: 'pointer',
            },
          }}
          id="open-support-form-button"
          onClick={onContactSupportClick}
          onKeyDown={
            (e) => {
              if (e.code === 'Enter' || e.code === 'Space') {
                e.preventDefault();
                onContactSupportClick();
              }
            }
          }
        >
          <Typography sx={{ fontSize: '0.875rem' }} gutterBottom>
            {t('supportMenu.cant_find_answer')}
          </Typography>
          <Typography sx={{ fontSize: '0.875rem', fontWeight: 600 }}>
            {t('supportMenu.contact_support_team')}
            <ChevronIcon {...{ direction: 'right', width: '12px', height: '12px' }} />
          </Typography>
        </Button>
      </ListItem>
    </List>
  );
}
