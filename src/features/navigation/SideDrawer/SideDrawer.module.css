.container {
  width: 20rem;
  display: flex;
  flex-direction: column;
  padding: 0.25rem 0rem 1rem 1.2rem;
  overflow: hidden;
}

.drawerContainer {
  border-radius: 0;

}

.drawerSection {
  display: flex;
  flex-wrap: wrap;
  padding: 15px;
  border: solid 1px lightgrey;
  border-radius: 2px;
  margin-bottom: 1rem;
  text-decoration: none;
}

.drawerSectionBreak p {
  margin: 0;
  margin-right: 0.5rem;
  font-size: .9rem;
}

.drawerSectionBreak a, a:visited, a:hover, a:active {
  text-decoration: none;
  color: inherit;
}

.drawerSectionText, .link {
  text-decoration: none;
  font-weight: 600 !important;
  font-size: 14px;
}

.drawerSectionHR {
  flex-basis: 100%;
  width: 0;
  border: none;
  height: 1px;
  background-color: lightgrey;
  display: flex;
  align-items: center;
  margin: 0rem .5rem;
}

.drawerSectionBreak {
  flex-basis: 100%;
  width: 0;
  cursor: pointer;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
}

.drawerSectionNoBorder {
  flex-basis: 100%;
  width: 0;
  cursor: pointer;
  padding: 0rem 1rem;
  display: flex;
  align-items: center;
}

.drawerSectionNoBorder p {
  margin: 0;
  margin-right: 0.5rem;
  font-size: .9rem;
}

.drawerSectionNewline {
  flex-basis: 100%;
  width: 0;
  padding: 0rem 1rem .6rem 1rem;
  display: flex;
  align-items: center;
}

.seeAllLink {
  color: #006FDE !important;
  font-size: 12px !important;
}

.FAQText {
  font-weight: normal !important;
  font-size: .8rem;
  padding-top: 1em;
  padding-bottom: 0.5em;
}

.FAQHeading {
  font-weight: bold !important;
  font-size: 14px;
  padding-top: 1em;
  padding-bottom: 0.5em;
}

.FAQLink {
  color: inherit;
  text-decoration: none;
  font-weight: 600 !important;
  font-size: 0.875rem;
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  margin-top: 0.3rem;
  cursor: pointer;
  border: 3px solid transparent;
  padding: 0.3rem !important;
  border-radius: 0.25rem;
}

.logo {
  height: 2.5rem;
  padding: 0.2rem;
  margin-top: -0.25rem;
  margin-right: -0.2rem;
}

.customLogo {
  object-fit: contain;
  max-height: 3.75rem;
  max-width: 14rem;
}

.hamburger {
  position: relative;
  top: 4px;
}

.highlighted {
  background: #F2F0F6;
}

.overflowText {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.poweredBySection {
  width: 100%;
  display: flex;
  justify-content: right;
  align-items: center;
}

.logoSection {
  margin-top: .3rem;
  padding-left: .2rem;
}

.logoSmall {
  height: 1.6rem;
  width: 4.2rem;
}

.appLinksSection {
  margin-bottom: 0.8rem;
  margin-right: 0.8rem;
}

.appLink {
  width: 100%;
  display: flex;
  justify-content: right;
  font-size: .9rem;
}

.manageLink {
  text-decoration: none !important;
}

.manageLink:hover{
  font-weight: 600 !important;
}

.emailLink {
  display: flex;
  justify-content: center;
  font-size: .875rem;
}

.emailPreferenceLink:hover{
  font-weight: bold !important;
}
