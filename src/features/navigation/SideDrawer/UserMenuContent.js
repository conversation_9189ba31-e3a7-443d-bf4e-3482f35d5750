/* eslint-disable max-len */
import React, { useState } from 'react';
import { useTheme, ListItem, Typography, List } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { useUser } from '../../../hooks/useUser';
import { languageNames } from '../../../translations';
import Switch from '../../../components/Switch/Switch';
import InfoIcon from '../../../images/info-icon.png';
import ChevronIcon from '../../../icons/Chevron';
import Tooltip from '../../../components/Tooltip/Tooltip';
import HiddenAlert from '../../hidden-alert/HiddenAlert';

export default function UserMenuContent({
  handleLogout,
  onLanguageClick,
  onSaveUserAudio,
  id, manageView,
  preview }) {
  const { i18n, t } = useTranslation();
  const { palette } = useTheme();
  const user = useUser();

  const [audioFocused, setAudioFocused] = useState(false);
  const [audioUpdatedText, setAudioUpdatedText] = useState('');
  const history = useHistory();

  const toggleAudio = () => {
    setAudioUpdatedText(!user.audio ? t('sideDrawer.audio_on') : t('sideDrawer.audio_off'));
    onSaveUserAudio(!user.audio);
  };

  const onProfileClick = (e) => {
    if (e.code === 'Space' || e.code === 'Enter' || e.type === 'click') {
      e.preventDefault();
      history.push('/profile');
    }
  };

  const onLogoutClick = (e) => {
    if (e.code === 'Space' || e.code === 'Enter' || e.type === 'click') {
      e.preventDefault();
      handleLogout();
    }
  };

  const onLanguageKeyDown = (e) => {
    if (e.code === 'Space' || e.code === 'Enter') {
      e.preventDefault();
      onLanguageClick();
    }
  };

  const listIemtSx = {
    borderTop: `1px solid ${palette.primary.lightGreyOpacity30}`,
    gap: '0.5rem',
    '&:hover': {
      backgroundColor: 'primary.greyShade',
      cursor: 'pointer',
    },
    '&:focus': {
      backgroundColor: 'primary.greyShade',
    },
  };

  return (
    <List aria-labelledby="menuUser-button" disablePadding sx={{ width: '100%' }} id={id}>
      {!manageView && (
      <ListItem
        sx={{
          display: 'flex',
          gap: '0.5rem',
          borderTop: `1px solid ${palette.primary.lightGreyOpacity30}`,
          '&:hover': {
            backgroundColor: 'primary.greyShade',
            cursor: 'pointer',
          },
        }}
        onClick={() => toggleAudio()}
        role="group"
        aria-labelledby="audio-on-off"
      >
        <label htmlFor="audio-on-off" style={{ fontWeight: 600, fontSize: '0.875rem' }}>
          {t('sideDrawer.audio')}
        </label>
        <Typography
          sx={{ fontSize: '0.875rem' }}
          aria-hidden
        >
          {user.audio ? t('sideDrawer.on') : t('sideDrawer.off')}
        </Typography>
        <Switch
          inputProps={{ 'data-cy': 'audio', id: 'audio-on-off' }}
          checked={user.audio}
          role="switch"
          aria-checked={user.audio}
          aria-label={`${t('sideDrawer.audio')}: ${t('sideDrawer.audio_tooltip')}`}
          // Using the state to alter the focus style was necessary because I couldn't get a :focus style to work on this element.
          onFocus={() => setAudioFocused(true)}
          onBlur={() => setAudioFocused(false)}
          onChange={() => toggleAudio()}
          sx={{ outline: audioFocused ? `3px solid ${palette.card.backgroundColor}` : '',
            border: audioFocused ? `0.5px solid ${palette.primary.white}` : '',
            '& .MuiSwitch-switchBase': { padding: audioFocused ? '1.5px' : '2px' } }}
        />
        <HiddenAlert text={audioUpdatedText} />
        <Tooltip
          title={t('sideDrawer.audio_tooltip')}
          marginTop="0.375rem !important"
          maxWidth="9rem !important"
          placement="bottom-end"
          role="tooltip"
          aria-label={t('sideDrawer.audio_tooltip')}
          id="audio-info-tooltip"
        >
          <img
            src={InfoIcon}
            alt="infoIcon"
            style={{ width: '16px', height: 'auto', position: 'absolute', right: '1rem' }}
            aria-describedby="audio-info-tooltip"
          />
        </Tooltip>
      </ListItem>
      )}
      {!manageView && (
      <ListItem
        id="language-button"
        data-cy="language"
        tabIndex="0"
        role="button"
        sx={listIemtSx}
        onClick={onLanguageClick}
        onKeyDown={onLanguageKeyDown}
      >
        <Typography sx={{ fontWeight: 600, fontSize: '0.875rem' }}>{t('userMenu.language')}</Typography>
        <Typography sx={{ fontSize: '0.875rem', marginRight: '-2px' }}>{languageNames[i18n.language.toLowerCase()]}</Typography>
        <ChevronIcon {...{ color: palette.primary.dark, height: '9px', width: '9px', direction: 'right', styleProps: { marginBottom: '-2px' } }} />
      </ListItem>
      )}
      {!preview && (
        <ListItem
          id="profile-button"
          data-cy="profile"
          tabIndex="0"
          role="button"
          sx={listIemtSx}
          onClick={onProfileClick}
          onKeyDown={onProfileClick}
        >
          <Typography sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
            {manageView ? 'My Profile' : t('userMenu.my_profile')}
          </Typography>
        </ListItem>
      )}
      {user && !user.scorm && !preview && (
        <ListItem
          id="logout-button"
          data-cy="logout"
          tabIndex="0"
          role="button"
          sx={listIemtSx}
          onClick={onLogoutClick}
          onKeyDown={onLogoutClick}
        >
          <Typography sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
            {manageView ? 'Sign Out' : t('userMenu.sign_out')}
          </Typography>
        </ListItem>
      )}
    </List>
  );
}
