import React from 'react';
import { get } from 'lodash';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Button from '../../../components/Button/Button';
import EmtrainStandardLogo from '../../../images/emtrain-standard-logo.svg';

import styles from './SideDrawerOpenButton.module.css';
import { useUser } from '../../../hooks/useUser';
import { useMenuDrawer } from '../../../hooks/useDrawer';
import { useAuth } from '../../../hooks/useAuth';
import HamburgerIcon from '../../../icons/Hamburger';
import ChevronIcon from '../../../icons/Chevron';
import { clearUserClosedRightPanel } from '../../../services/layoutSettings';

export default function SideDrawerOpenButton({ manageView = false }) {
  const { isOpenDrawer, openDrawer } = useMenuDrawer();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const user = useUser();
  const ssoAdminAccount = get(user, 'accounts[1]', true);
  const { authedNavigateToAI } = useAuth();
  const customLogoSrc = get(user, 'accounts[0].customLogoSrc');
  const customDarkLogoSrc = get(user, 'accounts[0].customDarkLogoSrc');
  const drawerIsOpen = isOpenDrawer();
  const customLogoUrl = customLogoSrc && !customDarkLogoSrc ? customLogoSrc : customDarkLogoSrc || null;
  const openDrawerAndFocus = () => {
    openDrawer();
    clearUserClosedRightPanel();
    setTimeout(() => {
      const el = document.getElementById('side-drawer-close-button');
      if (el) {
        el.focus();
      }
    }, 500);
  };

  const handleSignoutSSOAdmin = async (e) => {
    e.preventDefault();
    authedNavigateToAI(user, 'signoutSSOadmin');
  };

  // hide if user is sso admin
  if (ssoAdminAccount && ssoAdminAccount.id) {
    return (
      <div className={styles.adminSSOButtonContainer}>
        <div className={styles.adminSSOAccountName}>
          {ssoAdminAccount.name}
          &nbsp;
          ({ssoAdminAccount.id})
          &nbsp;
        </div>
        <div className={styles.buttonContainer}>
          <Button
            variant="contained"
            className={styles.signOutButton}
            onClick={handleSignoutSSOAdmin}
          >
            Sign Out
          </Button>
        </div>
      </div>
    );
  }

  return (
    // use opacity to hide drawer so that page layout is not affected
    <div style={{ opacity: drawerIsOpen ? '0' : '100' }}>
      <Button
        tabIndex={drawerIsOpen ? -1 : 0}
        aria-label={t('platform.emtrain_navigation_panel')}
        id="side-drawer-close-button"
        className={styles.drawerBtn}
        onClick={() => openDrawerAndFocus()}
        aria-controls="side-drawer"
        onKeyDown={
          (e) => {
            if (e.code === 'Enter' || e.code === 'Space') {
              openDrawerAndFocus();
            }
          }
        }
        sx={{
          '&:hover': {
            backgroundColor: 'transparent',
          },
          '&:focus': {
            border: `2px solid ${palette.card.backgroundColor} !important`,
          },
        }}
      >
        <>
          <ChevronIcon {...{ color: palette.primary.dark, height: '16px', width: '9px', direction: 'left' }} />
          <HamburgerIcon {...{ color: palette.primary.dark }} />
        </>
        {(!customLogoUrl || manageView) && (
        <EmtrainStandardLogo
          role="img"
          aria-label={t('platform.emtrain_logo')}
          className={styles.emtrainLogo}
        />
        )}
        {customLogoUrl && !manageView && (
        <img
          src={customLogoUrl}
          alt="LogoImage"
          className={styles.customLogo}
        />
        )}
      </Button>
    </div>
  );
}
