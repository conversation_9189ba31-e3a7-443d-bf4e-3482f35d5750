import React from 'react';
import { Drawer as MaterialDrawer } from '@mui/material';
import { makeStyles } from '@mui/styles';

import { useAssignmentsDrawer } from '../../../hooks/useDrawer';

import styles from './AssignmentsDrawer.module.css';

export default function AssignmentsDrawer({ position, bannerHeight, children }) {
  const { isOpenDrawer, closeDrawer } = useAssignmentsDrawer();

  const useStyles = makeStyles({
    bannerTopMargin: {
      marginTop: `${bannerHeight}rem` || 0,
    },
  });
  const classes = useStyles();

  return (
    <MaterialDrawer
      className={styles.drawerContainer}
      classes={{
        paper: classes.bannerTopMargin,
      }}
      BackdropProps={{
        sx: {
          marginTop: `${bannerHeight}rem`,
        },
      }}
      anchor={position}
      open={isOpenDrawer()}
      onClose={closeDrawer}
    >
      {children}
    </MaterialDrawer>
  );
}
