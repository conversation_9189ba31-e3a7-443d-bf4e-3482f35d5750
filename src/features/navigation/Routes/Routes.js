import { <PERSON>ss<PERSON><PERSON>line } from '@mui/material';
import React, { Suspense, lazy } from 'react';
import {
  BrowserRouter as Router,
  Switch,
  Route,
  Redirect,
} from 'react-router-dom';

import Spinner from '../../../components/Spinner/Spinner';
import AppContainer from '../../../main/AppContainer/AppContainer';
import { appRoutes } from './AppRoutes';
import AuthedRoute from './AuthedRoute';

const {
  USER_PROFILE,
  HOME,
  PROGRAM_ASSIGNMENT,
  LESSON_ASSIGNMENT,
  LOGIN,
  SCORM_PROGRAM,
  SCORM_LESSON,
  PREVIEW_PROGRAM,
  PREVIEW_LESSON,
  PREVIEW_LESSON_CARD,
  SHARED_PROGRAM,
  SHARED_LESSON,
  SAML,
  SAML_ACCOUNT_ADMIN,
  JETT_SSO,
  EXPERT_QUESTION,
  RESET_PASSWORD,
  VERIFY_AND_RESET,
  SETUP_USER,
  PAGE_NOT_FOUND,
  LINKEDIN,
  ANSWERS,
  VERIFY,
  MANAGE,
  SCORM_EXIT,
  EXTERNAL_PROGRAM,
  EXTERNAL_LESSON,
  EXTERNAL_PROGRAM_ASSIGNMENT,
  EXTERNAL_LESSON_ASSIGNMENT,
} = appRoutes;

const LearnLayout = lazy(() => import('../../../layouts/LearnLayout'));
const Login = lazy(() => import('../../authentication/Login'));
const Scorm = lazy(() => import('../../scorm/Scorm'));
const Saml = lazy(() => import('../../saml/Saml'));
const JettSSO = lazy(() => import('../../jett-sso/JettSSO'));
const Preview = lazy(() => import('../../preview/Preview'));
const PageNotFound = lazy(() => import('../../error/PageNotFound'));
const SharedAssignment = lazy(() => import('../../shared-assignment/SharedAssignment'));
const ExternalAssignment = lazy(() => import('../../external-assignment/ExternalAssignment'));
const AIRedirect = lazy(() => import('../../ai-redirect/AIRedirect'));

const ManageLayout = lazy(() => import('../../../layouts/ManageLayout'));

export default function Routes({ isAuthed }) {
  return (
    <Router>
      <CssBaseline />
      <Suspense fallback={<Spinner customContainerHeight="100vh" />}>
        <AppContainer isAuthed={isAuthed}>
          <Switch>
            <AuthedRoute path={USER_PROFILE}>
              <LearnLayout profileView />
            </AuthedRoute>
            <AuthedRoute path={EXPERT_QUESTION}>
              <LearnLayout expertsQAview />
            </AuthedRoute>
            <AuthedRoute exact path={[PREVIEW_PROGRAM, PREVIEW_LESSON]}>
              <Preview />
            </AuthedRoute>
            <AuthedRoute exact path={[PREVIEW_LESSON_CARD]}>
              <Preview />
            </AuthedRoute>
            <AuthedRoute path={[SHARED_PROGRAM, SHARED_LESSON]}>
              <SharedAssignment />
            </AuthedRoute>
            <AuthedRoute path={[EXTERNAL_PROGRAM, EXTERNAL_LESSON]}>
              <ExternalAssignment />
            </AuthedRoute>
            <AuthedRoute
              exact
              path="/"
              render={() => (
                <Redirect to={HOME} />
              )}
            />
            <AuthedRoute path={[PROGRAM_ASSIGNMENT, LESSON_ASSIGNMENT, HOME]}>
              <LearnLayout />
            </AuthedRoute>
            <AuthedRoute path={[EXTERNAL_PROGRAM_ASSIGNMENT, EXTERNAL_LESSON_ASSIGNMENT]}>
              <LearnLayout fromExternal />
            </AuthedRoute>
            <AuthedRoute path={[MANAGE]}>
              <ManageLayout manageView />
            </AuthedRoute>
            <Route exact path={[LOGIN, RESET_PASSWORD, VERIFY_AND_RESET, SETUP_USER, LINKEDIN, VERIFY]}>
              <Login />
            </Route>
            <Route path={[SCORM_PROGRAM, SCORM_LESSON]}>
              <Scorm />
            </Route>
            <Route path={[SAML, SAML_ACCOUNT_ADMIN]}>
              <Saml />
            </Route>
            <Route path={[JETT_SSO]}>
              <JettSSO />
            </Route>
            <Route path={[ANSWERS]}>
              <AIRedirect />
            </Route>
            <Route path={[SCORM_EXIT]}>
              <PageNotFound />
            </Route>
            <Route path={[PAGE_NOT_FOUND]}>
              <PageNotFound />
            </Route>
          </Switch>
        </AppContainer>
      </Suspense>
    </Router>
  );
}
