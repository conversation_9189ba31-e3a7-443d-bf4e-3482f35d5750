export const appRoutes = {
  USER_PROFILE: '/profile',
  ANSWERS: '/answers/:questionId',
  HOME: '/home',
  PROGRAM_ASSIGNMENT: '/assignment/:assignmentId/program/:programId/lesson/:lessonId/card/:cardId',
  LESSON_ASSIGNMENT: '/assignment/:assignmentId/microlesson/:lessonId/card/:cardId',
  EXTERNAL_PROGRAM_ASSIGNMENT: '/external/assignment/:assignmentId/program/:programId/lesson/:lessonId/card/:cardId',
  EXTERNAL_LESSON_ASSIGNMENT: '/external/assignment/:assignmentId/microlesson/:lessonId/card/:cardId',
  LOGIN: '/login',
  SCORM_PROGRAM: '/scorm/programs/:programId',
  SCORM_LESSON: '/scorm/microlessons/:lessonId',
  PREVIEW_PROGRAM: '/preview/program/:programId',
  PREVIEW_LESSON: '/preview/microlesson/:lessonId',
  PREVIEW_LESSON_CARD: '/preview/microlesson/:lessonId/card/:cardId',
  SHARED_PROGRAM: '/share/program/:programId',
  SHARED_LESSON: '/share/microlesson/:lessonId',
  EXTERNAL_PROGRAM: '/external/program/:programId',
  EXTERNAL_LESSON: '/external/microlesson/:lessonId',
  SAML: '/saml/:token',
  SAML_ACCOUNT_ADMIN: '/account-admin',
  JETT_SSO: '/jett-sso',
  EXPERT_QUESTION: '/question/:questionId',
  RESET_PASSWORD: '/reset/:token',
  VERIFY_AND_RESET: '/verifyAndReset/:token',
  SETUP_USER: '/setupuser/:token',
  VERIFY: '/verify/:token',
  PAGE_NOT_FOUND: '/*',
  PAGE_UNAUTHORIZED: '/unauthorized',
  SCORM_EXIT: '/scorm-exit/:message',
  LINKEDIN: '/linkedin',
  // ManageRoutes
  MANAGE: '/manage',
  INSIGHTS_PROGRAMS: '/manage/insights/reports/program/:programId',
  INSIGHTS_LESSONS: '/manage/insights/reports/lesson/:lessonId',
  RESOURCES: '/manage/resources',
  ANALYTICS: '/manage/analytics/:page',
  ANALYTICS_PAGE: '/manage/analytics/:page/:analyticspage',
  CONTENT_LIBRARY: '/manage/new-content-library',
  CONTENT_LIBRARY_DETAILS: '/manage/new-content-library/:mclType/:mclID/content/:contentId',
  CONTENT_CONFIGURE: '/manage/new-content-library/:mclType/:mclID/content/:contentId/courseConfigure',
  CONTENT_POLICIES: '/manage/new-content-library/:mclType/:mclID/content/:contentId/policies',
  CONTENT_COURSE_LESSON_DETAILS: '/manage/new-content-library/:mclType/:mclID/content/:contentId/lesson/:lessonId',
  COURSE_LESSON_DETAILS: '/manage/new-content-library/lesson/:lessonId',
};

export const pushToProgram = ({ assignmentId, programId, lessonId }) => {
  return `/assignment/${assignmentId}/program/${programId}/lesson/${lessonId}/card/_`;
};

export const pushToContentFromExternal = ({ assignmentId, programId, lessonId, contentType }) => {
  let url = '/home/';
  if (contentType === 'program') {
    url = `/external/assignment/${assignmentId}/program/${programId}/lesson/${lessonId}/card/_`;
  } else if (contentType === 'microlesson') {
    url = `/external/assignment/${assignmentId}/microlesson/${lessonId}/card/_`;
  }
  return url;
};

export const pushToMicroLesson = ({ assignmentId, lessonId }) => {
  return `/assignment/${assignmentId}/microlesson/${lessonId}/card/_`;
};

export const pushToCard = (cardId) => {
  return `./${cardId}`;
};

export const pushToHome = () => {
  return '/home';
};

export const pushToExpertQA = ({ questionId }) => {
  return `/question/${questionId}`;
};

export const pushToPageNotFound = () => {
  return '/pagenotfound';
};

export const pushToInsightProgramReports = (id, queryString) => {
  return `/manage/insights/reports/program/${id}${queryString}`;
};

export const pushToInsightLessonReports = (id, queryString) => {
  return `/manage/insights/reports/lesson/${id}${queryString}`;
};

export const scormExit = ({ message }) => {
  return `/scorm-exit/${message}`;
};

export const pushToAnalyticsSubMenu = (menu, subMenu = null) => {
  return (subMenu ? `/manage/analytics/${menu}/${subMenu}` : `/manage/analytics/${menu}`);
};

export const pushToExploreAnalyticsData = (path) => {
  return `/manage/analytics/${path}`;
};

export const pushToContentLibraryDetails = (mclType, mclID, contentId) => {
  return `/manage/new-content-library/${mclType}/${mclID}/content/${contentId}`;
};

export const pushToCourseConfigure = (mclType, mclID, contentId) => {
  return `/manage/new-content-library/${mclType}/${mclID}/content/${contentId}/courseConfigure`;
};

export const pushToContentPreview = (type = 'lesson', contentId) => {
  const contentType = type === 'lesson' ? 'microlesson' : type;
  return `/preview/${contentType}/${contentId}`;
};

export const pushToPolicies = (mclType, mclID, contentId) => {
  return `/manage/new-content-library/${mclType}/${mclID}/content/${contentId}/policies`;
};

export const pushToCourseLessonDetails = (mclType, mclID, contentId, lessonId) => {
  if (mclType === 'lesson') {
    return `/manage/new-content-library/lesson/${mclID}`;
  }
  return `/manage/new-content-library/${mclType}/${mclID}/content/${contentId}/lesson/${lessonId}`;
};

export const pushToPageUnauthorized = () => {
  return '/unauthorized';
};
