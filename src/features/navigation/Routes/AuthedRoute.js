import React, { useEffect } from 'react';
import { get } from 'lodash';
import { Route, Redirect, useLocation } from 'react-router-dom';
import { matchPath } from 'react-router';
import { useUser } from '../../../hooks/useUser';
import { appRoutes } from './AppRoutes';
import { useAuth } from '../../../hooks/useAuth';

const {
  SHARED_PROGRAM,
  SHARED_LESSON,
  USER_PROFILE,
  EXPERT_QUESTION,
  ANSWERS,
  PREVIEW_PROGRAM,
  PREVIEW_LESSON,
  EXTERNAL_PROGRAM,
  EXTERNAL_LESSON,
  EXTERNAL_PROGRAM_ASSIGNMENT,
  EXTERNAL_LESSON_ASSIGNMENT,
} = appRoutes;

export default function AuthedRoute({ children, ...rest }) {
  const user = useUser();

  // Check to see if the user is trying to access a valid authed route when they are not logged in.
  // If they are, we will forward to this route after they login. As other routes are determined to
  // be valid, we can add them to the list here. For now, I am not going to forward to specific assignments.
  // However, it's likely that forwarding to the assignment routes will be requested after we release.
  const routeLocation = useLocation();
  const { search } = useLocation();
  const { verifyContentPreview, logout } = useAuth();
  const queryParams = new URLSearchParams(search);
  const authKey = queryParams.get('AUTH_KEY');
  const authToken = queryParams.get('AUTH_TOKEN');

  useEffect(() => {
    async function contentPreviewVerification() {
      try {
        await verifyContentPreview(authKey, authToken);
        const validForwardToRoutes = [SHARED_LESSON, EXTERNAL_PROGRAM_ASSIGNMENT, EXTERNAL_LESSON_ASSIGNMENT];
        const foundMatch = validForwardToRoutes.find(((nextRoute) => {
          return !!matchPath(routeLocation.pathname, { path: nextRoute, exact: true, strict: true });
        }));
        if (foundMatch) {
          localStorage.setItem('forwardToRoute', routeLocation.pathname);
        }
      } catch (e) {
        await logout();
        return window.location.replace('/login');
      }
    }
    if (authKey && authToken) {
      contentPreviewVerification();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authKey, authToken]);

  if (!user && routeLocation && routeLocation.pathname && !authKey && !authToken) {
    localStorage.removeItem('forwardToRoute');
    const validForwardToRoutes = [SHARED_PROGRAM, SHARED_LESSON, USER_PROFILE, EXPERT_QUESTION, ANSWERS,
      PREVIEW_PROGRAM, PREVIEW_LESSON, EXTERNAL_PROGRAM, EXTERNAL_LESSON,
      EXTERNAL_PROGRAM_ASSIGNMENT, EXTERNAL_LESSON_ASSIGNMENT];
    matchPath(routeLocation.pathname, { path: ANSWERS, exact: true, strict: true });
    const foundMatch = validForwardToRoutes.find(((nextRoute) => {
      return !!matchPath(routeLocation.pathname, { path: nextRoute, exact: true, strict: true });
    }));
    if (foundMatch) {
      localStorage.setItem('forwardToRoute', routeLocation.pathname);
    }
  }

  if (!user && !authKey && !authToken) {
    return (
      <Route {...rest}>
        {({ location }) => <Redirect to={{ pathname: '/login', state: { from: location } }} />}
      </Route>
    );
  }
  const isJettClient = get(user, 'accounts[0].isJettClient');
  if (!isJettClient) {
    // This commented out code below is not necessary because we redirect to legacy elsewhere
    // const aiURL = aiAppURL();
    // window.location.assign(aiURL);
    return (
      <div />
    );
  }

  return (
    <Route {...rest}>
      {children}
    </Route>
  );
}
