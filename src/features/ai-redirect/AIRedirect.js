import React, { useEffect } from 'react';

import { useRouteMatch, useHistory } from 'react-router-dom';
import { pushToHome } from '../navigation/Routes/AppRoutes';

import { getCurrentAccount } from '../../services/api/currentAccount';

function AIRedirect() {
  const { path, params: { questionId } } = useRouteMatch();
  const [, action] = path.split('/');
  const history = useHistory();

  useEffect(() => {
    async function handleAIRedirect() {
      if (action === 'answers') {
        const currentAccount = await getCurrentAccount();
        if (!currentAccount) {
          return;
        }

        if (!currentAccount.isJettClient) {
          localStorage.setItem('aiForwardTo', `answers/${questionId}`);
          history.push(pushToHome());
        } else {
          localStorage.setItem('answersForwardTo', '/profile');
          localStorage.setItem('myProfileTab', 'questions');
          history.push('/profile');
          return false;
        }
      }
    }
    handleAIRedirect();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div />
  );
}

export default AIRedirect;
