import React from 'react';
import { useRouteMatch } from 'react-router-dom';
import Summary from '../Common/CultureSkills/Summary';
import QuestionScores from '../Common/CultureSkills/QuestionScores';
import CultureSkillScores from '../Common/CultureSkills/SkillScores';
import Recommendations from './Recommendations';

function Skills({ analyticsIdHash }) {
  const { params: routeParams } = useRouteMatch();
  const path = routeParams?.analyticspage;
  const cultureSkillsContainer = () => {
    switch (path) {
      case 'summary':
        return <Summary analyticsIdHash={analyticsIdHash} />;
      case 'skillScores':
        return <CultureSkillScores analyticsIdHash={analyticsIdHash} />;
      case 'questionScores':
        return <QuestionScores analyticsIdHash={analyticsIdHash} />;
      case 'recommendations':
        return <Recommendations analyticsIdHash={analyticsIdHash} />;
      default:
        return null;
    }
  };
  return cultureSkillsContainer();
}
export default Skills;
