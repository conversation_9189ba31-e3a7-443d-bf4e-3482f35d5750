import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Box, Grid, Card, CardContent, Typography, Button, Dialog, useTheme, Divider } from '@mui/material';
import { get } from 'lodash';
import ArrowForwardIcon from '@mui/icons-material/ArrowForwardIos';
import { useHistory } from 'react-router-dom';
import TableauReportContainer from '../Common/TableauReportContainer';
import PeopleIcon from '../../../images/people.png';
import BulbIcon from '../../../images/lighbulb.png';
import HRPersonalIcon from '../../../images/hr-personnel-circle.png';
import CultureIcon from '../../../images/culture-circle.png';
import ComplianceIcon from '../../../images/compliance-circle.png';
import externalLinkIcon from '../../../images/external-link.png';
import CloseBtn from '../../../images/close_button.png';
import { useUser } from '../../../hooks/useUser';
import { getOverViewList } from '../../../services/api/AnalyticsRisks';
import styles from '../AnalyticsContainer.module.css';
import { pushToExploreAnalyticsData } from '../../navigation/Routes/AppRoutes';
import CalendarIcon from '../../../images/overview_calendar.png';
import MenuBookIcon from '../../../images/overview_cards.png';
import UsersIcon from '../../../images/overview_people.png';
import ChatIcon from '../../../images/overview_chat_bubble.png';
import SpinnerGif from '../../../images/spinner.gif';

export const StyledActivityStatsContainer = styled.div`
  display: flex;
  align-items: stretch;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 6px;
  margin: 20px 0 20px 0;
  height: 100px;
  width: 1208px;
`;

const ViewDetailsIcon = styled.img`
  width: 20px;
  height: 20px;
  margin-top: 2px;
  color: #4A87C6;
`;

export const StyledContainer = styled.div`
  display: grid;
  grid-template-columns: 1.2fr 1fr 1fr 1fr 0.8fr;
  justify-content: center;
  &.button {
    margin: 8px;
    width: 168px;
  }
`;

export const StyledViz = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-around;
`;

const ViewDetailsButton = styled(Button)`
&&& {
  font-weight: 700;
  color: #4183C4;
  display: flex;
  align-items: flex-start;
  background-color: #FFFFFF;
  :active, :hover, :focus {
    background-color: #FFFFFF;
    color: #4A87C6;
  }
  span{
    text-transform: none;
    padding-right: 8px;
    text-align: left;
    line-height: 1em;
  }
}
`;

const IconImage = styled('img')`
  width: 50px;
  height: 50px;
`;

const initialOverViewData = [
  {
    riskPersona: 'Culture',
    title: 'Culture Skills',
    description:
      'Measure employees’ behavior in the key competencies of Respect, Ethics, Inclusion and Belonging skills.',
    insights: '0 of 16',
    responses: '0%',
    icon: CultureIcon,
    buttonLabel: 'Explore strengths and opportunities',
    path: 'skills/summary',
  },
  {
    riskPersona: 'HR',
    title: 'HR & People Risks',
    description:
      'Spot emerging workplace conflicts early and take action to minimize risks and claims.',
    insights: '0 of 5',
    responses: '0%',
    icon: HRPersonalIcon,
    buttonLabel: 'See where there are HR & People Risks',
    path: 'risks/summary',
  },
  {
    riskPersona: 'Business',
    title: 'Business Compliance Risks',
    description:
      'Uncover and address compliance gaps to protect your business from costly penalties.',
    insights: '0 of 5',
    responses: '0%',
    icon: ComplianceIcon,
    buttonLabel: 'See where there are Compliance risks',
    path: 'compliance/summary',
  },
];
const initialOverViewStatsData = {
  contentCount: 0,
  userCount: 0,
  responseCount: 0,
  dateMinResponse: '',
  dateMaxResponse: ''
}

function Overview({ analyticsIdHash, setPermissionDialogueDisabled }) {
  const { palette } = useTheme();
  const history = useHistory();
  const [overView, setOverview] = useState([]);
  const [overViewStats, setOverviewStats] = useState(initialOverViewStatsData);
  const [open, setOpen] = useState(false);
  const [loader, setLoader] = useState(true);
  const user = useUser();
  let analyticsId = get(user, 'accounts[1].analyticsId');
  if (!analyticsId) {
    analyticsId = get(user, 'accounts[0].analyticsId');
  }

  useEffect(() => {
    async function fetchOverViews() {
      const overViewResponse = await getOverViewList(analyticsId);
      setOverview(overViewResponse.overview);
      setOverviewStats(overViewResponse.overviewStats[0]);
      setLoader(false);
    }
    fetchOverViews();
  }, [analyticsId]);

  const overViewData = initialOverViewData.map((item) => {
    const matchedData = overView.find((d) => d.riskPersona === item.riskPersona) || [];
    return {
      ...item,
      insights: matchedData ? `${matchedData.countRiskArea || 0} of ${item.insights.split(' of ')[1]}` : item.insights,
      responses: matchedData ? `${matchedData.maxResponseRate || 0}%` : item.responses,
    };
  });

  const navigateToExploreData = (path) => {
    history.push(pushToExploreAnalyticsData(path));
  };

  const StatBox = ({ iconSrc, label }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', px: 2 }}>
      <IconImage src={iconSrc} />
      <Box sx={{ ml: 2 }}>
        <Typography sx={{ lineHeight: '1.3' }} dangerouslySetInnerHTML={{ __html: label }} />
      </Box>
    </Box>
  );

  if (loader) {
    return (
      <div className={styles.loadSpinner}>
        <img src={SpinnerGif} width={50} aria-label="tilesLoading" />
      </div>
    )
  }

  return (
    <div>
      <StyledActivityStatsContainer>
        <StyledContainer>
          <StyledViz>
            <StatBox
              iconSrc={CalendarIcon}
              label={`Response Range <br/> ${overViewStats.dateMinResponse} - <br/> ${overViewStats.dateMaxResponse}`}
            />
            <Divider orientation="vertical" flexItem sx={{ margin: '16px 0px' }} />
          </StyledViz>
          <StyledViz>
            <StatBox
              iconSrc={MenuBookIcon}
              label={`<b>${overViewStats.contentCount.toLocaleString()}</b> Courses and Microlessons`}
            />
            <Divider orientation="vertical" flexItem sx={{ margin: '16px 0px' }} />
          </StyledViz>
          <StyledViz>
            <StatBox
              iconSrc={UsersIcon}
              label={`<b>${overViewStats.userCount.toLocaleString()}</b><br/>Learners`}
            />
            <Divider orientation="vertical" flexItem sx={{ margin: '16px 0px' }} />
          </StyledViz>
          <StyledViz>
            <StatBox
              iconSrc={ChatIcon}
              label={`<b>${overViewStats.responseCount.toLocaleString()}</b><br/>Responses`}
            />
            <Divider orientation="vertical" flexItem sx={{ margin: '16px 0px' }} />
          </StyledViz>
          <StyledViz onClick={() => { setOpen(true); setPermissionDialogueDisabled(false); }}>
            <ViewDetailsButton disableRipple>
              <span>
                View Content
                <br />
                &nbsp;
                Deployment
              </span>
              <ViewDetailsIcon
                src={externalLinkIcon}
                alt="View Details"
              />
            </ViewDetailsButton>
          </StyledViz>
        </StyledContainer>
      </StyledActivityStatsContainer>
      <Grid container spacing={3}>
        {overViewData.map(({ riskPersona, title, description, icon, insights, responses, path, buttonLabel }) => (
          <Grid item xs={12} md={4} key={riskPersona}>
            <Card
              className={styles.cardStyle}
              sx={{ backgroundColor: palette.primary.white, color: palette.primary.main }}
            >
              <Box
                sx={{ backgroundColor: palette.primary.light }}
                className={styles.boxHeader}
              >
                <Typography variant="h6" fontWeight="bold" sx={{ ml: '1.9rem' }}>
                  {title}
                </Typography>
                <Box
                  className={styles.headerImage}
                >
                  <img
                    src={icon}
                    alt={title}
                    width={75}
                    height={75}
                  />
                </Box>
              </Box>
              <CardContent className={styles.cardContent}>
                <Typography
                  variant="body2"
                  className={styles.cardDescription}
                >
                  {description}
                </Typography>
                <Box
                  className={styles.detailsSection}
                  sx={{ backgroundColor: palette.primary.light }}
                >
                  <img
                    src={BulbIcon}
                    alt="bulbIcon"
                    height={20}
                  />
                  <Typography variant="span">
                    Insights available for <b>{insights}</b>
                    &nbsp;{riskPersona === 'Culture' ? 'skill' : 'risk'} areas
                  </Typography>
                </Box>
                <Box
                  className={styles.detailsSection}
                  sx={{ backgroundColor: palette.primary.light }}
                >
                  <img
                    src={PeopleIcon}
                    alt="peopleIcon"
                    height={18}
                  />
                  <Typography variant="span">
                    Responses from <b>{responses}</b> of your learners
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  fullWidth
                  disableRipple
                  className={styles.exploreButton}
                  sx={{
                    color: palette.primary.white,
                    backgroundColor: palette.primary.main,
                    '&:hover': {
                      color: palette.primary.white,
                      backgroundColor: palette.button.login.hoverBackgroundColor,
                    },
                    '&:focus': {
                      border: `solid ${palette.card.backgroundColor} 3px`,
                    },
                  }}
                  onClick={() => navigateToExploreData(path)}
                >
                  {buttonLabel}
                  <ArrowForwardIcon className={styles.buttonArrow} />
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
      {open && (
        <Dialog
          open
          onClick={() => {
            setOpen(false);
            setPermissionDialogueDisabled(true);
          }}
          PaperProps={{
            style: {
              margin: 0,
              borderRadius: '1rem',
              padding: '2rem 0 2rem 0',
              position: 'absolute',
              maxWidth: '890px',
              maxHeight: '800px',
              overflow: 'hidden',
              backgroundColor: palette.background.white,
              filter: palette.border.dropShadow,
            },
          }}
          BackdropProps={{
            style: {
              backdropFilter: 'blur(5px)',
            },
          }}
        >
          <Button
            className={styles.closeModal}
            onClick={() => {
              setOpen(false);
              setPermissionDialogueDisabled(true);
            }}
          >
            <img
              id="close_button"
              src={CloseBtn}
              className={styles.closeButtonImage}
              alt="closeButton"
            />
          </Button>
          <TableauReportContainer
            view="contentContributing/ContentContributing"
            analyticsIdHash={analyticsIdHash}
            options={{ width: '890px', height: '62vh' }}
          />
        </Dialog>
      )}
    </div>
  );
}
export default Overview;
