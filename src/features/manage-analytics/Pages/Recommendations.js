import React from 'react';
import styled from 'styled-components';

import TableauReportContainer from '../Common/TableauReportContainer';

export const StyledVizContainer = styled.div`
  width: 1220px;
  display: flex;
  justify-content: flex-start;
`;

function Recommendations({ analyticsIdHash, riskPersona = null }) {
  const analyticsParams = {};
  let recommendationHeight = '650px';
  const recommendationView = () => {
    switch (riskPersona) {
      case 'HR':
      case 'Business':
        analyticsParams.personaParam = riskPersona;
        recommendationHeight = '5000px';
        return 'riskRecommendedActionsCompliance/recActionsPage';
      default:
        return 'recommendations/RecommendedContent';
    }
  };

  return (
    <StyledVizContainer style={{ height: recommendationHeight }}>
      <TableauReportContainer
        view={recommendationView()}
        tokenName="Recommendations"
        analyticsIdHash={analyticsIdHash}
        analyticsParams={analyticsParams}
      />
    </StyledVizContainer>
  );
}
export default Recommendations;
