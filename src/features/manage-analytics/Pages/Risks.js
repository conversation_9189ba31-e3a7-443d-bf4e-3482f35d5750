import React from 'react';
import { useRouteMatch } from 'react-router-dom';
import RiskComplianceMain from '../Common/AnalyticRisks/RiskComplianceMain';
import Summary from '../Common/AnalyticRisks/Summary';
import Recommendations from './Recommendations';

function Risks({ setClickProblemLink, analyticsIdHash, setPermissionDialogueDisabled }) {
  const { params: routeParams } = useRouteMatch();
  const path = routeParams?.analyticspage;
  const riskContainer = () => {
    switch (path) {
      case 'summary':
        return <Summary riskPersona="HR" />;
      case 'recommendations':
        return <Recommendations analyticsIdHash={analyticsIdHash} riskPersona="HR" />;
      default:
        return (
          <RiskComplianceMain
            riskPersona="HR"
            setClickProblemLink={setClickProblemLink}
            setPermissionDialogueDisabled={setPermissionDialogueDisabled}
          />
        );
    }
  };
  return riskContainer();
}
export default Risks;
