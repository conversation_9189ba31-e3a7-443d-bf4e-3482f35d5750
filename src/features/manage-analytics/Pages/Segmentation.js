import React from 'react';
import { useRouteMatch } from 'react-router-dom';
import ByCultureSkill from '../Common/Segmentation/ByCultureSkill';
import ByQuestion from '../Common/Segmentation/ByQuestion';

function Segmentation({ analyticsIdHash }) {
  const { params: routeParams } = useRouteMatch();
  const path = routeParams?.analyticspage;
  const cultureSkillsContainer = () => {
    switch (path) {
      case 'byQuestion':
        return <ByQuestion analyticsIdHash={analyticsIdHash} />;
      case 'bySkill':
        return <ByCultureSkill analyticsIdHash={analyticsIdHash} />;
      default:
        return null;
    }
  };
  return cultureSkillsContainer();
}
export default Segmentation;
