import React from 'react';
import { useRouteMatch } from 'react-router-dom';
import RiskComplianceMain from '../Common/AnalyticRisks/RiskComplianceMain';
import Summary from '../Common/AnalyticRisks/Summary';
import Recommendations from './Recommendations';

function Compliance({ setClickProblemLink, analyticsIdHash, setPermissionDialogueDisabled }) {
  const { params: routeParams } = useRouteMatch();
  const path = routeParams?.analyticspage;
  const complianceContainer = () => {
    switch (path) {
      case 'summary':
        return <Summary riskPersona="Business" />;
      case 'recommendations':
        return <Recommendations analyticsIdHash={analyticsIdHash} riskPersona="Business" />;
      default:
        return (
          <RiskComplianceMain
            riskPersona="Business"
            setClickProblemLink={setClickProblemLink}
            setPermissionDialogueDisabled={setPermissionDialogueDisabled}
          />
        );
    }
  };
  return complianceContainer();
}
export default Compliance;
