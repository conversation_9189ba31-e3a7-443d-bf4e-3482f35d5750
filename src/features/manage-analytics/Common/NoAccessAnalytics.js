/* eslint-disable import/no-duplicates */
/* eslint-disable max-len */
import React, { useState } from 'react';
import { Button, Modal, Box, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useHistory } from 'react-router-dom';
import { get } from 'lodash';
import styles from './NoAccessAnalytics.module.css';
import questionScoresImg from '../../../images/question_scores.png';
import skillScoresImg from '../../../images/skill_scores.png';
import hrRisks from '../../../images/hr_risks.png';
import complianceRisks from '../../../images/compliance_risks.png';
import hrRiskScores from '../../../images/hr_risk_scores.png';
import complianceRiskScores from '../../../images/compliance_risk_scores.png';
import segBySkills from '../../../images/seg_bySkills.png';
import segByQuestion from '../../../images/seg_byQuestion.png';
import { useUser } from '../../../hooks/useUser';

function NoAccessAnalytics({ previousUrl, page, subPath, analyticsRolesData, clickProblemLink, setClickProblemLink, setPermissionDialogueDisabled, hasCultureSkills, hasHrPeopleRisk, hasBusinessComplianceRisk, reportingAccess }) {
  const user = useUser();
  const name = `${user?.firstName} ${user?.lastName}`;
  const email = `${user?.email}`;
  const accountName = user?.adminAccountId ? get(user, 'accounts[1].name') : get(user, 'accounts[0].name');
  const [open, setOpen] = useState(false);
  const history = useHistory();

  const navigateToPreviousTab = () => {
    setOpen(false);
    setPermissionDialogueDisabled(true);
    setClickProblemLink(false);
    const isWhereProblemModal = window.sessionStorage.getItem('whereIsProblem') || false;
    if (isWhereProblemModal) {
      const analyticsTab = window.sessionStorage.getItem('analyticsTabUrl');
      sessionStorage.removeItem('whereIsProblem');
      setPermissionDialogueDisabled(false);
      return history.push(`/manage/analytics/${analyticsTab}`);
    }
    return history.push(`/manage/analytics/${previousUrl}`);
  };
  const userRoleIs = (array, roleName) => {
    return array.some((item) => item.role.name === roleName);
  };

  const problemModelDetails = {
    header: "Segmentation isn't included in your package…but it could be!",
    chatWith: 'Chat with your Client Account Executive to',
    content: "Now that we've identified there may be a problem, take the next step to see the specific hotspots that may need additional attention. Spot groups at risk and take action to avoid costly penalties and claims.",
    img: page === 'risks' ? hrRiskScores : complianceRiskScores,
    submitText: 'Unlock Emtrain Intelligence™',
    topicOrQuestion: 'I would like to discuss adding Segmented Risk data to my Emtrain Intelligence package.',
  };

  const risksSummary = [
    {
      page: 'risks',
      header: "HR & People Risks aren't included in your package…but they could be!",
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Spot emerging HR and People Risks early, including areas like Harassment & Discrimination, Workplace Safety, and Retaliation. See how your employees’ responses compare to industry benchmarks, and align your strategy to address emerging workforce risks effectively.',
      img: hrRisks,
      submitText: 'Unlock Emtrain Intelligence™',
      topicOrQuestion: 'I would like to discuss adding HR & People Risk data to my Emtrain Intelligence package.',
    },
    {
      page: 'risks',
      header: "HR & People Risks Scores aren't included in your package...but they could be!",
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Access question-level responses and recommendations around emerging HR and People Risks early, including areas like Harassment & Discrimination, Workplace Safety, and Retaliation. See how your employees’ responses compare to industry benchmarks, and align your strategy to address emerging workforce risks effectively.',
      img: hrRisks,
      submitText: 'Unlock Emtrain Intelligence™',
      level: 'level1',
      topicOrQuestion: 'I would like to discuss adding HR & People Risk data to my Emtrain Intelligence package.',
    },
    {
      page: 'risks',
      header: "You don't have permissions to view this data",
      chatWith: '',
      content: "Your organization has access to HR & People Risks data, but you don't have the necessary permissions to view it.",
      img: null,
      submitText: 'Request Access to HR & People Risks',
      level: 'level2',
      topicOrQuestion: 'HR & People Risks',
      individualAccessForm: true,
    },
  ];

  const complianceSummary = [
    {
      page: 'compliance',
      header: "Business Compliance Risks aren't included in your package…but they could be!",
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Uncover and address compliance gaps to protect your business from costly penalties, including areas like Code of Conduct, Cybersecurity, and Bribery & Corruption. See how your employees’ responses compare to industry benchmarks, and align your strategy to address emerging compliance risks effectively.',
      img: complianceRisks,
      submitText: 'Unlock Emtrain Intelligence™',
      level: 'level1',
      topicOrQuestion: 'I would like to discuss adding Business Compliance Risk data to my Emtrain Intelligence package.',
    },
    {
      page: 'compliance',
      header: "Business Compliance Risks Scores aren't included in your package…but they could be!",
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Access question-level responses and recommendations around various compliance gaps to protect your business from costly penalties, including areas like Code of Conduct, Cybersecurity, and Bribery & Corruption. See how your employees’ responses compare to industry benchmarks, and align your strategy to address emerging compliance risks effectively.',
      img: complianceRisks,
      submitText: 'Unlock Emtrain Intelligence™',
      level: 'level1',
      topicOrQuestion: 'I would like to discuss adding Business Compliance Risk data to my Emtrain Intelligence package.',
    },
    {
      page: 'compliance',
      header: "You don't have permissions to view this data",
      chatWith: '',
      content: "Your organization has access to Business Compliance Risk data, but you don't have the necessary permissions to view it.",
      img: null,
      submitText: 'Request Access to Business Compliance Risks',
      level: 'level2',
      topicOrQuestion: 'Business Compliance Risks',
      individualAccessForm: true,
    },
  ];

  const enhanceAnalyticsPermissions = [
    {
      page: 'skills',
      subPath: 'skillScores',
      header: "Culture Skill Scores aren't currently included in your package... But they could be!",
      chatWith: 'Chat with your Client Account Executive to',
      content: 'See how your learners responded to the Likert questions included in your content, benchmarked against our global client dataset. Align your strategies with workforce trends and standards for more impactful results.',
      img: skillScoresImg,
      submitText: 'Unlock Emtrain Intelligence™',
      topicOrQuestion: 'I would like to discuss adding Culture Skill Scores to my Emtrain Intelligence package.',
    },
    {
      page: 'skills',
      subPath: 'questionScores',
      header: "Question Scores aren't currently included in your package... But they could be!",
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Access raw, aggregated sentiment feedback to gauge the pulse of your organization. See how your learners responded to the Likert questions included in your content and how sentiments have changed over time. Identify strengths and opportunities to shape a workplace that resonates with your team.',
      img: questionScoresImg,
      submitText: 'Unlock Emtrain Intelligence™',
      topicOrQuestion: 'I would like to discuss adding Question Scores to my Emtrain Intelligence package.',
    },
  ];
  const segmentationPermissions = [
    {
      page: 'segmentation',
      subPath: 'byQuestion',
      header: 'Segmentation isn’t currently included in your package… But it could be!',
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Use Emtrain’s Segmentation heatmaps to drill down on specific demographics and employee characteristics to gather detailed insights. Identify those groups having a positive or negative outlier experience at your organization and intervene strategically, backed by data.',
      img: segByQuestion,
      submitText: 'Unlock Emtrain Intelligence™',
      topicOrQuestion: 'I would like to discuss adding Segmentation data to my Emtrain Intelligence package.',
    },
    {
      page: 'segmentation',
      subPath: 'bySkill',
      header: 'Segmentation isn’t currently included in your package… But it could be!',
      chatWith: 'Chat with your Client Account Executive to',
      content: 'Use Emtrain’s Segmentation heatmaps to drill down on specific demographics and employee characteristics to gather detailed insights. Identify those groups having a positive or negative outlier experience at your organization and intervene strategically, backed by data.',
      img: segBySkills,
      submitText: 'Unlock Emtrain Intelligence™',
      topicOrQuestion: 'I would like to discuss adding Segmentation data to my Emtrain Intelligence package.',
    },
  ];

  const summaryData = {
    risks: risksSummary,
    compliance: complianceSummary,
    skills: enhanceAnalyticsPermissions,
    segmentation: segmentationPermissions,
  };
  // eslint-disable-next-line no-shadow
  const getData = (page, subPath, hasCultureSkills, hasHrPeopleRisk, hasBusinessComplianceRisk, reportingAccess) => {
    const userRiskPermission = user?.adminAccountId ? true : userRoleIs(analyticsRolesData, 'hrPersonnelRisk');
    const userBusinessPermission = user?.adminAccountId ? true : userRoleIs(analyticsRolesData, 'businessComplianceRisk');
    const isSummaryOnly = reportingAccess === 'SummaryOnly';
    const isQuestionsAndSummary = reportingAccess === 'Questions+Summary';

    if (page === 'risks') {
      if (clickProblemLink && isQuestionsAndSummary) {
        return problemModelDetails;
      }
      // First make sure the account itself has people risk - if not show the general product blocker modal
      if (!hasHrPeopleRisk) {
        return summaryData[page][0];
      }
      // Otherwise, they do have HrPeopleRisk - if on scores or recs, and are summary only, show specific blocker modal about scores
      if (['scores', 'recommendations'].includes(subPath) && isSummaryOnly) {
        return summaryData[page][1];
      }
      // Finally, if we get here check individual user access - if no user permission, show individual access blocker modal
      if (!userRiskPermission) {
        // if user doesn'thave permission
        return summaryData[page][2];
      }
    } else if (page === 'compliance') {
      if (clickProblemLink && isQuestionsAndSummary) {
        return problemModelDetails;
      }
      // First make sure the account itself has business compliance risk - if not show the general product blocker modal
      if (!hasBusinessComplianceRisk) {
        return summaryData[page][0];
      }
      // Otherwise, they do have business compliance risk - if on scores or recs, and are summary only, show specific blocker modal about scores
      if (['scores', 'recommendations'].includes(subPath) && isSummaryOnly) {
        return summaryData[page][1];
      }
      // Finally, if we get here check individual user access - if no user permission, show individual access blocker modal
      if (!userBusinessPermission) {
        // if user doesn't have permission
        return summaryData[page][2];
      }
    } else if (page === 'skills' && hasCultureSkills) {
      // This culture skills is set to true for all accounts by default
      if (subPath === 'skillScores' && isSummaryOnly) {
        return summaryData[page][0];
      } if (subPath === 'questionScores' && isSummaryOnly) {
        return summaryData[page][1];
      }
    } else if (page === 'segmentation' && hasCultureSkills) {
      const isFullAccess = reportingAccess === 'Segmentation+Questions+Summary';
      if (subPath === 'byQuestion' && !isFullAccess) {
        return summaryData[page][0];
      } if (subPath === 'bySkill' && !isFullAccess) {
        return summaryData[page][1];
      }
    }
  };
  const enhanceData = getData(page, subPath, hasCultureSkills, hasHrPeopleRisk, hasBusinessComplianceRisk, reportingAccess);
  if (!open && enhanceData) {
    setOpen(true);
    setPermissionDialogueDisabled(false);
  }

  const boxWidth = (sPath) => {
    const widthMap = new Map([
      ['skills_questionScores', 650],
    ]);
    return widthMap.get(sPath) || 620;
  };

  const unlockEmtrainURL = (topicOrQuestion, individualAccessForm) => {
    let encodedQueryParams = `Your Company Name=${encodeURIComponent(accountName)}&Your Name=${encodeURIComponent(name)}&Your Email Address=${encodeURIComponent(email)}&Agenda Topics / Questions=${encodeURIComponent(topicOrQuestion)}`;
    let unlockAnalyticsURL = `https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820?${encodedQueryParams}`;
    if (individualAccessForm) {
      encodedQueryParams = `Your Company Name=${encodeURIComponent(accountName)}&Your Name=${encodeURIComponent(name)}&Your Email Address=${encodeURIComponent(email)}&Which Intelligence Product Do You Need Access To?=${encodeURIComponent(topicOrQuestion)}`;
      unlockAnalyticsURL = `https://app.smartsheet.com/b/form/a7145686911b4837b91e1eaa15b466c0?${encodedQueryParams}`;
    }
    return unlockAnalyticsURL;
  };

  return (
    <>
      <div>
        <Modal
          open={open}
          onClose={() => {}}
          BackdropProps={{
            style: {
              backdropFilter: 'blur(5px)',
              backgroundColor: 'rgba(0, 0, 0, 0.2)',
            },
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              width: boxWidth(`${page}_${subPath}`),
              transform: 'translate(-50%, -50%)',
              bgcolor: 'background.white',
              borderRadius: 3,
              boxShadow: 24,
              p: 4,
              maxWidth: '100%',
            }}
            style={{ outline: 'none' }}
          >
            {/* Close icon */}
            <IconButton
              onClick={navigateToPreviousTab}
              role="button"
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                color: 'gray',
                '&:hover': { color: 'black' },
              }}
            >
              <CloseIcon />
            </IconButton>

            {/* Header */}
            <h2 className={styles.headerNoAccess}>{enhanceData?.header}</h2>
            <p className={styles.content}>{enhanceData?.content}</p>

            {enhanceData && enhanceData?.img && (
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <img src={enhanceData.img} alt="Enhance Data" style={{ width: '85%' }} />
              </Box>
            )}

            {enhanceData && enhanceData?.chatWith && (
              <p className={styles.centeredText}>
                {enhanceData.chatWith}
              </p>
            )}
            {enhanceData && enhanceData?.submitText && (
              <div className={styles.submitContainer}>
                <Button
                  className={styles.submitButton}
                  onClick={() => window.open(unlockEmtrainURL(enhanceData?.topicOrQuestion, enhanceData?.individualAccessForm))}
                  variant="contained"
                  data-cy="unblock_analytics_access"
                >
                  {enhanceData.submitText}
                </Button>
              </div>
            )}
          </Box>
        </Modal>
      </div>
    </>
  );
}
export default NoAccessAnalytics;
