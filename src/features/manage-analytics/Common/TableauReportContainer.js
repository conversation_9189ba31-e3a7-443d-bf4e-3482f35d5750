/* eslint-disable max-len */
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { TABLEAU_URL } from '../../../config/constants';

// Check if we're in development mode (localhost)
const isDevelopment = window.location.hostname === 'localhost';

// Mock Tableau component for development
function MockTableauViz({ view, options, analyticsIdHash, analyticsParams }) {
  const mockStyle = {
    width: options?.width || '100%',
    height: options?.height || '400px',
    border: '2px dashed #ccc',
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    color: '#666',
    fontSize: '14px',
    textAlign: 'center',
    padding: '20px',
    boxSizing: 'border-box',
  };

  return (
    <div style={mockStyle}>
      <div style={{ marginBottom: '10px', fontWeight: 'bold' }}>
        📊 Tableau Visualization (Development Mode)
      </div>
      <div style={{ marginBottom: '8px' }}>View: {view}</div>
      {analyticsIdHash && <div style={{ marginBottom: '8px' }}>Analytics ID: {analyticsIdHash}</div>}
      {analyticsParams?.idParam && <div style={{ marginBottom: '8px' }}>ID Param: {analyticsParams.idParam}</div>}
      <div style={{ fontSize: '12px', color: '#999', marginTop: '10px' }}>
        This is a mock placeholder. Tableau integration is bypassed in development mode.
      </div>
    </div>
  );
}

function TableauReportContainer({ options, view, analyticsIdHash, analyticsParams }) {
  const { getTableauJwtToken } = useAuth();
  const [tableauJwtToken, setTableauJwtToken] = useState(null);
  const [tokenError, setTokenError] = useState(false);
  const tableauURL = `${TABLEAU_URL}/${view}`;

  useEffect(() => {
    async function fetchToken() {
      if (isDevelopment) {
        // In development mode, use a mock token to bypass authentication
        console.log('Development mode: Bypassing Tableau authentication');
        setTableauJwtToken('mock-token-for-development');
        return;
      }

      try {
        const token = await getTableauJwtToken();
        setTableauJwtToken(token);
      } catch (error) {
        console.error('Failed to fetch Tableau JWT token:', error);
        setTokenError(true);
        // Set a mock token as fallback
        setTableauJwtToken('mock-token-fallback');
      }
    }
    fetchToken();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // If we're in development mode or there was a token error, show mock component
  if (isDevelopment || tokenError) {
    return (
      <div>
        <MockTableauViz
          view={view}
          options={options}
          analyticsIdHash={analyticsIdHash}
          analyticsParams={analyticsParams}
        />
      </div>
    );
  }

  return (
    <div>
      {view && analyticsIdHash && tableauJwtToken && options && (
        <tableau-viz
          id="tableau-viz"
          src={tableauURL}
          token={tableauJwtToken}
          width={options && options.width}
          height={options && options.height}
          toolbar="hidden"
          hide-tabs
        >
          <viz-parameter name="tokenParam" value={analyticsIdHash} />
          {analyticsParams && analyticsParams?.idParam && <viz-parameter name="idParam" value={analyticsParams.idParam} />}
        </tableau-viz>
      )}
      {view && analyticsIdHash && tableauJwtToken && !options && (
        <tableau-viz
          id="tableau-viz"
          hide-tabs
          src={tableauURL}
          token={tableauJwtToken}
          toolbar="hidden"
        >
          <viz-parameter name="tokenParam" value={analyticsIdHash} />
          {analyticsParams && analyticsParams?.idParam && <viz-parameter name="idParam" value={analyticsParams.idParam} />}
        </tableau-viz>
      )}
    </div>
  );
}

export default TableauReportContainer;
