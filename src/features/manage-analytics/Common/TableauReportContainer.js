/* eslint-disable max-len */
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { TABLEAU_URL } from '../../../config/constants';

function TableauReportContainer({ options, view, analyticsIdHash, analyticsParams }) {
  const { getTableauJwtToken } = useAuth();
  const [tableauJwtToken, setTableauJwtToken] = useState(null);
  const tableauURL = `${TABLEAU_URL}/${view}`;

  useEffect(() => {
    async function fetchToken() {
      const token = await getTableauJwtToken();
      setTableauJwtToken(token);
    }
    fetchToken();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      {view && analyticsIdHash && tableauJwtToken && options && (
        <tableau-viz
          id="tableau-viz"
          src={tableauURL}
          token={tableauJwtToken}
          width={options && options.width}
          height={options && options.height}
          toolbar="hidden"
          hide-tabs
        >
          <viz-parameter name="tokenParam" value={analyticsIdHash} />
          {analyticsParams && analyticsParams?.idParam && <viz-parameter name="idParam" value={analyticsParams.idParam} />}
        </tableau-viz>
      )}
      {view && analyticsIdHash && tableauJwtToken && !options && (
        <tableau-viz
          id="tableau-viz"
          hide-tabs
          src={tableauURL}
          token={tableauJwtToken}
          toolbar="hidden"
        >
          <viz-parameter name="tokenParam" value={analyticsIdHash} />
          {analyticsParams && analyticsParams?.idParam && <viz-parameter name="idParam" value={analyticsParams.idParam} />}
        </tableau-viz>
      )}
    </div>
  );
}

export default TableauReportContainer;
