/* eslint-disable max-len */
import React, { useState } from 'react';
import AnalyticsNavigation from './AnalyticsNavigation';
import Overview from '../Pages/Overview';
import Risks from '../Pages/Risks';
import Compliance from '../Pages/Compliance';
import Skills from '../Pages/Skills';
import Segmentation from '../Pages/Segmentation';
import NoaAccessAnalytics from './NoAccessAnalytics';

function AnalyticsLayout({ previousUrl, pages, page, subPath, analyticsIdHash, analyticsRolesData, hasCultureSkills, hasHrPeopleRisk, hasBusinessComplianceRisk, reportingAccess}) {
  const [clickProblemLink, setClickProblemLink] = useState(false);
  const [permissionDialogueDisabled, setPermissionDialogueDisabled] = useState(true);
  return (
    <>
      <AnalyticsNavigation page={page} pages={pages} permissionDialogueDisabled={permissionDialogueDisabled} />
      {(!page || page === 'overview') && <Overview analyticsIdHash={analyticsIdHash} setPermissionDialogueDisabled={setPermissionDialogueDisabled} />}
      {page === 'risks' && <Risks analyticsIdHash={analyticsIdHash} setClickProblemLink={setClickProblemLink} setPermissionDialogueDisabled={setPermissionDialogueDisabled} />}
      {page === 'compliance' && <Compliance analyticsIdHash={analyticsIdHash} setClickProblemLink={setClickProblemLink} setPermissionDialogueDisabled={setPermissionDialogueDisabled} />}
      {page === 'skills' && <Skills analyticsIdHash={analyticsIdHash} />}
      {page === 'segmentation' && <Segmentation analyticsIdHash={analyticsIdHash} />}
      <NoaAccessAnalytics
        previousUrl={previousUrl}
        page={page}
        subPath={subPath}
        hasCultureSkills={hasCultureSkills}
        hasHrPeopleRisk={hasHrPeopleRisk}
        hasBusinessComplianceRisk={hasBusinessComplianceRisk}
        reportingAccess={reportingAccess}
        analyticsRolesData={analyticsRolesData}
        clickProblemLink={clickProblemLink}
        setClickProblemLink={setClickProblemLink}
        setPermissionDialogueDisabled={setPermissionDialogueDisabled}
      />
    </>
  );
}
export default AnalyticsLayout;
