import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

import TableauReportContainer from '../TableauReportContainer';

export const StyledVizContainer = styled.div`
height: 500px;
margin-top: 1.25rem;
`;

function ByQuestion({ analyticsIdHash }) {
  const useWindowWidth = () => {
    const [width, setWidth] = useState(window.innerWidth);
    useEffect(() => {
      const handleResize = () => setWidth(window.innerWidth);
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);
    return width;
  };
  const windowWidth = useWindowWidth() - 210;

  return (
    <StyledVizContainer style={{ width: `${windowWidth}px` }}>
      <TableauReportContainer
        view="segmentation_jett/QuestionsALL"
        analyticsIdHash={analyticsIdHash}
      />
    </StyledVizContainer>
  );
}
export default ByQuestion;
