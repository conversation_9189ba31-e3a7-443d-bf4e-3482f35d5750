.container {
  font-family: Source Sans Pro, sans-serif;
  width: 1200px;
  margin: 1.4rem 0 0 0;
  padding: 0;
  
  background: transparent;
  border-bottom: 1px solid #C7C9DD;

  display: flex;
  align-items: flex-start;
  flex-direction: row;
}

.button {
  text-transform: none !important;
  font-family: Source Sans Pro, sans-serif !important;
  font-weight: 500 !important;
  display: flex;
  align-self: center;
  font-size: 1.063rem !important;
  background: transparent;
  border: 0;
  color: #40405D !important;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  padding: 0 !important;
  margin-right: 2.125rem;
  width: max-content;
  border-radius: 0;
  &.active {
    border-bottom: 3px solid #555572 !important;
    cursor: pointer !important;
  }
  &:hover {
    background: transparent !important;
  }
  &.focus {
    background: transparent !important;
  }
}

.menu {
  font-weight: 400;
  font-size: 0.9rem;
  width: 10rem;
  background-color: transparent !important;
  padding: 0.25rem 1rem !important;
  &.active {
    background-color:#E0E1EC !important;
    font-weight: bold;
  }
}

.menu:hover {
  font-weight: bold;
}

.downloadButton {
  margin-left: auto;
}