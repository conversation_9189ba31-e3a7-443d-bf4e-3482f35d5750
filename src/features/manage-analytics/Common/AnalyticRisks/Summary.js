/* eslint-disable max-len */
import React, { useEffect, useState } from 'react';
import { get } from 'lodash';
import {
  Box,
  Typography,
  Slider,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  useTheme,
} from '@mui/material';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faCalendar } from '@fortawesome/free-solid-svg-icons';
import styles from './Summary.module.css';
import { calendarYearRange, sixMonthsRange } from './calendarYearRanges';
import { useUser } from '../../../../hooks/useUser';
import RiskSummary from './RiskSummary';
import { getRisksSummary } from '../../../../services/api/AnalyticsRisks';
import SpinnerGif from '../../../../images/spinner.gif';
import DeployedContent from './DeployedContent';
import RiskToolTip from './RiskToolTip';
import { getSelectedSessionFilters, updateSessionRiskFilters } from '../../../../services/analyticsSettings';

function Summary({ riskPersona }) {
  const riskFilters = getSelectedSessionFilters(riskPersona);
  const { palette } = useTheme();
  const sixMonthsRanges = sixMonthsRange();
  const calendarYearRanges = calendarYearRange();
  const [isDefaultPeriod, setIsDefaultPeriod] = useState(!riskFilters?.periodId);
  const [periodCategory, setPeriodCategory] = useState(riskFilters?.periodId?.toString().length === 1 ? 'Calendar Year' : '6 Months');
  const [minResponseRate, setMinResponseRate] = useState(riskFilters?.minResponseRate || 10);
  const [includeNewHires, setIncludeNewHires] = useState(riskFilters?.includeNewHires !== undefined ? riskFilters?.includeNewHires : true);
  const [riskSummary, updateRiskSummary] = useState([]);
  const [flagHasTenureData, setFlagHasTenureData] = useState(false);
  const [loader, setLoader] = useState(true);

  const user = useUser();
  let analyticsId = get(user, 'accounts[1].analyticsId');
  if (!analyticsId) {
    analyticsId = get(user, 'accounts[0].analyticsId');
  }

  let selectedPeriod = null;
  if (sixMonthsRanges && sixMonthsRanges.length) {
    selectedPeriod = sixMonthsRanges.slice(-1)[0].dateId;
  }
  const [period, setPeriod] = useState(riskFilters?.periodId || selectedPeriod);

  useEffect(() => {
    async function fetchRisksSummary() {
      setLoader(true);
      const reqRisksSummary = { analyticsId, period, includeNewHires, periodCategory };
      reqRisksSummary.riskPersona = riskPersona;
      reqRisksSummary.isDefaultPeriod = !riskFilters?.periodId && isDefaultPeriod;
      if (analyticsId) {
        const response = await getRisksSummary(reqRisksSummary);
        updateRiskSummary(response.riskSummaryData);
        setFlagHasTenureData(response.flagHasTenureData);
        if (isDefaultPeriod && !riskFilters?.periodId) {
          const periodCategoryCheck = response?.riskSummaryData[0]?.periodId?.toString().length !== 1 ? '6 Months' : 'Calendar Year';
          setIsDefaultPeriod(false);
          const isPeriodData = response?.riskSummaryData?.filter(({ periodId }) => periodId);
          setPeriod(isPeriodData[0]?.periodId || period);
          setPeriodCategory(periodCategoryCheck);
          updateSessionRiskFilters(riskPersona, { periodId: (isPeriodData[0]?.periodId || period), periodCategory: periodCategoryCheck });
        }
      }
      setLoader(false);
    }
    if (riskFilters?.periodId || isDefaultPeriod) {
      fetchRisksSummary();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [analyticsId, periodCategory, period, includeNewHires, riskPersona, isDefaultPeriod]);

  const handleCheckboxChange = async (event) => {
    const newHires = event.target.checked;
    setIncludeNewHires(newHires);
    updateSessionRiskFilters(riskPersona, { includeNewHires: newHires });
  };

  // Handle slider change
  const handleSliderChange = () => (event, newValue) => {
    setMinResponseRate(newValue);
    updateSessionRiskFilters(riskPersona, { minResponseRate: newValue });
  };

  const handleDateTypeChange = (event) => {
    const periodValue = event.target.value;
    setPeriodCategory(periodValue);
    const periodId = periodValue === '6 Months' ? selectedPeriod : (calendarYearRanges[1]?.dateId || null);
    setPeriod(periodId);
    updateSessionRiskFilters(riskPersona, { periodId, periodCategory: periodValue });
  };

  const handleDateChange = (event) => {
    setPeriod(event.target.value);
    updateSessionRiskFilters(riskPersona, { periodId: event.target.value });
  };

  const summarizedRiskAreas = riskSummary.filter((item) => !item.isDeploy) || [];
  const deployedContent = riskSummary.filter((item) => item.isDeploy) || [];

  return (
    <Box className={styles.summaryContainer}>
      {loader && (
      <div className={styles.loadSpinner}>
        <img src={SpinnerGif} width={50} aria-label="loading..." />
      </div>
      )}
      {summarizedRiskAreas.length > 0 && (
      <Box className={styles.summaryBox} sx={{ backgroundColor: palette.background.white }}>
        <Box className={styles.summaryHeader}>
          <Typography className={styles.summaryHeading}>Responses</Typography>
          <Box className={styles.monthsBox}>
            <FontAwesomeIcon icon={faCalendar} className={styles.faCalendarIcon} color={palette.primary.main} />
            <Box className={styles.monthsBoxInner}>
              <Select
                value={periodCategory}
                onChange={handleDateTypeChange}
                className={styles.sideMenuDates}
                sx={{
                  background: palette.background.white,
                  '&.Mui-focused': { borderColor: palette.background.lightShadeGrey },
                  '& .MuiOutlinedInput-notchedOutline': { borderColor: palette.background.lightShadeGrey },
                  '& .MuiSelect-select': { padding: '0.25rem', borderColor: palette.background.lightShadeGrey },
                  height: '1.75rem',
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      '& ul': {
                        background: palette.background.white,
                        '& li': {
                          fontSize: '0.875rem',
                          padding: '0.313rem 0.625rem',
                        },
                      },
                    },
                  },
                }}
              >
                <MenuItem key="6 Months" value="6 Months">Six Months</MenuItem>
                <MenuItem key="Calendar Year" value="Calendar Year">Calendar Year</MenuItem>
              </Select>
              <Select
                value={period}
                onChange={handleDateChange}
                className={styles.sideMenuDates}
                sx={{
                  background: palette.background.white,
                  height: '1.75rem',
                  '&.Mui-focused': { borderColor: palette.background.lightShadeGrey },
                  '& .MuiOutlinedInput-notchedOutline': { borderColor: palette.background.lightShadeGrey },
                  '& .MuiSelect-select': { padding: '0.25rem', borderColor: palette.background.lightShadeGrey },
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      '& ul': {
                        height: periodCategory === '6 Months' ? '12rem' : 'auto',
                        '& li': {
                          background: palette.background.white,
                          fontSize: '0.875rem',
                          padding: '0.313rem 0.625rem',
                        },
                      },
                    },
                  },
                }}
              >
                {periodCategory && periodCategory === '6 Months' && sixMonthsRanges && sixMonthsRanges.map((item) => (
                  <MenuItem key={item.dateId} value={item.dateId}>{item.dateValue}</MenuItem>
                ))}
                {periodCategory && periodCategory === 'Calendar Year' && calendarYearRanges && calendarYearRanges.map((item) => (
                  <MenuItem key={item.dateId} value={item.dateId}>{item.dateValue}</MenuItem>
                ))}
              </Select>
            </Box>
          </Box>
          <Box className={styles.responseRateBox}>
            <Box className={styles.responseRateBoxInner}>
              <FontAwesomeIcon icon={faUsers} className={styles.faUsersIcon} color={palette.primary.main} />
              <Typography variant="body2" gutterBottom sx={{ marginBottom: '0.063rem' }}>
                Minimum Response Rate
              </Typography>
            </Box>
            <Box className={styles.SliderBox}>
              <Slider
                sx={{
                  flex: 1,
                  mr: 1,
                  '& .MuiSlider-track': { color: palette.background.secondary },
                  '& .MuiSlider-rail': { color: palette.background.secondary, opacity: 100 },
                  '& .MuiSlider-thumb': {
                    backgroundColor: palette.background.white,
                    border: `0.063rem  solid ${palette.primary.mainBlue}`,
                    width: '18px',
                    height: '18px',
                  },
                }}
                aria-label="Minimum Response Rate"
                onChange={handleSliderChange()}
                value={minResponseRate}
                step={5}
                min={10}
                max={100}
              />
              <Typography sx={{ fontSize: 15 }}>{minResponseRate}%</Typography>
            </Box>
          </Box>
          <FormControlLabel
            sx={{ margin: '0rem' }}
            control={(
              <Checkbox
                checked={includeNewHires}
                onChange={handleCheckboxChange}
                name="includeNewHires"
                id="includeNewHires"
                disabled={!flagHasTenureData}
                sx={{
                  backgroundColor: 'transparent',
                  padding: 0,
                  '& .MuiSvgIcon-root': {
                    backgroundColor: 'none',
                    borderRadius: 2,
                    width: '1.15rem',
                  },
                  '& .MuiCheckbox-root': {
                    borderRadius: '0.25rem',
                    backgroundColor: 'transparent',
                  },
                  '& .MuiIconButton-root': {
                    padding: 0,
                  },
                  '& .MuiCheckbox-root.Mui-checked': {
                    backgroundColor: 'transparent',
                  },
                }}
              />
              )}
            label={(
              <Box className={styles.hireTooltip}>
                <Typography sx={{ fontSize: 14 }}>Include New Hires</Typography>
                {!flagHasTenureData && (
                <RiskToolTip labelInfo={{ title: "This feature isn't available because your account doesn't include data for employee hire date." }} />
                )}
              </Box>
              )}
          />
        </Box>
        <RiskSummary
          riskAreas={summarizedRiskAreas}
          minResponseRate={minResponseRate}
          riskPersona={riskPersona}
          periodId={period}
          includeNewHires={includeNewHires}
          periodCategory={periodCategory}
        />
      </Box>
      )}
      {deployedContent.length > 0 && (
        <DeployedContent
          riskPersona={riskPersona}
          deployedContent={deployedContent}
        />
      )}
    </Box>
  );
}
export default Summary;
