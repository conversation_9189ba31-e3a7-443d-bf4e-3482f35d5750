/* eslint-disable max-len */
import React, { useEffect, useCallback, useState } from 'react';
import { debounce } from 'lodash';

import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Slider,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  Divider,
  useTheme,
  Input,
} from '@mui/material';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faLayerGroup, faCalendar } from '@fortawesome/free-solid-svg-icons';
import { sixMonthsRange, calendarYearRange } from './calendarYearRanges';
import styles from './AnalyticRisks.module.css';
import RiskToolTip from './RiskToolTip';
import { updateSessionRiskFilters } from '../../../../services/analyticsSettings';

const Sidebar = (props) => {
  const {
    riskQuestions,
    riskAreas,
    catalog,
    selectedRiskArea,
    updateSelectedRiskArea,
    minResponseRate,
    updateMinResponseRate,
    updatePeriodCategory,
    updatePeriod,
    updateIncludeNewHires,
    includeNewHires,
    periodCategory,
    period,
    flagHasTenureData,
    riskPersona,
  } = props;
  const { palette } = useTheme();
  const deployed = riskQuestions && riskQuestions.deployed;
  const sixMonthsRanges = sixMonthsRange();
  const calendarYearRanges = calendarYearRange();

  const [inputValue, setInputValue] = useState(minResponseRate);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleChange = useCallback(
    debounce((newValue) => {
      updateMinResponseRate(newValue);
    }, 200),
    [updateMinResponseRate],
  );

  const handleCheckboxChange = async (event) => {
    updateIncludeNewHires(event.target.checked);
  };

  // Handle slider change
  const handleSliderChange = () => (event, newValue) => {
    debouncedHandleChange(newValue);
  };

  const handleInputChange = (event) => {
    const rawValue = event.target.value;
    const numericValue = Number(rawValue);
    // eslint-disable-next-line no-restricted-globals, no-restricted-globals
    if (!isNaN(numericValue)) {
      setInputValue(numericValue);
      debouncedHandleChange(numericValue);
    }
  };

  const handleBlur = () => {
    let finalValue = Number(inputValue);
    // eslint-disable-next-line no-restricted-globals
    if (finalValue < 10) {
      finalValue = 10;
    } else if (finalValue >= 100) {
      finalValue = (Math.log10(finalValue) % 1 === 0) ? 100 : Number(String(finalValue).slice(0, 2));
    }
    setInputValue(finalValue); // Update UI with valid value
    updateMinResponseRate(finalValue); // Ensure state update
  };

  const handleDateTypeChange = (event) => {
    setTimeout(() => {
      document.activeElement?.blur();
    }, 0);
    updatePeriodCategory(event.target.value);
  };

  const handleDateChange = (event) => {
    setTimeout(() => {
      document.activeElement?.blur();
    }, 0);
    updatePeriod(event.target.value);
  };

  const handleRiskArea = (itemName) => {
    updateSelectedRiskArea(itemName);
    updateSessionRiskFilters(riskPersona, { riskAreaId: itemName.riskId });
  };

  useEffect(() => {
    return () => {
      debouncedHandleChange.cancel();
    };
  }, [debouncedHandleChange]);

  return (
    <Box
      className={styles.sideMenuBox}
      sx={{
        backgroundColor: palette.background.white,
        boxShadow: `0rem 0.25rem 0.375rem ${palette.primary.blackBorder}`,
        borderRight: `0.063rem solid ${palette.primary.pureWhite}`,
      }}
    >
      {/* Risk Areas */}
      <Typography className={styles.sideMenuHeading} color={palette.primary.main}>Risk Areas</Typography>
      <Divider sx={{ borderBottom: palette.border.lightLightPurpleShade }} />
      <List sx={{ padding: '0.5rem 0rem' }}>
        {riskAreas && riskAreas.map(({ riskAreaId, riskArea }) => (
          <ListItem
            key={riskAreaId}
            className={styles.sideMenuRiskItems}
          >
            <ListItemButton
              sx={{
                padding: '0.2rem 0.8rem',
                '&:hover': {
                  backgroundColor: 'transparent',
                  fontWeight: 600,
                },
                '&.Mui-selected': {
                  backgroundColor: selectedRiskArea?.riskId === riskAreaId && palette.primary.light,
                  borderRadius: '3px',
                  '&:hover': {
                    backgroundColor: palette.primary.light,
                  },
                },
              }}
              disableRipple
              onClick={() => handleRiskArea({ riskId: riskAreaId, riskName: riskArea })}
              selected={selectedRiskArea?.riskId === riskAreaId}
            >
              <ListItemText
                sx={{
                  '.MuiTypography-root': {
                    fontSize: 14,
                    fontWeight: selectedRiskArea?.riskId === riskAreaId && 600,
                    '&:hover': {
                      fontWeight: 600,
                    },
                  },
                }}
                primary={riskArea}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      {deployed && (
        <>
          <Typography className={styles.sideMenuHeading} color={palette.primary.main}>
            Responses
          </Typography>
          <Divider sx={{ borderBottom: palette.border.lightLightPurpleShade }} />
          <Box className={styles.sideMenuResponse}>
            {catalog && catalog.length > 0 && (
              <Box className={styles.catalogBox}>
                <FontAwesomeIcon icon={faLayerGroup} className={styles.faLayerGroupIcon} color={palette.primary.main} />
                <Box sx={{ paddingBottom: '0.5rem' }}>
                  {catalog.map(({ catalogTitle }) => (
                    <Typography key={`catalog-${catalogTitle}`} className={styles.catalogTitleRow}>
                      {catalogTitle}
                    </Typography>
                  ))}
                </Box>
              </Box>
            )}
            <Box className={styles.monthsBox}>
              <FontAwesomeIcon icon={faCalendar} className={styles.faCalendarIcon} color={palette.primary.main} />
              <Box className={styles.monthsBoxInner}>
                <Select
                  value={periodCategory}
                  onChange={handleDateTypeChange}
                  className={styles.sideMenuDates}
                  sx={{
                    '&.Mui-focused': { borderColor: palette.background.lightShadeGrey },
                    '& .MuiOutlinedInput-notchedOutline': { borderColor: palette.background.lightShadeGrey },
                    '& .MuiSelect-select': { padding: '0.25rem', borderColor: palette.background.lightShadeGrey },
                    height: '1.75rem',
                  }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        '& ul': {
                          background: palette.background.white,
                          '& li': {
                            fontSize: '0.875rem',
                            padding: '0.313rem 0.625rem',
                          },
                        },
                      },
                    },
                  }}
                >
                  <MenuItem key="6 Months" value="6 Months">Six Months</MenuItem>
                  <MenuItem key="Calendar Year" value="Calendar Year">Calendar Year</MenuItem>
                </Select>
                <Select
                  value={period}
                  onChange={handleDateChange}
                  className={styles.sideMenuDates}
                  sx={{
                    height: '1.75rem',
                    '&.Mui-focused': { borderColor: palette.background.lightShadeGrey },
                    '& .MuiOutlinedInput-notchedOutline': { borderColor: palette.background.lightShadeGrey },
                    '& .MuiSelect-select': { padding: '0.25rem', borderColor: palette.background.lightShadeGrey },
                  }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        '& ul': {
                          height: periodCategory === '6 Months' ? '12rem' : 'auto',
                          '& li': {
                            background: palette.background.white,
                            fontSize: '0.875rem',
                            padding: '0.313rem 0.625rem',
                          },
                        },
                      },
                    },
                  }}
                >
                  {periodCategory && periodCategory === '6 Months' && sixMonthsRanges && sixMonthsRanges.map((item) => (
                    <MenuItem key={item.dateId} value={item.dateId}>{item.dateValue}</MenuItem>
                  ))}

                  {periodCategory && periodCategory === 'Calendar Year' && calendarYearRanges && calendarYearRanges.map((item) => (
                    <MenuItem key={item.dateId} value={item.dateId}>{item.dateValue}</MenuItem>
                  ))}
                </Select>
              </Box>
            </Box>
            <Box className={styles.responseRateBox}>
              <Box className={styles.responseRateBoxInner}>
                <FontAwesomeIcon icon={faUsers} className={styles.faUsersIcon} color={palette.primary.main} />
                <Typography variant="body2" gutterBottom sx={{ marginBottom: '0.063rem' }}>
                  Minimum Response Rate
                </Typography>
              </Box>
              <Box className={styles.SliderBox}>
                <Slider
                  sx={{
                    flex: 1,
                    mr: 1,
                    '& .MuiSlider-track': { color: palette.background.lightShadeGrey },
                    '& .MuiSlider-rail': { color: palette.background.lightShadeGrey },
                    '& .MuiSlider-thumb': {
                      backgroundColor: palette.background.white,
                      border: `0.063rem  solid ${palette.primary.mainBlue}`,
                      width: '18px',
                      height: '18px',
                    },
                  }}
                  aria-label="Minimum Response Rate"
                  onChange={handleSliderChange(selectedRiskArea)}
                  value={minResponseRate}
                  step={5}
                  min={10}
                  max={100}
                />
                <Input
                  value={minResponseRate}
                  size="small"
                  onChange={handleInputChange}
                  onBlur={handleBlur}
                  sx={{ ml: 1, textAlignLast: 'end', width: '40px', mt: '-2px', height: '1.25em' }}
                  inputProps={{
                    step: 5,
                    min: 10,
                    max: 100,
                    type: 'number',
                    'aria-labelledby': 'input-slider',
                  }}
                />
                <Typography sx={{ fontSize: 14, marginBottom: '5px' }}>%</Typography>
              </Box>
            </Box>
            <FormControlLabel
              sx={{ margin: '0rem 0.875rem 0rem 1.6rem' }}
              control={(
                <Checkbox
                  checked={includeNewHires}
                  onChange={handleCheckboxChange}
                  name="includeNewHires"
                  id="includeNewHires"
                  disabled={!flagHasTenureData}
                  sx={{
                    backgroundColor: 'transparent',
                    padding: 0,
                    '& .MuiSvgIcon-root': {
                      backgroundColor: 'none',
                      borderRadius: 2,
                      width: '1.15rem',
                    },
                    '& .MuiCheckbox-root': {
                      borderRadius: '0.25rem',
                      backgroundColor: 'transparent',
                    },
                    '& .MuiIconButton-root': {
                      padding: 0,
                    },
                    '& .MuiCheckbox-root.Mui-checked': {
                      backgroundColor: 'transparent',
                    },
                  }}
                />
              )}
              label={(
                <Box className={styles.hireTooltip}>
                  <Typography sx={{ fontSize: 14 }}>Include New Hires</Typography>
                  {!flagHasTenureData && (
                    <RiskToolTip labelInfo={{ title: "This feature isn't available because your account doesn't include data for employee hire date." }} />
                  )}
                </Box>
              )}
            />
          </Box>
        </>
      )}
    </Box>
  );
};

export default Sidebar;
