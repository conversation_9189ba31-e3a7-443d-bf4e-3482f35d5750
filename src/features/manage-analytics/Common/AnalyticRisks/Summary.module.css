.summaryContainer {
  display: flex;
  padding-top: 1.125rem;
  row-gap: 1.5rem;
  margin-bottom: 3rem;
  flex-direction: column;
}

.summaryBox {
  flex: 1;
  padding: 0.125rem;
  width: 75.5rem;
  border-radius: 0.375rem;
  height: fit-content;
}

.summaryHeader {
  display: grid;
  align-items: center;
  background-color: #f2f0f6;
  height: 3rem;
  grid-template-columns: 0.2fr 1.2fr 1fr 0.35fr;
  padding: 0rem 1rem;

}

.summaryHeading {
  font-size: 1.05rem;
  font-weight: 600;
  margin-right: 1rem;
}

.deployedHeading {
  font-size: 1.1rem;
  font-weight: 600;
  margin-right: 1rem;
}

.deploymentBox {
  flex: 1;
  padding: 0.125rem;
  width: 75.5rem;
  border-radius: 0.375rem;
  height: fit-content;
}

.deploymentHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f2f0f6;
  height: 3rem;
  padding: 0rem 1rem;
}

.supportButton {
  text-transform: none !important;
  font-family: Source Sans Pro, sans-serif !important;
  font-weight: 600 !important;
  background: transparent;
  padding: 0;
  float: right;
  font-size: 1rem;
  align-items: center;

  &.active {
    cursor: pointer !important;
  }

  &:hover {
    background: transparent !important;
  }

  &.focus {
    background: transparent !important;
  }
}

.buttonArrow {
  width: 0.8rem;
  height: 0.8rem;
  margin-top: 0.2rem;
}

.loadSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  z-index: 9;
}

.riskStatus {
  margin-left: -40px;
  width: 3.125rem;
  height: 3.125rem;
}

.contentTitleImage {
  width: 1.125rem;
  height: 1.125rem;
}

.sideMenuDates {
  min-width: 7.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;

  &ul {
    &li {
      font-size: 0.75rem !important;
    }
  }
}

.sideMenuDates .MuiSelect-select {
  padding: 0.25rem !important;
}

.faCalendarIcon {
  width: 0.938rem;
  height: 1.125rem;
}

.faUsersIcon {
  width: 1.25rem;
  height: 1rem;
}

.SliderBox {
  display: flex;
  align-items: center;
  margin: 0rem 1rem;
  width: 180px;
}

.monthsBox {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  align-items: center;
}

.monthsBoxInner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.responseRateBox {
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: end;
}

.responseRateBoxInner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.hireTooltip {
  display: flex;
  align-items: center;
  padding-left: 3px;
}

.cardContent {
  padding: 0 0 1.25rem 0 !important;
}

.cardGrid {
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0rem;
  p{
    font-size: 1.1rem;
    font-weight: 700;
  }
}

.containerBox {
  display: grid;
  grid-template-columns: 1.1fr 1.2fr 1fr;
  padding: 0rem 2.5rem;
  gap: 30px;
}

.risksLevelText {
  padding: 0rem 2rem 0rem 1rem;
  font-weight: 600;
  font-size: 1.05rem;
  line-height: 1.3;
}

.catalogTitle {  
  font-size: 0.92rem;
  font-weight: 700;
  line-height: 1.2;
  padding: .2rem 0rem;
}

.hiddenData {
  font-size: 0.92rem;
  font-weight: 600;
  line-height: 1.3;
}

.learners {
  font-size: 0.92rem;
  font-weight: 700;
  line-height: 1.3;
}

.responseRate {
  font-size: 0.85rem;
  font-weight: 600;
  line-height: 1.3;
}