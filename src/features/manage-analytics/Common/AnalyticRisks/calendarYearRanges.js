export const sixMonthsRange = (timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone) => {
  const ranges = [];
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone,
    year: 'numeric',
    month: 'short',
    day: '2-digit',
  });

  const formatterForPeriodId = new Intl.DateTimeFormat('en-US', {
    timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });

  const currentDate = new Date(new Date().toLocaleString('en-US', { timeZone }));
  let startDate = new Date(currentDate.getFullYear(), (currentDate.getMonth() + 1) - 6, 1);
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < 24; i++) {
    const rangeStart = new Date(startDate);
    const rangeEnd = new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);
    const parts = formatterForPeriodId.formatToParts(rangeStart);
    const year = parts.find((part) => part.type === 'year').value;
    const month = parts.find((part) => part.type === 'month').value;
    const formattedYearDate = `${year}${month}`;
    const formattedStart = formatter.format(rangeStart).replace(',', '');
    let formattedEnd = formatter.format(rangeEnd).replace(',', '');
    if (i === 0) {
      formattedEnd = formatter.format(new Date()).replace(',', '');
    }
    ranges.push({ dateId: formattedYearDate, dateValue: `${formattedStart}-${formattedEnd}` });
    startDate = new Date(startDate.getFullYear(), startDate.getMonth() - 1, 1);
  }
  return ranges.reverse();
};

export const calendarYearRange = () => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const twoYearAgo = currentYear - 2;
  const priorYear = currentYear - 1;
  const range1Start = new Date(currentYear, 0, 1);
  const range1End = currentDate;

  const twoYearAgoStart = new Date(twoYearAgo, 0, 1);
  const twoYearAgoEnd = new Date(twoYearAgo, 11, 31);
  const range2Start = new Date(priorYear, 0, 1); // January 1st of the prior year
  const range2End = new Date(priorYear, 11, 31); // December 31st of the prior year
  const formatDate = (date) => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]} ${date.getDate().toString().padStart(2, '0')} ${date.getFullYear()}`;
  };
  const range1 = `${formatDate(range1Start)}-${formatDate(range1End)}`;
  const range2 = `${formatDate(range2Start)}-${formatDate(range2End)}`;
  const range3 = `${formatDate(twoYearAgoStart)}-${formatDate(twoYearAgoEnd)}`;
  // Return ranges in ascending order with most recent year selected by default
  return [
    { dateId: 3, dateValue: range3 },
    { dateId: 2, dateValue: range2 },
    { dateId: 1, dateValue: range1 },
  ];
};
