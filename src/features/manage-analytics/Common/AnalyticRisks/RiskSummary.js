import React from 'react';
import { Card, CardContent, Typography, Grid, Box, Button, Divider, useTheme } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForwardIos';
import { useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import HealthyLevelIcon from '../../../../images/green_circle.png';
import ConcerningLevelIcon from '../../../../images/yellow_circle.png';
import WarningLevelIcon from '../../../../images/orange_circle.png';
import RiskyLevelIcon from '../../../../images/red_circle.png';
import HiddenLevelIcon from '../../../../images/data-hidden-circle.png';
import HealthyBox from '../../../../images/green_box.png';
import ConcerningBox from '../../../../images/yellow_box.png';
import WarningBox from '../../../../images/orange_box.png';
import RiskyBox from '../../../../images/red_box.png';
import NoLearnersIcon from '../../../../images/learners-no-data-circle.png';
import LearnersIcon from '../../../../images/learners-data-circle.png';
import ContentTitlesIcon from '../../../../images/content-titles-circle.png';
import styles from './Summary.module.css';
import { colors } from '../../../../theme/colors';
import { pushToAnalyticsSubMenu } from '../../../navigation/Routes/AppRoutes';
import { updateSessionRiskFilters } from '../../../../services/analyticsSettings';

const Icons = {
  Healthy: HealthyLevelIcon,
  Warning: WarningLevelIcon,
  Concerning: ConcerningLevelIcon,
  Risky: RiskyLevelIcon,
  DataHidden: HiddenLevelIcon,
};

const RiskBox = {
  Healthy: HealthyBox,
  Warning: WarningBox,
  Concerning: ConcerningBox,
  Risky: RiskyBox,
};

const RiskSummary = ({ riskAreas, minResponseRate, riskPersona }) => {
  const { palette } = useTheme();
  const history = useHistory();
  const navigateToRiskScores = (riskAreaId) => {
    const menu = riskPersona === 'Business' ? 'compliance' : 'risks';
    updateSessionRiskFilters(riskPersona, { riskAreaId });
    history.push(pushToAnalyticsSubMenu(menu, 'scores'));
  };

  return (
    <Box sx={{ margin: 'auto', padding: '0px 20px' }}>
      {riskAreas.map((risk, index) => {
        const { riskLevel, riskArea, responseRateRiskArea, questionCountRiskAreaRiskLevel,
          riskAreaId, questionCountRiskArea, respondentCountRiskArea, catalogTitle } = risk;
        const isGreaterMinResponseRate = (responseRateRiskArea && (minResponseRate <= responseRateRiskArea));
        return (
          <Box key={riskArea}>
            <Card sx={{ borderRadius: 2, background: 'transparent !important', color: palette.primary.main }}>
              <CardContent className={styles.cardContent}>
                <Grid container className={styles.cardGrid}>
                  <Typography>
                    {riskArea}
                  </Typography>
                  <Button
                    disableRipple
                    className={styles.supportButton}
                    sx={{ color: colors.cyanBlue }}
                    onClick={() => navigateToRiskScores(riskAreaId.toString())}
                  >
                    See the Scores <ArrowForwardIcon className={styles.buttonArrow} />
                  </Button>
                </Grid>

                <Grid
                  container
                  alignItems="start"
                  className={styles.containerBox}
                >
                  <Grid item>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: 1.5,
                        padding: '8px 12px',
                        ml: 2,
                        backgroundImage: isGreaterMinResponseRate ? `url(${RiskBox[riskLevel]})` : `url(${null})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        background: (riskLevel && isGreaterMinResponseRate) ? RiskBox[riskLevel] : colors.shadeGrey,
                      }}
                    >
                      <img
                        className={styles.riskStatus}
                        src={(riskLevel && isGreaterMinResponseRate) ? Icons[riskLevel] : HiddenLevelIcon}
                        alt="risksLevel"
                      />
                      {isGreaterMinResponseRate ? (
                        <Typography
                          variant="body2"
                          className={styles.risksLevelText}
                        >
                          Responses to&nbsp;
                          <strong>{questionCountRiskAreaRiskLevel} of {questionCountRiskArea}</strong>
                        &nbsp;questions have a risk level of &nbsp;
                          <strong>{riskLevel}</strong>
                        </Typography>
                      )
                        : (
                          <Typography
                            variant="body2"
                            className={styles.risksLevelText}
                          >
                            <strong>Data hidden</strong> for this risk area
                          </Typography>
                        )}
                    </Box>
                  </Grid>

                  <Grid item>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: catalogTitle.length > 2 ? 'flex-start' : 'center',
                        border: `1px solid ${colors.lightSlateGrey}`,
                        borderRadius: 1.5,
                        padding: '7px 12px',
                        ml: 2.6,
                      }}
                    >
                      <img
                        className={styles.riskStatus}
                        src={ContentTitlesIcon}
                        alt="catalog"
                      />
                      <Box style={{ padding: '0rem 1rem' }}>
                        {catalogTitle?.length ? catalogTitle.map((item) => (
                          <Typography key={item} className={styles.catalogTitle}>
                            {item}
                          </Typography>
                        )) : (
                          <Typography
                            variant="body2"
                            className={styles.hiddenData}
                          >
                            <strong>Data hidden</strong> for this risk area
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        border: (riskLevel && isGreaterMinResponseRate) && `1px solid ${colors.lightSlateGrey}`,
                        borderRadius: 1.5,
                        padding: '8px 12px',
                        ml: 2.6,
                        background: !(riskLevel && isGreaterMinResponseRate) && colors.shadeGrey,
                      }}
                    >
                      <img
                        className={styles.riskStatus}
                        src={isGreaterMinResponseRate ? LearnersIcon : NoLearnersIcon}
                        alt="learners"
                      />
                      <Box style={{ paddingLeft: '1rem' }}>
                        {isGreaterMinResponseRate ? (
                          <>
                            <Typography className={styles.learners}>
                              {respondentCountRiskArea.toLocaleString()} learners responding
                            </Typography>
                            <Typography className={styles.responseRate}>
                              ({responseRateRiskArea}% response rate)
                            </Typography>
                          </>
                        ) : (
                          <Typography className={styles.learners}>
                            Adjust the response period and/or rate above to see scores for this area
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            {index !== riskAreas.length - 1 && <Divider sx={{ borderBottom: palette.border.main }} />}
          </Box>
        );
      })}
    </Box>
  );
};

RiskSummary.propTypes = {
  riskPersona: PropTypes.string.isRequired,
  riskAreas: PropTypes.array.isRequired,
  minResponseRate: PropTypes.number.isRequired,
};

export default RiskSummary;
