import React, { useState } from 'react';
import styled from 'styled-components';
import styles from './AnalyticRisks.module.css';
import Dialog from '../../../../components/Modal/Dialog';
import TableauReportContainer from '../TableauReportContainer';

export const StyledModelMenuContainer = styled.div`
  background: transparent;
  padding: 0;
  margin-top: -15px;
  border-bottom: 1px solid #C7C9DD;
  display: flex;
  align-items: flex-start;
`;

export const StyledTableauDivider = styled.div`
  margin-top: 15px;
`;

export const StyledModalLinkText = styled.div`
  display: flex;
  align-self: center;
  font-size: 16px;
  background: transparent;
  color: #40405D;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  width: max-content;
  &.active {
    border-bottom: 3px solid #555572;
    cursor: default;
  }
  &.modalSpacer {
    width: 20px;
    cursor: default;
  }
`;

const WhereTheProblem = ({ reportingAccess, whereTheProblem, setOpen, setWhereTheProblem, open,
  setClickProblemLink, setPermissionDialogueDisabled }) => {
  const [subTabView, setSubTabView] = useState('Segmentation');
  const analyticsParams = {};
  if (whereTheProblem) {
    analyticsParams.idParam = whereTheProblem.id;
  }
  const onModalClose = () => {
    setOpen(false);
    setWhereTheProblem(null);
    setClickProblemLink(false);
    setPermissionDialogueDisabled(true);
    setSubTabView('Segmentation');
  };

  return (
    <Dialog
      whereTheProblem
      ariaLabelledby="expanded-card-header"
      open={open}
      onClose={() => onModalClose()}
    >
      <>
        <StyledModelMenuContainer>
          <StyledModalLinkText
            onClick={() => { setSubTabView('Segmentation'); }}
            className={subTabView === 'Segmentation' ? 'active' : ''}
            data-cy="Segmentation"
          >
            Segmentation
          </StyledModalLinkText>
          <StyledModalLinkText className="modalSpacer" />

          <StyledModalLinkText
            onClick={() => { setSubTabView('Recommended'); }}
            className={subTabView === 'Recommended' ? 'active' : ''}
            data-cy="Recommended"
          >
            Recommended Actions
          </StyledModalLinkText>
          <StyledModalLinkText className="modalSpacer" />

        </StyledModelMenuContainer>
        {reportingAccess && reportingAccess !== 'Segmentation+Questions+Summary' && <div className={styles.whereProblemiFrame} />}
        {whereTheProblem && reportingAccess === 'Segmentation+Questions+Summary' && (
        <StyledTableauDivider>
          {subTabView === 'Segmentation' && (
          <TableauReportContainer
            view="segmentationModal/segmentationModal"
            analyticsIdHash={whereTheProblem.analyticsIdHash}
            analyticsParams={analyticsParams}
          />
          )}

          {subTabView === 'Recommended' && (
          <TableauReportContainer
            view="riskRecommendedActions/recActionsModal"
            analyticsIdHash={whereTheProblem.analyticsIdHash}
            analyticsParams={analyticsParams}
          />
          )}
        </StyledTableauDivider>
        )}
      </>
    </Dialog>
  );
};
export default WhereTheProblem;
