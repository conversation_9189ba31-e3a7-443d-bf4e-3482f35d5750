/* eslint-disable max-len */
import React from 'react';
import { get } from 'lodash';
import {
  Box,
  Typography,
  useTheme,
  Button,
  Divider,
  Grid,
} from '@mui/material';
import PropTypes from 'prop-types';
import ArrowForwardIcon from '@mui/icons-material/ArrowForwardIos';
import styles from './Summary.module.css';
import { colors } from '../../../../theme/colors';
import { useUser } from '../../../../hooks/useUser';
import ContentTitlesIcon from '../../../../images/content-titles.png';

const supportTopics = {
  HR: 'I would like help deploying training to get more HR & People risk level data.',
  Business: 'I would like help deploying training to get more Business Compliance risk level data.',
};

function DeployedContent({ deployedContent, riskPersona }) {
  const { palette } = useTheme();
  const user = useUser();
  const name = `${user?.firstName} ${user?.lastName}`;
  const email = `${user?.email}`;
  const accountName = get(user, 'accounts[0].name');

  const unlockEmtrainURL = (topicOrQuestion) => {
    const encodedQueryParams = `Your Company Name=${encodeURIComponent(accountName)}&Your Name=${encodeURIComponent(name)}&Your Email Address=${encodeURIComponent(email)}&Agenda Topics / Questions=${encodeURIComponent(topicOrQuestion)}`;
    const unlockAnalyticsURL = `https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820?${encodedQueryParams}`;
    return unlockAnalyticsURL;
  };

  return (
    <Box className={styles.deploymentBox} sx={{ backgroundColor: palette.background.white }}>
      <Box className={styles.deploymentHeader}>
        <Typography className={styles.deployedHeading}>Deploy training to get risk levels for these areas</Typography>
        <Button
          onClick={() => window.open(unlockEmtrainURL(supportTopics[riskPersona]))}
          disableRipple
          className={styles.supportButton}
          sx={{ color: colors.cyanBlue }}
        >
          Contact Client Success
          <ArrowForwardIcon className={styles.buttonArrow} />
        </Button>
      </Box>
      <Box sx={{ p: 2.5 }}>
        {deployedContent.map((category, index) => (
          <Box key={category.riskArea}>
            <Typography fontSize="1.05rem" fontWeight="700">
              {category.riskArea}
            </Typography>
            <Grid container spacing={1} columnGap={1} alignItems="center" sx={{ mt: 1, ml: 2.5, width: 'auto' }}>
              <Grid item>
                <img
                  className={styles.contentTitleImage}
                  src={ContentTitlesIcon}
                  alt="catalogTitle"
                />
              </Grid>
              <Grid item xs sx={{ padding: '0px !important' }}>
                <Typography variant="body2" fontWeight="500" fontSize="1rem">
                  {category.catalogTitle.length ? category.catalogTitle.join(', ') : (
                    <>
                      <strong>Data hidden&nbsp;</strong>for this risk area
                    </>
                  )}
                </Typography>
              </Grid>
            </Grid>
            {index !== deployedContent.length - 1 && <Divider sx={{ my: 1, borderBottom: palette.border.main }} />}
          </Box>
        ))}
      </Box>
    </Box>
  );
}

DeployedContent.propTypes = {
  riskPersona: PropTypes.string.isRequired,
  deployedContent: PropTypes.array.isRequired,
};

export default DeployedContent;
