/* eslint-disable no-nested-ternary */
/* eslint-disable max-len */
import React from 'react';
import { Box, LinearProgress, Typography, useTheme } from '@mui/material';

function LinearProgressBar({
  percentageComparison,
  percentageHealthy,
  flagIsTrue,
  benchmarkAgainst,
  diffLongitudinal,
}) {
  const { palette } = useTheme();

  const showComparisonLine = percentageComparison && benchmarkAgainst;
  const isOneYearPrior = benchmarkAgainst === 'oneYearPrior';
  const showLabel = showComparisonLine && (!isOneYearPrior || Number(diffLongitudinal) !== 0);

  const labelText = isOneYearPrior
    ? `one year prior ${percentageComparison || ''}%`
    : `${flagIsTrue ? (benchmarkAgainst === 'industry' ? 'industry' : 'org size') : 'global'} avg ${percentageComparison || ''}%`;

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        flex: 1,
        marginX: 2,
      }}
    >
      <Box sx={{ position: 'relative', width: '100%', height: '40px' }}>
        <LinearProgress
          variant="determinate"
          value={percentageHealthy}
          sx={{
            height: 14,
            borderRadius: 5,
            backgroundColor: palette.background.lightShadeGrey,
            '& .MuiLinearProgress-bar': {
              backgroundColor: palette.primary.darkGrey,
            },
          }}
        />

        {showComparisonLine && (
          <>
            {(Number(diffLongitudinal) !== 0 || !isOneYearPrior) && (
              <Box
                sx={{
                  position: 'absolute',
                  left: `${percentageComparison}%`,
                  top: 0,
                  height: '55%',
                  borderLeft: `2px dotted ${palette.primary.greyPurple}`,
                }}
              />
            )}

            {showLabel && (
              <Typography
                variant="body2"
                sx={{
                  position: 'absolute',
                  left: `${percentageComparison}%`,
                  top: '20px',
                  transform: 'translateX(-70%)',
                  color: palette.primary.mainBlue,
                  fontSize: '10px',
                  width: '35%',
                }}
              >
                {labelText}
              </Typography>
            )}
          </>
        )}
      </Box>

      <Typography
        sx={{
          fontSize: 15,
          fontWeight: 600,
          margin: '-20px 8px 8px 8px',
        }}
      >
        {percentageHealthy}%
      </Typography>
    </Box>
  );
}

export default LinearProgressBar;
