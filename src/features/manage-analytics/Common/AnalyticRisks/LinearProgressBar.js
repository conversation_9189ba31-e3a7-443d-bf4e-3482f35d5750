import React from 'react';
import { Box, LinearProgress, Typography, useTheme } from '@mui/material';

function LinearProgressBar({ percentageHealthy, percentageComparisonIndustry, flagIsTrueIndustry }) {
  const { palette } = useTheme();
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        flex: 1,
        marginX: 2,
      }}
    >
      <Box
        sx={{ position: 'relative', width: '100%', height: '40px' }}
      >

        <LinearProgress
          variant="determinate"
          value={percentageHealthy}
          sx={{
            height: 14,
            borderRadius: 5,
            flex: 1,
            backgroundColor: palette.background.lightShadeGrey,
            '& .MuiLinearProgress-bar': {
              backgroundColor: palette.primary.darkGrey,
            },
          }}
        />

        <Box
          sx={{
            position: 'absolute',
            left: `${percentageComparisonIndustry}%`,
            top: 0,
            height: '55%',
            borderLeft: `2px dotted ${palette.primary.greyPurple}`,
          }}
        />

        <Typography
          variant="body2"
          sx={{
            position: 'absolute',
            left: `${percentageComparisonIndustry}%`,
            top: '20px',
            transform: 'translateX(-80%)',
            color: palette.primary.mainBlue,
            fontSize: '10px',
          }}
        >
          {flagIsTrueIndustry ? 'industry' : 'global'}&nbsp;avg&nbsp;{percentageComparisonIndustry}%
        </Typography>
      </Box>
      <Typography
        sx={{
          fontSize: 15,
          fontWeight: 600,
          margin: '-20px 8px 8px 8px',
        }}
      >
        {percentageHealthy}%
      </Typography>
    </Box>
  );
}

export default LinearProgressBar;
