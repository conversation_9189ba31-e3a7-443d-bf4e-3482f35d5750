/* eslint-disable max-len, no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import { useUser } from '../../../../hooks/useUser';
import { calendarYearRange, sixMonthsRange } from './calendarYearRanges';
import { getRiskAreas, getRisksQuestions } from '../../../../services/api/AnalyticsRisks';
import QuestionArea from './QuestionArea';
import SideMenu from './SideMenu';
import { getSelectedSessionFilters, updateSessionRiskFilters } from '../../../../services/analyticsSettings';

function RiskComplianceMain({ riskPersona, setClickProblemLink, setPermissionDialogueDisabled }) {
  const riskFilters = getSelectedSessionFilters(riskPersona);
  const [riskQuestions, updateRiskQuestions] = useState({ data: [], deployed: null });
  const [riskAreas, setRiskAreas] = useState([]);
  const [catalog, setCatalog] = useState([]);
  const [flagHasTenureData, setFlagHasTenureData] = useState(false);
  const [isDefaultPeriod, setIsDefaultPeriod] = useState(!riskFilters?.periodId);
  const [selectedRiskArea, setSelectedRiskArea] = useState(null);
  const [periodCategory, setPeriodCategory] = useState(riskFilters?.periodId?.toString().length === 1 ? 'Calendar Year' : '6 Months');
  const [minResponseRate, setMinResponseRate] = useState(riskFilters?.minResponseRate || 10);
  const [includeNewHires, setIncludeNewHires] = useState(riskFilters?.includeNewHires !== undefined ? riskFilters?.includeNewHires : true);
  const [periodString, setPeriodString] = useState(null);
  const [sortCriteria, setSortCriteria] = useState(null);
  const [benchmarkAgainst, setBenchmarkAgainst] = useState('industry');
  const [catalogList, setCatalogList] = useState(null);
  const [loader, setLoader] = useState(true);
  const user = useUser();
  let analyticsId = get(user, 'accounts[1].analyticsId');
  if (!analyticsId) {
    analyticsId = get(user, 'accounts[0].analyticsId');
  }
  const sixMonthsRanges = sixMonthsRange();
  const calendarYearRanges = calendarYearRange();
  let selectedPeriod = null;
  if (sixMonthsRanges && sixMonthsRanges.length) {
    selectedPeriod = sixMonthsRanges.slice(-1)[0].dateId;
  }
  const [period, setPeriod] = useState(riskFilters?.periodId || selectedPeriod);

  useEffect(() => {
    async function fetchRiskAreas() {
      const risksFiltersData = await getRiskAreas(analyticsId);
      if (risksFiltersData && risksFiltersData.riskAreas && risksFiltersData.riskAreas.length) {
        const filteredRiskAreas = risksFiltersData.riskAreas.filter(({ riskPersona: persona }) => persona === riskPersona);
        setRiskAreas(filteredRiskAreas);
        setFlagHasTenureData(risksFiltersData.flagHasTenureData);
        const sortBy = filteredRiskAreas[0]?.riskArea === 'Harassment & Discrimination' ? 'Skill Area' : 'Risk Level';
        setSortCriteria(sortBy);
        const selectedIndex = selectedRiskArea?.riskId
          ? filteredRiskAreas.findIndex((item) => item.riskAreaId === selectedRiskArea.riskId)
          : 0;

        const selected = filteredRiskAreas[selectedIndex] || filteredRiskAreas[0];
        setSelectedRiskArea({
          riskId: riskFilters?.riskAreaId || selected?.riskAreaId,
          riskName: filteredRiskAreas[0]?.riskArea,
        });
      }
    }
    fetchRiskAreas();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [analyticsId, riskPersona]);

  const updateLoader = (value) => {
    setLoader(value);
  };

  const updatePeriodCategory = (value) => {
    setPeriodCategory(value);
    const periodId = value === '6 Months' ? selectedPeriod : (calendarYearRanges[2]?.dateId || null);
    setPeriod(periodId);
    updateSessionRiskFilters(riskPersona, { periodId, periodCategory: value });
  };

  const updatePeriod = (value) => {
    setPeriod(value);
    updateSessionRiskFilters(riskPersona, { periodId: value });
  };

  const updateIncludeNewHires = (value) => {
    setIncludeNewHires(value);
    updateSessionRiskFilters(riskPersona, { includeNewHires: value });
  };

  const updateMinResponseRate = (value) => {
    setMinResponseRate(value);
    updateSessionRiskFilters(riskPersona, { minResponseRate: value });
  };

  const updateSelectedRiskArea = (value) => {
    const sortBy = value?.riskName === 'Harassment & Discrimination' ? 'Skill Area' : 'Risk Level';
    setSortCriteria(sortBy);
    setSelectedRiskArea(value);
  };

  const updateSortCriteria = (value) => {
    setSortCriteria(value);
  };

  const updateBenchmarkAgainst = (value) => {
    setBenchmarkAgainst(value);
  };

  useEffect(() => {
    async function fetchQuestions() {
      // Check if minResponseRate is valid before proceeding
      if (minResponseRate < 10 || minResponseRate > 100) {
        return;
      }
      setLoader(true);
      const reqRisksQuestions = { analyticsId, minResponseRate, period, includeNewHires, periodCategory, sortCriteria, benchmarkAgainst };
      reqRisksQuestions.riskId = riskFilters?.riskAreaId || selectedRiskArea?.riskId;
      reqRisksQuestions.isDefaultPeriod = !riskFilters?.periodId && isDefaultPeriod;
      if (analyticsId && selectedRiskArea) {
        const response = await getRisksQuestions(reqRisksQuestions);
        setCatalogList(response.catalogList);
        setCatalog(response.responses);
        updateRiskQuestions({ data: response.data, deployed: response.deployed });
        setPeriodString(response?.periodString || null);

        if (isDefaultPeriod && !riskFilters?.periodId) {
          setIsDefaultPeriod(false);
          const periodCategoryCheck = response?.data[0]?.periodId?.toString().length !== 1 ? '6 Months' : 'Calendar Year';
          const isPeriodData = response?.data?.filter(({ periodId }) => periodId);
          setPeriod(isPeriodData[0]?.periodId || period);
          setPeriodCategory(periodCategoryCheck);
          updateSessionRiskFilters(riskPersona, { periodId: (isPeriodData[0]?.periodId || period), periodCategory: periodCategoryCheck });
        }
      }
      setLoader(false);
    }
    if (riskFilters?.periodId || isDefaultPeriod || riskFilters?.riskAreaId) {
      fetchQuestions();
    }
  }, [analyticsId, periodCategory, period, selectedRiskArea, includeNewHires, minResponseRate, sortCriteria, benchmarkAgainst, riskAreas, isDefaultPeriod, riskFilters?.periodId, riskFilters?.riskAreaId, riskPersona]);

  return (
    <Box
      sx={{
        display: 'flex',
        paddingTop: '20px',
        columnGap: '1.5rem',
        marginBottom: '3rem',
      }}
    >
      <SideMenu
        riskQuestions={riskQuestions}
        riskAreas={riskAreas}
        catalog={catalog}
        analyticsId={analyticsId}
        updateLoader={updateLoader}
        selectedRiskArea={selectedRiskArea}
        updateSelectedRiskArea={updateSelectedRiskArea}
        minResponseRate={minResponseRate}
        updateMinResponseRate={updateMinResponseRate}
        updatePeriodCategory={updatePeriodCategory}
        updatePeriod={updatePeriod}
        updateIncludeNewHires={updateIncludeNewHires}
        includeNewHires={includeNewHires}
        periodCategory={periodCategory}
        period={period}
        flagHasTenureData={flagHasTenureData}
        riskPersona={riskPersona}
      />
      {selectedRiskArea && (
        <QuestionArea
          riskQuestions={riskQuestions}
          loader={loader}
          riskAreas={riskAreas}
          selectedRiskArea={selectedRiskArea}
          updateSortCriteria={updateSortCriteria}
          sortCriteria={sortCriteria}
          setClickProblemLink={setClickProblemLink}
          catalogList={catalogList}
          setPermissionDialogueDisabled={setPermissionDialogueDisabled}
          benchmarkAgainst={benchmarkAgainst}
          periodString={periodString}
          updateBenchmarkAgainst={updateBenchmarkAgainst}
        />
      )}
    </Box>
  );
}

RiskComplianceMain.propTypes = {
  riskPersona: PropTypes.string.isRequired,
};

export default RiskComplianceMain;
