/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import { get } from 'lodash';
import {
  Box,
  Typography,
  Select,
  MenuItem,
  List,
  ListItem,
  Divider,
  useTheme,
} from '@mui/material';
import { Link } from 'react-router-dom';
import styles from './AnalyticRisks.module.css';
import HealthyLevelIcon from '../../../../images/risk-healthy.png';
import ConcerningLevelIcon from '../../../../images/risk-concerning.png';
import WarningLevelIcon from '../../../../images/risk-warning.png';
import RiskyLevelIcon from '../../../../images/risk-risky.png';
import LinearProgressBar from './LinearProgressBar';
import SpinnerGif from '../../../../images/spinner.gif';
import RiskToolTip from './RiskToolTip';
import { useUser } from '../../../../hooks/useUser';
import WhereTheProblem from './WhereTheProblem';

const QuestionArea = (props) => {
  const { riskQuestions, loader, riskAreas, selectedRiskArea, updateSortCriteria, sortCriteria,
    setClickProblemLink, catalogList, setPermissionDialogueDisabled } = props;
  const { palette } = useTheme();
  const user = useUser();
  const reportingAccess = get(user, 'accounts[0].reportingAccess');
  const name = `${user?.firstName} ${user?.lastName}`;
  const email = `${user?.email}`;
  const accountName = get(user, 'accounts[0].name');
  const [open, setOpen] = useState(false);
  const [whereTheProblem, setWhereTheProblem] = useState(null);
  const isHarassmentDiscrimination = (selectedRiskArea && selectedRiskArea?.riskName === 'Harassment & Discrimination') || false;
  const Icons = {
    Healthy: HealthyLevelIcon,
    Warning: WarningLevelIcon,
    Concerning: ConcerningLevelIcon,
    Risky: RiskyLevelIcon,
  };
  const handleSortChange = (event) => {
    updateSortCriteria(event.target.value);
  };

  const isQuestionData = riskQuestions && riskQuestions.data && riskQuestions.data.length > 0;
  let questionData = riskQuestions.data;
  const deployed = riskQuestions.deployed;
  let riskAreaObj = {};
  if (riskAreas && riskAreas.length) {
    riskAreaObj = riskAreas.find((obj) => obj.riskAreaId === selectedRiskArea.riskId);
  }
  const industryGroups = isQuestionData && riskQuestions.data.length ? riskQuestions.data[0]?.industryGroup : '';
  const isQuestionGroup = riskAreaObj && riskAreaObj?.riskArea === 'Harassment & Discrimination' && sortCriteria === 'Skill Area';

  if (isQuestionGroup) {
    questionData = questionData.filter(({ skillArea }) => skillArea).sort((a, b) => a.skillAreaId - b.skillAreaId);
  }
  const selectRiskArea = riskAreaObj ? riskAreaObj.riskArea : '';
  const requestMessage = `I would like help deploying training to get more ${selectRiskArea} risk level data.`;

  let firstGroupHeading = '';
  let firstGroupToolTip = '';
  if (isQuestionGroup) {
    const groupedBySkillArea = questionData.reduce((acc, item) => {
      const key = item.skillArea;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});
    questionData = groupedBySkillArea;
    firstGroupHeading = Object.keys(questionData)[0];
    const groupToolTip = riskQuestions?.data.filter(({ skillArea }) => skillArea === firstGroupHeading) || [];
    firstGroupToolTip = groupToolTip[0]?.skillAreaDescription;
  }

  const sampleToolTip =
  {
    healthyToolTip: {
      title: 'The percentage of your learners who agreed with positive questions or disagreed with negative ones.',
    },
    benchMarkToolTip: {
      title: `Your industry is <strong>${industryGroups}</strong>`,
      description: 'We benchmark your score against your industry’s average when we have sufficient data. Otherwise, we benchmark against Emtrain’s global average.',
      comingSoonTitle: 'Coming soon! Compare to…',
      comingSoonData: ['Other organizations your size', 'Same period 12 months prior'],
    },
    riskLevelToolTip: {
      title: "Emtrain's estimate of your risk level is calculated based your organization’s distance from the benchmark average, taking into consideration the overall distribution of scores within the benchmark.",
    },
  };

  const handleOpen = (e, analyticsIdHash, id) => {
    e.preventDefault();
    setPermissionDialogueDisabled(false);
    setWhereTheProblem({ analyticsIdHash, id });
    if (reportingAccess === 'Segmentation+Questions+Summary') {
      setOpen(true);
    } else {
      window.sessionStorage.setItem('whereIsProblem', true);
      setClickProblemLink(true);
    }
  };

  const unlockEmtrainURL = (topicOrQuestion) => {
    const encodedQueryParams = `Your Company Name=${encodeURIComponent(accountName)}&Your Name=${encodeURIComponent(name)}&Your Email Address=${encodeURIComponent(email)}&Agenda Topics / Questions=${encodeURIComponent(topicOrQuestion)}`;
    const unlockAnalyticsURL = `https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820?${encodedQueryParams}`;
    return unlockAnalyticsURL;
  };

  const nonGroupData = (isNonGroupQuestionData, nonGroupQuestionData) => {
    return (
      <>
        {isNonGroupQuestionData && nonGroupQuestionData.length && nonGroupQuestionData.map((item, index) => (
          <React.Fragment key={item.id}>
            <ListItem className={styles.questionListItem}>
              <Typography component="div" className={styles.questionGroup}>
                {item.questionGroup}
                <Typography
                  className={styles.learnerRes}
                  sx={{
                    color: 'gray',
                  }}
                >
                  {item.totalRespondents.toLocaleString()} learners responding ({item.responseRate}%)
                </Typography>
              </Typography>
              <LinearProgressBar
                percentageComparisonIndustry={item.percentageComparisonIndustry}
                percentageHealthy={item.percentageHealthy}
                flagIsTrueIndustry={item.flagIsTrueIndustry}
              />
              <Typography component="div" className={styles.benchmark}>
                {item.diffIndustry !== 0 && (
                  <span>
                    {Math.abs(item.diffIndustry)} {Math.abs(item.diffIndustry) === 1 ? 'point' : 'points'}{' '}
                  </span>
                )}
                <span style={{ fontWeight: 'bold' }}>{item.diffIndustry < 0 ? 'below' : item.diffIndustry > 0 ? 'above' : 'Equal to'}{' '}</span>
                {item.flagIsTrueIndustry ? 'industry' : 'global'}{' '}
                average
                {item.riskStatus !== 'Healthy' && (
                  <Typography
                    className={styles.problemLink}
                    sx={{
                      color: palette.link.document,
                    }}
                  >
                    <a href="#" onClick={(e) => handleOpen(e, item.analyticsIdHash, item.id)}>
                      Where’s the problem?
                    </a>
                  </Typography>
                )}
              </Typography>
              <Box className={styles.riskStatus}>
                <img src={Icons[item.riskStatus]} alt="risksLevel" />
                <span>{item.riskStatus}</span>
              </Box>
            </ListItem>
            {index < nonGroupQuestionData.length - 1 && (
            <Divider sx={{ borderColor: palette.border.lightLightPurpleShade,
              borderBottomStyle: 'dotted',
            }}
            />
            )}
          </React.Fragment>
        ))}
      </>
    );
  };
  const groupData = (isGroupQuestionData, groupedBySkillArea) => {
    if (!isGroupQuestionData || !groupedBySkillArea) return null;
    let isFirstGroup = true;
    return (
      <>
        {Object.entries(groupedBySkillArea).map(([skillArea, items]) => {
          const currentIsFirstGroup = isFirstGroup;
          if (isFirstGroup) isFirstGroup = false;
          return (
            <React.Fragment key={skillArea}>
              {!currentIsFirstGroup && (
                <>
                  <RiskToolTip label={skillArea} labelInfo={{ title: items[0]?.skillAreaDescription }} isGroupLabel />
                  <Divider />
                </>
              )}
              {items.map((item, index) => (
                <React.Fragment key={item.id}>
                  <ListItem className={styles.questionListItem}>
                    <Typography component="div" className={styles.questionGroup}>
                      {item.questionGroup}
                      <Typography
                        className={styles.learnerRes}
                        sx={{ color: '#8E8DA0' }}
                      >
                        {item.totalRespondents.toLocaleString()} learners responding ({item.responseRate}%)
                      </Typography>
                    </Typography>
                    <LinearProgressBar
                      percentageComparisonIndustry={item.percentageComparisonIndustry}
                      percentageHealthy={item.percentageHealthy}
                      flagIsTrueIndustry={item.flagIsTrueIndustry}
                    />
                    <Typography component="div" className={styles.benchmark}>
                      {item.diffIndustry !== 0 && (
                        <span>
                          {Math.abs(item.diffIndustry)}{' '}
                          {Math.abs(item.diffIndustry) === 1 ? 'point' : 'points'}{' '}
                        </span>
                      )}
                      <span style={{ fontWeight: 'bold' }}>
                        {item.diffIndustry < 0
                          ? 'below'
                          : item.diffIndustry > 0
                            ? 'above'
                            : 'Equal to'}{' '}
                      </span>
                      {item.flagIsTrueIndustry ? 'industry' : 'global'} average
                      {item.riskStatus !== 'Healthy' && (
                        <Typography
                          className={styles.problemLink}
                          sx={{ color: palette.primary.cyanBlue }}
                        >
                          <a
                            href="#"
                            onClick={(e) => handleOpen(
                              e,
                              item.analyticsIdHash,
                              item.id,
                            )}
                          >
                            Where’s the problem?
                          </a>
                        </Typography>
                      )}
                    </Typography>
                    <Box className={styles.riskStatus}>
                      <img
                        src={Icons[item.riskStatus]}
                        alt="risksLevel"
                      />
                      <span>{item.riskStatus}</span>
                    </Box>
                  </ListItem>
                  {index < items.length - 1 && (
                    <Divider sx={{ borderColor: palette.border.lightLightPurpleShade, borderBottomStyle: 'dotted' }} />
                  )}
                </React.Fragment>
              ))}
            </React.Fragment>
          );
        })}
      </>
    );
  };

  return (
    <Box className={styles.questionBox} sx={{ backgroundColor: palette.background.white }}>
      <Box className={styles.questionHeader}>
        <Typography className={styles.questionHeading}>{selectRiskArea}</Typography>
        {isQuestionData && (
          <Box
            className={styles.questionsBox}
            sx={{
              border: palette.border.altoGrey,
              backgroundColor: palette.background.white,
            }}
          >
            <Typography
              className={styles.sortByLabel}
              sx={{
                color: palette.primary.mainBlue,
              }}
            >
              Sort by:
            </Typography>
            <Select
              className={styles.riskSortDropdown}
              value={sortCriteria}
              onChange={handleSortChange}
              disableunderline="true"
              sx={{
                width: '11rem',
                textAlign: 'left',
                '.MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '& .MuiOutlinedInput-input': {
                  textAlign: 'left',
                  marginTop: '2px',
                  padding: '16.5px 6px',
                },
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    marginTop: '-0.625rem !important',
                    '& .Mui-selected': {
                      fontWeight: 600,
                    },
                    '& ul': {
                      background: palette.background.white,
                      '& li': {
                        fontSize: '0.875rem',
                        padding: '0.313rem 0.625rem',
                      },
                    },
                  },
                },
              }}
            >
              <MenuItem value="Risk Level" key="riskLevel">Risk Level</MenuItem>
              <MenuItem value="Healthy Responses" key="healthyResponses">% Healthy Responses</MenuItem>
              <MenuItem value="Industry Comparison" key="industryComparison">Industry Comparison</MenuItem>
              {isHarassmentDiscrimination && (
                <MenuItem value="Skill Area" key="skillArea">Skill Area</MenuItem>
              )}
            </Select>
          </Box>
        )}
      </Box>
      {loader && (
        <div className={styles.loadSpinner}>
          <img src={SpinnerGif} width={50} aria-label="tilesLoading" />
        </div>
      )}
      <List sx={{ padding: '1rem' }}>
        {isQuestionData && (
          <>
            <ListItem className={styles.questionHeaderRow}>
              {isQuestionGroup ? (
                <RiskToolTip label={firstGroupHeading} labelInfo={{ title: firstGroupToolTip }} isGroupLabel />
              ) :
                <Typography />}
              <RiskToolTip label="% Healthy" labelInfo={sampleToolTip.healthyToolTip} />
              <RiskToolTip label="vs. Benchmark" labelInfo={sampleToolTip.benchMarkToolTip} />
              <RiskToolTip label="Risk Level" labelInfo={sampleToolTip.riskLevelToolTip} />
            </ListItem>
            <Divider sx={{ borderBottom: palette.border.lightLightPurpleShade }} />
          </>
        )}
        {isQuestionGroup ?
          groupData(isQuestionData, questionData) :
          nonGroupData(isQuestionData, questionData)}

        {!loader && !deployed && (
          <Box className={styles.catalogList}>
            <Typography>Deploy one or more of the following courses to measure risk here:</Typography>
            <Typography>
              <strong>{catalogList ? catalogList.replace(/;/g, ',') : ''}</strong>
            </Typography>
            <Link target="_blank" className="contactLink" to={{ pathname: unlockEmtrainURL(requestMessage) }}>
              <Typography
                gutterBottom
                sx={{ fontWeight: 800, color: palette.primary.cyanBlue, marginTop: '1rem' }}
              >Contact your client success team for assistance &#187;
              </Typography>
            </Link>
          </Box>
        )}

        {!loader && !isQuestionData && deployed && (
          <Typography sx={{ fontSize: '0.875rem' }}>
            There is not enough data to show risk levels based on your selected Date Range and Minimum Response Rate.
          </Typography>
        )}
      </List>
      <WhereTheProblem
        reportingAccess={reportingAccess}
        whereTheProblem={whereTheProblem}
        setOpen={setOpen}
        setWhereTheProblem={setWhereTheProblem}
        open={open}
        setClickProblemLink={setClickProblemLink}
        setPermissionDialogueDisabled={setPermissionDialogueDisabled}
      />
    </Box>
  );
};

export default QuestionArea;
