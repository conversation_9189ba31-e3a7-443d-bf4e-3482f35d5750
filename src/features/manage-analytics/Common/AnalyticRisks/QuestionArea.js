/* eslint-disable no-restricted-globals */
/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import { get } from 'lodash';
import {
  Box,
  Typography,
  Select,
  MenuItem,
  List,
  ListItem,
  Divider,
  useTheme,
} from '@mui/material';
import { Link } from 'react-router-dom';
import styles from './AnalyticRisks.module.css';
import HealthyLevelIcon from '../../../../images/risk-healthy.png';
import ConcerningLevelIcon from '../../../../images/risk-concerning.png';
import WarningLevelIcon from '../../../../images/risk-warning.png';
import RiskyLevelIcon from '../../../../images/risk-risky.png';
import LinearProgressBar from './LinearProgressBar';
import SpinnerGif from '../../../../images/spinner.gif';
import RiskToolTip from './RiskToolTip';
import { useUser } from '../../../../hooks/useUser';
import WhereTheProblem from './WhereTheProblem';

const QuestionArea = (props) => {
  const { riskQuestions, loader, riskAreas, selectedRiskArea, updateSortCriteria, sortCriteria,
    setClickProblemLink, catalogList, setPermissionDialogueDisabled, benchmarkAgainst, periodString, updateBenchmarkAgainst } = props;
  const { palette } = useTheme();
  const user = useUser();
  const reportingAccess = get(user, 'accounts[0].reportingAccess');
  const name = `${user?.firstName} ${user?.lastName}`;
  const email = `${user?.email}`;
  const accountName = get(user, 'accounts[0].name');
  const [open, setOpen] = useState(false);
  const [whereTheProblem, setWhereTheProblem] = useState(null);
  const isHarassmentDiscrimination = (selectedRiskArea && selectedRiskArea?.riskName === 'Harassment & Discrimination') || false;
  const Icons = {
    Healthy: HealthyLevelIcon,
    Warning: WarningLevelIcon,
    Concerning: ConcerningLevelIcon,
    Risky: RiskyLevelIcon,
  };
  const handleSortChange = (event) => {
    updateSortCriteria(event.target.value);
  };

  const handleBenchmarkAgainstChange = (event) => {
    if ((event.target.value === 'orgsSimilarSize' || event.target.value === 'oneYearPrior') && sortCriteria === 'Risk Level') {
      // eslint-disable-next-line no-unused-expressions
      (isHarassmentDiscrimination) ? updateSortCriteria('Skill Area') : updateSortCriteria('Healthy Responses');
    }
    updateBenchmarkAgainst(event.target.value);
  };

  const isQuestionData = riskQuestions && riskQuestions.data && riskQuestions.data.length > 0;
  let questionData = riskQuestions.data;
  const deployed = riskQuestions.deployed;
  let riskAreaObj = {};
  if (riskAreas && riskAreas.length) {
    riskAreaObj = riskAreas.find((obj) => obj.riskAreaId === selectedRiskArea.riskId);
  }
  const organizationSize = isQuestionData && riskQuestions.data.length ? riskQuestions.data[0]?.clientSize : '';
  const industryGroups = isQuestionData && riskQuestions.data.length ? riskQuestions.data[0]?.industryGroup : '';
  const isQuestionGroup = riskAreaObj && riskAreaObj?.riskArea === 'Harassment & Discrimination' && sortCriteria === 'Skill Area';
  if (isQuestionGroup) {
    questionData = questionData.filter(({ skillArea }) => skillArea).sort((a, b) => a.skillAreaId - b.skillAreaId);
  }
  const selectRiskArea = riskAreaObj ? riskAreaObj.riskArea : '';
  const requestMessage = `I would like help deploying training to get more ${selectRiskArea} risk level data.`;

  let firstGroupHeading = '';
  let firstGroupToolTip = '';
  if (isQuestionGroup) {
    const groupedBySkillArea = questionData.reduce((acc, item) => {
      const key = item.skillArea;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});
    questionData = groupedBySkillArea;
    firstGroupHeading = Object.keys(questionData)[0];
    const groupToolTip = riskQuestions?.data.filter(({ skillArea }) => skillArea === firstGroupHeading) || [];
    firstGroupToolTip = groupToolTip[0]?.skillAreaDescription;
  }

  const renderBenchmarkInfo = (value, positionText, benchmarkLabel) => {
    if (!isFinite(value)) return null;

    return (
      <>
        {value !== 0 && (
        <span>
          {Math.abs(value)} {Math.abs(value) === 1 ? 'point' : 'points'}{' '}
        </span>
        )}
        <span style={{ fontWeight: 'bold' }}>{positionText}{' '}</span>
        {benchmarkLabel} average
      </>
    );
  };

  const getTooltipsInfo = (category) => {
    const tooltipMap = {
      industry: {
        healthyToolTip: {
          title: 'The percentage of your learners who agreed with positive questions or disagreed with negative ones.',
        },
        benchMarkToolTip: {
          title: `Your industry is <strong>${industryGroups ?? ''}</strong>`,
          description: 'We benchmark your score against your industry’s average when we have sufficient data. Otherwise, we benchmark against Emtrain’s global average.',
          benchmarkTooltip: 'Check out other comparisons by changing the <b>Benchmark against</b> option in the menu above.',
        },
        riskLevelToolTip: {
          title: "Emtrain's estimate of your risk level is calculated based your organization’s distance from the benchmark average, taking into consideration the overall distribution of scores within the benchmark.",
        },
      },

      orgsSimilarSize: {
        healthyToolTip: {
          title: 'The percentage of your learners who agreed with positive questions or disagreed with negative ones.',
        },
        benchMarkToolTip: {
          title: `Your organization size is <strong>${organizationSize ?? ''}</strong> employees.`,
          description:
          'We benchmark your score against other organizations of similar size when we have sufficient data. Otherwise, we benchmark against Emtrain’s global average.',
          benchmarkTooltip:
          'Check out other comparisons by changing the <b>Benchmark against</b> option in the menu above.',
        },
        riskLevelToolTip: {
          title:
          'Change the <b>Benchmark against</b> option in the menu above to <b>Industry</b> to see your organization’s Risk Levels.',
        },
        benchmarkImgToolTip: {
          title: 'Change the <b>Benchmark against</b> option in the menu above to <b>Industry</b> to see your organization’s Risk Levels',
        },
      },

      oneYearPrior: {
        healthyToolTip: {
          title: 'The percentage of your learners who agreed with positive questions or disagreed with negative ones.',
        },
        benchMarkToolTip: {
          title: `Your selected period is <b>${periodString && periodString.length ? periodString[0] : ''}</b>.`,
          description:
          `When possible, we benchmark your scores against your own results from the same period one year prior (${periodString && periodString.length ? periodString[1] : ''}).<br /><br /> Trends are only shown for questions where prior data exists and sample sizes were similar enough to support a fair comparison.`,
          benchmarkTooltip:
          'Check out other comparisons by changing the <b>Benchmark against</b> option in the menu above.',
        },
        riskLevelToolTip: {
          title:
          'Change the <b>Benchmark against</b> option in the menu above to <b>Industry</b> to see your organization’s Risk Levels.',
        },
        benchmarkImgToolTip: {
          title: 'Change the <b>Benchmark against</b> option in the menu above to <b>Industry</b> to see your organization’s Risk Levels.',
        },
      },
    };

    return tooltipMap[category] || {};
  };

  const sampleToolTip = getTooltipsInfo(benchmarkAgainst);
  const handleOpen = (e, analyticsIdHash, id) => {
    e.preventDefault();
    setPermissionDialogueDisabled(false);
    setWhereTheProblem({ analyticsIdHash, id });
    if (reportingAccess === 'Segmentation+Questions+Summary') {
      setOpen(true);
    } else {
      window.sessionStorage.setItem('whereIsProblem', true);
      setClickProblemLink(true);
    }
  };

  const unlockEmtrainURL = (topicOrQuestion) => {
    const encodedQueryParams = `Your Company Name=${encodeURIComponent(accountName)}&Your Name=${encodeURIComponent(name)}&Your Email Address=${encodeURIComponent(email)}&Agenda Topics / Questions=${encodeURIComponent(topicOrQuestion)}`;
    const unlockAnalyticsURL = `https://app.smartsheet.com/b/form/ee594ed9245f430e82ad61005b744820?${encodedQueryParams}`;
    return unlockAnalyticsURL;
  };

  const nonGroupData = (isNonGroupQuestionData, nonGroupQuestionData) => {
    return (
      <>
        {isNonGroupQuestionData && nonGroupQuestionData.length && nonGroupQuestionData.map((item, index) => (
          <React.Fragment key={item.id}>
            <ListItem className={styles.questionListItem}>
              <Typography component="div" className={styles.questionGroup}>
                {item.questionGroup}
                <Typography
                  className={styles.learnerRes}
                  sx={{
                    color: 'gray',
                  }}
                >
                  {item.totalRespondents.toLocaleString()} learners responding ({item.responseRate}%)
                </Typography>
              </Typography>
              <LinearProgressBar
                percentageComparison={
                    benchmarkAgainst === 'orgsSimilarSize'
                      ? item.percentageComparisonClientSize
                      : benchmarkAgainst === 'industry'
                        ? item.percentageComparisonIndustry
                        : item.percentageComparisonLongitudinal
                  }
                percentageHealthy={item.percentageHealthy}
                flagIsTrue={
                    benchmarkAgainst === 'orgsSimilarSize'
                      ? item?.flagIsTrueClientSize?.data?.[0] === 1
                      : benchmarkAgainst === 'industry'
                        ? item?.flagIsTrueIndustry?.data?.[0] === 1
                        : 'N/A'
                  }
                benchmarkAgainst={benchmarkAgainst}
                diffLongitudinal={item.diffLongitudinal || null}
              />
              <Typography component="div" className={styles.benchmark}>
                {benchmarkAgainst === 'industry' && renderBenchmarkInfo(
                  item.diffIndustry,
                  item.diffIndustry < 0 ? 'below' : item.diffIndustry > 0 ? 'above' : 'Equal to',
                  item.flagIsTrueIndustry ? 'industry' : 'global',
                )}

                {benchmarkAgainst === 'orgsSimilarSize' && renderBenchmarkInfo(
                  item.diffClientSize,
                  item.diffClientSize < 0 ? 'below' : item.diffClientSize > 0 ? 'above' : 'Equal to',
                  item?.flagIsTrueClientSize?.data?.[0] === 1 ? 'org size' : 'global',
                )}

                {benchmarkAgainst === 'oneYearPrior' && (
                <>
                  {Number(item.diffLongitudinal) === 0 && <span>Insufficient comparison data</span>}
                  {Number(item.diffLongitudinal) !== 0 && isFinite(item.diffLongitudinal) && (
                  <>
                    <span>
                      {Math.abs(item.diffLongitudinal)} {Math.abs(item.diffLongitudinal) === 1 ? 'point' : 'points'}{' '}
                    </span>
                    <span style={{ fontWeight: 'bold' }}>
                      {item.diffLongitudinal < 0 ? 'below' : item.diffLongitudinal > 0 ? 'above' : 'Equal to'}{' '}
                    </span>
                    one year prior
                  </>
                  )}
                </>
                )}

                {!loader &&
                    item.riskStatus !== 'Healthy' &&
                    benchmarkAgainst &&
                    !['orgsSimilarSize', 'oneYearPrior'].includes(
                      benchmarkAgainst,
                    ) && (
                    <Typography
                      className={styles.problemLink}
                      sx={{ color: palette.link.document }}
                    >
                      <a href="#" onClick={(e) => handleOpen(e, item.analyticsIdHash, item.id)}>
                        Where’s the problem?
                      </a>
                    </Typography>
                )}
              </Typography>
              <Box className={styles.riskStatus}>
                {benchmarkAgainst &&
                    (benchmarkAgainst === 'orgsSimilarSize' ||
                      benchmarkAgainst === 'oneYearPrior') && (
                      <RiskToolTip
                        label={benchmarkAgainst}
                        labelInfo={sampleToolTip.benchmarkImgToolTip}
                      />
                )}
                {benchmarkAgainst && benchmarkAgainst === 'industry' && item?.riskStatus && (
                  <><img src={Icons[item.riskStatus]} alt="risksLevel" /><span>{item.riskStatus}</span></>
                )}
              </Box>
            </ListItem>
            {index < nonGroupQuestionData.length - 1 && (
            <Divider sx={{ borderColor: palette.border.lightLightPurpleShade,
              borderBottomStyle: 'dotted',
            }}
            />
            )}
          </React.Fragment>
        ))}
      </>
    );
  };
  const groupData = (isGroupQuestionData, groupedBySkillArea) => {
    if (!isGroupQuestionData || !groupedBySkillArea) return null;
    let isFirstGroup = true;
    return (
      <>
        {Object.entries(groupedBySkillArea).map(([skillArea, items]) => {
          const currentIsFirstGroup = isFirstGroup;
          if (isFirstGroup) isFirstGroup = false;
          return (
            <React.Fragment key={skillArea}>
              {!currentIsFirstGroup && (
                <>
                  <RiskToolTip label={skillArea} labelInfo={{ title: items[0]?.skillAreaDescription }} isGroupLabel />
                  <Divider />
                </>
              )}
              {items.map((item, index) => (
                <React.Fragment key={item.id}>
                  <ListItem className={styles.questionListItem}>
                    <Typography component="div" className={styles.questionGroup}>
                      {item.questionGroup}
                      <Typography
                        className={styles.learnerRes}
                        sx={{ color: '#8E8DA0' }}
                      >
                        {item.totalRespondents.toLocaleString()} learners responding ({item.responseRate}%)
                      </Typography>
                    </Typography>
                    <LinearProgressBar
                      percentageComparison={
                        benchmarkAgainst === 'orgsSimilarSize'
                          ? item.percentageComparisonClientSize
                          : benchmarkAgainst === 'industry'
                            ? item.percentageComparisonIndustry
                            : item.percentageComparisonLongitudinal
                      }
                      percentageHealthy={item.percentageHealthy}
                      flagIsTrue={
                        benchmarkAgainst === 'orgsSimilarSize'
                          ? item?.flagIsTrueClientSize?.data?.[0] === 1
                          : item?.flagIsTrueIndustry?.data?.[0] === 1
                      }
                      benchmarkAgainst={benchmarkAgainst}
                      diffLongitudinal={item.diffLongitudinal || null}
                    />
                    <Typography component="div" className={styles.benchmark}>
                      {benchmarkAgainst === 'industry' &&
                        renderBenchmarkInfo(
                          item.diffIndustry,
                          item.diffIndustry < 0 ? 'below' : item.diffIndustry > 0 ? 'above' : 'Equal to',
                          item.flagIsTrueIndustry ? 'industry' : 'global',
                        )}

                      {benchmarkAgainst === 'orgsSimilarSize' &&
                        renderBenchmarkInfo(
                          item.diffClientSize,
                          item.diffClientSize < 0 ? 'below' : item.diffClientSize > 0 ? 'above' : 'Equal to',
                          item?.flagIsTrueClientSize?.data?.[0] === 1 ? 'org size' : 'global',
                        )}

                      {benchmarkAgainst === 'oneYearPrior' && (
                      <>
                        {Number(item.diffLongitudinal) === 0 ? (
                          <span>Insufficient comparison data</span>
                        ) : (
                          isFinite(item.diffLongitudinal) && renderBenchmarkInfo(
                            item.diffLongitudinal,
                            item.diffLongitudinal < 0 ? 'below' : item.diffLongitudinal > 0 ? 'above' : 'Equal to',
                            'one year prior',
                          )
                        )}
                      </>
                      )}

                      {!loader && item.riskStatus !== 'Healthy' &&
                        !['orgsSimilarSize', 'oneYearPrior'].includes(benchmarkAgainst) && (
                          <Typography
                            className={styles.problemLink}
                            sx={{ color: palette.primary.cyanBlue }}
                          >
                            <a
                              href="#"
                              onClick={(e) => handleOpen(e, item.analyticsIdHash, item.id)}
                            >
                              Where’s the problem?
                            </a>
                          </Typography>
                      )}
                    </Typography>
                    <Box className={styles.riskStatus}>
                      {benchmarkAgainst &&
                        (benchmarkAgainst === 'orgsSimilarSize' ||
                          benchmarkAgainst === 'oneYearPrior') && (
                          <RiskToolTip
                            label={benchmarkAgainst}
                            labelInfo={sampleToolTip.benchmarkImgToolTip}
                          />
                      )}
                      {benchmarkAgainst && benchmarkAgainst === 'industry' && item?.riskStatus && (
                      <><img
                        src={Icons[item.riskStatus]}
                        alt="risksLevel"
                      />
                        <span>{item.riskStatus}</span>
                      </>
                      )}
                    </Box>
                  </ListItem>
                  {index < items.length - 1 && (
                    <Divider sx={{ borderColor: palette.border.lightLightPurpleShade, borderBottomStyle: 'dotted' }} />
                  )}
                </React.Fragment>
              ))}
            </React.Fragment>
          );
        })}
      </>
    );
  };

  return (
    <Box className={styles.questionBox} sx={{ backgroundColor: palette.background.white }}>
      <Box className={styles.questionHeader}>
        <Typography
          className={styles.questionHeading}
        >{selectRiskArea}
        </Typography>
        <>
          <Typography
            className={styles.rightSection}
            sx={{
              color: palette.primary.mainBlue,
            }}
            component="div"
          >
            Benchmark against:
            <Box
              className={styles.benchmarkBox}
              sx={{
                border: palette.border.altoGrey,
                backgroundColor: palette.background.white,
              }}
            >
              <Select
                className={styles.benchmarkDropdown}
                value={benchmarkAgainst}
                onChange={handleBenchmarkAgainstChange}
                disableunderline="true"
                sx={{
                  width: '9.5rem',
                  textAlign: 'left',
                  '.MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiOutlinedInput-input': {
                    textAlign: 'left',
                    marginTop: '2px',
                    padding: '16.5px 6px',
                  },
                  '& .MuiSelect-icon': {
                    right: '4px',
                  },
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      marginTop: '-0.625rem !important',
                      '& .Mui-selected': {
                        fontWeight: 600,
                      },
                      '& ul': {
                        background: palette.background.white,
                        '& li': {
                          fontSize: '0.875rem',
                          padding: '0.313rem 0.625rem',
                        },
                      },
                    },
                  },
                }}
              >
                <MenuItem value="industry" key="industry">Industry</MenuItem>
                <MenuItem value="orgsSimilarSize" key="orgsSimilarSize">Orgs of similar size</MenuItem>
                <MenuItem value="oneYearPrior" key="oneYearPrior">One year prior</MenuItem>
              </Select>
            </Box>
          </Typography>
          <Box
            className={styles.questionsBox}
            sx={{
              border: palette.border.altoGrey,
              backgroundColor: palette.background.white,
            }}
          >
            <Select
              className={styles.riskSortDropdown}
              value={sortCriteria}
              onChange={handleSortChange}
              disableunderline="true"
              displayEmpty
              renderValue={() => (
                <Box
                  component="span"
                  fontWeight="bold"
                  sx={{
                    display: 'inline-block',
                    width: '4.5rem',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    marginTop: '5px',
                  }}
                >
                  Sort by
                </Box>
              )}
              sx={{
                width: '11rem',
                textAlign: 'left',
                '.MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '& .MuiOutlinedInput-input': {
                  textAlign: 'left',
                  marginTop: '2px',
                  padding: '16.5px 6px',
                },
                '& .MuiSelect-icon': {
                  right: '-4px',
                },
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    marginTop: '-0.625rem !important',
                    '& .Mui-selected': {
                      fontWeight: 600,
                    },
                    '& ul': {
                      background: palette.background.white,
                      '& li': {
                        fontSize: '0.875rem',
                        padding: '0.313rem 0.625rem',
                      },
                    },
                  },
                },
              }}
            >
              {benchmarkAgainst && benchmarkAgainst === 'industry' && (
                <MenuItem value="Risk Level" key="riskLevel">Risk Level</MenuItem>
              )}
              <MenuItem value="Healthy Responses" key="healthyResponses">% Healthy Responses</MenuItem>
              <MenuItem value="Industry Comparison" key="industryComparison">Benchmark Comparison</MenuItem>
              {isHarassmentDiscrimination && (
                <MenuItem value="Skill Area" key="skillArea">Skill Area</MenuItem>
              )}
            </Select>
          </Box>
        </>
      </Box>
      {loader && (
        <div className={styles.loadSpinner}>
          <img src={SpinnerGif} width={50} aria-label="tilesLoading" />
        </div>
      )}
      <List sx={{ padding: '1rem' }}>
        {isQuestionData && (
        <>
          <ListItem className={styles.questionHeaderRow}>
            {isQuestionGroup ? (
              <RiskToolTip label={firstGroupHeading} labelInfo={{ title: firstGroupToolTip }} isGroupLabel />
            ) :
              <Typography />}
            <RiskToolTip label="% Healthy" labelInfo={sampleToolTip.healthyToolTip} />
            <RiskToolTip label="vs. Benchmark" labelInfo={sampleToolTip.benchMarkToolTip} />
            <RiskToolTip label="Risk Level" labelInfo={sampleToolTip.riskLevelToolTip} />
          </ListItem>
          <Divider sx={{ borderBottom: palette.border.lightLightPurpleShade }} />
        </>
        )}
        {isQuestionGroup ?
          groupData(isQuestionData, questionData) :
          nonGroupData(isQuestionData, questionData)}

        {!deployed && (
        <Box className={styles.catalogList}>
          <Typography>Deploy one or more of the following courses to measure risk here:</Typography>
          <Typography>
            <strong>{catalogList ? catalogList.replace(/;/g, ',') : ''}</strong>
          </Typography>
          <Link target="_blank" className="contactLink" to={{ pathname: unlockEmtrainURL(requestMessage) }}>
            <Typography
              gutterBottom
              sx={{ fontWeight: 800, color: palette.primary.cyanBlue, marginTop: '1rem' }}
            >Contact your client success team for assistance &#187;
            </Typography>
          </Link>
        </Box>
        )}

        {!isQuestionData && deployed && (
        <Typography sx={{ fontSize: '0.875rem' }}>
          There is not enough data to show risk levels based on your selected Date Range and Minimum Response Rate.
        </Typography>
        )}
      </List>
      <WhereTheProblem
        reportingAccess={reportingAccess}
        whereTheProblem={whereTheProblem}
        setOpen={setOpen}
        setWhereTheProblem={setWhereTheProblem}
        open={open}
        setClickProblemLink={setClickProblemLink}
        setPermissionDialogueDisabled={setPermissionDialogueDisabled}
      />
    </Box>
  );
};

export default QuestionArea;
