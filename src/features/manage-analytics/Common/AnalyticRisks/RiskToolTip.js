/* eslint-disable max-len */
import React from 'react';
import { Box, Divider, Tooltip, Typography, useTheme, tooltipClasses } from '@mui/material';
import styled from '@emotion/styled';
import InfoIcon from '../../../../images/info-icon.png';
import BenchmarkIcon from '../../../../images/benchmark.png';
import styles from './AnalyticRisks.module.css';

function RiskToolTip({ label, labelInfo, isGroupLabel = false, placement = 'bottom-end' }) {
  const { palette } = useTheme();
  const { title, description, benchmarkTooltip } = labelInfo || {};
  const isBenchmark = label === 'orgsSimilarSize' || label === 'oneYearPrior';

  const CustomTooltip = styled(({ className, maxWidth, ...props }) => (
    <Tooltip
      {...props}
      tabIndex="0"
      classes={{ popper: className }}
      arrow
    />
  ))(({ maxWidth }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: maxWidth || '11rem',
      marginTop: '0.6rem !important',
      borderRadius: 4,
      border: palette.border.darkGrey,
      backgroundColor: palette.background.white,
      color: palette.primary.main,
      padding: '0.625rem',
      minWidth: '11.188rem',
      boxShadow: '3px 3px 6px rgba(0, 0, 0, 0.3)',
      fontSize: '1.2em',
      boxSizing: 'border-box',
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: palette.background.white,
    },
  }));

  const labelStyle = isGroupLabel
    ? { mr: 0.6, fontSize: '1rem !important', fontWeight: '600 !important', paddingLeft: 0, mt: 1 }
    : { mr: 0.6 };

  const icon = isBenchmark ? BenchmarkIcon : InfoIcon;
  const iconStyle = {
    width: isBenchmark ? undefined : '0.6rem',
    cursor: 'pointer',
  };

  return (
    <Box display="flex" alignItems={isGroupLabel ? 'baseline' : 'center'}>
      {label && !isBenchmark && (
        <Typography sx={labelStyle}>{label}</Typography>
      )}
      {title && (
        <CustomTooltip
          maxWidth={205}
          placement={placement}
          PopperProps={{
            modifiers: [{ name: 'offset', options: { offset: [10, 0] } }],
          }}
          title={(
            <Box>
              <Typography
                gutterBottom
                sx={{ fontSize: '0.75rem' }}
                dangerouslySetInnerHTML={{ __html: title }}
              />
              {description && (
                <Typography
                  className={styles.tooltipDesc}
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              )}
              {benchmarkTooltip && (
                <>
                  <Divider />
                  <Typography
                    className={styles.benchmarkTooltip}
                    dangerouslySetInnerHTML={{ __html: benchmarkTooltip }}
                  />
                </>
              )}
            </Box>
          )}
        >
          <img src={icon} alt="infoIcon" style={iconStyle} />
        </CustomTooltip>
      )}
    </Box>
  );
}
export default RiskToolTip;
