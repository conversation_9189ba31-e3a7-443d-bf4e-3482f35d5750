import React from 'react';
import { Box, Divider, Tooltip, Typography, useTheme, tooltipClasses } from '@mui/material';
import styled from '@emotion/styled';
import InfoIcon from '../../../../images/info-icon.png';
import styles from './AnalyticRisks.module.css';

function RiskToolTip({ label, labelInfo, isGroupLabel = false, placement = 'bottom-end' }) {
  const { palette } = useTheme();
  const { title, description, comingSoonTitle, comingSoonData } = labelInfo;
  const CustomTooltip = styled(({ className, maxWidth, ...props }) => (
    <Tooltip
      {...props}
      tabIndex="0"
      classes={{ popper: className }}
      arrow
    />
  ))(({ maxWidth }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: maxWidth || '11rem',
      marginTop: '0.6rem !important',
      borderRadius: 4,
      border: palette.border.darkGrey,
      backgroundColor: palette.background.white,
      color: palette.primary.main,
      padding: '0.625rem',
      boxSizing: 'border-box',
      minWidth: '11.188rem',
      boxShadow: '3px 3px 6px rgba(0, 0, 0, 0.3)',
      fontSize: '1.2em',
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: palette.background.white,
    },
  }));
  const labelStyle = isGroupLabel
    ? { mr: 0.6, fontSize: '1rem !important', fontWeight: '600 !important', paddingLeft: '0rem !important', mt: 1 }
    : { mr: 0.6 };
  const alignLabel = isGroupLabel ? 'baseline' : 'center';
  return (
    <Box display="flex" alignItems={alignLabel}>
      <Typography sx={labelStyle}>
        {label}
      </Typography>
      {title && (
        <CustomTooltip
          maxWidth={205}
          placement={placement}
          PopperProps={{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [10, 0],
                },
              },
            ],
          }}
          title={(
            <Box>
              <Typography gutterBottom sx={{ fontSize: '0.75rem' }} dangerouslySetInnerHTML={{ __html: title }} />
              {description && (
                <Typography className={styles.tooltipDesc}>
                  {description}
                </Typography>
              )}
              {comingSoonTitle && (
                <>
                  <Divider />
                  <Typography className={styles.comingsoonTitle}>
                    {comingSoonTitle}
                  </Typography>
                </>
              )}
              {comingSoonData && (
                <ul className={styles.comingsoonUl}>
                  {comingSoonData.map((item) => (
                    <li
                      key={`${item}`}
                      style={{
                        display: 'list-item',
                        fontSize: '0.75rem',
                      }}
                    >
                      <Typography sx={{ fontSize: '0.75rem', marginLeft: '-0.313rem' }}>{item}</Typography>
                    </li>
                  ))}
                </ul>
              )}
            </Box>
          )}
        >
          <img
            src={InfoIcon}
            alt="infoIcon"
            style={{ width: '0.6rem', cursor: 'pointer' }}
          />
        </CustomTooltip>
      )}
    </Box>
  );
}
export default RiskToolTip;
