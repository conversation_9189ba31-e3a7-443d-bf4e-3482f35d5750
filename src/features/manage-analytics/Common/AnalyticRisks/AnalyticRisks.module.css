.sideMenuBox {
  width: 15.813rem;
  height: fit-content;
  padding: 0.125rem;
  border-radius: 0.375rem;
  padding: 1rem;
}

.sideMenuHeading {
  font-weight: 600;
  font-size: 1.05rem;
}

.sideMenuDivider {
  width: 14.438rem;
}

.sideMenuRiskItems {
  padding: 0;
  cursor: pointer;
}

.sideMenuResponse {
  padding-top: 0rem;
  margin: 0.5rem 0rem;
}

.sideMenuDates {
  width: 12.25rem;
  margin-bottom: 0.625rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;

  &ul {
    &li {
      font-size: 0.75rem !important;
    }
  }
}

.sideMenuDates .MuiSelect-select {
  padding: 0.25rem !important;
}

.questionBox {
  flex: 1;
  padding: 0.125rem;
  width: 58.063rem;
  border-radius: 0.375rem;
  height: fit-content;
}

.benchmarkBox {
  display: flex;
  align-items: center;
  border-radius: 0.25rem;
  padding: 0rem 0.5rem;
  width: fit-content;
  height: 2rem;
  margin-right: 15px
}

.questionsBox {
  display: flex;
  align-items: center;
  border-radius: 0.25rem;
  padding: 0rem 0.5rem;
  width: fit-content;
  height: 2rem;
  margin-right: 1rem;
}

.questionList {
  border-bottom: 0.063rem solid #ddd;
  padding-top: 0.125rem;
}

.questionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f2f0f6;
  height: 3rem;
}

.questionHeading {
  font-size: 1.05rem;
  font-weight: 700;
  margin-left: 1rem;
  width: 51%;
}

.questionHeaderRow {
  display: grid;
  grid-template-columns: 2fr 1.8fr 0.85fr 0.65fr;
  align-items: end;
  justify-content: space-between;
  padding: 0.313rem 0rem 0rem;
  border-radius: 0.375rem;

  p {
    font-size: 0.85rem;
    font-weight: 500;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 0.3rem;
  }

  > :not(:first-child) p {
    padding-left: 1rem;
  }
}

.questionListItem {
  display: grid;
  grid-template-columns: 2fr 1.8fr 0.85fr 0.6fr;
  padding: 0.313rem 0rem;
  div {
    line-height: 1.25 !important;
  }
}

.riskSortDropdown {
  max-width: 4.6rem;
  text-align: left;
  font-size: 0.875rem;
  border-bottom-right-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
}

.benchmarkDropdown {
  max-width: 10rem;
  text-align: left;
  font-size: 0.875rem;
  border-bottom-right-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
}

.rightSection {
  display: flex;
  gap: 0.41rem; /* space between dropdowns */
  align-items: center;
}

.loadSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 60%;
  top: 50%;
  z-index: 9;
}

.whereProblemiFrame {
  width: 39.125rem;
  height: 35.625rem;
  padding: 0.125rem;
  box-sizing: border-box;
}

.benchmarkTooltip {
  font-size: 0.75rem;
  padding-top: 0.313rem;
  line-height: 1.25;
}

.comingsoonUl {
  margin: 0;
  padding-left: 0.9rem;
  font-size: 0.75rem;
  line-height: 1.25;
}

.tooltipDesc {
  display: block;
  font-size: 0.75rem !important;
  padding: 0.313rem 0rem 0.625rem 0rem !important;
  line-height: 1.25;
}

.sortByLabel {
  font-size: 0.875rem;
  font-weight: 600;
}

.learnerRes {
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.125rem;
  line-height: 1.9;
}

.benchmark {
  font-size: 0.835rem;
  font-weight: 500;
  padding: 0rem 1rem;
}

.problemLink {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.125rem;
}

.riskStatus {
  font-size: 0.835rem;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding: 0rem 1rem;
  img {
    width: 2.75rem;
    height: 2.75rem;
  }
}

.courseNotDeployed {
  text-decoration: none;
  font-weight: 600;
}

.questionGroup {
  font-size: 0.875rem;
  font-weight: 500;
}

.catalogBox {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
}

.catalogTitleRow {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.3rem;
  line-height: 1.4;
}

.faCalendarIcon {
  width: 0.938rem;
  height: 1.125rem;
  margin-top: 0.2rem;
}

.faLayerGroupIcon {
  width: 1.125rem;
  height: 1.125rem;
  margin-top: 0.3rem;
}

.faUsersIcon {
  width: 1.25rem;
  height: 1rem;
}

.SliderBox {
  display: flex;
  align-items: center;
  margin: 0rem 0.875rem 0rem 1.75rem;
}

.monthsBox {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
}

.monthsBoxInner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.responseRateBox {
  font-size: 0.9rem;
  margin-top: 0.75rem;
}

.responseRateBoxInner {
  display: flex;
  align-items: center;
  gap: 0.5rem ;
}

.catalogList {
  p {
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

.contactLink a, a:link, a:visited, a:hover, a:focus, a:active {
  color: inherit !important;
  text-decoration: none !important;
}

.hireTooltip {
  display: flex;
  align-items: center;
  padding-left: 3px;
}
