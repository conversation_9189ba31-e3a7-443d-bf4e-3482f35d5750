import React from 'react';
import styled from 'styled-components';
import { Card, CardContent, Typography, <PERSON>ton, Stack, Box, Divider, useTheme, Link } from '@mui/material';
import { useHistory } from 'react-router-dom';
import ArrowForwardIcon from '@mui/icons-material/ArrowForwardIos';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShareFromSquare, faDownload } from '@fortawesome/free-solid-svg-icons';
import CultureIcon from '../../../../images/culture-skill-scores.png';
import QuestionScoresIcon from '../../../../images/question-scores.png';
import ScoresBySegmentIcon from '../../../../images/scores-by-segment.png';
import BulbIcon from '../../../../images/lightbulb.png';
import ScreenShareIcon from '../../../../images/screenshare.png';
import TableauReportContainer from '../TableauReportContainer';
import styles from './CultureSkills.module.css';
import { pushToExploreAnalyticsData } from '../../../navigation/Routes/AppRoutes';
import { colors } from '../../../../theme/colors';
import { useUser } from '../../../../hooks/useUser';
import { getJettSSOToken } from '../../../../services/api/authentication';
import { aiAppURL } from '../../../../config/constants';
import { useAuth } from '../../../../hooks/useAuth';

export const StyledActivityStatsContainer = styled.div`
  background: rgb(242, 240, 246);
  margin: 20px 0 20px 0;
  border-radius: 6px;
  height: 720px;
  width: 1208px;
  colo
`;

export const StyledActivityStats = styled.div`
  display: grid;
  align-items: stretch;
  justify-content: center;
  grid-template-columns: 860px 350px;
`;

const dataItems = [
  {
    title: 'Culture Skill Scores',
    description: 'Gain insight into your team’s strengths and gaps.',
    icon: CultureIcon,
    path: 'skills/skillScores',
  },
  {
    title: 'Question Scores',
    description: 'See how many employees gave healthy responses.',
    icon: QuestionScoresIcon,
    path: 'skills/questionScores',
  },
  {
    title: 'Scores by Segment',
    description: 'Identify which groups need additional support.',
    icon: ScoresBySegmentIcon,
    path: 'segmentation/bySkill',
  },
];

function Summary({ analyticsIdHash }) {
  const history = useHistory();
  const { palette } = useTheme();
  const user = useUser();
  const { authedNavigateToAI } = useAuth();

  const navigateToExploreData = (path) => {
    history.push(pushToExploreAnalyticsData(path));
  };

  const navigateToAISummaryDownload = async () => {
    const token = await getJettSSOToken();
    const aiURL = aiAppURL();
    const link = document.createElement('a');
    document.body.appendChild(link);
    const forwardToParam = 'forwardTo=manage/analytics/summary_download';
    // eslint-disable-next-line max-len
    const identityParam = user.email ? `&email=${encodeURIComponent(user.email)}` : `&employeeId=${encodeURIComponent(user.employeeId)}`;
    link.href =
        `${aiURL}/jett-sso/${encodeURIComponent(token)}/?${forwardToParam}&${identityParam}`;
    link.target = '_blank';
    link.rel = 'opener';
    link.setAttribute('type', 'hidden');
    link.click();
    return false;
  };

  const navigateToAI = async (e, forwardTo) => {
    e.preventDefault();
    authedNavigateToAI(user, forwardTo);
  };

  const ExploreDataCard = ({ title, description, icon, path }) => (
    <div>
      <Button
        className={styles.exploreDataButton}
        sx={{
          color: palette.primary.white,
          backgroundColor: palette.primary.main,
          '&:hover': {
            color: palette.primary.white,
            backgroundColor: palette.button.login.hoverBackgroundColor,
          },
        }}
        onClick={() => navigateToExploreData(path)}
      >
        <Box
          component="img"
          src={icon}
          alt={title}
          sx={{
            width: 45,
            marginLeft: '-0.5rem',
            height: 45,
            borderRadius: '50%',
            zIndex: 1,
          }}
        />
        <Typography sx={{ flexGrow: 1, textAlign: 'start' }}>
          {title}
        </Typography>
        <ArrowForwardIcon className={styles.buttonArrow} />
      </Button>
      <Stack style={{ marginTop: '0.5rem' }}>
        <Typography variant="body2" sx={{ paddingLeft: '0.13rem' }}>
          {description}
        </Typography>
      </Stack>
    </div>
  );

  return (
    <StyledActivityStatsContainer>
      <StyledActivityStats>
        <div style={{ margin: '1rem 0.5rem 0rem 1rem' }}>
          <TableauReportContainer
            view="consolidatedOverviewPage/overview"
            analyticsIdHash={analyticsIdHash}
          />
        </div>
        <Stack spacing={2} className={styles.stack}>
          <Card
            sx={{ backgroundColor: colors.lavenderBlue, color: palette.primary.main }}
            className={styles.card}
          >
            <CardContent sx={{ padding: '0.625rem 1rem !important' }}>
              <Typography variant="h6">
                Explore the Data
              </Typography>
              <Stack spacing={2}>
                {dataItems.map((item, index) => (
                  <div key={`card-${item.title}`}>
                    <ExploreDataCard key={`card-${item.title}`} {...item} />
                    {index < 2 ? <Divider style={{ marginTop: '0.55rem' }} /> : null}
                  </div>
                ))}
              </Stack>
            </CardContent>
          </Card>

          <Card
            sx={{ backgroundColor: colors.snowPink, color: palette.primary.main }}
            className={styles.shareOrUseCard}
          >
            <CardContent>
              <Box className={styles.boxContent}>
                <Box className={styles.content}>
                  <Typography variant="h6">Share the Data</Typography>
                  <Typography variant="body2">
                    Download the Executive Summary of your organization’s culture skills and gap areas.
                  </Typography>
                </Box>
                <img
                  src={ScreenShareIcon}
                  alt="ScreenShareIcon"
                  width={55}
                  height={45}
                />
              </Box>
              <Button
                onClick={() => navigateToAISummaryDownload()}
                disableRipple
                className={styles.shareOrDownloadButton}
                endIcon={<FontAwesomeIcon icon={faDownload} style={{ width: '0.875rem' }} />}
                sx={{ color: colors.cyanBlue }}
              >
                Download
              </Button>
            </CardContent>
          </Card>

          <Card
            sx={{ backgroundColor: colors.floralWhite, color: palette.primary.main }}
            className={styles.shareOrUseCard}
          >
            <CardContent>
              <Box className={styles.boxContent}>
                <Box className={styles.content}>
                  <Typography variant="h6">Use the Data</Typography>
                  <Typography variant="body2">
                    How can I use this data to drive cultural change in my organization?
                  </Typography>
                </Box>
                <img
                  src={BulbIcon}
                  alt="bulb"
                  width={55}
                />
              </Box>
              <Link
                target="_blank"
                href="https://answers-support.emtrain.com/hc/en-us/articles/9142609027469-How-to-Be-a-Game-Changer"
              >
                <Button
                  disableRipple
                  className={styles.shareOrDownloadButton}
                  endIcon={<FontAwesomeIcon icon={faShareFromSquare} style={{ width: '0.875rem' }} />}
                  sx={{ color: colors.cyanBlue, marginTop: '1rem' }}
                >
                  Learn how
                </Button>
              </Link>
            </CardContent>
          </Card>
        </Stack>
      </StyledActivityStats>
      {/* <NavLink to="/manage/new-content-library"> */}
      <Button
        disableRipple
        className={styles.deployButton}
        sx={{ color: colors.cyanBlue }}
        onClick={(e) => navigateToAI(e, 'manage/content-library')}
      >
        Deploy content to establish a stronger baseline
      </Button>
      {/* </NavLink> */}
    </StyledActivityStatsContainer>
  );
}
export default Summary;
