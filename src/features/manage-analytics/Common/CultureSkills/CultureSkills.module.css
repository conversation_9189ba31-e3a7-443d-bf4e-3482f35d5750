.buttonArrow {
  width: 0.8rem;
  height: 0.8rem;
}

.stack {
  max-width: 400;
  margin: 1rem 1rem 0 0;
}

.boxContent {
  display: grid;
  grid-template-columns: 1fr 0.4fr;
  justify-items: center;
}

.card {
  border-radius: 8px;
  border: 3px solid white;
  h6 {
    margin-bottom: 0.675rem;
    font-weight: 650;
  }
}

.shareOrUseCard {
  border-radius: 8px;
  border: 3px solid white;
  height: 9.25rem;
  margin-top: 20px !important;
}

.content {
  h6 {
    font-weight: 650;
  }
  p {
    padding-top: 0.5rem;
  }
}

.exploreDataButton {
  display: flex;
  align-items: center;
  border-radius: 1.875rem;
  width: 100%;
  height: 2.1rem;
  padding-right: 0.875rem;
  gap: 1rem;
  text-transform: none;
  p {
    font-size: 0.875rem;
    font-weight: 600;
  }
}

.shareOrDownloadButton {
  text-transform: none !important;
  font-family: Source Sans Pro, sans-serif !important;
  font-weight: 600 !important;
  background: transparent;
  padding: 0;
  float: right;
  align-items: stretch;
  &.active {
    cursor: pointer !important;
  }
  &:hover {
    background: transparent !important;
  }
  &.focus {
    background: transparent !important;
  }
}

.deployButton {
  text-transform: none !important;
  font-family: Source Sans Pro, sans-serif !important;
  font-weight: 600 !important;
  background: transparent;
  padding: 1rem 1.25rem;
  &.active {
    cursor: pointer !important;
  }
  &:hover {
    background: transparent !important;
  }
  &.focus {
    background: transparent !important;
  }
}