import React from 'react';
import styled from 'styled-components';

import TableauReportContainer from '../TableauReportContainer';

export const StyledVizContainer = styled.div`
  width: 1220px;
  height: 650px;
  margin-top: 1.25rem;
`;

function QuestionScores({ analyticsIdHash }) {
  return (
    <StyledVizContainer>
      <TableauReportContainer
        view="questions/Questions"
        analyticsIdHash={analyticsIdHash}
      />
    </StyledVizContainer>
  );
}
export default QuestionScores;
