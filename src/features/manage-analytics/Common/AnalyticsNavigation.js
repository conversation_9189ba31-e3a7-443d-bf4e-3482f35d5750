import React, { useRef, useState } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { Box, Button, Menu, MenuItem } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import styles from './AnalyticsNavigation.module.css';
import { pushToAnalyticsSubMenu } from '../../navigation/Routes/AppRoutes';

const AnalyticsNavigation = ({ pages, page, permissionDialogueDisabled }) => {
  const history = useHistory();
  const { params: routeParams } = useRouteMatch();
  const subPath = routeParams?.analyticspage;
  let menuDelayInterval = null;
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedSubTab, setSelectedSubTab] = useState('');
  const buttonRef = useRef(null);
  const menuListRef = useRef(null);

  const displayTabIndex = permissionDialogueDisabled ? 1500 : 1000;

  const navigateToPage = (key, subMenu) => {
    const menu = Object.keys(pages).find((item) => key === item);
    history.push(pushToAnalyticsSubMenu(menu, subMenu));
    setAnchorEl(null);
  };
  const clearDelay = () => {
    if (menuDelayInterval) {
      clearTimeout(menuDelayInterval);
      menuDelayInterval = null;
    }
  };

  const handleMouseLeave = () => {
    clearDelay();
    setAnchorEl(null);
    setSelectedSubTab('');
  };

  const handlePointerLeave = (event) => {
    if (!menuListRef.current || !buttonRef.current) return;

    const menuRect = menuListRef.current.getBoundingClientRect();
    const buttonRect = buttonRef.current.getBoundingClientRect();
    const { clientX: pointerX, clientY: pointerY } = event;

    const topBoundary = Math.min(menuRect.top, buttonRect.top);
    const bottomBoundary = Math.max(menuRect.bottom, buttonRect.bottom);
    const leftBoundary = Math.min(menuRect.left, buttonRect.left);
    const rightBoundary = Math.min(menuRect.right, buttonRect.right);
    if (
      menuRect.right === pointerX ||
      topBoundary > pointerY ||
      pointerY > bottomBoundary ||
      pointerX < leftBoundary ||
      pointerX >= rightBoundary
    ) {
      handleMouseLeave();
    }
  };

  const handleTabLeave = (key) => {
    clearDelay();
    if (key !== selectedSubTab && Boolean(anchorEl)) {
      handleMouseLeave();
    }
  };

  const handleMouseEnter = (event, key) => {
    clearDelay();
    const eventData = event.currentTarget;
    menuDelayInterval = setTimeout(() => {
      setAnchorEl(eventData);
      setSelectedSubTab(key);
    }, 10);
  };

  const handleOnWheele = (event) => {
    if (event.deltaY > 0) {
      handleMouseLeave();
    }
  };

  return (
    <div className={styles.container}>
      {Object.entries(pages).map(([key, item]) => (
        <Box key={key} zIndex={displayTabIndex}>
          <Button
            disableRipple
            ref={buttonRef}
            sx={{ '& .MuiButton-endIcon': { marginLeft: '1px !important' } }}
            className={`${styles.button} ${page === key ? styles.active : ''}`}
            onClick={(event) => (item?.submenu ? handleMouseEnter(event, key) : navigateToPage(key))}
            onPointerLeave={(event) => handlePointerLeave(event)}
            onWheel={(event) => handleOnWheele(event)}
            onMouseEnter={(event) => (item?.submenu ? handleMouseEnter(event, key) : handleTabLeave(key))}
            endIcon={item?.submenu && <KeyboardArrowDownIcon />}
          >
            {pages[key].title}
          </Button>
          {item.submenu && Boolean(anchorEl) && key === selectedSubTab && (
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMouseLeave}
              MenuListProps={{
                ref: menuListRef,
                onMouseEnter: () => clearDelay(),
                onPointerLeave: (event) => handlePointerLeave(event),
              }}
            >
              {item.submenu.map(({ title, path }) => (
                <MenuItem
                  key={title}
                  disableRipple
                  className={`${styles.menu} ${key === page && subPath === path ? styles.active : ''}`}
                  onClick={() => navigateToPage(key, path)}
                >
                  {title}
                </MenuItem>
              ))}
            </Menu>
          )}
        </Box>
      ))}
      {/* <div className={styles.downloadButton}>
        [download btns]
      </div> */}
    </div>
  );
};

export default AnalyticsNavigation;
