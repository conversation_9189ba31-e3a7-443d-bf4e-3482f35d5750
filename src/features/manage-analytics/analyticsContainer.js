import React, { useState, useEffect } from 'react';
import { useRouteMatch, useLocation } from 'react-router-dom';
import { getCurrentAccount } from '../../services/api/currentAccount';
import { useUser } from '../../hooks/useUser';
import AnalyticsLayout from './Common/AnalyticsLayout';
import styles from './AnalyticsContainer.module.css';
import { colors } from '../../theme/colors';

function AnalyticsContainer() {
  const { params: routeParams } = useRouteMatch();
  const location = useLocation();
  const { pathname } = location;
  const [currentAccount, setCurrentAccount] = useState(null);
  const user = useUser();
  const userAccount = user?.adminAccountId ? user?.accounts?.[1] : user?.accounts?.[0];
  const licenseLevel = userAccount?.licenseLevel;
  const hasCultureSkills = userAccount?.hasCultureSkills;
  const hasHrPeopleRisk = userAccount?.hasHrPeopleRisk;
  const hasBusinessComplianceRisk = userAccount?.hasBusinessComplianceRisk;
  const reportingAccess = userAccount?.reportingAccess;
  const analyticsRolesData = user?.analyticsRolesData;
  const subPath = routeParams?.analyticspage;
  const [previousUrl, setPreviousUrl] = useState(null);
  const pages = {
    overview: { title: 'Overview', component: 'Overview' },
    skills: { title: 'Culture Skills',
      component: 'Skills',
      submenu: [{ path: 'summary', title: 'Summary' }, { path: 'skillScores', title: 'Culture Skill Scores' },
        { path: 'questionScores', title: 'Question Scores' }, { path: 'recommendations', title: 'Recommendations' }] },
    risks: { title: 'HR & People Risks',
      component: 'Risks',
      submenu: [{ path: 'summary', title: 'Summary' }, { path: 'scores', title: 'Scores by Risk Area' },
        { path: 'recommendations', title: 'Recommendations' }] },
    compliance: { title: 'Business Compliance Risks',
      component: 'Compliance',
      submenu: [{ path: 'summary', title: 'Summary' }, { path: 'scores', title: 'Scores by Risk Area' },
        { path: 'recommendations', title: 'Recommendations' }] },
    segmentation: { title: 'Segmentation',
      component: 'Segmentation',
      submenu: [{ path: 'byQuestion', title: 'By Question' }, { path: 'bySkill', title: 'By Culture Skill' }] },
  };

  const page = Object.keys(pages).find((key) => key === routeParams.page) || 'overview';

  const updateAnalyticsTabUrl = () => {
    const subUrl = pathname.split('/manage/analytics/')[1] || 'overview';
    const analyticsTab = window.sessionStorage.getItem('analyticsTabUrl');

    setPreviousUrl(analyticsTab && analyticsTab !== subUrl ? analyticsTab : 'overview');
    window.sessionStorage.setItem('analyticsTabUrl', subUrl);
    return subUrl;
  };

  useEffect(() => {
    async function handleAccount() {
      sessionStorage.removeItem('whereIsProblem');
      const account = await getCurrentAccount();
      setCurrentAccount(account);
    }
    handleAccount();
    const subUrl = updateAnalyticsTabUrl();
    window.sessionStorage.setItem('previousUrl', subUrl);
  }, []);

  useEffect(() => {
    updateAnalyticsTabUrl();
  }, [page, subPath]);

  const analyticsIdHash = currentAccount?.analyticsIdHash && currentAccount.analyticsIdHash !== 'null'
    ? currentAccount.analyticsIdHash
    : null;

  return (
    <div className={styles.container} style={{ color: colors.mainPurple }}>
      <AnalyticsLayout
        previousUrl={previousUrl}
        page={page}
        pages={pages}
        subPath={subPath}
        analyticsIdHash={analyticsIdHash}
        licenseLevel={licenseLevel}
        hasCultureSkills={hasCultureSkills}
        hasHrPeopleRisk={hasHrPeopleRisk}
        hasBusinessComplianceRisk={hasBusinessComplianceRisk}
        reportingAccess={reportingAccess}
        analyticsRolesData={analyticsRolesData}
      />
    </div>
  );
}
export default AnalyticsContainer;
