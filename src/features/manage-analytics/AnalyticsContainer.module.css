.container {
  font-family: Source Sans Pro, sans-serif;
  width: 1208px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  font-size: 1rem;
  padding: 0 0 0 1.3rem;
  margin-top: -0.4rem;
}

.description {
  font-family: Source Sans Pro, sans-serif;
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  padding: 0;
  margin: 0;
  /* border: 1px solid blue; */
}

.loadSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
}

.cardContent {
  padding: 1rem 1.9rem 1.563rem 1.9rem !important;
}

.cardStyle {
  height: 25.875rem;
  width: 24.125rem;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  text-align: left;
  overflow: hidden;
}

.boxHeader {
  display: grid;
  grid-template-columns: 2.3fr 1fr;
  align-items: center;
  justify-items: start;
  position: relative;
  height: 4.063rem;
  margin: 0.1rem;
  border-radius: 0.275rem;
}

.headerImage {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 80%;
  right: 5%;
  transform: translateY(-50%);
  border-radius: 50%;
}

.cardDescription {
  margin-bottom: 0.75rem;
  padding-right: 1rem;
  height: 4.688rem;
  width: 16.25rem;
  font-size: 0.95rem;
}

.detailsSection {
  display: grid;
  align-items: center;
  border-radius: 1rem;
  padding: 0rem 1rem;
  grid-template-columns: 0.1fr 1fr;
  margin-bottom: 1.65rem;
  height: 4rem;
  width: 20rem;
  span {
    font-size: 0.95rem;
  }
}

.exploreButton {
  padding: 0.313rem 1.6rem;
  text-transform: none;
  font-size: 0.9rem;
  font-weight: 700;
  border-radius: 5.125rem !important;
  gap: 0.3rem;
  box-shadow: none !important;
}

.buttonArrow {
  width: 0.8rem;
  height: 0.8rem;
}

.closeButtonImage {
  height: 0.9rem;
  width: 0.9rem;
}

.closeModal {
  position: absolute !important;
  right: 1rem;
  top: 1rem;
  cursor: pointer;
  z-index: 10;
}