import React, { useEffect } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { useMutation } from '@apollo/client';
import { usePreviewMode } from '../../hooks/usePreviewMode';
import CreatePreviewAssignmentMutation from './CreatePreviewAssignmentMutation.graphql';
import { pushToHome } from '../navigation/Routes/AppRoutes';
import { setIsPreviewModeEnable } from '../../services/api/authentication';

function Preview() {
  const { params: routeParams } = useRouteMatch();
  const history = useHistory();
  const { setPreviewMode, setPreviewAssignmentData } = usePreviewMode();

  const [createPreviewAssignment] = useMutation(CreatePreviewAssignmentMutation);

  useEffect(() => {
    async function PreviewLaunch() {
      const programId = routeParams.programId;
      const lessonId = routeParams.lessonId;
      const cardId = routeParams.cardId;
      if (programId) {
        await createPreviewAssignment({ variables: { programId } });
      } else if (lessonId) {
        const variables = cardId ? { lessonId, cardId } : { lessonId };
        await createPreviewAssignment({ variables });
      }
      setPreviewMode(true);
      setIsPreviewModeEnable(JSON.stringify({ programId, lessonId, cardId }));
      setPreviewAssignmentData({ programId, lessonId, cardId });
      history.push(pushToHome());
    }
    PreviewLaunch();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div />
  );
}

export default Preview;
