/* eslint-disable prefer-const */
import React, { useState } from 'react';
import { TextField, TextareaAutosize, InputLabel, useTheme, Icon, InputAdornment } from '@mui/material';

import { Visibility, VisibilityOff } from '@mui/icons-material';
import HiddenAlert from '../hidden-alert/HiddenAlert';

export default function FormikTextField(props) {
  let { formik, value, required, name, autoComplete, id, autoFocus, type, className,
    labelText, labelClassName, labelStyle, InputProps, size, fontSize, style,
    minRows, maxRows, ariaDescribedby, customError, key } = props;

  const { palette } = useTheme();
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword((prev) => !prev);
  };
  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  if (!formik || !name || !labelText) {
    if (props?.field) {
      name = props?.field?.name;
      value = props?.field?.value;
      if (!formik || !name || !labelText) {
        console.error('FormikTextField: missing required props'); // eslint-disable-line no-console
        return null;
      }
    } else {
      console.error('FormikTextField: missing required props'); // eslint-disable-line no-console
      return null;
    }
  }

  const onChange = formik.handleChange;
  const onBlur = formik.handleBlur;
  const error = formik.touched[name] && Boolean(formik.errors[name]);
  const helperText = customError || (formik.touched[name] && formik.errors[name]);

  let modType = type || 'text';
  if (!modType && name.toLowerCase().includes('password')) {
    modType = 'password';
  }

  const modInputProps = InputProps || {};
  modInputProps['data-cy'] = modInputProps['data-cy'] || name;
  modInputProps.style = modInputProps.style ||
    { color: palette.primary.darkLightPurple, borderColor: palette.border.greyShade };
  modInputProps['aria-describedby'] = ariaDescribedby
    ? `${ariaDescribedby} ${name}-feedback`
    : `${name}-feedback`;
  const modSize = size || 'small';
  const modFontSize = `'${fontSize} !important'` || '1rem !important';
  const sx = { '& .MuiOutlinedInput-root': { fontSize: modFontSize, borderRadius: '8px' },
    '&. MuiInputBase-input': { fontSize: modFontSize, background: palette.background.lightLightBlue } };
  return (
    <>
      <InputLabel
        className={labelClassName}
        style={labelStyle || { color: palette.primary.darkLightPurple }}
        htmlFor={name}
        id={`${name}-label`}
        key={key ? `${key}-label` : `${name}-label`}
      >
        {labelText}
      </InputLabel>
      {['text', 'password'].includes(modType) && (
        <TextField
          name={name}
          value={value}
          key={key || name}
          {...(autoComplete && { autoComplete })}
          {...(id && { id })}
          {...(autoFocus && { autoFocus })}
          {...(modSize && { size: modSize })}
          {...(modType && { type: modType })}
          {...(required && { required: true })}
          {...(onChange && { onChange })}
          {...(onBlur && { onBlur })}
          {...(error && { error })}
          {...(className && { className })}
          // eslint-disable-next-line no-nested-ternary
          type={(name.toLowerCase().includes('password') ||
          name.toLowerCase().includes('confirmnew')) ? (showPassword ? 'text' : 'password') : modType}
          {...(modInputProps && { inputProps: modInputProps })}
          sx={sx}
          InputProps={{
            ...modInputProps,
            endAdornment: (value && (name.toLowerCase().includes('password')
            || name.toLowerCase().includes('confirmnew'))) && (
              <InputAdornment position="end">
                <Icon
                  sx={{ '& .MuiSvgIcon-root': { cursor: 'pointer', padding: '0.125rem' } }}
                  aria-label="toggle password visibility"
                  onClick={handleClickShowPassword}
                  onMouseDown={handleMouseDownPassword}
                  edge="end"
                >
                  {showPassword ? <Visibility /> : <VisibilityOff />}
                </Icon>
              </InputAdornment>
            ),
          }}
        />
      )}
      {modType === 'textarea' && (
        <TextareaAutosize
          name={name}
          value={value}
          key={key || name}
          {...(id && { id })}
          {...(autoFocus && { autoFocus })}
          {...(modType && { type: modType })}
          {...(required && { required })}
          {...(className && { className })}
          {...(onChange && { onChange })}
          {...(onBlur && { onBlur })}
          {...(minRows && { minRows })}
          {...(maxRows && { maxRows })}
          iserror={formik.touched[name] && Boolean(formik.errors[name]) ? 'true' : 'false'}
          {...(style && { style })}
          {...(modInputProps['aria-describedby'] && { 'aria-describedby': modInputProps['aria-describedby'] })}
        />
      )}
      {/* live region feedback for screen readers */}
      {helperText && (
        <HiddenAlert key={`${name}-feedback`} text={helperText} id={`${name}-feedback`} className="srHelperText" />
      )}
    </>
  );
}
