query GetLessonCardsQuery($lessonId: ID!, $enrollmentId: ID!) {
  getLessonCards(lessonId: $lessonId, enrollmentId: $enrollmentId) {
    id
    title
    description
    type
    speechAudioURL
    fontColor
    lastViewedCard
    translationLanguage
    ... on TextImageCard {
      imageUrl
      imageAltText
      backgroundColor
      textOffset
    }
    ... on VideoCard {
      videoId
      isTranscriptAdded
    }
    ... on OrderedListCard {
      listItems
      answer {
        checkboxAnswer {
          answer1
          answer2
          answer3
          answer4
          answer5
        }
      }
    }
    ... on ClickExpandCard {
      items {
        text
        expandedText
        speechAudioURL
      }
    }
    ... on MapCard {
      mapKey
      mapTraits {
        id
        locationAbbreviation
        locationTraitText
        speechAudioURL
      }
    }
    ... on BooleanCard {
      booleanLabel {
        trueText
        falseText
      }
      answer {
        booleanAnswer
      }
    }
    ... on SliderCard {
      sliderMax
      spectrumText {
        minText
        maxText
      }
      labelText
      answer {
        sliderAnswer
      }
    }
    ... on SingleChoiceCard {
      gated
      choices {
        gatedChoice
        text
      }
      correctChoiceFeedback,
      wrongChoiceErrorMessage,
      gatedFeedbackSpeechAudioURL,
      answer {
        singleChoiceAnswer
      }
    }
    ... on MultipleChoiceCard {
      multipleChoices: choices
      answer {
        multipleChoiceAnswer {
          answer1
          answer2
          answer3
          answer4
          answer5
          answer6
          answer7
          answer8
          answer9
          answer10
        }
      }
    }
    ... on PolicyAcknowledgementCard {
      policyType
      url
      answer {
        policyAcknowledgementAnswer
      }
    }
    ... on WorkplaceColorSpectrumCard {
      regionAbbreviation
      correctAnswer
      answer {
        workplaceColorSpectrumAnswer
      }
    }
    ... on FreeformTextCard {
      answer {
        freeFormTextAnswer
      }
    }
    ... on EndOfLessonCard {
      eolImageUrl: imageUrl
      eolImageAltText: imageAltText
      restartPrompt
      nexLessonPrompt
    }
  }
}
