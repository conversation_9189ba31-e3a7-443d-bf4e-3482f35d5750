/* eslint-disable max-len */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import { get } from 'lodash';
import { useApolloClient, useMutation, useQuery, useLazyQuery, gql } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useRouteMatch } from 'react-router';
import { useHistory } from 'react-router-dom';
import { useTheme, Button } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useActiveAssignment } from '../../hooks/useActiveAssignment';
import { pushToCard } from '../navigation/Routes/AppRoutes';

import ArrowRightIcon from '../../images/desktop-right.svg';
import ArrowRightIconHover from '../../images/desktop-right-hover.svg';
import ArrowLeftIcon from '../../images/desktop-left.svg';
import ArrowLeftIconHover from '../../images/desktop-left-hover.svg';
import ArrowRightIconMobile from '../../images/mobile-right.svg';
import ArrowRightIconHoverMobile from '../../images/mobile-right-hover.svg';
import ArrowLeftIconMobile from '../../images/mobile-left.svg';
import ArrowLeftIconHoverMobile from '../../images/mobile-left-hover.svg';
import LessonCompleteGif from '../../images/lesson-complete.gif';

import ProgramTimer from '../assignment/Program/ProgramTimer';
import Footer from '../footer/Footer';
import LessonCard from '../../components/LessonCard/LessonCard';
import Tooltip from '../../components/Tooltip/Tooltip';
import LessonsContainer from './LessonsContainer';
import AskTopicExpertButton from './AskTopicExpertButton';
import TimerPausedDialog from '../../components/Modal/TimerPausedDialog';
import TimeRequirementDialog from '../../components/Modal/TimeRequirementDialog';
import Spinner from '../../components/Spinner/Spinner';
import GetLessonCardsQuery from './GetLessonCardsQuery.graphql';
import SendViewEventMutation from './SendViewEventMutation.graphql';
import SaveAnswerMutation from './LessonCards/SaveAnswerMutation.graphql';
import AssignmentFragment from '../assignment/AssignmentFragment.graphql';
import CheckAssignmentCompletionQuery from '../assignment/CheckAssignmentCompletionQuery.graphql';

import styles from './Lessons.module.css';
import { getLessonCardComponent, cardRequiresUserResponse, isQuizCardType } from './lessonCardUtils';
import { useInterval } from '../../hooks/useInterval';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import { useUser } from '../../hooks/useUser';
import { usePreviewMode } from '../../hooks/usePreviewMode';
import { useSpeechAudio } from '../../hooks/useSpeechAudio';
import { useViewedVideos } from '../../hooks/useViewedVideos';
import { useViewedClickExpand } from '../../hooks/useViewedClickExpand';
import { setPassed } from '../../services/api/scormRusticiAPI';
import { getIsPreviewModeEnable, setIsPreviewModeEnable } from '../../services/api/authentication';

const VIEW_EVENT_INTERVAL_SECONDS = 1;
const VIEW_CONTINUE_INTERVAL_TICK_LIMIT = 30;
const START_TRANSITION_TIME_SECONDS = 1.5;
const VIEW_EVENT_TIMEOUT_COUNT = 10;

// These states are to control the order of events when a new lesson loads.
// 1. Fetch the lesson cards.
// 2. Go to the bookmarked lesson.
// 3. Send a view event for the lesson card.
const INITIAL_STATE = 0;
const BOOKMARKING_STATE = 1;
const BOOKMARKED_STATE = 2;

function Lessons({ onNextAssignment, checkMoreAssignmentsAvailable,
  showCompletionCertificate, setShowCompletionCertificate,
  removeCompletedTodoItems, timerPaused, setTimerPaused,
  loadedRouteParams, setUrlLoadedCardDone, urlLoadedCardDone }) {
  const client = useApolloClient();
  const history = useHistory();
  const { pauseCardAudio, pausePopupAudio } = useSpeechAudio();
  const { viewedVideos, viewedFullVideos, addViewedVideo } = useViewedVideos();
  const { viewedClickExpand, updateClickExpandCardTooltip, clickExpandBox, showClickExpandTooltip } = useViewedClickExpand();

  const { t } = useTranslation();
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [rightButtonHovered, setRightButtonHovered] = useState(false);
  const [leftButtonHovered, setLeftButtonHovered] = useState(false);
  const [lessonInCache, setLessonInCache] = useState(false);
  // This state manages the intra-program lesson transition states.
  // The possible states are ['inactive', 'active', 'visible', 'fadeout'];
  const [lessonTransitionState, setLessonTransitionState] = useState('inactive');
  const [showLiveUpdatesNotice, setShowLiveUpdatesNotice] = useState(false);
  const [viewContinueTickCount, setViewContinueTickCount] = useState(0);
  const [viewContinueCount, setViewContinueCount] = useState(0);
  // We need to save the active lesson state in a ref so that it will
  // be available when the component unmounts. This is for parial viewContinue events.
  const savedViewEventInputRef = useRef(null);
  const savedPausedRef = useRef(null);

  // The card transition state is used to reset the viewContinue interval
  const [cardTransitionState, setCardTransitionState] = useState(false);
  const [programTimerIsOpen, setProgramTimerIsOpen] = useState(false);
  const [urlLoadedCardFound, setUrlLoadedCardFound] = useState(false);

  const [lastViewEventCardId, setLastViewEventCardId] = useState(-1);
  const [eventLoading, setEventLoading] = useState(false);

  // This state is used to ensure that there are no race conditions
  // when a new lesson loads and is forwarded to the bookmarked card
  // before a view event is recorded for the first card in the lesson.
  const [bookmarkState, setBookmarkState] = useState(INITIAL_STATE);
  const [scormCompletionSent, setScormCompletionSent] = useState({ assignmentId: null, sent: false });
  const [backNavigation, setBackNavigation] = useState(false);

  const [showVideoTooltip, setShowVideoTooltip] = useState(false);

  const [showLanguageWarningLabel, setShowLanguageWarningLabel] = useState(true);

  const { params: { lessonId, programId, assignmentId, cardId } } = useRouteMatch();
  const [sendViewEvent] = useMutation(SendViewEventMutation);
  const { palette } = useTheme();
  const user = useUser();
  const askExpertEnabled = get(user, 'accounts[0].enableAskExpert', false);
  const isContentReviewer = get(user, 'accounts[0].accountUsers.roleId') === 7;
  const { activeAssignmentInfo, setActiveAssignmentInfoFromCacheItem } = useActiveAssignment();
  const { previewMode, previewAssignmentData, setPreviewAssignmentData } = usePreviewMode();

  const accountId = user?.accounts?.[0]?.id;
  const isGo1Account = [1248, 1488].includes(accountId);
  const isEnforceFullVideoView = get(user, 'accounts[0].enforceFullVideoView', true);

  const [timeRequirementModalOpen, setTimeRequirementOpen] = useState(true);
  const assignmentStatus = activeAssignmentInfo?.status;
  const lessonEnrollmentId = getLessonEnrollmentId();
  const enforceScormSequence = !!((user?.scorm && user?.enforceSequence));

  // There is a bug in apollo graphql that is fixed in version 3.5.9.
  // https://github.com/apollographql/apollo-client/issues/6190
  // This bug is causing this query to be run, even if skip is set to true.
  // This produces an error in the console. It's less risky to just live with this
  // bug for now and upgrade apollo to the latest version after CEA.
  const { loading, error, data, refetch } = useQuery(GetLessonCardsQuery, {
    variables: { lessonId, enrollmentId: lessonEnrollmentId },
    skip: !lessonEnrollmentId,
  });

  // Add a useEffect on the query variables to ensure that this query
  // is refetched every time the assignmentId, lessonId or enrollmentId change
  useEffect(() => {
    async function initLessonCards() {
      setCurrentCardIndex(0);
      await refetch();
      setBookmarkState(BOOKMARKING_STATE);
    }
    if (assignmentId && lessonId && lessonEnrollmentId) {
      initLessonCards();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignmentId, lessonId, lessonEnrollmentId]);

  useEffect(() => {
    async function initLessonCardsOnLanguage() {
      setCurrentCardIndex(currentCardIndex);
      await refetch();
    }
    initLessonCardsOnLanguage();
    setShowLanguageWarningLabel(true);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user.language]);

  useMemo(() => {
    setTimeRequirementOpen(client.readFragment({
      id: `AssignmentItem:${assignmentId}`,
      fragment: gql`fragment acked on AssignmentItem { acked }`,
    })?.acked !== true);
  }, [assignmentId]);

  const { isMobile, isSmallMobile, getDevice } = useResponsiveMode();
  // eslint-disable-next-line no-nested-ternary
  const navButtonTopOffset = isSmallMobile ? '11rem' : isMobile ? '15rem' : '18rem';

  const device = getDevice();
  let askExpertButtonWidth = '100%';
  let AEButtonSpacerWidth = '0';
  if (device === 'smallMobile') {
    askExpertButtonWidth = '97vw';
  }
  if (device === 'mobile') {
    AEButtonSpacerWidth = '4rem';
  }
  if (device === 'tablet') {
    AEButtonSpacerWidth = '10%';
  }

  const lessonCards = get(data, 'getLessonCards', []);
  const currentCardProps = lessonCards[currentCardIndex];
  const lessonCardCount = get(data, 'getLessonCards.length', 0);
  const LessonCardComponent = useMemo(
    () => getLessonCardComponent(currentCardProps, showCompletionCertificate),
    [currentCardProps, showCompletionCertificate]);

  const isViewingLesson = !!lessonId && !!data && !loading;
  const cardAnswer = currentCardProps ? currentCardProps.answer : null;
  const isResultCard = !!cardAnswer;

  const [saveCardAnswer, { loading: saveAnswerLoading }] = useMutation(SaveAnswerMutation);

  const { programTitle, completedMessage, hasCertificate,
    downloadInstructions, title: lessonTitle, language: lessonLanguage,
    sourceLifecycle: lessonLifecycle, status: lessonStatus } = getCachedLesson();
  const isLastProgramLesson = checkIsLastProgramLesson();

  // Determine if next/previous buttons are shown
  const showPreviousButton = currentCardIndex !== 0
    && lessonTransitionState !== 'visible' && lessonTransitionState !== 'fadeout' && !showLiveUpdatesNotice && eventLoading;

  const disablePreviousButton = currentCardIndex !== 0
    && lessonTransitionState !== 'visible' && lessonTransitionState !== 'fadeout' && !showLiveUpdatesNotice && !eventLoading;

  const showNextButton = currentCardProps ?
    ((!cardRequiresUserResponse(currentCardProps.type) || !!get(currentCardProps, 'answer'))
    && (currentCardIndex !== lessonCardCount - 1)
    && !showCompletionCertificate
    && !showLiveUpdatesNotice
    && lessonTransitionState !== 'visible' && lessonTransitionState !== 'fadeout') && eventLoading :
    false;

  const disableNextButton = currentCardProps ?
    ((!cardRequiresUserResponse(currentCardProps.type) || !!get(currentCardProps, 'answer'))
    && (currentCardIndex !== lessonCardCount - 1)
    && !showCompletionCertificate
    && !showLiveUpdatesNotice
    && lessonTransitionState !== 'visible' && lessonTransitionState !== 'fadeout')
    && !eventLoading : false;

  const handleTimeRequirementModalClose = () => {
    client.writeFragment({
      id: `AssignmentItem:${assignmentId}`,
      fragment: gql`fragment acked on AssignmentItem { acked }`,
      data: {
        acked: true,
      },
    });
    setTimeRequirementOpen(false);
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);
  };

  const setAssignmentInProgress = (cache) => {
    const assignmentItem =
    client.readFragment({ id: `AssignmentItem:${assignmentId}`, fragment: AssignmentFragment });
    if (get(assignmentItem, 'type') === 'program') {
      const currentLesson = assignmentItem.content.lessons.find((lesson) => lesson.uid === lessonId);
      // if the program assignment has a status of open, or the current lesson has a status of open, we need to update the fragment
      if (get(assignmentItem, 'content.status') === 'open' || currentLesson.status === 'open') {
        cache.modify({
          id: `AssignmentItem:${assignmentId}`,
          fields: {
            content(existingContent) {
              const updatedLessons = existingContent.lessons.map(
                (lesson) => (lesson.id === lessonId ?
                  { ...lesson, status: 'inProgress' } : lesson));
              const newContent = { ...existingContent, status: 'inProgress', lessons: updatedLessons };
              return newContent;
            },
          },
        });
      }
    } else if (get(assignmentItem, 'content.status') === 'open') {
      cache.modify({
        id: `AssignmentItem:${assignmentId}`,
        fields: {
          content(existingContent) {
            return { ...existingContent, status: 'inProgress' };
          },
        },
      });
    }
  };

  let nextButtonExpandCard = true;
  const checkObjectKey = (obj, id, cardProps) => {
    // Check if the object is present
    if (!obj || typeof obj !== 'object') {
      return false;
    }
    const expandCardId = Number(id);
    if (!(expandCardId in obj)) {
      return false;
    }

    if (cardProps && cardProps.items) {
      if (obj[expandCardId] && obj[expandCardId].length === cardProps.items.length) {
        return true;
      }
      return false;
    }
    // Return the value of the key
    return obj[expandCardId];
  };

  // eslint-disable-next-line no-underscore-dangle
  if (isGo1Account && currentCardProps && currentCardProps.__typename === 'ClickExpandCard' && currentCardProps.id) {
    if (!checkObjectKey(viewedClickExpand, currentCardProps.id, currentCardProps)) {
      nextButtonExpandCard = false;
    }
  }

  useEffect(() => {
    if (showClickExpandTooltip) {
      updateClickExpandCardTooltip(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clickExpandBox]);

  let videoId;
  // eslint-disable-next-line no-underscore-dangle
  if (currentCardProps && currentCardProps.__typename === 'VideoCard' && currentCardProps.videoId) {
    videoId = currentCardProps.videoId;
  }

  useEffect(() => {
    const videoTimer = window.setTimeout(() => {
      if (videoId && (!viewedVideos || !viewedVideos.includes(videoId))) {
        addViewedVideo(videoId);
      }
    }, 30000);
    // clear the video timer if the component unmounts
    return () => window.clearTimeout(videoTimer);
  }, [videoId, viewedVideos, addViewedVideo]);

  useEffect(() => {
    // reset the tooltip state when the videoId changes
    setShowVideoTooltip(false);
  }, [videoId]);

  // Called when urlLoadedCardFound changes before we are finished with trying to find the loadedRouteParams.
  useEffect(() => {
    if (showClickExpandTooltip) {
      updateClickExpandCardTooltip(false);
    }
    if (urlLoadedCardDone) {
      return;
    }
    const previewModeEnable = getIsPreviewModeEnable();
    if (loadedRouteParams && previewModeEnable) {
      setIsPreviewModeEnable(JSON.stringify({ ...JSON.parse(previewModeEnable), assignmentId }));
    }
    if (!urlLoadedCardDone && !urlLoadedCardFound && loadedRouteParams) {
      const preloadedIndex = lessonCards.findIndex((card) => card.id === loadedRouteParams.cardId);
      if (preloadedIndex !== -1) {
        setCurrentCardIndex(preloadedIndex);
        setUrlLoadedCardFound(true);
        setUrlLoadedCardDone(true);
      } else if (lessonCards && lessonCards.length > 0 && preloadedIndex === -1) {
        setUrlLoadedCardFound(false);
        setUrlLoadedCardDone(true);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [urlLoadedCardFound, loadedRouteParams, lessonCards]);

  // Called when the assignmentId or the lessonId changes.
  useEffect(() => {
    removeCompletedTodoItems();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignmentId, lessonId]);

  useEffect(() => {
    if (loadedRouteParams && !urlLoadedCardFound && !urlLoadedCardDone) {
      return;
    }
    if (loadedRouteParams && urlLoadedCardFound && !urlLoadedCardDone) {
      const loadedCard = lessonCards[currentCardIndex];
      history.push(pushToCard(loadedCard.id));
      setUrlLoadedCardDone(true);
      setBookmarkState(BOOKMARKED_STATE);
      return;
    }
    if (!currentCardProps) {
      return;
    }
    if (bookmarkState === BOOKMARKED_STATE && cardId !== '_' && cardId !== currentCardProps.id) {
      history.push(pushToCard(currentCardProps.id));
      return;
    }
    if (bookmarkState !== BOOKMARKING_STATE || !lessonCards ||
       lessonCards.length === 0 || !currentCardProps) {
      return;
    }
    let bookmarkedIndex = 0;
    let bookmarkedCard;
    bookmarkedIndex = lessonCards.findIndex(({ lastViewedCard }) => lastViewedCard);
    const { status } = getCachedLesson();
    // Check if the index is greater than or equal to the length - 2 because of the end of lesson card.
    // We don't want to return to the last content card in the lesson.
    if (status === 'completed' || lessonCards.length <= 2 ||
      bookmarkedIndex === -1 || bookmarkedIndex === lessonCards.length - 1) {
      bookmarkedIndex = 0;
      if (cardId) {
        bookmarkedIndex = lessonCards.findIndex(({ id }) => id === cardId);
      }
    } else {
      const lastViewedCardIndex = lessonCards.findIndex(({ lastViewedCard }) => lastViewedCard === true);
      bookmarkedCard = lessonCards[lastViewedCardIndex];
      if (bookmarkedCard) {
        bookmarkedIndex = lastViewedCardIndex;
      } else {
        bookmarkedCard = lessonCards[bookmarkedIndex];
      }
    }
    if (!bookmarkedCard) {
      bookmarkedIndex = 0;
      bookmarkedCard = lessonCards[0];
    }

    const previewModeEnable = getIsPreviewModeEnable();
    const previewModeData = JSON.parse(previewModeEnable);
    if (previewAssignmentData && previewAssignmentData.cardId) {
      let loadCardId = previewAssignmentData.cardId;
      let previewBookmarkIndex = lessonCards.findIndex(({ id }) => id === previewAssignmentData.cardId);
      if (previewModeData && previewModeData.cardId) {
        previewBookmarkIndex = lessonCards.findIndex(({ id }) => id === previewModeData.cardId);
        loadCardId = previewModeData.cardId;
      }
      setCurrentCardIndex(previewBookmarkIndex);
      history.push(pushToCard(loadCardId));
      setBookmarkState(BOOKMARKED_STATE);
      setPreviewAssignmentData({ ...previewAssignmentData, cardId: null });
    } else {
      if (previewModeData && previewModeData.cardId) {
        bookmarkedIndex = lessonCards.findIndex(({ id }) => id === previewModeData.cardId);
      }
      setCurrentCardIndex(bookmarkedIndex);
      history.push(pushToCard(bookmarkedCard.id));
      setBookmarkState(BOOKMARKED_STATE);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookmarkState, cardId, lessonId, currentCardProps, lessonCards, urlLoadedCardDone, previewAssignmentData]);

  // this interval handles the intra program transition fade out
  useInterval(() => {
    if (lessonTransitionState === 'visible') {
      setLessonTransitionState('fadeout');
    }
  }, (lessonTransitionState === 'visible') ? START_TRANSITION_TIME_SECONDS * 1000 : null);

  // detect intra-program transition cards so that we don't go to the end of lesson card.
  useEffect(() => {
    if (!activeAssignmentInfo) {
      return;
    }
    const isQuizQuestionCard = currentCardProps ? isQuizCardType(currentCardProps.type) && !isResultCard : false;
    // Also need to put up a transition card if it's the last card but other cards were skipped.
    const isTransitionCard = (currentCardIndex === lessonCardCount - 2) && !isQuizQuestionCard &&
      !!programId && !isLastProgramLesson && lessonStatus === 'completed';
    if (isTransitionCard) {
      setLessonTransitionState('active');
    } else {
      setLessonTransitionState('inactive');
    }

    const liveUpdatesDetected = (currentCardIndex === lessonCardCount - 1) && !isQuizQuestionCard && lessonStatus !== 'completed';
    if (liveUpdatesDetected) {
      setShowLiveUpdatesNotice(true);
    } else {
      setShowLiveUpdatesNotice(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentCardIndex, isLastProgramLesson, programId, isResultCard, lessonCardCount, activeAssignmentInfo, lessonStatus]);

  // Call the rustici API when course completion is detected for scorm users.
  useEffect(() => {
    if (!user.scorm || (scormCompletionSent.assignmentId === assignmentId && scormCompletionSent.sent)) {
      return;
    }
    if (activeAssignmentInfo && scormCompletionSent.assignmentId === assignmentId && !scormCompletionSent.sent &&
      activeAssignmentInfo.programId && activeAssignmentInfo.percentComplete === 100 &&
      activeAssignmentInfo.programIsComplete && activeAssignmentInfo.timeRequirementMet) {
      setPassed();
      setScormCompletionSent({ ...scormCompletionSent, sent: true });
      return;
    }
    if (activeAssignmentInfo && scormCompletionSent.assignmentId === assignmentId && !scormCompletionSent.sent &&
      activeAssignmentInfo.lessonId && activeAssignmentInfo.percentComplete === 100) {
      setPassed();
      setScormCompletionSent({ ...scormCompletionSent, sent: true });
      return;
    }
    if (scormCompletionSent.assignmentId !== assignmentId) {
      setScormCompletionSent({ assignmentId, sent: false });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentCardIndex, scormCompletionSent, activeAssignmentInfo]);

  const [checkAssignmentCompletion, { refetch: refetched }] = useLazyQuery(CheckAssignmentCompletionQuery, { fetchPolicy: 'network-only',
    onCompleted: () => { setEventLoading(true); } });
  useEffect(() => {
    async function sendViewEventAsync() {
      if (savedViewEventInputRef.current && !savedPausedRef.current) {
        sendPartialViewContinueEvent(savedViewEventInputRef.current);
      }
      const viewEvent = createViewEventInput({ type: 'view', timeIncrement: VIEW_EVENT_INTERVAL_SECONDS });
      if (viewEvent && viewEvent.lessonId && viewEvent.lessonCardId && viewEvent.enrollmentId) {
        savedViewEventInputRef.current = { ...viewEvent, type: 'viewcontinue', timeIncrement: 0 };
        const eventData = await sendViewEvent({
          update: (cache) => {
            return setAssignmentInProgress(cache);
          },
          variables: { input: viewEvent } },
        );
        if (eventData && !eventLoading && !(currentCardIndex === lessonCardCount - 2)) {
          setEventLoading(true);
        }
        if ((eventData && lessonCardCount !== 0 && (currentCardIndex === lessonCardCount - 2 || currentCardIndex === lessonCardCount - 1)) ||
          (eventData && activeAssignmentInfo && activeAssignmentInfo.programId && activeAssignmentInfo.minTimeInMinutes)) {
          setEventLoading(false);
          checkAssignmentCompletion({
            variables: {
              assignmentUserLessonId: assignmentId,
              lessonId,
              enrollmentId: viewEvent.enrollmentId,
              scormProgramId: user.scorm ? user.scormProgramId : null,
              // eslint-disable-next-line no-nested-ternary
              assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : isContentReviewer ? programId : null,
              // eslint-disable-next-line no-nested-ternary
              assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : isContentReviewer ? lessonId : null,
              scormEnforceSequence: enforceScormSequence,
            },
          });
        }
        setTimerPaused(false);
        savedPausedRef.current = false;
        setViewContinueCount(1);
        setViewContinueTickCount(0);
        setCardTransitionState(true);
      }
    }
    if (bookmarkState === BOOKMARKED_STATE && cardId !== '_' && currentCardProps && lastViewEventCardId !== cardId) {
      setLastViewEventCardId(cardId);
      sendViewEventAsync();
      const assignmentItem =
        client.readFragment({ id: `AssignmentItem:${assignmentId}`, fragment: AssignmentFragment });
      if (assignmentItem) {
        setActiveAssignmentInfoFromCacheItem(assignmentItem);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lessonId, cardId, currentCardIndex, bookmarkState, currentCardProps, lessonInCache]);

  const resetCurrentLesson = async () => {
    setShowLiveUpdatesNotice(false);
    setLessonTransitionState('inactive');
    setCurrentCardIndex(0);
    await refetched();
    await refetch();
  };

  useInterval(async () => {
    if (viewContinueTickCount === VIEW_CONTINUE_INTERVAL_TICK_LIMIT) {
      setViewContinueTickCount(0);
      const viewContinueEvent =
        createViewEventInput({ type: 'viewcontinue', timeIncrement: VIEW_CONTINUE_INTERVAL_TICK_LIMIT });
      if (viewContinueEvent && viewContinueEvent.lessonId &&
        viewContinueEvent.lessonCardId && viewContinueEvent.enrollmentId) {
        const eventData = await sendViewEvent({
          update: (cache) => {
            return setAssignmentInProgress(cache);
          },
          variables: {
            input: viewContinueEvent,
          },
        });

        if (eventData && !eventLoading) {
          // setEventLoading(true);
          await refetched();
        }
        // Reset the time for the saved viewContinue event.
        savedViewEventInputRef.current = { ...savedViewEventInputRef.current, timeIncrement: 0 };
        if (activeAssignmentInfo && activeAssignmentInfo.programId && activeAssignmentInfo.minTimeInMinutes) {
          checkAssignmentCompletion({
            variables: {
              assignmentUserLessonId: assignmentId,
              lessonId,
              enrollmentId: viewContinueEvent.enrollmentId,
              scormProgramId: user.scorm ? user.scormProgramId : null,
              // eslint-disable-next-line no-nested-ternary
              assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : isContentReviewer ? programId : null,
              // eslint-disable-next-line no-nested-ternary
              assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : isContentReviewer ? lessonId : null,
              scormEnforceSequence: enforceScormSequence,
            },
          });
        }
      }
      if (viewContinueCount === (VIEW_EVENT_TIMEOUT_COUNT)) {
        setTimerPaused(true);
        savedPausedRef.current = true;
      } else {
        setViewContinueCount(viewContinueCount + 1);
      }
      const assignmentItem =
        client.readFragment({ id: `AssignmentItem:${assignmentId}`, fragment: AssignmentFragment });
      if (assignmentItem) {
        setActiveAssignmentInfoFromCacheItem(assignmentItem);
      }
    } else {
      savedViewEventInputRef.current = { ...savedViewEventInputRef.current, timeIncrement: viewContinueTickCount + 1 };
      setViewContinueTickCount(viewContinueTickCount + 1);
    }
  },
  // The view continue timer needs to reset on a page transition and also when the pause limit has been reached.
  isViewingLesson && !timerPaused && viewContinueCount <= VIEW_EVENT_TIMEOUT_COUNT && !cardTransitionState ?
    VIEW_EVENT_INTERVAL_SECONDS * 1000 : null);

  // if the card transition state is detect, we should reset it.
  // This logic is so that a new interval timer is created for viewContinue events.
  useEffect(() => {
    if (cardTransitionState) {
      setCardTransitionState(false);
    }
  }, [cardTransitionState]);

  // this effect is equivalent to an unmount function that is needed for partial viewContinue events.
  useEffect(() => {
    return () => {
      sendPartialViewContinueEvent(savedViewEventInputRef.current);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function sendPartialViewContinueEvent(viewContinueEvent) {
    if (timerPaused || savedPausedRef.current) {
      return;
    }
    const partialViewContinueEvent = viewContinueEvent ||
      createViewEventInput({ type: 'viewcontinue', timeIncrement: viewContinueTickCount });

    if (partialViewContinueEvent && partialViewContinueEvent.lessonId &&
        partialViewContinueEvent.lessonCardId && partialViewContinueEvent.enrollmentId) {
      sendViewEvent({
        update: (cache) => {
          return setAssignmentInProgress(cache);
        },
        variables: {
          input: partialViewContinueEvent,
        },
      });
    }
    // after sending a partial view continue, null out the ref.
    setViewContinueTickCount(0);
    savedViewEventInputRef.current = null;
  }

  function getCachedLesson() {
    const assignmentItem = client.readFragment({ id: `AssignmentItem:${assignmentId}`, fragment: AssignmentFragment });
    if (assignmentItem && currentCardProps) {
      if (!lessonInCache) {
        setLessonInCache(true);
      }
      const lesson = assignmentItem.type === 'program'
        ? assignmentItem.content.lessons.find(({ uid }) => uid === lessonId)
        : assignmentItem.content;

      const { title, enrollmentId, sourceLifecycle, status, language } = lesson;
      const lessonCardId = currentCardProps.id;
      return {
        programTitle: assignmentItem.type === 'program' ? assignmentItem.content.title : null,
        completedMessage: assignmentItem.type === 'program' ? assignmentItem.content.completedMessage : null,
        title,
        lessonCardId,
        enrollmentId,
        sourceLifecycle,
        status,
        language,
        hasCertificate: assignmentItem.type === 'program' ? assignmentItem.content.hasCertificate : null,
        downloadInstructions: assignmentItem.type === 'program' ? assignmentItem.content.downloadInstructions : null,
      };
    }
    return {};
  }

  function getLessonEnrollmentId() {
    const assignmentItem = client.readFragment({ id: `AssignmentItem:${assignmentId}`, fragment: AssignmentFragment });
    if (assignmentItem) {
      const lesson = assignmentItem.type === 'program'
        ? assignmentItem.content.lessons.find(({ uid }) => uid === lessonId)
        : assignmentItem.content;

      const { enrollmentId } = lesson;
      return enrollmentId;
    }
    return null;
  }

  function checkIsLastProgramLesson() {
    const assignmentItem = client.readFragment({ id: `AssignmentItem:${assignmentId}`, fragment: AssignmentFragment });
    if (assignmentItem && currentCardProps) {
      if (!lessonInCache) {
        return false;
      }
      if (assignmentItem.type !== 'program') {
        return false;
      }
      const programLessons = get(assignmentItem, 'content.lessons', []);
      if (programLessons.length > 0 && programLessons[programLessons.length - 1].uid === lessonId) {
        return true;
      }
    }
    return false;
  }

  async function saveAnswer({ answer, updateCacheKey }) {
    const { lessonCardId, enrollmentId, sourceLifecycle } = getCachedLesson();
    if (lessonCardId) {
      setEventLoading(false);
      const savedCardResponse = await saveCardAnswer({
        update: (cache, { data: response }) => {
          cache.modify({
            id: updateCacheKey,
            fields: {
              answer: () => {
                // Remove the object keys from saveAnswer that aren't relevant for this card type.
                return response.saveAnswer ?
                  Object.keys(response.saveAnswer)
                    .filter((key) => response.saveAnswer[key] !== null)
                    .reduce((newObj, keyWithData) => {
                      // eslint-disable-next-line no-param-reassign
                      newObj[keyWithData] = response.saveAnswer[keyWithData];
                      return newObj;
                    }, {}) : null;
              },
            },
          });
        },
        variables: {
          answer: {
            ...answer,
            lessonId,
            lessonCardId,
            sourceLifecycle,
            enrollmentId,
            programId,
          },
        },
      });
      if (savedCardResponse && !(currentCardIndex === lessonCardCount - 2)) {
        setEventLoading(true);
      }
      if (savedCardResponse && currentCardIndex === lessonCardCount - 2) {
        checkAssignmentCompletion({
          variables: {
            assignmentUserLessonId: assignmentId,
            lessonId,
            enrollmentId,
            scormProgramId: user.scorm ? user.scormProgramId : null,
            // eslint-disable-next-line no-nested-ternary
            assignmentProgramId: previewMode && previewAssignmentData ? previewAssignmentData.programId : isContentReviewer ? programId : null,
            // eslint-disable-next-line no-nested-ternary
            assignmentLessonId: previewMode && previewAssignmentData ? previewAssignmentData.lessonId : isContentReviewer ? lessonId : null,
            scormEnforceSequence: enforceScormSequence,
          },
        });
      }
    }
  }

  function createViewEventInput({ type, timeIncrement }) {
    const { lessonCardId, enrollmentId, sourceLifecycle } = getCachedLesson();
    if (lessonCardId) {
      return {
        type,
        lessonId,
        lessonCardId,
        enrollmentId,
        timeIncrement,
        sourceLifecycle,
      };
    }
  }

  function setPrevCardIndex(currIndex) {
    setEventLoading(false);
    sendPartialViewContinueEvent();
    return currIndex === 0 ? 0 : currIndex - 1;
  }

  function setNextCardIndex(currIndex) {
    const lastCardIndex = data.getLessonCards.length - 1;
    return currIndex === lastCardIndex ? lastCardIndex : currIndex + 1;
  }

  function navigateToNextCard() {
    setEventLoading(false);
    pauseCardAudio();
    pausePopupAudio();
    setBackNavigation(false);
    sendPartialViewContinueEvent();
    if (lessonTransitionState === 'active') {
      setLessonTransitionState('visible');
    } else {
      setCurrentCardIndex(setNextCardIndex);
    }
  }

  function transitionToNextProgramLesson() {
    setEventLoading(false);
    setLessonTransitionState('inactive');
    onNextAssignment();
  }

  function skipCard() {
    setCurrentCardIndex(setNextCardIndex);
  }

  function onProgramTimerClick() {
    setProgramTimerIsOpen(!programTimerIsOpen);
  }

  function unPauseTimer() {
    setTimerPaused(false);
    savedPausedRef.current = false;
    setViewContinueCount(1);
    setViewContinueTickCount(0);
  }

  if (loading || !currentCardProps) {
    return (
      <div
        className={(isSmallMobile || isMobile) ? `${styles.spinnerContainer} ${styles.spinnerContainerMobile}` : styles.spinnerContainer}
      >
        <Spinner customContainerHeight="100vh" />
      </div>
    );
  }

  if (error) {
    return (
      <LessonsContainer>
        <p>{error.message}</p>
      </LessonsContainer>
    );
  }

  const lessonCompleteFontSize = !isMobile && !isSmallMobile ? '2.2rem' : '1.5em';

  const videoList = isEnforceFullVideoView ? viewedFullVideos : viewedVideos;

  // Check the conditions for next button and tooltip
  const nextButtonDisabled = !!(!showNextButton || !nextButtonExpandCard || (videoId && !videoList.includes(videoId)));
  const includeVideoTooltip = videoId && !videoList.includes(videoId);
  const onVideoTooltipClick = () => {
    if (!nextButtonExpandCard) {
      updateClickExpandCardTooltip(true);
    }
    if (includeVideoTooltip) {
      setShowVideoTooltip(true);
    }
  };

  return (
    <LessonsContainer>
      <div role="region" aria-roledescription="carousel" aria-label="lesson-card-container" className={styles.lessonWrapper}>
        <div
          className={`${isSmallMobile ? styles.mobileButtonSectionSpacer : styles.buttonSectionSpacer}`}
        >
          <Button
            id="previous_lesson_card_button"
            disableElevation
            disableRipple
            className={styles.lessonNavButton}
            onClick={() => {
              updateClickExpandCardTooltip(false);
              setBackNavigation(true);
              if (showCompletionCertificate) {
                setShowCompletionCertificate(false);
              } else {
                setCurrentCardIndex(setPrevCardIndex);
              }
            }}
            onMouseEnter={() => setRightButtonHovered(true)}
            onMouseLeave={() => setRightButtonHovered(false)}
            disabled={!showPreviousButton}
            style={{
              position: 'absolute',
              top: navButtonTopOffset,
              right: isSmallMobile ? '-1.4rem' : '1rem',
              opacity: disablePreviousButton ? 0.3 : showPreviousButton ? 1 : 0,
              borderColor: palette.card.backgroundColor }}
            data-cy="previous"
            aria-label={t('lessonCards.prevCard')}
          >
            {!rightButtonHovered && !isSmallMobile && (
              <ArrowLeftIcon className={disablePreviousButton ? styles.arrow : ''} />
            )}
            {rightButtonHovered && !isSmallMobile && (
              <ArrowLeftIconHover />
            )}
            {!rightButtonHovered && isSmallMobile && (
              <ArrowLeftIconMobile className={disablePreviousButton ? styles.arrow : ''} />
            )}
            {rightButtonHovered && isSmallMobile && (
              <ArrowLeftIconHoverMobile />
            )}
          </Button>
        </div>
        <div
          className={styles.cardWrapper}
        >
          {showLanguageWarningLabel && lessonLanguage && lessonLanguage.toLowerCase().replace('-mt', '') !== user.language.toLowerCase() && (
            <div className={styles.languageWarningLabel} style={{ backgroundColor: palette.background.paused, color: palette.primary.dark }}>
              <CloseIcon className={styles.closeWarningIcon} onClick={() => { setShowLanguageWarningLabel(false); }} />
              {t('lessons.languageWarningLabel')}
            </div>
          )}
          {activeAssignmentInfo && activeAssignmentInfo.programId &&
            !activeAssignmentInfo.timeRequirementMet && activeAssignmentInfo.percentComplete === 100 && (
            <div>
              <ProgramTimer
                isOpen={programTimerIsOpen}
                onClick={onProgramTimerClick}
                minTimeInMinutes={activeAssignmentInfo.minTimeInMinutes}
                elapsedTime={activeAssignmentInfo.elapsedTime}
                programIsComplete={activeAssignmentInfo.programIsComplete}
                timeRequirementMet={activeAssignmentInfo.timeRequirementMet}
                timerPaused={timerPaused}
                contentArea
              />
              <div style={{ height: '1rem' }}>&nbsp;</div>
            </div>
          )}

          <div
            className={`${styles.lessonCompleteWrapper} ${currentCardIndex === 0 ? styles.fadeIn : ''}`}
          >
            {(lessonTransitionState === 'visible' || lessonTransitionState === 'fadeout') && (
              <div
                style={{ background: palette.background.eol_transition_background }}
                className={styles.lessonCompleteSuccessContainer}
              >
                <div
                  // eslint-disable-next-line max-len
                  className={`${styles.lessonCompleteSuccessBackgroundBox}  ${(lessonTransitionState === 'fadeout') ? styles.sectionFadeout : ''}`}
                  onTransitionEnd={() => transitionToNextProgramLesson()}
                  onClick={() => transitionToNextProgramLesson()}
                  style={{ background: palette.background.eol_transition_box }}
                >
                  <img
                    src={LessonCompleteGif}
                    alt="lesson complete"
                    className={styles.lessonCompleteGif}
                  />
                  <div className={styles.lessonCompleteText} style={{ fontSize: lessonCompleteFontSize }} data-cy="lesson_complete">
                    {t('lessonCards.lesson_complete')}
                  </div>
                </div>
              </div>
            )}
            {bookmarkState === BOOKMARKED_STATE && (
              <LessonCard
                type={currentCardProps.type}
                currentCard={currentCardIndex + 1}
                lessonLength={data.getLessonCards.length}
                isResultCard={isResultCard}
                showCompletionCertificate={showCompletionCertificate}
                speechAudioURL={currentCardProps.speechAudioURL}
                gatedFeedbackSpeechAudioURL={currentCardProps.gatedFeedbackSpeechAudioURL}
                eventLoading={eventLoading}
                translationLanguage={currentCardProps.translationLanguage}
                language={currentCardProps.language}
              >
                {/* do not wrap LessonCardComponent in other elements -- children is being accessed in components/LessonCard/index
                and the lesson card would be nested */}
                <LessonCardComponent
                  {...currentCardProps}
                  key={currentCardProps.id}
                  onSaveAnswer={saveAnswer}
                  onNextAssignment={onNextAssignment}
                  onNextCard={navigateToNextCard}
                  checkMoreAssignmentsAvailable={checkMoreAssignmentsAvailable}
                  saveAnswerLoading={saveAnswerLoading}
                  lessonTitle={lessonTitle}
                  lessonStatus={lessonStatus}
                  programId={programId}
                  lessonId={lessonId}
                  lessonLifecycle={lessonLifecycle}
                  programTitle={programTitle}
                  completedMessage={completedMessage}
                  onSkipCard={skipCard}
                  isMicroLesson={!programId}
                  isLastLessonInProgram={isLastProgramLesson}
                  responseThreshold={get(user, 'accounts.0.responseThreshold')}
                  setShowCompletionCertificate={setShowCompletionCertificate}
                  hasCertificate={hasCertificate}
                  downloadInstructions={downloadInstructions}
                  isTimedProgram={!!get(activeAssignmentInfo, 'minTimeInMinutes')}
                  timeRequirementMet={get(activeAssignmentInfo, 'timeRequirementMet')}
                  programIsComplete={get(activeAssignmentInfo, 'programIsComplete')}
                  percentComplete={get(activeAssignmentInfo, 'percentComplete')}
                  backNavigation={backNavigation}
                  resetCurrentLesson={resetCurrentLesson}
                  addViewedVideo={addViewedVideo}
                  viewedFullVideos={viewedFullVideos}
                  setShowVideoTooltip={setShowVideoTooltip}
                  eventLoading={eventLoading}
                />
              </LessonCard>
            )}
          </div>
        </div>
        <div
          className={`${isSmallMobile ? styles.mobileButtonSectionSpacer : styles.buttonSectionSpacer}`}
        >
          <div
            style={{
              position: 'absolute',
              top: navButtonTopOffset,
              left: isSmallMobile ? '-1.4rem' : '1rem',
              height: '3rem',
            }}
          >
            <div
              onMouseEnter={() => setLeftButtonHovered(true)}
              onMouseLeave={() => setLeftButtonHovered(false)}
              onClick={() => onVideoTooltipClick()}
            >
              {showVideoTooltip && includeVideoTooltip && (
                <Tooltip
                  open
                  title={t('lessons.viewVideoTootip')}
                  placement="bottom-end"
                  maxWidth={isSmallMobile ? '6.7rem' : '6.7rem'}
                  top={isSmallMobile ? '2.7rem' : '0.85rem'}
                  marginRight={isSmallMobile ? '-0.5rem' : '-1.25rem'}
                >
                  <span>&nbsp;</span>
                </Tooltip>
              )}
              {showClickExpandTooltip && (
                <Tooltip
                  open
                  title={t('lessons.viewClickExpandTootip')}
                  placement="bottom-end"
                  maxWidth={isSmallMobile ? '6.7rem' : '9rem'}
                  top={isSmallMobile ? '2.7rem' : '0.85rem'}
                  marginRight={isSmallMobile ? '-0.5rem' : '-1.25rem'}
                >
                  <span>&nbsp;</span>
                </Tooltip>
              )}
              <Button
                id="next_lesson_card_button"
                disableElevation
                disableRipple
                className={styles.lessonNavButton}
                onClick={() => navigateToNextCard()}
                // onMouseEnter and onMouseLeave do not fire when button is disabled - moved them to outer div
                disabled={nextButtonDisabled}
                style={{
                  top: 0,
                  opacity: disableNextButton ? 0.3 : showNextButton ? 1 : 0,
                  borderColor: palette.card.backgroundColor }}
                data-cy="next"
                aria-label={t('lessonCards.nextCard')}
              >
                {(!leftButtonHovered || includeVideoTooltip) && !isSmallMobile && (
                  <ArrowRightIcon className={disableNextButton ? styles.arrow : ''} />
                )}
                {!includeVideoTooltip && leftButtonHovered && !isSmallMobile && (
                  <ArrowRightIconHover />
                )}
                {(!leftButtonHovered || includeVideoTooltip) && isSmallMobile && (
                  <ArrowRightIconMobile className={disableNextButton ? styles.arrow : ''} />
                )}
                {!includeVideoTooltip && leftButtonHovered && isSmallMobile && (
                  <ArrowRightIconHoverMobile />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
      {askExpertEnabled && !previewMode && (
        <div className={styles.askExpertButtonRow} style={{ width: askExpertButtonWidth }}>
          <div style={{ width: AEButtonSpacerWidth }} />
          <AskTopicExpertButton />
          <div style={{ width: AEButtonSpacerWidth }} />
        </div>
      )}
      <div className={styles.footerRow}>
        <Footer />
      </div>
      {timerPaused && activeAssignmentInfo.programId && !!activeAssignmentInfo.minTimeInMinutes &&
      activeAssignmentInfo.minTimeInMinutes > 0 && !activeAssignmentInfo.timeRequirementMet && (
        <TimerPausedDialog onClose={() => unPauseTimer()} minTimeInMinutes={activeAssignmentInfo.minTimeInMinutes} />
      )}
      {timeRequirementModalOpen && !previewMode && activeAssignmentInfo?.programId && !!activeAssignmentInfo?.minTimeInMinutes &&
      activeAssignmentInfo?.minTimeInMinutes > 0 && !activeAssignmentInfo?.timeRequirementMet && (
        <TimeRequirementDialog
          onClose={() => handleTimeRequirementModalClose()}
          minTimeInMinutes={activeAssignmentInfo?.minTimeInMinutes}
          elapsedTime={activeAssignmentInfo?.elapsedTime}
          programTitle={programTitle}
          assignmentStatus={assignmentStatus}
          isScorm={user?.scorm}
        />
      )}
    </LessonsContainer>
  );
}

export default Lessons;
