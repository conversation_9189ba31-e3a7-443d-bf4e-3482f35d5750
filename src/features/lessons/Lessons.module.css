.title {
  font-size: 1.5em;
  text-align: center;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-grow: 1;
}

.spinnerContainer {
  width: 100%;
  height: calc(100vh - 7rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.spinnerContainerMobile {
  height: calc(100vh - 15rem);
}

.lessonWrapper {
  display: flex;
  justify-content: center;
  width: 98%;
  max-width: 40rem;
  min-width: 20rem;
}

.cardWrapper {
  /* width: 517px; */
  width: 100%;
  background: transparent;
}

.askAnExpert {
  margin-top: 0.33rem;
  align-items: center;
  justify-content: center;
  display: flex;
  border-radius: 5px;
  padding: 0px;
  cursor: pointer;
}

.askAnExpertText {
   display: flex;
   margin-left: 0.5rem;
   align-items: center;
}

.chevron {
  font-size: 2rem;
  margin-left: 0.25rem;
  position: relative;
  bottom: 1px;
}

.buttonSectionSpacer {
  position: relative;
  width: 4rem;
  padding-top: 40vh;
}

.mobileButtonSectionSpacer {
  position: relative;
  width: 0.2rem;
}

.lessonNavButton, .lessonNavButton :hover {
  border: none;
  background: none;
  background-color: transparent !important;
  z-index: 2;
  height: auto;
  align-self: center;
  padding: 0 !important;
}

.lessonNavButton:focus {
  border: solid 3px;
}

.lessonCompleteWrapper {
  width: 100%;
  position: relative;
  background: transparent;
}

.fadeIn {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.lessonCompleteSuccessContainer {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.lessonCompleteSuccessBackgroundBox {
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;
  aspect-ratio: 1 / 1.1;
  width: 70%;
  border-radius: 0.5rem;
  animation: fadeIn .5s ease-in-out;
}

.lessonCompleteGif {
  aspect-ratio: 1 / 1;
  width: 90%;
  margin: 0 auto;
}

.lessonCompleteText {
  font-weight: bold;
  animation: fadeUp 0.1s ease-in;
  padding: 0;
  line-height: 1;
}

@keyframes fadeUp {
  0% { opacity: 0; transform: scale(0); }
  100% { opacity: 1; transform: scale(1); }
}

.sectionFadeout {
  opacity: 0.5;
  transition: opacity .6s linear;
}

.languageWarningLabel {
  animation: fadeIn linear 1s;
  position: relative;
  height: 3rem;
  margin-bottom: .4rem;
  text-align: center;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to   { opacity: 1; }
}

.closeWarningIcon {
  position: absolute;
  top: .3rem;
  right: .2rem;
  height: 1.1rem;
  cursor: pointer;
}

.footerRow {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  flex-grow: 1;
  width: 100%;
  max-width: 32rem;
  min-width: 20rem;
}

.askExpertButtonSpacer {
  width: 4rem;
}

.askExpertButtonRow {
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes arrowsFadeInOut {
  0% { -webkit-transform: translateY(0); opacity: 0.2 }
  100% { -webkit-transform: translateY(0); opacity: 1 }
}
.arrow {
  animation: arrowsFadeInOut 0.6s infinite alternate ease-in-out;
}