/* eslint-disable max-len */
/* eslint-disable react/no-find-dom-node */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/no-danger */
import React, { useEffect, useRef, useState } from 'react';
import { get } from 'lodash';
import { useTheme, Box, styled, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import ReactDOM from 'react-dom';
import { RESPONSE_THRESHOLD_DEFAULT } from '../../../../config/constants';
import HalfDoughnutGraph from '../../../../components/HalfDoughnutGraph/HalfDoughnutGraph';
import { NotEnoughResults } from '../../../../components/NotEnoughResults/NotEnoughResults';
import { useLessonCardResults } from '../../../../hooks/useLessonCardResults';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import Spinner from '../../../../components/Spinner/Spinner';
import { useNumberFormat } from '../../../../hooks/useNumberFormat';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useUser } from '../../../../hooks/useUser';
import { WCSResultsText } from '../../../../hooks/useScreenReaderText';
import UserSquareIcon from '../../../../images/user-square-solid.svg';
import GreenCheck from '../../../../images/green_check.png';
import YellowCheck from '../../../../images/yellow_check.png';
import OrangeCheck from '../../../../images/orange_check.png';
import RedCheck from '../../../../images/red_check.png';
import MyAnswerContainer from '../MyAnswerContainer';
import styles from './WorkplaceColorSpectrumResultCard.module.css';

const IconLabel = styled((props) => <Box id="labels" {...props} />)({
  display: 'flex',
  alignItems: 'center',
  flexDirection: 'column',
  '& span': {
    fontSize: '0.85rem',
  },
});

const GlobalResponseContainer = styled((props) => <Box {...props} id="globalResponseContainer" />)({
  display: 'flex',
  justifyContent: 'space-between',
});

const LegendRootContainer = styled((props) => <Box id="legend" {...props} />)({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
});

const LegendContainer = styled((props) => <Box {...props} />)({
  display: 'grid',
  gridTemplateColumns: 'repeat(4, 1fr)',
  justifyContent: 'center',
  // transition: 'width 0.5s linear',
});

const answers = {
  1: 'GREEN',
  2: 'YELLOW',
  3: 'ORANGE',
  4: 'RED',
};

const checkIcons = [GreenCheck, YellowCheck, OrangeCheck, RedCheck];

export default function WorkplaceColorSpectrumResultCard(props) {
  const { id, answer, correctAnswer, description, title, fontColor, responseThreshold, insights, organization, global, lessonLifecycle } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();
  const user = useUser();
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { loading, data } = !insights && useLessonCardResults({ lessonCardId: id, lessonLifecycle });
  const { formatReportTotal } = useNumberFormat();
  const { isSmallMobile, isMobile } = useResponsiveMode();

  const [myorgWidth, setMyorgWidth] = useState(undefined);
  const [globalWidth, setGlobalWidth] = useState(undefined);
  const [correctAnswerIndex, setCorrectAnswerIndex] = useState(0);

  // if an sso admin is previewing the lesson, the relevant account is user.accounts[1]
  const accountIndex = get(user, 'accounts[1]') ? 1 : 0;
  const hideOrg = !insights ? get(user, `accounts[${accountIndex}].hideOrg`, false) : false;

  // Ref are used to get g element width which is applied to color gradient bar
  const myorgChartRef = useRef(null);
  const globalChartRef = useRef(null);
  let myorgGElementWidth = 285;
  let globalGElementWidth = 285;
  if (myorgChartRef && myorgChartRef.current) {
    const myorgRoot = ReactDOM.findDOMNode(myorgChartRef.current);
    myorgGElementWidth = myorgRoot?.children[0].getBoundingClientRect().width;
  }
  if (globalChartRef && globalChartRef.current) {
    const globalRoot = ReactDOM.findDOMNode(globalChartRef.current);
    globalGElementWidth = globalRoot?.children[0].getBoundingClientRect().width;
  }

  useEffect(() => {
    const correctOffset = Object.keys(answers).findIndex((key) => answers[key] === correctAnswer);
    setCorrectAnswerIndex(correctOffset);
  }, [correctAnswer]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (myorgChartRef && myorgChartRef.current) {
      setMyorgWidth(myorgGElementWidth);
    }
    if (globalChartRef && globalChartRef.current) {
      setGlobalWidth(globalGElementWidth);
    }
  }, [myorgGElementWidth, globalGElementWidth]);

  const lessonCardFocusElement = document.getElementById('lesson-card-focus-element');
  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (!loading && lessonCardFocusElement && contentContainerElement) {
      lessonCardFocusElement.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [loading, lessonCardFocusElement, contentContainerElement]);

  if (loading) {
    return (
      <Spinner />
    );
  }

  const legendBarData = [
    { color: t('lessonCards.green'), text: t('lessonCards.green_text') },
    { color: t('lessonCards.yellow'), text: t('lessonCards.yellow_text') },
    { color: t('lessonCards.orange'), text: t('lessonCards.orange_text') },
    { color: t('lessonCards.red'), text: t('lessonCards.red_text') },
  ];

  const rawOrgData = insights ? organization.colorSpectrumReportData : get(data, 'getResultsCard.organization.colorSpectrumReportData');
  const organizationData = rawOrgData.percentages.map(({ response, percentage }) => ({
    x: response,
    y: percentage,
    correctAnswer: answers[response] === correctAnswer,
    myAnswer: answers[response] === answer.workplaceColorSpectrumAnswer,
    style: { fill: 'red' },
  }));

  const rawGlobalData = insights ? global.colorSpectrumReportData : get(data, 'getResultsCard.global.colorSpectrumReportData');
  const globalData = rawGlobalData.percentages.map(({ response, percentage }) => ({
    x: response,
    y: percentage,
    correctAnswer: answers[response] === correctAnswer,
  }));

  const organizationTotal = get(rawOrgData, 'total') || 0;
  const globalTotal = get(rawGlobalData, 'total') || 0;
  const adjustedReponseThreshold = responseThreshold || RESPONSE_THRESHOLD_DEFAULT;
  let displayResults = organizationTotal >= adjustedReponseThreshold && globalTotal >= adjustedReponseThreshold;
  if (lessonLifecycle === 'preview' && organizationTotal && globalTotal) {
    displayResults = true;
  }

  // eslint-disable-next-line no-nested-ternary
  const myOrgMarginUp = (isSmallMobile || insights) ? styles.myOrgMoveDoughnutUpSmallMobile :
    isMobile ? styles.myOrgMoveDoughnutUpMobile : styles.myOrgMoveDoughnutUp;
  const cardContent = insights ? styles.insightsCardContent : styles.cardContent;
  const globalLegendText = insights ? styles.insightsGlobalLegendText : styles.globalLegendText;
  const legendMyOrgWidth = insights ? '60%' : myorgWidth;
  const legendGlobalWidth = insights ? '60%' : globalWidth;

  return (
    <CardResponsiveText
      containerStyle={cardContent}
      key={`WorkplaceColorSpectrumResultCard-${id}`}
    >
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      {!displayResults && <NotEnoughResults insights={insights} />}
      {displayResults && globalData && legendBarData && (
        <WCSResultsText
          t={t}
          hideOrg={hideOrg}
          organizationData={organizationData}
          globalData={globalData}
          legendBarData={legendBarData}
        />
      )}
      {displayResults && (
        <>
          <div aria-hidden className={styles.myAnswerSection}>
            {!insights && (<MyAnswerContainer />)}
          </div>
          {/* My Organization Response */}
          <Box id="myOrgContainer" sx={{ display: 'grid', rowGap: '0.5rem' }}>
            {/* Chart */}
            <Box lineHeight={0}>
              {!hideOrg && (
                <HalfDoughnutGraph
                  isResultsCard
                  ref={myorgChartRef}
                  isMiddleLabelsTooltip
                  middleLabelsTooltip={t('lessonCards.myOrg_tooltip')}
                  hideOuterLabels
                  onlyShowValue
                  /* animate={{ duration: 1000 }}
                  interval="400" */
                  colorScale={palette.background.wcs}
                  className={`${styles.doughnut} ${myOrgMarginUp}`}
                  data={organizationData}
                  dataCY="myOrgResponse"
                  middleLabels={[
                    {
                      x: 200,
                      y: 170,
                      text: `${t('lessonCards.myOrg')}`,
                      tooltip: `${t('lessonCards.myOrg_tooltip')}`,
                      numResponsesText: `${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      style: {
                        fontSize: 17,
                        fontWeight: 600,
                        fontFamily: 'Source Sans Pro',
                        fill: palette.primary.dark,
                        textAlign: 'center',
                        lineHeight: '0.9rem',
                        marginTop: '0.2rem',
                      },
                    },
                    {
                      x: 200,
                      y: 190,
                      text: `${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      // eslint-disable-next-line max-len
                      style: { fontSize: 14, fontFamily: 'Source Sans Pro', fill: palette.primary.main, textAlign: 'center', lineHeight: '0.5rem', marginTop: '0.4rem' },
                    },
                  ]}
                />
              )}
              {hideOrg && (
                <HalfDoughnutGraph
                  isResultsCard
                  ref={myorgChartRef}
                  isMiddleLabelsTooltip
                  middleLabelsTooltip={t('lessonCards.global_tooltip')}
                  hideOuterLabels
                  onlyShowValue
                  /* animate={{ duration: 1000 }}
                  interval="400" */
                  colorScale={palette.background.wcs}
                  className={`${styles.doughnut} ${myOrgMarginUp}`}
                  data={globalData}
                  dataCY="globalResponse"
                  middleLabels={[
                    {
                      x: 200,
                      y: 170,
                      text: `${t('lessonCards.global')}`,
                      tooltip: `${t('lessonCards.global_tooltip')}`,
                      numResponsesText: `${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      style: {
                        fontSize: 17,
                        fontWeight: 600,
                        fontFamily: 'Source Sans Pro',
                        fill: palette.primary.dark,
                        textAlign: 'center',
                        lineHeight: '0.9rem',
                        marginTop: '0.2rem',
                      },
                    },
                    {
                      x: 200,
                      y: 190,
                      text: `${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      // eslint-disable-next-line max-len
                      style: { fontSize: 14, fontFamily: 'Source Sans Pro', fill: palette.primary.main, textAlign: 'center', lineHeight: '0.5rem', marginTop: '0.4rem' },
                    },
                  ]}
                />
              )}
            </Box>
            {/* Legend */}
            <LegendRootContainer aria-hidden mt={1.5} mb={1} className={styles.delayedFadeIn}>
              <LegendContainer id="legendBar" width={legendMyOrgWidth}>
                {palette.background.wcs.map((nextBar, index) => (
                  <div
                    key={`legendBar-${index}`}
                    className={styles.myOrgLegendBarSegment}
                    style={{ backgroundColor: nextBar }}
                    data-cy={`legendBar-${index}`}
                  >
                    {!insights && answer.workplaceColorSpectrumAnswer === answers[`${index + 1}`] && (
                      <div
                        aria-hidden
                        className={styles.myAnswerIndicator}
                        data-cy="myAnswer"
                      >
                        <UserSquareIcon />
                      </div>
                    )}
                  </div>
                ))}
              </LegendContainer>
              <LegendContainer pt={1} id="legendText" width={legendMyOrgWidth} style={{ gap: '0.2rem', whiteSpace: 'nowrap' }}>
                {legendBarData.map((nextBar, index) => (
                  <div
                    key={`legendBarColor-${index}`}
                    className={styles.myOrgLegendTextSegment}
                    style={{ fontSize: isMobile ? '0.6em' : '0.8em' }}
                    data-cy={`legendBarColor-${index}`}
                  >
                    <p style={{ marginBlockStart: insights && '0em', marginBlockEnd: insights && '0em' }}>{nextBar.color}</p>
                    <span
                      className={styles.text}
                      style={{ fontSize: isMobile ? '0.8em' : '0.7em' }}
                      data-cy={`legendBarText-${index}`}
                    >{nextBar.text}
                    </span>
                    <p
                      key={`globalLegendText-${index}`}
                      className={globalLegendText}
                      // eslint-disable-next-line max-len
                      style={{ paddingTop: '0.3rem', color: palette.primary.main, fontSize: isMobile ? '0.8rem' : '1rem' }}
                      data-cy={`globalLegendText-${index}`}
                    >
                      {`${organizationData[index].y}%`}
                    </p>
                  </div>
                ))}
              </LegendContainer>
            </LegendRootContainer>
          </Box>
          {/* Global Response Container */}
          <GlobalResponseContainer mb={isSmallMobile || insights ? '3rem' : 0}>
            <Box
              className={styles.correctAnswerContainer}
              style={{
                flex: hideOrg ? '0 1 100%' : '',
                paddingTop: hideOrg ? '3rem' : '',
                margin: hideOrg && !isMobile ? '0 6rem' : '' }}
            >
              <Box
                aria-hidden
                id="staticLabels"
                sx={{
                  border: `2px solid ${palette.primary.main}`,
                  borderRadius: '.5rem',
                }}
                p={1}
              >
                {correctAnswer && (
                  <>
                    <div className={styles.correctAnswer}>
                      <img
                        src={checkIcons[correctAnswerIndex]}
                        alt="CorrectIcon"
                        className={styles.correctAnswerIcon}
                      />
                    </div>
                    <IconLabel>
                      <span
                        style={{ fontSize: isMobile ? '.7rem' : '' }}
                      >
                        {t('lessonCards.correct_answer')}:
                      </span>
                      <span
                        style={{ fontWeight: 'bold', textTransform: 'capitalize', fontSize: isMobile ? '.7rem' : '' }}
                      >
                        {t(`lessonCards.${correctAnswer.toLowerCase()}`)}
                      </span>
                    </IconLabel>
                  </>
                )}
                {!correctAnswer && (
                  <IconLabel>
                    <span
                      className={styles.noCorrectAnswerHeading}
                      style={{ fontSize: (isMobile || insights) ? '.7rem' : '' }}
                    >
                      {t('lessonCards.no_correct_answer')}
                    </span>
                    <span
                      className={styles.noCorrectAnswerText}
                      style={{ fontSize: (isMobile || insights) ? '.7rem' : '' }}
                    >
                      {t('lessonCards.results_reflect_input')}
                    </span>
                  </IconLabel>
                )}
              </Box>
            </Box>
            {!hideOrg && (
              <Box
                sx={{
                  display: 'grid',
                  rowGap: '0.5rem',
                  border: palette.border.main,
                  borderRadius: '4px',
                  width: '50%',
                  marginBottom: isSmallMobile || insights ? '1rem' : 0,
                  marginTop: '0.8rem',
                }}
              >
                {/* Chart */}
                <Box aria-hidden lineHeight={0}>
                  <HalfDoughnutGraph
                    isResultsCard
                    ref={globalChartRef}
                    isMiddleLabelsTooltip
                    middleLabelsTooltip={t('lessonCards.global_tooltip')}
                    hideOuterLabels
                    onlyShowValue
                    animate={{ duration: 1000 }}
                    colorScale={palette.background.wcsGlobal}
                    className={`${styles.doughnut} ${styles.globalMoveDoughnutUp}`}
                    data={globalData}
                    dataCY="globalResponse"
                    middleLabels={[
                      {
                        x: 200,
                        y: 180,
                        text: `${t('lessonCards.global')}`,
                        tooltip: `${t('lessonCards.global_tooltip')}`,
                        numResponsesText: `${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                        style: { fontSize: 24,
                          fontWeight: 600,
                          textAlign: 'center',
                          lineHeight: '1.3rem',
                          marginTop: '0.5rem',
                          wordWrap: 'break-word',
                          fontFamily: 'Source Sans Pro',
                          fill: palette.primary.dark,
                        },
                      },
                    ]}
                  />
                  {/* Response Label */}
                  <Typography textAlign="center" lineHeight="1rem" fontSize="0.75rem" mt={-0.1} data-cy="globalResponse">
                    {`${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`}
                  </Typography>
                </Box>

                {/* Global Legend */}
                <LegendRootContainer aria-hidden mb={1} mt={-0.5} className={styles.delayedFadeIn}>
                  <LegendContainer mb={0.2} id="globalLegendBar" width={legendGlobalWidth}>
                    {palette.background.wcsGlobal.map((nextBar, index) => (
                      <div
                        key={`globalLegendBarSegment-${index}`}
                        className={styles.globalLegendBarSegment}
                        style={{ backgroundColor: nextBar }}
                        data-cy={`globalLegendBarSegment-${index}`}
                      />
                    ))}
                  </LegendContainer>
                  <LegendContainer id="globalLegendText" width={legendGlobalWidth} style={{ gap: '0.2rem' }}>
                    {globalData.map((nextData, index) => (
                      <div
                        key={`globalLegendText-${index}`}
                        // eslint-disable-next-line max-len
                        style={{ color: palette.primary.main, whiteSpace: 'nowrap', fontSize: isMobile ? '0.5rem' : '0.7rem' }}
                      >
                        <p
                          className={globalLegendText}
                          // eslint-disable-next-line max-len
                          style={{ color: palette.primary.main, fontSize: isMobile ? '0.4rem' : '0.58rem' }}
                          data-cy={`globalLegendColor-${index}`}
                        >
                          {legendBarData[index].color}
                        </p>
                        <p
                          className={globalLegendText}
                          style={{ color: palette.primary.main, fontSize: isMobile ? '0.5rem' : '0.7rem' }}
                          data-cy={`globalLegendText-${index}`}
                        >
                          {`${nextData.y}%`}
                        </p>
                      </div>
                    ))}
                  </LegendContainer>
                </LegendRootContainer>
              </Box>
            )}
          </GlobalResponseContainer>
        </>
      )}
    </CardResponsiveText>
  );
}
