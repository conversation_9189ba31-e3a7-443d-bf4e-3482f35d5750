.cardContent {
  margin-top: 1.5rem;
}

.doughnut {
  width: 100%;
}

/* My Org Container*/
.myOrgMoveDoughnutUp {
  margin-top: -5rem;
}

.myOrgMoveDoughnutUpMobile {
  margin-top: -4rem;
}

.myOrgMoveDoughnutUpSmallMobile {
  margin-top: -3rem;
}

.delayedFadeIn {
  animation: 5s ease 0s normal forwards 1 fadeIn;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  20% { opacity: 0; }
  100%   { opacity: 1; }
}

.myOrgLegendBarSegment {
  position: relative;
  height: .4rem;
}

.myAnswerIndicator {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.myOrgLegendTextSegment {
  text-align: center;
  line-height: .8rem;
  font-weight: 600;
}

.myOrgLegendTextSegment .text {
  font-weight: normal;
}

/* Global Container */

.globalMoveDoughnutUp {
  margin-top: -1.1rem;
}

.globalLegendBarSegment {
  height: .4rem;
  animation: fadeIn 5s;
}

.globalLegendText {
  text-align: center;
  font-weight: 600;
  line-height: 1rem;
}

.insightsGlobalLegendText{
  text-align: center;
  font-weight: 600;
  line-height: 1rem;
  margin-block-start: auto;
  margin-block-end: auto;
}

.myAnswerSection {
  display: flex;
  width: 100%;
  justify-content: right;
  margin-bottom: .8rem;
}

.correctAnswerContainer {
  display: flex;
  flex: 0 1 50%;
  align-items: center;
  justify-content: center;
  margin: 0 2rem;
}

.correctAnswerContainerBox {
  border-radius: .8rem;
}

.correctAnswer {
  margin-top: -1.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.correctAnswerIcon {
  width: 24px;
  height: auto;
}

.noCorrectAnswerHeading {
  font-weight: 600;
  text-align: center;
  padding-bottom: .3rem;
}

.noCorrectAnswerText {
  font-weight: 200;
  text-align: center;
}

.insightsCardContent {
  margin-top: 0rem !important;
  font-size: 1.1rem !important;
}

.insightsCardContent h2 {
   margin-block-start: 0em !important;
}

