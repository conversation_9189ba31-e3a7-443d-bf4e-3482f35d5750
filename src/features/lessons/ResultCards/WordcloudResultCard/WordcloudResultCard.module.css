.cardContent {
  margin-top: 1.5rem;
}

.disabledFreeformTextArea {
  background-color: #EAEBF1;
  font-size: 1.2em !important;
  margin-top: 1rem;
}

.disabledFreeformTextArea textarea {
  font-size: 1.2em !important;
}

.viewContainer {
  display: grid;
  width: 100%;
  grid-template-columns: 106px 81px 78px;
  column-gap: 1em;
  justify-content: center;
}
  
.viewButtonContainer {
  width: auto !important;
}
  
.viewButton {
  text-transform: none;
  border-radius: 0.5rem;
}

.viewButtonSmall {
  font-size: 0.875rem;
  padding: .25em 0.5em;
}

.viewButtonLarge {
  font-size: 1rem;
  padding: .25em 1em;
}

.viewAsListButtonContainer {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  align-content: center;
}
.viewAsListButton {
  text-transform: none !important;
  border-radius: 10rem;
  margin: 0 auto;
}

.listButtonContainer {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}
.listButton {
  text-transform: none !important;
  border-radius: 10rem;
}

.wordCloudContainer {
  width: 100%;
  height: 12rem;
  border: 1px solid red;
}

.listOuterContainer {
  margin: 0 auto;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.listContainer {
  width: 100%;
  border: 1px solid #E0E0E0;
  border-radius: 0.3rem;
  padding: 0;
  margin: 1rem 0 1rem 0;
  text-align:center;
}

.listHeader {
  display: flex;
  justify-content: center;
  font-weight: 700;
  padding: 0.1rem 0;
  background: #EAEBF1;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  text-transform: capitalize;
}

.listText {
  font-size: 0.875rem;
  display: flex;
  justify-content: center;
  padding: 0.25rem 0;
}

.wordList {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.buttonGroup {
  height: 2.2rem;
  border-radius: 5rem;
  border: none;
}

.viewAsListButton, .viewAsWordCloudButton {
  border: 1px solid;
  text-transform: none;
  height: 2.3rem;
  white-space: nowrap;
}

.viewAsWordCloudButton {
  border-top-left-radius: 5rem;
  border-bottom-left-radius: 5rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.viewAsListButton {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 5rem;
  border-bottom-right-radius: 5rem;
}

.buttonSelected {
  font-weight: 700;
  text-transform: none;
}

.insightsCardContent {
  margin-top: 0rem !important;
  font-size: 1.1rem !important;
}

.insightsCardContent h2 {
   margin-block-start: 0em !important;
}

.header {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: .2rem .5rem;
}

.boxWordCloud {
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.boxContainer {
  display: flex;
  flex-direction: column;
  row-gap: 1.5em;
  margin-top: 1em;
}
