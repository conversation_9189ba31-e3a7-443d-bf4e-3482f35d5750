/* eslint-disable react/no-danger */
import React, { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, TextField, useTheme, ButtonGroup } from '@mui/material';
import { get } from 'lodash';
import Wordcloud from './Wordcloud';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { NotEnoughResults } from '../../../../components/NotEnoughResults/NotEnoughResults';
import Button from '../../../../components/Button/Button';
import { useLessonCardResults } from '../../../../hooks/useLessonCardResults';
import { useUser } from '../../../../hooks/useUser';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import Tooltip from '../../../../components/Tooltip/Tooltip';
import Spinner from '../../../../components/Spinner/Spinner';
import styles from './WordcloudResultCard.module.css';

function WordcloudResultCard(props) {
  const { id, description, title, fontColor, answer, insights, organization, global, lessonLifecycle } = props;

  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();
  const user = useUser();
  const { palette } = useTheme();
  const cardContent = insights ? styles.insightsCardContent : styles.cardContent;

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { data } = !insights && useLessonCardResults({ lessonCardId: id, lessonLifecycle });
  const haveData = data && data.getResultsCard;

  // if an sso admin is previewing the lesson, the relevant account is user.accounts[1]
  const accountIndex = get(user, 'accounts[1]') ? 1 : 0;
  const hideOrg = get(user, `accounts[${accountIndex}].hideOrg`, false);

  const wordsToString = (theWords) => {
    let string = '';
    if (theWords && theWords.length > 0) {
      let n = 0;
      const limit = theWords.length >= 5 ? 5 : theWords.length;
      while (n < limit) {
        const punctuation = n < limit - 1 ? ', ' : '.';
        string += `${theWords[n].text}${punctuation}`;
        n += 1;
      }
    } else {
      string = 'No results.';
    }
    return string;
  };

  const organizationWords = insights ? organization.freeFormReportData
    : get(data, 'getResultsCard.organization.freeFormReportData');
  const globalWords = insights ? global.freeFormReportData : get(data, 'getResultsCard.global.freeFormReportData');

  // The number of answers threshold is handled on the server
  // If the client receives an array with length > 0 it means the words should be displayed.
  const displayResults = organizationWords !== undefined && organizationWords.length > 0
    && globalWords !== undefined && globalWords.length > 0;

  const [currentView, setCurrentView] = useState('me');
  const [words, setWords] = useState(() => organizationWords || []);
  const [tabPanelId, setTabPanelId] = useState(null);
  const [listView, setListView] = useState(false);

  useEffect(() => {
    if (organizationWords !== undefined) {
      setWords(organizationWords);
    }
  }, [organizationWords]);

  const handleKeyDown = (e, key) => {
    e.preventDefault();
    let newKey;
    let button;
    // handle button foucus on arrow key input
    if (e.code === 'ArrowRight') {
      newKey = key + 1 <= 3 ? key + 1 : 1;
      button = document.getElementById(`tab-${newKey}`);
      button.focus();
      setTabPanelId(newKey);
    }
    if (e.code === 'ArrowLeft') {
      newKey = key - 1 >= 1 ? key - 1 : 3;
      button = document.getElementById(`tab-${newKey}`);
      button.focus();
      setTabPanelId(newKey);
    }
    // handle button foucus on tab key input
    if (e.code === 'Tab' && !e.shiftKey) {
      if (tabPanelId === null) {
        setTabPanelId(1);
        button = document.getElementById('tab-1');
        button.focus();
      } else if (tabPanelId === 1 || tabPanelId === 2) {
        newKey = tabPanelId + 1;
        button = document.getElementById(`tab-${newKey}`);
        button.focus();
        button = document.getElementById(`tab-${newKey}`);
        button.focus();
      } else if (tabPanelId === 3 && currentView === 'me') {
        newKey = tabPanelId + 1;
        button = document.getElementById('next_lesson_card_button');
        button.focus();
      } else {
        button = listView
          ? document.getElementById('list_view_button')
          : document.getElementById('wcs_view_button');
        button.focus();
      }
    }
    if (e.code === 'Tab' && e.shiftKey) {
      if (tabPanelId === 1) {
        button = document.getElementById('previous_lesson_card_button');
        button.focus();
      } else {
        newKey = tabPanelId - 1;
        button = document.getElementById(`tab-${newKey}`);
        button.focus();
      }
    }
    // handle enter key input to make the selection of the button
    if (e.code === 'Enter' && (key >= 1 || key <= 3)) {
      if (key === 2) {
        setCurrentView('organization'); setWords(organizationWords);
      } else if (key === 3) {
        setCurrentView('global'); setWords(globalWords);
      } else {
        setCurrentView('me');
      }
    }
  };

  const buttonStyles = isSmallMobile ? styles.viewButtonSmall : styles.viewButtonLarge;

  const lessonCardFocusElement = document.getElementById('lesson-card-focus-element');
  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (!insights && haveData && displayResults && lessonCardFocusElement && contentContainerElement) {
      lessonCardFocusElement.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [insights, haveData, displayResults, lessonCardFocusElement, contentContainerElement]);

  if (!haveData && !insights) {
    return (
      <Spinner />
    );
  }

  let wordsListMarkup = '';
  const wordsListHeaderText = words.length >= 10
    ? `${t('lessonCards.top')} 10 ${t('lessonCards.responses')}`
    : null;
  if (haveData && displayResults && words && words.length > 0) {
    const slicedWords = words && words.length > 10 ? words.slice(0, 10) : words;
    slicedWords.forEach((item, i) => {
      wordsListMarkup += `<li key="words-${i} ">${item.text} (${item.value})</li>`;
    });
  }

  return (
    <CardResponsiveText aria-hidden containerStyle={cardContent} key={`WordcloudResultCard-${id}`}>
      <CardTitleDescription aria-hidden title={title} description={description} fontColor={fontColor} />
      {!displayResults && (
        <NotEnoughResults insights={insights} />
      )}
      <p className="srOnly" id="my_answer_described_by">
        {`${t('lessonCards.my')} ${t('lessonCards.answer')}: ${get(answer, 'freeFormTextAnswer')}`}
      </p>
      {displayResults && !hideOrg && (
        <p className="srOnly" id="org_tooltip_text">
          {t('lessonCards.myOrg_tooltip')}
          :
          {t('lessonCards.topFive')}
          :
          {wordsToString(organizationWords)}
        </p>
      )}
      {displayResults && (
        <p className="srOnly" id="global_tooltip_text">
          {t('lessonCards.global_tooltip')}
          :
          {t('lessonCards.topFive')}
          :
          {wordsToString(globalWords)}
        </p>
      )}
      <div>
        {displayResults && !insights && (
          <Box
            role="tablist"
            display="flex"
            width="100%"
            columnGap={isSmallMobile ? '0.5em' : '1em'}
            justifyContent="center"
            justifyItems="center"
          >
            <Button
              aria-describedby="my_answer_described_by"
              id="tab-1"
              role="tab"
              aria-controls="tabpanel-1"
              aria-selected={tabPanelId === 1}
              containerStyle={styles.viewButtonContainer}
              className={`${styles.viewButton} ${buttonStyles}`}
              style={{ fontWeight: currentView === 'me' ? 600 : 500 }}
              variant={currentView === 'me' ? 'contained' : 'outlined'}
              onClick={() => { setCurrentView('me'); setTabPanelId(1); }}
              onFocus={() => { setTabPanelId(1); }}
              onKeyDown={(e) => handleKeyDown(e, 1)}
              data-cy="myAnswer"
              sx={{
                '&:focus': {
                  outline: `solid ${palette.card.backgroundColor} 3px`,
                },
              }}
            >
              {`${t('lessonCards.my')} ${t('lessonCards.answer')}`}
            </Button>
            {!hideOrg && (
              <Tooltip
                title={t('lessonCards.myOrg_tooltip')}
                marginTop="0.375rem !important"
                role="tooltip"
                aria-describedby="org_tooltip_text"
              >
                <div>
                  <Button
                    id="tab-2"
                    role="tab"
                    aria-controls="tabpanel-2"
                    aria-selected={tabPanelId === 2}
                    containerStyle={styles.viewButtonContainer}
                    className={`${styles.viewButton} ${buttonStyles}`}
                    style={{ fontWeight: currentView === 'organization' ? 600 : 500 }}
                    variant={currentView === 'organization' ? 'contained' : 'outlined'}
                    onClick={() => { setCurrentView('organization'); setWords(organizationWords); setTabPanelId(2); }}
                    onFocus={() => { setTabPanelId(2); }}
                    onKeyDown={(e) => handleKeyDown(e, 2)}
                    data-cy="myOrg"
                    sx={{
                      '&:focus': {
                        outline: `solid ${palette.card.backgroundColor} 3px`,
                      },
                    }}
                  >
                    {t('lessonCards.myOrg')}
                  </Button>
                </div>
              </Tooltip>
            )}
            <Tooltip
              title={t('lessonCards.global_tooltip')}
              marginTop="0.375rem !important"
              role="tooltip"
              aria-describedby="global_tooltip_text"
            >
              <div>
                <Button
                  id="tab-3"
                  role="tab"
                  aria-controls="tabpanel-3"
                  aria-selected={tabPanelId === 3}
                  containerStyle={styles.viewButtonContainer}
                  className={`${styles.viewButton} ${buttonStyles}`}
                  style={{ fontWeight: currentView === 'global' ? 600 : 500 }}
                  variant={currentView === 'global' ? 'contained' : 'outlined'}
                  onClick={() => { setCurrentView('global'); setWords(globalWords); setTabPanelId(3); }}
                  onFocus={() => { setTabPanelId(3); }}
                  onKeyDown={(e) => handleKeyDown(e, 3)}
                  data-cy="global"
                  sx={{
                    '&:focus': {
                      outline: `solid ${palette.card.backgroundColor} 3px`,
                    },
                  }}
                >
                  {t('lessonCards.global')}
                </Button>
              </div>
            </Tooltip>
          </Box>

        )}
        <Box role="tabpanel" mb={3}>
          {displayResults && !insights && currentView === 'me' && (
            <TextField
              role="tabpanel"
              id="tabpanel-1"
              aria-labelledby="tab-1"
              className={styles.disabledFreeformTextArea}
              disabled
              multiline
              fullWidth
              rows={5}
              value={get(answer, 'freeFormTextAnswer') || ''}
              inputProps={{ 'data-cy': 'freeformText' }}
            />
          )}
          {!listView && displayResults && !insights && (['organization', 'global'].includes(currentView)) && (
            <div aria-hidden>
              <Wordcloud words={words} tabPanelId={tabPanelId} />
            </div>
          )}
          {listView && wordsListMarkup && displayResults && !insights &&
            displayResults && (['organization', 'global'].includes(currentView)) && (
            <div className={styles.listOuterContainer}>
              <div className={styles.listContainer}>
                {wordsListHeaderText && (
                  <div className={`${styles.listHeader} ${buttonStyles}`}>{wordsListHeaderText}</div>
                )}
                <div className={styles.listText}>
                  <ul
                    className={styles.wordList}
                    dangerouslySetInnerHTML={{ __html: wordsListMarkup }}
                  />
                </div>
              </div>
            </div>
          )}
          {currentView !== 'me' && (
            <Box textAlign="center">
              <ButtonGroup
                className={styles.buttonGroup}
                variant="contained"
                disableElevation
                disableRipple
              >
                <Button
                  id="wcs_view_button"
                  disableRipple
                  // eslint-disable-next-line max-len
                  className={`${styles.viewAsWordCloudButton} ${!listView ? styles.buttonSelected : ''} ${buttonStyles}`}
                  onClick={() => setListView(false)}
                  sx={{
                    background: listView
                      ? palette.button.buttonGroupButton.background
                      : palette.button.buttonGroupButton.backgroundSelected,
                    color: listView
                      ? palette.button.buttonGroupButton.color
                      : palette.button.buttonGroupButton.colorSelected,
                    borderColor: listView
                      ? `${palette.button.buttonGroupButton.borderColor} !important`
                      : `${palette.button.buttonGroupButton.borderColorSelected} !important`,
                    '&:hover': {
                      background: listView
                        ? palette.button.buttonGroupButton.background
                        : palette.button.buttonGroupButton.backgroundSelected,
                    },
                    '&:focus': {
                      border: `3px solid ${palette.card.backgroundColor} !important`,
                      boxShadow: 'none',
                    },
                  }}
                >
                  {t('lessonCards.view_as_wordcloud')}
                </Button>
                <Button
                  id="list_view_button"
                  disableRipple
                  className={`${styles.viewAsListButton} ${listView ? styles.buttonSelected : ''} ${buttonStyles}`}
                  onClick={() => setListView(true)}
                  sx={{
                    background: !listView
                      ? palette.button.buttonGroupButton.background
                      : palette.button.buttonGroupButton.backgroundSelected,
                    color: !listView
                      ? palette.button.buttonGroupButton.color
                      : palette.button.buttonGroupButton.colorSelected,
                    borderColor: !listView
                      ? `${palette.button.buttonGroupButton.borderColor} !important`
                      : `${palette.button.buttonGroupButton.borderColorSelected} !important`,
                    '&:hover': {
                      background: !listView
                        ? palette.button.buttonGroupButton.background
                        : palette.button.buttonGroupButton.backgroundSelected,
                    },
                    '&:focus': {
                      border: `3px solid ${palette.card.backgroundColor} !important`,
                      boxShadow: 'none',
                    },
                  }}
                >
                  {t('lessonCards.view_as_list')}
                </Button>
              </ButtonGroup>
            </Box>
          )}
          {displayResults && insights && (
            <div className={styles.boxContainer}>
              <div
                className={styles.boxWordCloud}
                style={{ border: `${palette.border.lightGreyShade}`, color: palette.primary.white }}
              >
                <div className={styles.header} style={{ background: palette.background.wordCloudHeader }}>My Org</div>
                <div aria-hidden>
                  <Wordcloud words={organizationWords} tabPanelId={tabPanelId} insights={insights} />
                </div>
              </div>
              <div
                className={styles.boxWordCloud}
                style={{ border: `${palette.border.lightGreyShade}`, color: palette.primary.white }}
              >
                <div className={styles.header} style={{ background: palette.background.wordCloudHeader }}>Global</div>
                <div aria-hidden>
                  <Wordcloud words={globalWords} tabPanelId={tabPanelId} insights={insights} />
                </div>
              </div>
            </div>
          )}
        </Box>
      </div>
    </CardResponsiveText>
  );
}

export default memo(WordcloudResultCard);
