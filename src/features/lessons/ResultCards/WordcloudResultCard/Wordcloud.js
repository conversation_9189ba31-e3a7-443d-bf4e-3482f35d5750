import { useTheme } from '@mui/material';
import React, { memo } from 'react';
import ReactWordcloud from 'react-wordcloud';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

const Wordcloud = ({ words, tabPanelId, insights }) => {
  const { typography } = useTheme();
  const { isMobile } = useResponsiveMode();

  const callbacks = { getWordTooltip: () => null };
  const wordcloudColors = ['#40405D', '#417505', '#475574', '#0F3088', '#410EB5',
    '#6541B7', '#7E146B', '#C0440F', '#166FD7', '#82164B', '#2E8540', '#CD1E74'];

  return (
    <ReactWordcloud
      id={`tabpanel-${tabPanelId}`}
      aria-labelledby={`tab-${tabPanelId}`}
      data-cy="wordCloud"
      style={{ width: 'calc(100% - 1px)', height: insights ? '9rem' : '12rem' }}
      maxWords={isMobile ? 50 : 70}
      callbacks={callbacks}
      options={{
        rotations: 1,
        rotationAngles: [0, 0],
        fontSizes: [10, 50],
        scale: 'log',
        spiral: 'rectangular',
        colors: wordcloudColors,
        fontFamily: typography.fontFamily,
      }}
      words={words}
    />
  );
};

export default memo(Wordcloud);
