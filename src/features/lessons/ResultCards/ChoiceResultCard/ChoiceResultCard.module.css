.cardContent {
  margin-top: 1.5rem;
}

.answerText {
  font-size: 1.2rem;
  line-height: 1.2;
}

.answerText p {
  margin-bottom: 0.4rem;
}

.insightsAnswerText {
  font-size: 1.1rem;
  line-height: 1.2;
}

.insightsAnswerText p {
  margin: 0;
  margin-bottom: 0.4rem;
}

.choicesSectionWrapper {
  width: 100%;
}

.choicesSection {
  position: relative;
  margin-left: 1.4rem;
  margin: 1rem 0;
  border-radius: 0.5rem;
  padding: 0.6rem 2rem 0.6rem 0.6rem;
}

.organizationIndicator {
  position: relative;
  height: 1.8rem;
  border-radius: 0.5rem;
  margin-bottom: .5rem;
  font-size: 1rem;
  animation: fillToRight 1s;
}

.globalIndicator {
  position: relative;
  height: 1.8rem;
  border-radius: 0.5rem;
  margin-bottom: .5rem;
  font-size: 1rem;
  animation: fillToRight 1s;
}

@keyframes fillToRight {
  0% {
    width: 0;
  }
}

.myAnswerIndicator {
  margin-left: -1.8rem;
  position: absolute;
  top: .6rem;
  right: 0.6rem
}

.percentageText {
  position: absolute;
  top: 0.1rem;
  left: .4rem;
  font-weight: 600;
}

.summaryOuterSection {
  display: flex;
  justify-content: center;
  margin: 0.2rem 0;
  padding-bottom: 0.2rem;
}

.summarySection {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-weight: bold;
  border-radius: 0.25rem;
  font-size: 1rem;
  gap: 0.5rem;
  margin: .2rem 0;
}

.myAnswerSection {
  display: flex;
  justify-content: space-between;
  gap: 0.2rem;
  margin-top: .25rem;
}

.summaryItemIcon {
  line-height: 1px;
}

.summaryItemDivider {
  height: 3rem;
}

.summaryItemText {
  font-size: .8em;
  line-height: 1rem;
  text-align: left;
  width: 'max-content';
  font-weight: 600;
  white-space: nowrap;
}

.summaryTotal {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  font-size: .8em;
  gap: 0.4rem;
}

.summaryTotal :last-child {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
}

.summaryTotal > div > p:first-child {
  margin: 0 0.4rem 0 0;
  font-weight: 600;
}

.markerBox {
  height: 1rem;
  width: 1rem;
  border-radius: 0.2rem;
  align-self: self-start;
  margin-top: 0.25rem;
}

.lightFont {
  font-weight: normal;
}

.insightsCardContent {
  margin-top: 0rem;
  font-size: 1.1rem !important;
}

.insightsCardContent h2 {
   margin-block-start: 0em !important;
}

.correctContainer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.correctIcon {
  height: 1rem !important;
}

.correctText {
  font-weight: 600;
  font-size: 0.8rem;
  padding-left: 0.14rem;
}