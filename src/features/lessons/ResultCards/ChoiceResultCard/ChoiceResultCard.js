/* eslint-disable react/no-danger */
import React, { useEffect } from 'react';
import { get } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material';
import { RESPONSE_THRESHOLD_DEFAULT } from '../../../../config/constants';
import { useLessonCardResults } from '../../../../hooks/useLessonCardResults';
import { useNumberFormat } from '../../../../hooks/useNumberFormat';
import { NotEnoughResults } from '../../../../components/NotEnoughResults/NotEnoughResults';
import UserSquareIcon from '../../../../images/user-square-solid.svg';
import CheckCircle from '../../../../icons/CheckCircle';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import Tooltip from '../../../../components/Tooltip/Tooltip';
import Spinner from '../../../../components/Spinner/Spinner';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useUser } from '../../../../hooks/useUser';
import styles from './ChoiceResultCard.module.css';

export default function ChoiceResultCard(props) {
  const { id, description, title, fontColor, multipleChoices, choices, answer, responseThreshold,
    organization, global, insights, lessonLifecycle } = props;

  const user = useUser();
  const { palette } = useTheme();
  const { t } = useTranslation();
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { loading, data } = !insights && useLessonCardResults({ lessonCardId: id, lessonLifecycle });
  const { formatReportTotal } = useNumberFormat();
  const { isSmallMobile } = useResponsiveMode();
  const cardContent = insights ? styles.insightsCardContent : styles.cardContent;
  const answerText = insights ? styles.insightsAnswerText : styles.answerText;

  const singleChoiceResults = !!choices;
  const mappedChoices = singleChoiceResults ? choices.map(({ text }) => text) : multipleChoices;

  // if an sso admin is previewing the lesson, the relevant account is user.accounts[1]
  const accountIndex = get(user, 'accounts[1]') ? 1 : 0;
  const hideOrg = !insights ? get(user, `accounts[${accountIndex}].hideOrg`, false) : false;

  const lessonCardFocusElement = document.getElementById('lesson-card-focus-element');
  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (!loading && lessonCardFocusElement && contentContainerElement) {
      lessonCardFocusElement.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [loading, lessonCardFocusElement, contentContainerElement]);

  // This function calculates the left padding for percentages less than 6%.
  // In these cases, the number bar was so small that it was overlapping the number.
  // This shift the number to the right of the bar.
  const calculateSmallLeftPadding = (percentage) => {
    return 0.8 + (0.2 * (percentage - 1));
  };

  if (loading) {
    return (
      <Spinner />
    );
  }

  const reportDataKey = singleChoiceResults ? 'singleChoiceReportData' : 'multiChoiceReportData';
  const organizationData = organization ? get(organization, `${reportDataKey}`)
    : get(data, `getResultsCard.organization.${reportDataKey}`);
  const globalData = global ? get(global, `${reportDataKey}`) : get(data, `getResultsCard.global.${reportDataKey}`);

  const organizationTotal = get(organizationData, 'total') || 0;
  const globalTotal = get(globalData, 'total') || 0;
  const adjustedReponseThreshold = responseThreshold || RESPONSE_THRESHOLD_DEFAULT;
  let displayResults = organizationTotal >= adjustedReponseThreshold && globalTotal >= adjustedReponseThreshold;
  if (lessonLifecycle === 'preview' && organizationTotal && globalTotal) {
    displayResults = true;
  }

  const mergedChoices = mappedChoices.map((choice, i) => ({
    html: choice,
    orgPercentage: get(organizationData, `percentages[${i}]`),
    globalPercentage: get(globalData, `percentages[${i}]`),
    myAnswer: singleChoiceResults
      ? answer.singleChoiceAnswer === (i + 1)
      : answer.multipleChoiceAnswer[`answer${i + 1}`],
    correct: choices && choices[i] && choices[i].gatedChoice,
  }));

  return (
    <CardResponsiveText
      containerStyle={cardContent}
      key={`ChoiceResultCard-${id}`}
    >
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      {!displayResults && (
        <NotEnoughResults insights={insights} />
      )}
      {displayResults && (
        <>
          <div
            className={styles.summaryOuterSection}
            style={{
              marginBottom: (isSmallMobile || insights) ? '2.25rem' : 0,
              borderTop: palette.border.myAnswer,
              borderBottom: palette.border.myAnswer }}
          >
            <div className={styles.summarySection}>
              <div className={styles.summaryContainer}>
                {!hideOrg && (
                  <div className={styles.summaryTotal}>
                    <div className={styles.markerBox} style={{ background: palette.background.pink }} />
                    <div>
                      <Tooltip
                        title={t('lessonCards.myOrg_tooltip')}
                        tabIndex="0"
                        role="tooltip"
                        // aria-describedby="myOrgResponse"
                      >
                        <p>{t('lessonCards.myOrg')}</p>
                      </Tooltip>
                      <p className={styles.lightFont} data-cy="myOrgResponse">
                        {/* eslint-disable-next-line max-len */}
                        {`(${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')})`}
                      </p>
                    </div>
                  </div>
                )}
                <div className={styles.summaryTotal}>
                  <div
                    className={styles.markerBox}
                    style={{
                      background: hideOrg ? palette.background.pink : palette.background.lighterPink,
                    }}
                  />
                  <div>
                    <Tooltip
                      title={t('lessonCards.global_tooltip')}
                      tabIndex="0"
                      role="tooltip"
                      /// aria-describedby="globalOrgResponse"
                    >
                      <p>{t('lessonCards.global')}</p>
                    </Tooltip>
                    <p className={styles.lightFont} data-cy="globalOrgResponse">
                      {/* eslint-disable-next-line max-len */}
                      {`(${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')})`}
                    </p>
                  </div>
                </div>
              </div>
              {!insights && (
              <div aria-hidden className={styles.myAnswerSection}>
                <p className={styles.summaryItemText}>{`${t('lessonCards.my')}\n${t('lessonCards.answer')}`}</p>
                <div className={styles.summaryItemIcon}>
                  <UserSquareIcon />
                </div>
              </div>
              )}
            </div>
          </div>
          <div className={styles.choicesSectionWrapper}>
            {
              mergedChoices.map(({ html, orgPercentage, globalPercentage, myAnswer, correct }, i) => {
                return (
                  <div
                    // eslint-disable-next-line react/no-array-index-key
                    key={`choicesList-${i}`}
                    className={styles.choicesSection}
                    style={{ backgroundColor: palette.background.choiceResultsSection }}
                    data-cy={`choicesList-${i}`}
                  >
                    {!insights && myAnswer && (
                      <div className={styles.myAnswerIndicator} data-cy="myAnswer">
                        <UserSquareIcon aria-label={t('lessonCards.my_answer')} />
                      </div>
                    )}
                    <div
                      className={answerText}
                      dangerouslySetInnerHTML={{ __html: html }}
                      data-cy={`choice-${i}`}
                    />
                    {!hideOrg && (
                      <div
                        className={styles.organizationIndicator}
                        style={{ width: `${orgPercentage}%`, background: palette.background.pink }}
                        data-cy={`myOrgIndicator-${i}`}
                      >
                        <div
                          className={styles.percentageText}
                          style={{
                            // eslint-disable-next-line no-nested-ternary
                            left: orgPercentage === 0 ? '0' :
                              (orgPercentage < 7 ? `${calculateSmallLeftPadding(orgPercentage)}rem` : ''),
                            color: palette.primary.dark,
                          }}
                        >
                          {`${orgPercentage}%`}
                          <span className="srOnly"> {t('lessonCards.myOrg')}</span>
                        </div>
                      </div>
                    )}
                    <div
                      className={styles.globalIndicator}
                      style={{
                        width: `${globalPercentage}%`,
                        background: hideOrg ? palette.background.pink : palette.background.lighterPink,
                      }}
                      data-cy={`globalIndicator-${i}`}
                    >
                      <div
                        className={styles.percentageText}
                        style={{
                          // eslint-disable-next-line no-nested-ternary
                          left: globalPercentage === 0 ? '0' :
                            (globalPercentage < 7 ? `${calculateSmallLeftPadding(globalPercentage)}rem` : ''),
                          color: palette.primary.dark,
                        }}
                      >
                        {`${globalPercentage}%`}
                        <span className="srOnly"> {t('lessonCards.global')}</span>
                      </div>
                    </div>
                    {correct && (
                      <div style={{ color: palette.primary.dark }} className={styles.correctContainer}>
                        <CheckCircle style={{ color: palette.primary.dark }} className={styles.correctIcon} />
                        <div className={styles.correctText}>Correct Answer</div>
                      </div>
                    )}
                  </div>
                );
              })
            }
          </div>
        </>
      )}
    </CardResponsiveText>
  );
}
