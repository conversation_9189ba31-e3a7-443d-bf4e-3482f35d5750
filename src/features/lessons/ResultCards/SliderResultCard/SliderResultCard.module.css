.cardContent {
  margin-top: 1.5rem;
  padding-bottom: 1rem;
}

.spacer {
  clear: both;
  height: 1rem;
}

.resultsBar {
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: row;
  width: 100%;
  border-radius: .5rem;
  transform-origin: 50% 50%;
  animation: fadeUp 0.5s;
}

@keyframes fadeUp {
  0% { opacity: 0; transform: scaleX(0); }
  100% { opacity: 1; transform: scaleX(1); }
}

.resultsBarHeadingRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  align-items: center;
  height: 1.875rem
}

.resultsBarHeading {
  flex: 1 1 0;
  font-size: .6em;
  text-align: center;
  line-height: 1.2;
}

.resultsBarSegmentKey {
  position: relative;
  display: flex;
  justify-content: center;
  height: .4rem;
  flex-grow: 1;
  animation: fadeIn 5s;
  z-index: 1;
  margin: 0 .3rem 0 .3rem;
}

.myAnswerIndicator {
  position: absolute;
  top: 60%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.segmentLabel {
  font-size: 1em;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

.segmentLabelTotal {
  font-size: .75em;
  text-align: center;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.resultsBarSegment {
  position: relative;
  display: flex;
  justify-content: center;
  height: 2.8rem;
  flex-grow: 1;
}

.resultsBarSegmentText {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: .6em;
  font-weight: bold;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to   { opacity: 1; }
}

.showMoreText {
  width: 100%;
  text-align: center;

}

.showMoreTextButton {
  display: inline-flex;
  padding: .1rem 1rem;
  border-radius: 1rem;
  font-size: .75em;
  font-weight: normal;
  cursor: pointer;
}

.showMoreTextButton:hover {
  font-weight: 600;
}

.verticalLineOuter {
  position:relative;
  text-align:center;
}

.verticalLine {
  position: absolute;
  top: 0;
  left: 50%;
  border-left: 1px solid;
  height: 100%;
}

.myChoiceContainer {
  display: flex;
  justify-content: left;
}

.myChoiceContainer span {
  font-size: 0.75em;
  margin-left: 0.5rem;
}

.myAnswerSection {
  display: flex;
  justify-content: space-between;
  width: 100%;
  justify-content: right;
  margin-bottom: .8rem;
}

.buttonGroup {
  height: 2.2rem;
  border-radius: 5rem;
  border: none;
}

.graphButton, .tableButton {
  border: 1px solid;
  text-transform: none;
  padding-top: 0;
  padding-bottom: 0;
}

.graphButton {
  border-top-left-radius: 5rem;
  border-bottom-left-radius: 5rem;
}

.tableButton {
  border-top-right-radius: 5rem;
  border-bottom-right-radius: 5rem;
}

.buttonSelected {
  font-weight: 700;
  text-transform: none;
}

.tableContainer {
  border: 1px solid;
  padding: 0;
  border-radius: 0.25rem;
}

.table {
  width: 100%;
  border: 0;
  margin: 0;
  border-collapse: collapse;
}

.iconTable {
  width: 100%;
  border: 0;
  margin: 0;
  padding: 0;
  border-collapse: collapse;  
}

.tableTh {
  padding: 0.5rem 0;
  vertical-align: middle;
  font-weight: normal;
}

.iconTableTh {
  padding: 0;
  vertical-align: middle;
}

.globalTd {
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  padding: 0.25rem;
}

.iconTableTh {
  vertical-align: bottom;
  padding: 0;
}

.orgTd {
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  padding: 0.25rem; 
}

/* table view summary section */
.summaryOuterSection {
  display: flex;
  justify-content: center;
  margin: 1.7rem 0 0 0 !important;
  padding-bottom: 0.2rem;
}

.summarySection {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-weight: bold;
  border-radius: 0.25rem;
  font-size: 1rem;
  gap: 0.5rem;
  margin: 0;
}

.summaryMyAnswerSection {
  display: flex;
  justify-content: space-between;
  gap: 0.2rem;
  margin-top: .25rem;
}

.summaryItemIcon {
  line-height: 1px;
}

.summaryItemDivider {
  height: 3rem;
}

.summaryItemText {
  font-size: .8em;
  line-height: 1rem;
  text-align: left;
  width: 'max-content';
  font-weight: 600;
  white-space: nowrap;
}

.summaryTotal {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  font-size: .8em;
  gap: 0.4rem;
}

.summaryTotal :last-child {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
}

.summaryTotal > div > p:first-child {
  margin: 0 0.4rem 0 0;
  font-weight: 600;
}

.markerBox {
  height: 1rem;
  width: 1rem;
  border-radius: 0.2rem;
  align-self: self-start;
  margin-top: 0.25rem;
}

.lightFont {
  font-weight: normal;
}

.floatingIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
  z-index: 2;
  top: 0.45rem;
}

.insightsCardContent {
  margin-top: 0rem !important;
  font-size: 1.1rem !important;
  padding-bottom: 1rem;
}

.insightsCardContent h2 {
   margin-block-start: 0em !important;
}

