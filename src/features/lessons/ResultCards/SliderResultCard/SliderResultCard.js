/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/no-danger */
import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { useTranslation } from 'react-i18next';
import { Box, useTheme, ButtonGroup, Button } from '@mui/material';
import { RESPONSE_THRESHOLD_DEFAULT } from '../../../../config/constants';
import { useNumberFormat } from '../../../../hooks/useNumberFormat';
import { NotEnoughResults } from '../../../../components/NotEnoughResults/NotEnoughResults';
import { useLessonCardResults } from '../../../../hooks/useLessonCardResults';
import { useUser } from '../../../../hooks/useUser';
import { SliderResultsText } from '../../../../hooks/useScreenReaderText';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import UserSquareIcon from '../../../../images/user-square-solid.svg';
import MyAnswerContainer from '../MyAnswerContainer';
import Tooltip from '../../../../components/Tooltip/Tooltip';
import Spinner from '../../../../components/Spinner/Spinner';
import styles from './SliderResultCard.module.css';

const resultBarPaddings = ['0 10% 0 10%', '0 20% 0 20%', '0 25% 0 25%'];

const VerticalLine = () => (
  <Box className={styles.verticalLineOuter} sx={{ height: '2.5rem' }}>
    <Box className={styles.verticalLine} sx={{ color: 'primary.greyPurple' }} />
  </Box>
);

export default function SliderResultCard(props) {
  const { id, description, title, fontColor, sliderMax, labelText, answer, responseThreshold,
    insights, organization, global, lessonLifecycle } = props;

  const [detailedView, setDetailedView] = useState(false);
  const [graphView, setGraphView] = useState(!insights && true);
  const { palette } = useTheme();
  const { t } = useTranslation();
  const user = useUser();
  const { formatReportTotal } = useNumberFormat();
  const { isSmallMobile, isMobile, isTablet, isLaptop } = useResponsiveMode();
  const cardContent = insights ? styles.insightsCardContent : styles.cardContent;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { loading, data } = !insights && useLessonCardResults({ lessonCardId: id, lessonLifecycle });

  // if an sso admin is previewing the lesson, the relevant account is user.accounts[1]
  const accountIndex = get(user, 'accounts[1]') ? 1 : 0;
  const hideOrg = !insights ? get(user, `accounts[${accountIndex}].hideOrg`, false) : false;

  const lessonCardFocusElement = document.getElementById('lesson-card-focus-element');
  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (!loading && lessonCardFocusElement && contentContainerElement) {
      lessonCardFocusElement.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [loading, lessonCardFocusElement, contentContainerElement]);

  if (loading) {
    return (
      <Spinner />
    );
  }

  const resultBarPadding = isMobile ? resultBarPaddings[0] :
    isTablet || isLaptop ? resultBarPaddings[1] : resultBarPaddings[2];
  const getResultsBarColors = (maxNumber) => {
    switch (maxNumber) {
      case 2:
      case 8:
      case 10:
        return palette.background.slider.slice(0, 1).concat(palette.background.slider.slice(6));
      case 3:
      case 9:
      case 11:
        return palette.background.slider.slice(0, 1)
          .concat(palette.background.slider.slice(3, 4)).concat(palette.background.slider.slice(6));
      case 4:
        return palette.background.slider.slice(0, 2).concat(palette.background.slider.slice(5));
      case 5:
        return palette.background.slider.slice(0, 2)
          .concat(palette.background.slider.slice(3, 4)).concat(palette.background.slider.slice(5));
      case 6:
        return palette.background.slider.slice(0, 3).concat(palette.background.slider.slice(4));
      case 7:
      default:
        return palette.background.slider;
    }
  };

  const getResultsBarSummaryColors = (maxNumber) => {
    if (maxNumber % 2 === 0) {
      return palette.background.slider.slice(0, 1).concat(palette.background.slider.slice(6));
    }
    return palette.background.slider.slice(0, 1)
      .concat(palette.background.slider.slice(3, 4)).concat(palette.background.slider.slice(6));
  };

  const getSummaryLabels = (allLabels, maxNumber) => {
    if (allLabels.length <= 3) {
      return allLabels;
    }
    if (maxNumber % 2 === 0) {
      return allLabels.slice(1, 2).concat(allLabels.slice(allLabels.length - 2, allLabels.length - 1));
    }
    return allLabels.slice(1, 2)
      .concat(allLabels.slice(
        Math.floor(allLabels.length / 2), Math.round(allLabels.length / 2)))
      .concat(allLabels.slice(allLabels.length - 2, allLabels.length - 1));
  };

  const getCompressedData = (originalData) => {
    let compressedData = (originalData.length % 2 === 0) ? [0, 0] : [0, 0, 0];
    compressedData = originalData.reduce((acc, current, index) => {
      if (current.percentage === 0) {
        return acc;
      }
      if (originalData.length % 2 !== 0 && index === Math.floor(originalData.length / 2)) {
        acc[1] = (Math.round(current.percentage * 10) / 10);
      } else if (index < Math.floor(originalData.length / 2)) {
        const percentage = acc[0] + current.percentage;
        acc[0] = (Math.round(percentage * 10) / 10);
      } else {
        const percentage = acc[compressedData.length - 1] + current.percentage;
        acc[compressedData.length - 1] = (Math.round(percentage * 10) / 10);
      }
      return acc;
    }, compressedData);
    return compressedData;
  };

  const getDataPaddingPercentages = (compressedData) => {
    const paddingPercentages = [];
    if (compressedData.length === 2) {
      paddingPercentages.push(compressedData[1] / 2);
      paddingPercentages.push(compressedData[0] / 2);
    } else if (compressedData.length === 3) {
      paddingPercentages.push((compressedData[2] + compressedData[1] / 2) / 2);
      paddingPercentages.push((compressedData[0] + compressedData[1] / 2) / 2);
    }
    return paddingPercentages;
  };

  const getMyAnswerSummaryIndex = () => {
    if (!answer.sliderAnswer) return -1;
    if (answer.sliderAnswer < ((sliderMax + 1) / 2)) {
      return 0;
    }
    if (answer.sliderAnswer > ((sliderMax + 1) / 2)) {
      return sliderMax % 2 === 0 ? 1 : 2;
    }
    return 1;
  };

  const getFirstNonZeroIndex = (dataArray, dataField) => {
    return dataField ?
      dataArray.findIndex((entry) => entry[dataField] !== 0) :
      dataArray.findIndex((entry) => entry !== 0);
  };

  const getLastNonZeroIndex = (dataArray, dataField) => {
    const reverseArray = [...dataArray].reverse();
    const firstIndexReversed = dataField ?
      reverseArray.findIndex((entry) => entry[dataField] !== 0) :
      reverseArray.findIndex((entry) => entry !== 0);
    if (firstIndexReversed === -1) {
      return -1;
    }
    return (dataArray.length - 1) - firstIndexReversed;
  };

  const organizationData = insights
    ? organization.sliderReportData
    : get(data, 'getResultsCard.organization.sliderReportData');
  const globalData = insights ? global.sliderReportData : get(data, 'getResultsCard.global.sliderReportData');

  const organizationTotal = get(organizationData, 'total') || 0;
  const globalTotal = get(globalData, 'total') || 0;
  const adjustedReponseThreshold = responseThreshold || RESPONSE_THRESHOLD_DEFAULT;
  let displayResults = organizationTotal >= adjustedReponseThreshold && globalTotal >= adjustedReponseThreshold;
  if (lessonLifecycle === 'preview' && organizationTotal && globalTotal) {
    displayResults = true;
  }

  const compressedOrganizationData = getCompressedData(organizationData.percentages);
  const compressedGlobalData = getCompressedData(globalData.percentages);
  const resultsBarColors = getResultsBarColors(sliderMax);
  const fullResultsBarColorPalette = palette.background.slider;
  const resultsBarSummaryColors = getResultsBarSummaryColors(sliderMax);
  const summaryLabels = getSummaryLabels(labelText, sliderMax);
  const orgDataPaddingPercentages = getDataPaddingPercentages(compressedOrganizationData);
  const globalDataPaddingPercentages = getDataPaddingPercentages(compressedGlobalData);
  const myAnswerSummaryIndex = getMyAnswerSummaryIndex();

  const barBorderLeft = `solid 1px ${palette.primary.white}`;
  // These calculations are required to determine the border radius on data segments
  const radiusFirstBar = '.5rem 0 0 .5rem';
  const radiusLastBar = '0 .5rem .5rem 0';
  const radiusBoth = '.5rem';
  const orgDataFirstNonZeroIndex = getFirstNonZeroIndex(organizationData.percentages, 'percentage');
  const globalDataFirstNonZeroIndex = getFirstNonZeroIndex(globalData.percentages, 'percentage');
  const compressedOrgDataFirstNonZeroIndex = getFirstNonZeroIndex(compressedOrganizationData);
  const compressedGlobalDataFirstNonZeroIndex = getFirstNonZeroIndex(compressedGlobalData);

  const orgDataLasttNonZeroIndex = getLastNonZeroIndex(organizationData.percentages, 'percentage');
  const globalDataLastNonZeroIndex = getLastNonZeroIndex(globalData.percentages, 'percentage');
  const compressedOrgDataLastNonZeroIndex = getLastNonZeroIndex(compressedOrganizationData);
  const compressedGlobalDataLastNonZeroIndex = getLastNonZeroIndex(compressedGlobalData);

  const renderTableHeader = () => {
    return labelText.map((label, index) => {
      return (
        <th
          width={`${Math.floor(100 / labelText.length)}%`}
          scope="row"
          key={`header-${index}`}
          id={`header-${index}`}
          data-cy={`header-${index}`}
          className={styles.tableTh}
          style={{ lineHeight: (isSmallMobile || insights) ? '0.7rem' : '0.9rem' }}
        >
          {label}
        </th>
      );
    });
  };

  const renderTableCells = (isOrg) => {
    if (!hideOrg && isOrg) {
      return organizationData.percentages.map((item) => {
        return (
          <td
            className={styles.orgTd}
            key={`org-td-${item.response}`}
            id={`org-td-${item.response}`}
            data-cy={`org-td-${item.response}`}
            style={{ background: palette.resultsTable.darkCellBackground, color: palette.resultsTable.darkCellColor }}
          >
            {item.percentage}%
          </td>
        );
      });
    }
    return globalData.percentages.map((item) => {
      return (
        <td
          className={styles.orgTd}
          key={`global-td-${item.response}`}
          id={`global-td-${item.response}`}
          data-cy={`global-td-${item.response}`}
          style={{ background: palette.resultsTable.lightCellBackground, color: palette.resultsTable.lightCellColor }}
        >
          {item.percentage}%
        </td>
      );
    });
  };
  const renderIconTableHeader = () => {
    return labelText.map((label, index) => {
      if ((index + 1) === answer.sliderAnswer && !insights) {
        return (
          <th
            width={`${Math.floor(100 / labelText.length)}%`}
            key={`icon-${index}`}
            id={`icon-${index}`}
            data-cy={`icon-${index}`}
            className={styles.iconTableTh}
          >
            <UserSquareIcon aria-hidden className={styles.floatingIcon} />
          </th>
        );
      }
      return (
        <th
          width={`${Math.floor(100 / labelText.length)}%`}
          key={`icon-${index}`}
          id={`icon-${index}`}
          data-cy={`icon-${index}`}
          className={styles.iconTableTh}
        >
          &nbsp;
        </th>
      );
    });
  };
  return (
    <CardResponsiveText containerStyle={cardContent} key={`SliderResultCard-${id}`}>
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      {!displayResults && (
        <NotEnoughResults insights={insights} />
      )}
      <div id="myOrg_tooltip_text" aria-hidden className="sr-only">{t('lessonCards.myOrg_tooltip')}</div>
      <div id="global_tooltip_text" aria-hidden className="sr-only">{t('lessonCards.global_tooltip')}</div>
      {displayResults && (
        <>
          <SliderResultsText
            t={t}
            sliderMax={sliderMax}
            labelText={labelText}
            answer={answer}
            hideOrg={hideOrg}
            organizationData={organizationData}
            globalData={globalData}
          />
          {!insights && (
          <div aria-hidden className={graphView ? styles.myAnswerSection : ''}>
            <ButtonGroup
              className={styles.buttonGroup}
              variant="contained"
              disableElevation
              disableRipple
            >
              <Button
                tabIndex={0}
                disableRipple
                className={`${styles.graphButton} ${graphView ? styles.buttonSelected : ''}`}
                onClick={() => setGraphView(true)}
                sx={{
                  background: !graphView
                    ? palette.button.buttonGroupButton.background
                    : palette.button.buttonGroupButton.backgroundSelected,
                  color: !graphView
                    ? palette.button.buttonGroupButton.color
                    : palette.button.buttonGroupButton.colorSelected,
                  borderColor: !graphView
                    ? `${palette.button.buttonGroupButton.borderColor} !important`
                    : `${palette.button.buttonGroupButton.borderColorSelected} !important`,
                  '&:hover': {
                    background: !graphView
                      ? palette.button.buttonGroupButton.background
                      : palette.button.buttonGroupButton.backgroundSelected,
                  },
                  '&:focus': {
                    border: `3px solid ${palette.card.backgroundColor} !important`,
                    boxShadow: 'none',
                  },
                }}
              >
                {t('lessonCards.graph')}
              </Button>
              <Button
                tabIndex={0}
                disableRipple
                className={`${styles.tableButton} ${!graphView ? styles.buttonSelected : ''}`}
                onClick={() => setGraphView(false)}
                sx={{
                  background: graphView
                    ? palette.button.buttonGroupButton.background
                    : palette.button.buttonGroupButton.backgroundSelected,
                  color: graphView
                    ? palette.button.buttonGroupButton.color
                    : palette.button.buttonGroupButton.colorSelected,
                  borderColor: graphView
                    ? `${palette.button.buttonGroupButton.borderColor} !important`
                    : `${palette.button.buttonGroupButton.borderColorSelected} !important`,
                  '&:hover': {
                    background: graphView
                      ? palette.button.buttonGroupButton.background
                      : palette.button.buttonGroupButton.backgroundSelected,
                  },
                  '&:focus': {
                    border: `3px solid ${palette.card.backgroundColor} !important`,
                    boxShadow: 'none',
                  },
                }}
              >
                {t('lessonCards.table')}
              </Button>
            </ButtonGroup>
            {graphView && (
              <MyAnswerContainer />
            )}
          </div>
          )}
          {!graphView && summaryLabels && compressedGlobalData && (
            <div aria-hidden>
              {/* results table summary */}
              <div
                aria-hidden
                className={styles.summaryOuterSection}
                style={{
                  marginBottom: (isSmallMobile) ? '2.25rem' : 0,
                  borderTop: palette.border.myAnswer,
                  borderBottom: palette.border.myAnswer }}
              >
                <div className={styles.summarySection}>
                  <div className={styles.summaryContainer}>
                    {!hideOrg && (
                      <div className={styles.summaryTotal}>
                        <div className={styles.markerBox} style={{ background: palette.markerBox.darkGrey }} />
                        <div>
                          {/* <Tooltip
                            title={t('lessonCards.myOrg_tooltip')}
                            tabIndex="0"
                            role="tooltip"
                            // aria-describedby="myOrgResponse"
                            aria-hidden
                          >
                            <p>{t('lessonCards.myOrg')}</p>
                          </Tooltip> */}
                          <p>{t('lessonCards.myOrg')}</p>
                          <p className={styles.lightFont} data-cy="myOrgResponse">
                            {/* eslint-disable-next-line max-len */}
                            {`(${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')})`}
                          </p>
                        </div>
                      </div>
                    )}
                    <div className={styles.summaryTotal}>
                      <div
                        className={styles.markerBox}
                        style={{
                          background: palette.markerBox.lightGrey,
                        }}
                      />
                      <div>
                        {/* <Tooltip
                          title={t('lessonCards.global_tooltip')}
                          tabIndex="0"
                          role="tooltip"
                          // aria-describedby="globalOrgResponse"
                          aria-hidden
                        >
                          <p>{t('lessonCards.global')}</p>
                        </Tooltip> */}
                        <p>{t('lessonCards.global')}</p>
                        <p className={styles.lightFont} data-cy="globalOrgResponse">
                          {/* eslint-disable-next-line max-len */}
                          {`(${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')})`}
                        </p>
                      </div>
                    </div>
                  </div>
                  {!insights && (
                  <div className={styles.summaryMyAnswerSection}>
                    <p className={styles.summaryItemText}>{`${t('lessonCards.my')}\n${t('lessonCards.answer')}`}</p>
                    <div className={styles.summaryItemIcon}>
                      <UserSquareIcon aria-hidden />
                    </div>
                  </div>
                  )}
                </div>
              </div>
              {/* results table */}
              <table aria-hidden className={styles.iconTable}>
                <thead>
                  <tr>
                    {answer && answer.sliderAnswer && (
                      renderIconTableHeader()
                    )}
                  </tr>
                </thead>
              </table>
              <div
                className={styles.tableContainer}
                style={{ borderColor: palette.resultsTable.borderColor }}
              >
                <table
                  className={styles.table}
                  style={{ fontSize: (isSmallMobile || insights) ? '0.5rem' : '0.75rem' }}
                >
                  <thead>
                    <tr>
                      {renderTableHeader(summaryLabels)}
                    </tr>
                  </thead>
                  <tbody>
                    <tr style={{ borderTop: '1px solid #B2B4CA' }}>
                      {renderTableCells(true)}
                    </tr>
                    {!hideOrg && (
                      <tr style={{ borderTop: '1px solid #B2B4CA' }}>
                        {renderTableCells()}
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
          {graphView && (
            <>
              <div
                aria-hidden
                className={styles.resultsBarHeadingRow}
                style={{ padding: `${detailedView ? '' : resultBarPadding}` }}
              >
                {detailedView && labelText.map((nextHeading, index) => (
                  /** If the view is mobile, only return the labels at the ends and the middle */
                  <div
                    key={`headings-${index}`}
                    className={styles.resultsBarHeading}
                    style={{ color: palette.primary.dark }}
                    data-cy={`headings-${index}`}
                    id={`headings-${index}`}
                  >
                    {!isMobile || index === 0 || index === labelText.length - 1 ||
                      (!!(labelText.length % 2) && index === (labelText.length - 1) / 2) ?
                      nextHeading : ''}
                  </div>
                ),
                )}
                {!detailedView && summaryLabels.map((nextHeading, index) => (
                  <div
                    key={`headings-${index}`}
                    className={styles.resultsBarHeading}
                    style={{ color: palette.primary.dark }}
                    data-cy={`headings-${index}`}
                    id={`headings-${index}`}
                  >
                    {nextHeading}
                  </div>
                ),
                )}
              </div>
              <div
                className={styles.resultsBar}
                style={{ padding: `${detailedView ? '' : resultBarPadding}` }}
              >
                {detailedView && resultsBarColors.map((nextBar, index) => (
                  <div
                    key={`headingColors-${index}`}
                    className={styles.resultsBarSegmentKey}
                    style={{ backgroundColor: nextBar }}
                    data-cy={`headingColors-${index}`}
                  />
                ))}
                {!detailedView && resultsBarSummaryColors.map((nextBar, index) => (
                  <div
                    key={`headingColors-${index}`}
                    className={styles.resultsBarSegmentKey}
                    style={{ backgroundColor: nextBar }}
                    data-cy={`headingColors-${index}`}
                  >
                    {index === myAnswerSummaryIndex ? (
                      <div
                        className={styles.myAnswerIndicator}
                        style={{ left: '50%' }}
                        data-cy="myAnswer"
                      >
                        <UserSquareIcon aria-hidden />
                      </div>
                    ) : ''}
                  </div>
                ))}
                {detailedView && answer.sliderAnswer && (
                  <div
                    className={styles.myAnswerIndicator}
                    style={{ left: `${((answer.sliderAnswer - 0.5) / sliderMax) * 100}%` }}
                    data-cy="myAnswer1"
                  >
                    <UserSquareIcon aria-hidden />
                  </div>
                )}
              </div>
            </>
          )}
          {graphView && !hideOrg && (
            <>
              <VerticalLine />
              <Tooltip
                title={t('lessonCards.myOrg_tooltip')}
                role="tooltip"
                style={{ outline: 'unset' }}
                aria-describedby="myOrg_tooltip_text"
              >
                <div>
                  <div
                    className={styles.segmentLabel}
                    style={{ color: palette.primary.dark }}
                  >
                    {t('lessonCards.myOrg')}
                  </div>
                  <div className={styles.segmentLabelTotal} data-cy="myOrgResponse">
                    {/* eslint-disable-next-line max-len */}
                    {`${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`}
                  </div>
                </div>
              </Tooltip>
            </>
          )}
        </>
      )}
      {graphView && displayResults && detailedView && !hideOrg && (
        <div
          className={styles.resultsBar}
          style={{ padding: `0 ${orgDataPaddingPercentages[1]}% 0 ${orgDataPaddingPercentages[0]}%` }}
        >
          {resultsBarColors.map((nextBar, index) => {
            const percentage = get(organizationData, `percentages[${index}].percentage`) || 0;
            let radius = '';
            if (index === orgDataFirstNonZeroIndex && index === orgDataLasttNonZeroIndex) {
              radius = radiusBoth;
            } else if (index === orgDataFirstNonZeroIndex) {
              radius = radiusFirstBar;
            } else if (index === orgDataLasttNonZeroIndex) {
              radius = radiusLastBar;
            }
            /* let animationClassname = '';
            if (radius) {
              animationClassname = 'barAnimation';
            } */
            return (
              <Tooltip
                key={`orgResults-${index}`}
                title={(isMobile && percentage < 14) || (!isMobile && percentage < 8) ? `${percentage}%` : ''}
                role="tooltip"
                aria-describedby={`headings-${index}`}
              >
                <div
                  className={`${styles.resultsBarSegment} ${styles.barAnimation}`}
                  style={{
                    backgroundColor: nextBar,
                    flexGrow: percentage,
                    borderRadius: `${radius}`,
                    borderLeft: index === 0 ? '' : barBorderLeft,
                  }}
                  data-cy={`orgResults-${index}`}
                >
                  <div
                    className={styles.resultsBarSegmentText}
                    style={{
                      display: percentage === 0 ? 'none' : 'inline-block',
                      // eslint-disable-next-line max-len
                      color: palette.primary.dark,
                      fontWeight: 600,
                    }}
                    aria-label={`${percentage}% ${t('lessonCards.org_customers')} ${labelText[index]}`}
                  >
                    {(isMobile && percentage < 14) || (!isMobile && percentage < 8) ? '' : `${percentage}%`}
                  </div>
                </div>
              </Tooltip>
            );
          })}
        </div>
      )}
      {graphView && displayResults && !detailedView && !hideOrg && (
        <div
          className={styles.resultsBar}
          style={{ padding: `0 ${orgDataPaddingPercentages[1]}% 0 ${orgDataPaddingPercentages[0]}%` }}
        >
          {compressedOrganizationData.map((nextData, index) => {
            let backgroundColor = fullResultsBarColorPalette[0];
            if (index === 1 && compressedOrganizationData.length === 3) {
              backgroundColor = fullResultsBarColorPalette[3];
            } else if (index !== 0) {
              backgroundColor = fullResultsBarColorPalette[6];
            }
            let radius = '';
            if (index === compressedOrgDataFirstNonZeroIndex && index === compressedOrgDataLastNonZeroIndex) {
              radius = radiusBoth;
            } else if (index === compressedOrgDataFirstNonZeroIndex) {
              radius = radiusFirstBar;
            } else if (index === compressedOrgDataLastNonZeroIndex) {
              radius = radiusLastBar;
            }
            return (
              <Tooltip
                key={`orgLessResults-${index}`}
                title={(isMobile && nextData < 14) || (!isMobile && nextData < 8) ? `${nextData}%` : ''}
                role="tooltip"
                aria-describedby={`headings-${index}`}
              >
                <div
                  className={styles.resultsBarSegment}
                  style={{
                    backgroundColor,
                    flexGrow: nextData,
                    borderRadius: `${radius}`,
                    borderLeft: index === 0 ? '' : barBorderLeft,
                  }}
                  data-cy={`orgLessResults-${index}`}
                >
                  <div
                    className={styles.resultsBarSegmentText}
                    style={{
                      display: nextData === 0 ? 'none' : 'inline-block',
                      color: palette.primary.dark,
                      fontWeight: 600,
                    }}
                    aria-label={`${nextData}% ${t('lessonCards.org_customers')} ${summaryLabels[index]}`}
                  >
                    {(isMobile && nextData < 14) || (!isMobile && nextData < 8) ? '' : `${nextData}%`}
                  </div>
                </div>
              </Tooltip>
            );
          })}
        </div>
      )}
      {graphView && displayResults && (
        <>
          <VerticalLine />
          <Tooltip
            title={t('lessonCards.global_tooltip')}
            role="tooltip"
            style={{ outline: 'unset' }}
            aria-describedby="global_tooltip_text"
          >
            <div>
              <div
                className={styles.segmentLabel}
                style={{ color: palette.primary.dark }}
              >
                {t('lessonCards.global')}
              </div>
              <div className={styles.segmentLabelTotal} data-cy="globalResponse">
                {/* eslint-disable-next-line max-len */}
                {`${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`}
              </div>
            </div>
          </Tooltip>
        </>
      )}
      {graphView && displayResults && detailedView && (
        <div
          className={styles.resultsBar}
          style={{ padding: `0 ${globalDataPaddingPercentages[1]}% 0 ${globalDataPaddingPercentages[0]}%` }}
        >
          {resultsBarColors.map((nextBar, index) => {
            const percentage = get(globalData, `percentages.[${index}].percentage`) || 0;
            let radius = '';
            if (index === globalDataFirstNonZeroIndex && index === globalDataLastNonZeroIndex) {
              radius = radiusBoth;
            } else if (index === globalDataFirstNonZeroIndex) {
              radius = radiusFirstBar;
            } else if (index === globalDataLastNonZeroIndex) {
              radius = radiusLastBar;
            }
            return (
              <Tooltip
                key={`globalResults-${index}`}
                title={(isMobile && percentage < 14) || (!isMobile && percentage < 8) ? `${percentage}%` : ''}
                role="tooltip"
                aria-describedby={`headings-${index}`}
              >
                <div
                  className={styles.resultsBarSegment}
                  style={{
                    backgroundColor: nextBar,
                    flexGrow: percentage,
                    borderRadius: `${radius}`,
                    borderLeft: index === 0 ? '' : barBorderLeft,
                  }}
                  data-cy={`globalResults-${index}`}
                >
                  <div
                    className={styles.resultsBarSegmentText}
                    style={{
                      display: percentage === 0 ? 'none' : 'inline-block',
                      color: palette.primary.dark,
                      fontWeight: 600,
                    }}
                    aria-label={`${percentage}% ${t('lessonCards.global_customers')} ${labelText[index]}`}
                  >
                    {(isMobile && percentage < 14) || (!isMobile && percentage < 8) ? '' : `${percentage}%`}
                  </div>
                </div>
              </Tooltip>
            );
          })}
        </div>
      )}
      {graphView && displayResults && !detailedView && (
        <div
          className={styles.resultsBar}
          style={{ padding: `0 ${globalDataPaddingPercentages[1]}% 0 ${globalDataPaddingPercentages[0]}%` }}
        >
          {compressedGlobalData.map((nextData, index) => {
            let backgroundColor = fullResultsBarColorPalette[0];
            if (index === 1 && compressedGlobalData.length === 3) {
              backgroundColor = fullResultsBarColorPalette[3];
            } else if (index !== 0) {
              backgroundColor = fullResultsBarColorPalette[6];
            }
            let radius = '';
            if (index === compressedGlobalDataFirstNonZeroIndex && index === compressedGlobalDataLastNonZeroIndex) {
              radius = radiusBoth;
            } else if (index === compressedGlobalDataFirstNonZeroIndex) {
              radius = radiusFirstBar;
            } else if (index === compressedGlobalDataLastNonZeroIndex) {
              radius = radiusLastBar;
            }
            return (
              <Tooltip
                key={`globalLessResults-${index}`}
                title={(isMobile && nextData < 14) || (!isMobile && nextData < 8) ? `${nextData}%` : ''}
                role="tooltip"
                aria-describedby={`headings-${index}`}
              >
                <div
                  className={styles.resultsBarSegment}
                  style={{
                    backgroundColor,
                    flexGrow: nextData,
                    borderRadius: `${radius}`,
                    borderLeft: index === 0 ? '' : barBorderLeft,
                  }}
                  data-cy={`globalLessResults-${index}`}
                >
                  <div
                    className={styles.resultsBarSegmentText}
                    style={{
                      display: nextData === 0 ? 'none' : 'inline-block',
                      color: palette.primary.dark,
                      fontWeight: 600,
                    }}
                    aria-label={`${nextData}% ${t('lessonCards.global_customers')} ${summaryLabels[index]}`}
                  >
                    {(isMobile && nextData < 14) || (!isMobile && nextData < 8) ? '' : `${nextData}%`}
                  </div>
                </div>
              </Tooltip>
            );
          })}
        </div>
      )}
      {graphView && displayResults && sliderMax > 3 && (
        <>
          <VerticalLine />
          {sliderMax <= 7 && (
            <div
              className={styles.showMoreText}
              onClick={() => setDetailedView(!detailedView)}
              data-cy={detailedView ? 'showLess' : 'showMore'}
            >
              <Box
                className={styles.showMoreTextButton}
                sx={{
                  border: palette.border.completedAssignmentItems,
                  backgroundColor: palette.background.detailsButton,
                  '&:hover': {
                    backgroundColor: palette.background.darkGrey,
                  },
                }}
                tabIndex="0"
                role="button"
                onKeyDown={
                  (e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      e.preventDefault();
                      setDetailedView(!detailedView);
                    }
                  }
                }
              >
                {detailedView ? t('lessonCards.showLess') : t('lessonCards.showMore')}
              </Box>
            </div>
          )}
        </>
      )}
    </CardResponsiveText>
  );
}
