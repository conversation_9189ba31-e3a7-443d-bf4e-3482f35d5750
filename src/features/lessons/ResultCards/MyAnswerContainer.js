import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, useTheme } from '@mui/material';
import MyAnswerIcon from '../../../images/user-square-solid.svg';

import styles from './MyAnswerContainer.module.css';

const MyAnswerContainer = (props) => {
  const { t } = useTranslation();
  const { palette } = useTheme();

  return (
    <Box
      id="myAnswer"
      sx={{ '& p': { fontSize: '0.75em' } }}
      {...props}
      data-cy="myAnswer"
      className={styles.myAnswerSection}
      style={{ borderTop: palette.border.myAnswer, borderBottom: palette.border.myAnswer }}
      aria-hidden
    >
      <MyAnswerIcon />
      <Typography>{`${t('lessonCards.my')} ${t('lessonCards.answer')}`}</Typography>
    </Box>
  );
};

export default MyAnswerContainer;
