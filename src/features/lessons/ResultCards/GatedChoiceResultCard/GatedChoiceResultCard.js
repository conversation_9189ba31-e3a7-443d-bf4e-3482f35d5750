/* eslint-disable react/no-danger */
import React, { useEffect } from 'react';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';

import styles from './GatedChoiceResultCard.module.css';

export default function GatedChoiceResultCard(props) {
  const { id, title, fontColor, correctChoiceFeedback } = props;
  const lessonCardFocusElement = document.getElementById('lesson-card-focus-element');
  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (lessonCardFocusElement && contentContainerElement) {
      lessonCardFocusElement.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [lessonCardFocusElement, contentContainerElement]);
  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`GatedChoiceResultCard-${id}`}
    >
      <CardTitleDescription title={title} fontColor={fontColor} />
      <div className={styles.feedbackMessage} dangerouslySetInnerHTML={{ __html: correctChoiceFeedback }} />
    </CardResponsiveText>
  );
}
