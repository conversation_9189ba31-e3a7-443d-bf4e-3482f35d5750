/* eslint-disable react/no-danger */
import React, { useEffect } from 'react';
import { useTheme } from '@emotion/react';
import { get } from 'lodash';
import { useTranslation } from 'react-i18next';
import { RESPONSE_THRESHOLD_DEFAULT } from '../../../../config/constants';
import DoughnutGraph from '../../../../components/DoughnutGraph/DoughnutGraph';
import { NotEnoughResults } from '../../../../components/NotEnoughResults/NotEnoughResults';
import { useLessonCardResults } from '../../../../hooks/useLessonCardResults';
import { useNumberFormat } from '../../../../hooks/useNumberFormat';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useUser } from '../../../../hooks/useUser';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import Spinner from '../../../../components/Spinner/Spinner';
import MyAnswerContainer from '../MyAnswerContainer';

import styles from './BooleanResultCard.module.css';

export default function BooleanResultCard(props) {
  const { id, description, title, fontColor, answer: { booleanAnswer }, booleanLabel, responseThreshold,
    organization, global, insights, lessonLifecycle } = props;
  const { palette, typography } = useTheme();
  const user = useUser();
  const { t } = useTranslation();
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { loading, data } = !insights && useLessonCardResults({ lessonCardId: id, lessonLifecycle });
  const { formatReportTotal } = useNumberFormat();
  const { isSmallMobile, isMobile } = useResponsiveMode();
  const translatedTrueText = booleanLabel.trueText === 'true' ? t('boolean.true') : t('boolean.yes');
  const translatedFalseText = booleanLabel.falseText === 'false' ? t('boolean.false') : t('boolean.no');

  const cardContent = insights ? styles.insightsCardContent : styles.cardContent;

  // if an sso admin is previewing the lesson, the relevant account is user.accounts[1]
  const accountIndex = get(user, 'accounts[1]') ? 1 : 0;
  const hideOrg = !insights ? get(user, `accounts[${accountIndex}].hideOrg`, false) : false;

  const lessonCardFocusElement = document.getElementById('lesson-card-focus-element');
  const contentContainerElement = document.getElementById('card-scrolltop-target');
  useEffect(() => {
    if (!loading && lessonCardFocusElement && contentContainerElement) {
      lessonCardFocusElement.focus();
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }, [loading, lessonCardFocusElement, contentContainerElement]);

  if (loading) {
    return (
      <Spinner />
    );
  }

  const organizationData = insights ? organization.booleanReportData
    : get(data, 'getResultsCard.organization.booleanReportData');
  const globalData = insights ? global.booleanReportData : get(data, 'getResultsCard.global.booleanReportData');

  const organizationTotal = get(organizationData, 'total') || 0;
  const globalTotal = get(globalData, 'total') || 0;
  const adjustedReponseThreshold = responseThreshold || RESPONSE_THRESHOLD_DEFAULT;
  let displayResults = organizationTotal >= adjustedReponseThreshold && globalTotal >= adjustedReponseThreshold;
  if (lessonLifecycle === 'preview' && organizationTotal && globalTotal) {
    displayResults = true;
  }

  let mainDoughnutClasses;
  let smallDoughnutClasses;

  if (isSmallMobile) {
    mainDoughnutClasses = styles.mainDoughnutSmallMobile;
    smallDoughnutClasses = styles.smallDoughnutSmallMobile;
  } else if (isMobile || insights) {
    mainDoughnutClasses = styles.mainDoughnutMobile;
    smallDoughnutClasses = styles.smallDoughnutMobile;
  } else {
    mainDoughnutClasses = styles.mainDoughnut;
    smallDoughnutClasses = styles.smallDoughnut;
  }

  return (
    <CardResponsiveText containerStyle={cardContent} key={`BooleanRersultCard-${id}`}>
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      {!displayResults && (
        <NotEnoughResults insights={insights} />
      )}
      {displayResults && (
        <>
          <div className={styles.mainDoughnutContainer}>
            <div className={styles.mainDoughnutWrapper}>
              {!insights && (
              <MyAnswerContainer {...{
                position: 'absolute',
                top: 0,
                right: 0,
                padding: '0.5em',
              }}
              />
              )}
              {!hideOrg && (
                <DoughnutGraph
                  className={`${styles.doughnut} ${mainDoughnutClasses}`}
                  colorScale={[palette.background.chartsBlue, palette.background.chartsOrange]}
                  legendLineColor={palette.primary.dark}
                  tooltipText={t('lessonCards.myOrg_tooltip')}
                  dataCY="myOrgResponse"
                  insights={insights}
                  data={filterZeroPercent([
                    {
                      x: translatedTrueText,
                      y: organizationData.percentage,
                      myChoice: booleanAnswer === true,
                      style: { fontSize: '1.5rem', fill: palette.primary.main },
                    },
                    {
                      x: translatedFalseText,
                      y: 100 - organizationData.percentage,
                      myChoice: booleanAnswer === false,
                      style: { fontSize: '1.5rem', fill: palette.primary.main },
                    },
                  ])}
                  middleLabels={[
                    {
                      x: 200,
                      y: 190,
                      text: `${t('lessonCards.myOrg')}`,
                      // eslint-disable-next-line max-len
                      numResponsesText: `${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      style: {
                        fontSize: 32,
                        fontWeight: 600,
                        textAlign: 'center',
                        lineHeight: '1',
                        marginTop: '0.5rem',
                        fontFamily: typography.fontFamily,
                        fill: palette.primary.dark,
                      },
                    },
                    {
                      x: 200,
                      y: 220,
                      // eslint-disable-next-line max-len
                      text: `${formatReportTotal(organizationTotal)} ${organizationTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      // eslint-disable-next-line max-len
                      style: { fontSize: 24, fontFamily: typography.fontFamily, fill: palette.primary.main, textAlign: 'center' },
                    },
                  ]}
                />
              )}
              {hideOrg && (
                <DoughnutGraph
                  className={`${styles.doughnut} ${mainDoughnutClasses}`}
                  colorScale={[palette.background.chartsBlue, palette.background.chartsOrange]}
                  legendLineColor={palette.primary.dark}
                  tooltipText={t('lessonCards.global_tooltip')}
                  dataCY="global"
                  insights={insights}
                  data={filterZeroPercent([
                    {
                      x: translatedTrueText,
                      y: globalData.percentage,
                      myChoice: booleanAnswer === true,
                      style: { fontSize: '1.5rem', fill: palette.primary.main },
                    },
                    {
                      x: translatedFalseText,
                      y: 100 - globalData.percentage,
                      myChoice: booleanAnswer === false,
                      style: { fontSize: '1.5rem', fill: palette.primary.main },
                    },
                  ])}
                  middleLabels={[
                    {
                      x: 200,
                      y: 190,
                      text: `${t('lessonCards.global')}`,
                      // eslint-disable-next-line max-len
                      numResponsesText: `${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      style: {
                        fontSize: 32,
                        fontWeight: 600,
                        textAlign: 'center',
                        fontFamily: typography.fontFamily,
                        fill: palette.primary.dark,
                      },
                    },
                    {
                      x: 200,
                      y: 220,
                      // eslint-disable-next-line max-len
                      text: `${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      // eslint-disable-next-line max-len
                      style: { fontSize: 24, fontFamily: typography.fontFamily, fill: palette.primary.main, textAlign: 'center' },
                    },
                  ]}
                />
              )}
            </div>
          </div>
          {!hideOrg && (
            <div className={isSmallMobile ? styles.smallDoughnutContainerSmallMobile : styles.smallDoughnutContainer}>
              <div
                className={styles.smallDoughtnutWrapper}
                style={{ border: palette.border.main }}
              >
                <DoughnutGraph
                  className={`${styles.doughnut} ${smallDoughnutClasses}`}
                  tooltipText={t('lessonCards.global_tooltip')}
                  colorScale={[palette.background.chartsBlueFaded, palette.background.chartsOrangeFaded]}
                  legendLineColor={palette.primary.greyPurple}
                  dataCY="globalResponse"
                  insights={insights}
                  data={filterZeroPercent([
                    {
                      x: translatedTrueText,
                      y: globalData.percentage,
                      style: { fontSize: '2rem', fill: palette.primary.main },
                    },
                    {
                      x: translatedFalseText,
                      y: 100 - globalData.percentage,
                      style: { fontSize: '2rem', fill: palette.primary.main },
                    },
                  ])}
                  middleLabels={[
                    {
                      x: 200,
                      y: 200,
                      text: `${t('lessonCards.global')}`,
                      // eslint-disable-next-line max-len
                      numResponsesText: `${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`,
                      style: {
                        fontSize: 36,
                        fontWeight: 600,
                        textAlign: 'center',
                        marginTop: '1rem',
                        fontFamily: typography.fontFamily,
                        fill: palette.primary.dark,
                      },
                    },
                  ]}
                />
                <div className={styles.smallDoughnutLabel} data-cy="globalResponse">
                  {/* eslint-disable-next-line max-len */}
                  {`${formatReportTotal(globalTotal)} ${globalTotal === 1 ? t('lessonCards.response') : t('lessonCards.responses')}`}
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </CardResponsiveText>
  );
}

function filterZeroPercent(data) {
  return data.filter(({ y }) => y > 0);
}
