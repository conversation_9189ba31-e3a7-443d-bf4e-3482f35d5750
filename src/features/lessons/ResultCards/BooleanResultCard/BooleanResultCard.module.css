.cardContent {
  margin-top: 1.5rem;
}

.doughnut {
  width: 100%;
}

.mainDoughnut {
  height: 20rem;
}

.mainDoughnutMobile {
  height: 14.5rem;
}

.mainDoughnutSmallMobile {
  height: 11.5rem;
}

.smallDoughnutContainer {
  display: flex;
  justify-content: flex-end;
}

.smallDoughnutContainerSmallMobile {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 4rem;
}

.smallDoughtnutWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 50%;
  border-radius: 4px;
}

.smallDoughnut {
  height: 10rem;
}

.smallDoughnutMobile {
  height: 7rem;
}

.smallDoughnutSmallMobile {
  height: 5.5rem;
}

.smallDoughnutLabel {
  width: fit-content;
  font-size: 0.75rem;
  margin-bottom: 0.4rem;
}

.mainDoughnutContainer {
  width: 100%;
  display: flex;
}

.mainDoughnutWrapper {
  position: relative;
  width: 100%;
  animation: pause, fadeIn;
  animation-duration: 250ms, 1000ms;
  animation-delay: 0ms, 250ms;   
}

@keyframes fadeIn {
  from { opacity: 0; }
  to   { opacity: 1; }
}

@keyframes pause {
  from { opacity: 0; }
  to   { opacity: 0; }
}

.myChoiceContainer {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0;
  left: 5px;
  padding: 0.5em;
}

.myChoiceContainer span {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

.insightsCardContent {
  margin-top: 0rem !important;
  font-size: 1.1rem !important;
}

.insightsCardContent h2 {
   margin-block-start: 0em !important;
}
