import React from 'react';
import { Paper, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useMenuDrawer } from '../../hooks/useDrawer';
import AskAnExpertIcon from '../../images/ask_an_expert.svg';

import styles from './Lessons.module.css';

function AskTopicExpertButton() {
  const { t } = useTranslation();
  const { openDrawer } = useMenuDrawer();
  const { palette } = useTheme();

  return (
    <Paper
      tabIndex="0"
      role="button"
      className={styles.askAnExpert}
      sx={{
        width: '33.3rem',
        bgcolor: 'background.paper',
        '&:hover': {
          bgcolor: palette.background.secondary,
        },
        '&:focus': {
          border: `2px solid ${palette.card.backgroundColor} !important`,
        },
      }}
      style={{ border: palette.border.askExpertButton, color: palette.primary.darkLightPurple }}
      onClick={() => openDrawer('expert')}
      onKeyDown={(e) => {
        if (['Enter', 'Space'].includes(e.code)) { openDrawer('expert'); }
      }}
    >
      <AskAnExpertIcon aria-hidden id="AskAnExpertIcon" />
      <div className={styles.askAnExpertText}>
        {t('lessons.askExpertTitle')}
        <p aria-hidden className={styles.chevron}>›</p>
      </div>
      <span className="sr-only">
        {t('sideDrawer.askExpert.headerText')}.
        {t('sideDrawer.askExpert.responsePrompt')}.
      </span>
    </Paper>
  );
}

export default AskTopicExpertButton;
