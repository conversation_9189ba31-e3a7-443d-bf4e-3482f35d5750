/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { useTheme, Box } from '@mui/material';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import styles from './TextImageCard.module.css';

export default function TextImageCard(props) {
  const { id, description, title, fontColor, imageUrl, imageAltText } = props;
  const [imageLoaded, setImageLoaded] = useState(false);
  const { isSmallMobile } = useResponsiveMode();
  const { palette } = useTheme();

  return (
    <CardResponsiveText
      key={`TextImageCard-${id}`}
      containerStyle={isSmallMobile ? styles.cardContent : ''}
    >
      <CardTitleDescription title={title} fontColor={fontColor} />
      <div>
        <div className={styles.imageWrapper}>
          <img
            className={styles.image}
            src={imageUrl}
            alt={imageAltText}
            onLoad={() => setImageLoaded(true)}
            style={{ display: imageLoaded ? '' : 'none' }}
            data-cy="image"
          />
        </div>
        <Box
          className={styles.paragraph}
          style={{ marginBottom: isSmallMobile ? '4.5rem' : 0, color: fontColor }}
          dangerouslySetInnerHTML={{ __html: description }}
          sx={{
            // eslint-disable-next-line max-len
            '& p > a, & h2 > a, & li > a': { color: `${palette.link.document} !important`, textDecoration: 'none', fontWeight: 'bold' },
          }}
          data-cy="imageDescription"
        />
      </div>
    </CardResponsiveText>
  );
}
