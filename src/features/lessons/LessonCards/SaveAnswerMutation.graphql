mutation SaveAnswerMutation($answer: SaveAnswerInput!) {
  saveAnswer(input: $answer) {
  booleanAnswer
  checkboxAnswer {
    answer1
    answer2
    answer3
    answer4
    answer5
  }
  sliderAnswer
  singleChoiceAnswer
  multipleChoiceAnswer {
    answer1
    answer2
    answer3
    answer4
    answer5
    answer6
    answer7
    answer8
    answer9
    answer10
  }
  freeFormTextAnswer
  policyAcknowledgementAnswer
  workplaceColorSpectrumAnswer
  }
}

