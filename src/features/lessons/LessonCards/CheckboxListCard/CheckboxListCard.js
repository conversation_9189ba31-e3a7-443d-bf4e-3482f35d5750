/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { get, omit } from 'lodash';
import { Checkbox } from '@mui/material';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useAccessibilityHelper } from '../../../../hooks/useAccessibilityHelper';

import styles from './CheckboxListCard.module.css';

export default function CheckboxListCard(props) {
  const { id, listItems, title, fontColor, answer, onSaveAnswer } = props;
  const [checkboxAnswer, setCheckboxAnswer] = useState(get(answer, 'checkboxAnswer') || {});
  const { isSmallMobile } = useResponsiveMode();
  const { stripHTMLForReader } = useAccessibilityHelper();

  function handleCheckboxChange({ key, value }) {
    const updatedCheckboxAnswer = {
      ...omit(checkboxAnswer, '__typename'),
      [key]: value,
    };
    setCheckboxAnswer(updatedCheckboxAnswer);
    onSaveAnswer({
      updateCacheKey: `OrderedListCard:${id}`,
      answer: {
        type: 'CHECKBOX_LIST',
        checkboxAnswer: updatedCheckboxAnswer,
      },
    });
  }

  return (
    <fieldset className={styles.fieldsetStyle}>
      <CardResponsiveText key={`NumberedListCard-${id}`}>
        <legend style={{ display: 'contents' }}>
          <CardTitleDescription title={title} fontColor={fontColor} />
        </legend>
        {listItems.map((item, i) => {
          const key = `answer${i + 1}`;
          return (
          // eslint-disable-next-line react/no-array-index-key
            <div className={styles.listItem} key={`CheckboxListItem-${i}`}>
              <div className={styles.itemCheckbox}>
                <Checkbox
                  id={`CheckboxListItemCheckbox-${i}`}
                  sx={{ '& .MuiSvgIcon-root': { fontSize: 24 }, padding: 0 }}
                  checked={get(checkboxAnswer, key, false)}
                  onChange={() => {
                    handleCheckboxChange({ key, value: !checkboxAnswer[key] });
                  }}
                  inputProps={{
                    'data-cy': `Choice-${i}`,
                    'aria-label': `${stripHTMLForReader(item)}`,
                    'aria-checked': get(checkboxAnswer, key, false),
                  }}
                />
              </div>
              <div
                id={`checkboxLabel-${i}`}
                style={{ color: fontColor }}
                className={styles.checkboxItemText}
                dangerouslySetInnerHTML={{ __html: `<label htmlFor="CheckboxListItemCheckbox-${i}">${item}</label>` }}
                data-cy={`checkBoxListItem-${i}`}
              />
            </div>
          );
        })}
        {isSmallMobile && <div style={{ marginBottom: '4.5rem' }} />}
      </CardResponsiveText>
    </fieldset>
  );
}
