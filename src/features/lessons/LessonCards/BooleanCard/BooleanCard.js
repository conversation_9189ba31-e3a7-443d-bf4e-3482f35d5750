import React, { useState } from 'react';
import { styled, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import Button from '../../../../components/Button/Button';
import HiddenAlert from '../../../hidden-alert/HiddenAlert';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

import styles from './BooleanCard.module.css';

const ThemedButton = styled('button')(
  ({ theme: { palette } }) => `
    color: ${palette.text.primary};
    background: ${palette.background.paper};
    border: ${palette.border.main};
    :hover {
      background: ${palette.background.highlighted};
    }
  `,
);

export default function BooleanCard(props) {
  const { id, title, description, fontColor,
    booleanLabel: { falseText, trueText }, onSaveAnswer, saveAnswerLoading, eventLoading } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();
  const [choice, setChoice] = useState(null);
  const [alertText, setAlertText] = useState('');
  const translatedTrueText = trueText === 'true' ? t('boolean.true') : t('boolean.yes');
  const translatedFalseText = falseText === 'false' ? t('boolean.false') : t('boolean.no');

  const selectItem = (response) => {
    setChoice(response);
    // eslint-disable-next-line max-len
    setAlertText(`${t('general.you')} ${t('general.selected')} ${response === 'true' ? translatedTrueText : translatedFalseText}.`);
  };

  function handleSubmit() {
    onSaveAnswer({
      updateCacheKey: `BooleanCard:${id}`,
      answer: {
        type: 'BOOLEAN',
        booleanAnswer: choice === 'true' || false,
      },
    });
  }

  const selected = {
    background: palette.background.ribbon,
    color: palette.primary.contrastText,
    fontWeight: 600,
  };

  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`BooleanCard-${id}`}
    >
      <CardTitleDescription
        title={title}
        description={description}
        fontColor={fontColor}
        hiddenText={t('general.question')}
        boxProps={{ id: `question-${id}` }}
      />
      <div className={styles.buttonContainer} style={{ marginBottom: isSmallMobile ? '4rem' : 0 }}>
        <div className="srOnly">
          {`${t('general.pleaseSelect')} ${translatedTrueText} ${t('general.or')} ${translatedFalseText}.`}
        </div>
        <HiddenAlert text={alertText} />
        <div
          role="radiogroup"
          aria-label={t('lessonCards.boolean_please_select', { translatedTrueText, translatedFalseText })}
          aria-labelledby={`question-${id}`}
        >
          <ThemedButton
            className={styles.button}
            style={choice === 'true' ? selected : null}
            aria-checked={choice === 'true'}
            onClick={() => selectItem('true')}
            data-cy="true"
            role="radio"
          >
            <p>{translatedTrueText}</p>
          </ThemedButton>
          <ThemedButton
            className={styles.button}
            style={choice === 'false' ? selected : null}
            aria-checked={choice === 'false'}
            onClick={() => selectItem('false')}
            data-cy="false"
            role="radio"
          >
            <p>{translatedFalseText}</p>
          </ThemedButton>
        </div>
      </div>
      <div className={styles.submitContainer}>
        <Button
          containerStyle={styles.submitButtonContainer}
          className={!eventLoading ? `${styles.submitButton} ${styles.buttonAnimation}` : styles.submitButton}
          style={{ borderColor: palette.card.backgroundColor }}
          loading={saveAnswerLoading}
          onClick={handleSubmit}
          disabled={!choice || !eventLoading}
          variant="contained"
          data-cy="submit"
        >
          {t('platform.submitButton')}
        </Button>
      </div>
    </CardResponsiveText>
  );
}
