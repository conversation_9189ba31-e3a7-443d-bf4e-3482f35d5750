.cardContent {
  padding-bottom: 6rem;
  margin-top: 1.5rem;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.button {
  font-family: Source Sans Pro, sans-serif;
  padding: .75em 0 .75em 0;
  width: 6em;
  font-size: 1.5rem;
  font-weight: normal;
  text-align: center;
  border-radius: 0.25rem;
  margin-left: 0.75rem;
  cursor: pointer;
  transition: all linear 0.1s;
}

.submitContainer {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 3rem;
  left: 0;
  right: 0;
}

.submitButtonContainer {
  width: auto !important;
}

.submitButton {
  border: none;
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.submitButton:focus {
  border: solid 3px;
}

@keyframes buttonFadeInOut {
  0% { -webkit-transform: translateY(0); opacity: 0.4 }
  100% { -webkit-transform: translateY(0); opacity: 0.9 }
}
.buttonAnimation {
  animation: buttonFadeInOut 0.6s infinite alternate ease-in-out;
}