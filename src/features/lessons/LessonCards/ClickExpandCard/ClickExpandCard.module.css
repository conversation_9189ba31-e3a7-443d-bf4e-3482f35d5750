.paragraph {
  line-height: 1.4em;
  padding-right: 1.2rem;
}

.paragraph p {
  margin-bottom: 1.2rem;
  font-size: 1.1em;
  padding-right: 1.2rem;
}

.expandedCardH2 {
  margin-block-start: 0;
  margin-block-end: 0;
  margin-bottom: 1.2rem;
  font-size: 1.1em;
  padding-right: 1.2rem;
}

.container {
  display: flex;
  justify-content: start;
  flex-wrap: wrap; 
}

.gridCell {
  display: grid;
  padding: 0.5rem;
}

.cardButton {
  font-size: 1em !important;
  font-family: Source Sans Pro, sans-serif;
  border: 0;
  background-color: inherit;
}

.gridItem {
  display: flex;
  flex-grow: 1;
  border-radius: 0.5em;
  cursor: pointer;
  padding: 1rem;
  overflow: hidden;
  height: 100%;
}

.gridItemText {
  display: flex;
  height: 100%;
  width: 100%;
  justify-content: center;
  align-items: center !important;
  font-size: 1em;
  line-height: 1.2em;
  font-weight: bold;
  text-align: center;
  align-items: flex-start;
  margin-inline-start: auto; /*dd tag styles */
}

.lineClamp p strong, .lineClamp p {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 5;
  overflow: hidden;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}

.closeIcon {
  display: inline-block;
  position: absolute;
  top: 2px;
  right: 2px;
}

.cardTitle {
  margin-block-start: 0em;
  margin-block-end: 0em;
}

.clickExpandList {
  margin-block-start: 0em;
  margin-block-end: 0em;
  padding-inline-start: initial;
}

.paragraph li {
  display: list-item;
  text-align: -webkit-match-parent;
}
