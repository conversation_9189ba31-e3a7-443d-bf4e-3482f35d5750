/* eslint-disable max-len */
/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { useTheme, Box } from '@mui/material';
import { useUser } from '../../../../hooks/useUser';
import { useSpeechAudio } from '../../../../hooks/useSpeechAudio';
import { useViewedClickExpand } from '../../../../hooks/useViewedClickExpand';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import AudioButton from '../../../../components/Button/AudioButton';
import SpeechAudio from '../../../../components/LessonCard/SpeechAudio/SpeechAudio';

import styles from './ClickExpandCard.module.css';
import Dialog from '../../../../components/Modal/Dialog';

const gridCellFlexStyleOptions = [
  ['0 0 100%'],
  ['0 0 50%', '0 0 50%'],
  ['0 0 50%', '0 0 50%', '0 0 100%'],
  ['0 0 50%', '0 0 50%', '0 0 50%', '0 0 50%']];

const gridCellRatioStyleOptions = [
  ['auto 1'],
  ['auto .5', 'auto .5'],
  ['auto 1', ' auto 1', 'auto 2'],
  ['auto 1', 'auto 1', 'auto 1', 'auto 1']];

export default function ClickExpandCard(props) {
  const { id, description, title, fontColor, items } = props;

  const { palette } = useTheme();

  const user = useUser();
  const { pausePopupAudio, setPopupOpen } = useSpeechAudio();
  const { addClickExpand } = useViewedClickExpand();

  const [open, setOpen] = useState(false);
  const [expandedText, setExpandedText] = useState(null);
  const [expandedAudio, setExpandedAudio] = useState(null);

  const expandTextInfo = (text) => {
    let expandCardText = text;
    const count = expandCardText.split('<p>').length - 1;
    if (count > 1) {
      // replaced first p tag with h2
      expandCardText = expandCardText.replace('<p>', `<h2 id="expanded-card-header" class=${styles.expandedCardH2}>`);
      expandCardText = expandCardText.replace('</p>', '</h2>');
    }
    return `<div id="modal-content">${expandCardText}</div>`;
  };

  const handleOpen = (text, audio, listIndex) => {
    setExpandedText(expandTextInfo(text));
    setExpandedAudio(audio);
    setPopupOpen(true);
    setOpen(true);
    addClickExpand(id, listIndex);
  };

  const gridFlexStyles = gridCellFlexStyleOptions[items.length - 1];
  const gridRatioStyles = gridCellRatioStyleOptions[items.length - 1];

  return (
    <>
      <CardResponsiveText key={`ClickExpandCard-${id}`}>
        <CardTitleDescription titleId={`header-${id}`} title={title} description={description} fontColor={fontColor} />
        <ul className={`${styles.container} ${styles.clickExpandList}`}>
          {items.map((item, i) => (
            <li
              style={{ flex: gridFlexStyles[i], aspectRatio: gridRatioStyles[i], display: 'grid' }}
              // eslint-disable-next-line react/no-array-index-key
              key={`NumberedListItem-${i}`}
            >
              <button
                className={`${styles.gridCell} ${styles.cardButton}`}
                onClick={() => handleOpen(items[i].expandedText, items[i].speechAudioURL, i)}
                data-cy={`item-${i}`}
                style={{ flex: gridFlexStyles[i], aspectRatio: gridRatioStyles[i] }}
                tabIndex="0"
                type="button"
                onKeyDown={(e) => { if (e.code === 'Enter' || e.code === 'Space') handleOpen(items[i].expandedText, items[i].speechAudioURL, i); }}
              >
                <dl
                  className={`${styles.gridItem} ${styles.cardTitle}`}
                  style={{
                    backgroundColor: palette.background.clickExpand,
                  // background:
                  // eslint-disable-next-line max-len
                  //  `linear-gradient(to right bottom, ${palette.background.clickExpand} 0%, ${palette.background.clickExpandGradient} 100%) 100% center no-repeat`,
                  }}
                >
                  <dd
                    style={{ color: palette.primary.white }}
                    className={`${styles.gridItemText} ${styles.lineClamp}`}
                    dangerouslySetInnerHTML={{ __html: items[i].text }}
                    data-cy={`expand_card-${i}`}
                  />
                </dl>
              </button>
            </li>
          ))}
        </ul>
      </CardResponsiveText>
      <Dialog ariaLabelledby="expanded-card-header" open={open} onClose={() => { pausePopupAudio(); setOpen(false); setPopupOpen(false); }}>
        <CardResponsiveText>
          <Box
            className={styles.paragraph}
            dangerouslySetInnerHTML={{ __html: expandedText }}
            data-cy={expandedText && expandedText.replace(/<\/?[^>]+(>|$)/g, '')}
            sx={{
              '& p > a, & h2 > a, & li > a': { color: `${palette.link.document} !important`, textDecoration: 'none', fontWeight: 'bold' },
            }}
          />
        </CardResponsiveText>
        <AudioButton inPopup />
        {user.audio && expandedAudio && (
          <SpeechAudio popupAudioURL={expandedAudio} open={open} />
        )}
      </Dialog>
    </>
  );
}
