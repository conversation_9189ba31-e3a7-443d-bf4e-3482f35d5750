/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RadioGroup, Radio, Box, styled, useTheme } from '@mui/material';
import { Circle as RadioButtonChecked } from '@mui/icons-material';
import Button from '../../../../components/Button/Button';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import HiddenAlert from '../../../hidden-alert/HiddenAlert';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useAccessibilityHelper } from '../../../../hooks/useAccessibilityHelper';
import BanCircle from '../../../../images/ban-circle.svg';
import styles from './SingleChoiceCard.module.css';

const RadioButtonCheckedIcon = styled(({ ...props }) => <RadioButtonChecked {...props} />)(
  ({ theme }) => ({
    border: `1px solid ${theme.palette.primary.contrastText}`,
    borderRadius: '50%',
    fontSize: '1.5rem',
    color: theme.palette.primary.dark,
  }));

const RadioButtonUncheckedIcon = styled((props) => <RadioButtonChecked {...props} />)(({ theme }) => ({
  border: theme.palette.border.slider,
  color: theme.palette.primary.contrastText,
  borderRadius: '50%',
  fontSize: '1.5rem',
  'input:hover ~ &': {
    border: theme.palette.border.slider,
    color: theme.palette.primary.contrastText,
    borderRadius: '50%',
  },
}));

export default function SingleChoiceCard(props) {
  const { id, choices, title, description, fontColor, gated, wrongChoiceErrorMessage,
    // eslint-disable-next-line no-unused-vars
    onSaveAnswer, saveAnswerLoading, onSkipCard, eventLoading } = props;

  const { t } = useTranslation();
  const { stripHTMLForReader } = useAccessibilityHelper();
  const [answer, setAnswer] = useState(0);
  const [firstAnswer, setFirstAnswer] = useState(0);
  const [gatedError, setGatedError] = useState(false);
  const [gatedLiveAlertMessage, setGatedLiveAlertMessage] = useState('');
  const { isSmallMobile } = useResponsiveMode();
  const { palette } = useTheme();
  const [groupFocused, setGroupFocused] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(null);

  const gatedChoiceIndex = choices.findIndex((choice) => choice.gatedChoice);

  function handleRadioButtonChange(key) {
    setGatedLiveAlertMessage('');
    setGatedError(false);
    setAnswer(key);
  }

  function handleSubmit() {
    // only set firstAnswer if it's not already set.
    if (firstAnswer === 0) {
      setFirstAnswer(answer);
    }
    if (gated && gatedChoiceIndex !== (answer - 1)) {
      // incorrect choice
      setGatedError(true);
      setGatedLiveAlertMessage(wrongChoiceErrorMessage || t('lessonCards.gatedError'));
      const firstInput = document.getElementById('singlechoice-radiogroup');
      firstInput?.focus();
    } else {
      // correct choice
      setGatedError(false);
      setGatedLiveAlertMessage('');
      const contentContainerElement = document.getElementById('card-scrolltop-target');
      onSaveAnswer({
        updateCacheKey: `SingleChoiceCard:${id}`,
        answer: {
          type: 'SINGLE_CHOICE',
          singleChoiceAnswer: firstAnswer === 0 ? answer : firstAnswer,
        },
      });
      if (contentContainerElement) {
        contentContainerElement.scrollIntoView({
          block: 'end',
          behavior: 'instant',
        });
      }
    }
  }

  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`SingleChoiceCard-${id}`}
    >
      <CardTitleDescription
        title={title}
        description={description}
        fontColor={fontColor}
        hiddenText={t('general.question')}
        boxProps={{ id: `question-${id}` }}
      />
      <div className={styles.spacer} />
      <div className="srOnly">{t('lessonCards.results_description_single', { numAnswers: choices.length })}</div>
      <HiddenAlert text={gatedLiveAlertMessage} />
      <RadioGroup
        aria-labelledby={`question-${id}`}
        sx={{
          border: '1px solid transparent',
          borderRadius: '5px',
          mb: isSmallMobile ? 5 : 0,
          // eslint-disable-next-line max-len
          outline: groupFocused && ![0, 1, 2, 3].includes(focusedIndex) ? `3px solid ${palette.card.backgroundColor}` : '',
        }}
        onFocus={() => setGroupFocused(true)}
        onBlur={() => setGroupFocused(false)}
        tabIndex="0"
        id="singlechoice-radiogroup"
      >
        {choices.map((item, i) => {
          const key = i + 1;
          return (
            <Box
              className={styles.listItem}
              // eslint-disable-next-line react/no-array-index-key
              key={`NumberedListItem-${i}`}
              onClick={() => { handleRadioButtonChange(key); }}
              sx={{
                color: (answer === key) ? 'primary.contrastText' : 'text.primary',
                // eslint-disable-next-line max-len, no-nested-ternary
                backgroundColor: answer === key ? 'primary.main' : (focusedIndex === i ? 'background.highlighted' : 'primary.light'),
                '&:hover': {
                  backgroundColor: answer === key ? 'primary.main' : 'background.highlighted',
                },
                outline: focusedIndex === i ? `3px solid ${palette.card.backgroundColor}` : '',
              }}
            >
              <div className={styles.itemCheckbox}>
                <Radio
                  // eslint-disable-next-line max-len
                  // aria-label={`${(t('general.option'))} ${key}: ${stripHTMLForReader(item.text)} ${answer && answer === key ? t('general.selected') : ''}`}
                  disableRipple
                  checked={answer === key}
                  // eslint-disable-next-line max-len
                  inputProps={{ 'aria-labelledby': `choice-${key}`, 'data-cy': `Choice-${i}`, 'aria-checked': answer === key }}
                  icon={<RadioButtonUncheckedIcon />}
                  checkedIcon={<RadioButtonCheckedIcon checked={answer === key} />}
                  sx={{ '&:hover': { backgroundColor: 'transparent' } }}
                  onFocus={() => setFocusedIndex(i)}
                  onBlur={() => setFocusedIndex(null)}
                  tabIndex="0"
                />
              </div>
              <div
                id={`choice-${key}`}
                aria-hidden
                className={styles.checkboxItemText}
                style={{ fontWeight: answer === key ? 600 : 'normal', color: fontColor }}
                dangerouslySetInnerHTML={{ __html: item.text }}
                data-cy={`radioButtonListItem-${i}`}
              />
            </Box>
          );
        })}
      </RadioGroup>
      {gatedError ? (
        <div className={styles.gatedErrorStrip}>
          <BanCircle aria-label="error" focusable="false" style={{ width: '7%' }} />
          <span style={{ color: palette.primary.dark }}>
            {gatedLiveAlertMessage}
          </span>
        </div>
      ) : (
        <>
          {/* <div className={styles.skipLink} onClick={() => onSkipCard()}>
            <span>{t('lessonCards.dontKnowSkip')}</span>
          </div> */}
          {answer >= 1 && answer <= choices.length && (
            <div className="srOnly">
              {`${t('general.you')} ${t('general.selected')} ${stripHTMLForReader(choices[answer - 1].text)}`}
            </div>
          )}
          <div className={styles.submitContainer}>
            <Button
              containerStyle={styles.submitButtonContainer}
              className={!eventLoading ? `${styles.submitButton} ${styles.buttonAnimation}` : styles.submitButton}
              style={{ borderColor: palette.card.backgroundColor }}
              loading={saveAnswerLoading}
              onClick={handleSubmit}
              disabled={answer === 0 || !eventLoading}
              variant="contained"
              data-cy="submit"
            >
              {t('platform.submitButton')}
            </Button>
          </div>
        </>
      )}
    </CardResponsiveText>
  );
}
