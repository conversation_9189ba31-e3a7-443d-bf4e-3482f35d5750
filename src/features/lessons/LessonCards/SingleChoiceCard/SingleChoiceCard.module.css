.cardContent {
  padding-bottom: 6rem;
  margin-top: 1.5rem;
}

.listItem {
  font-size: 1.1em;
  display: flex;
  align-items: flex-start;
  gap: 0.5em;
  margin-top: 1rem;
  padding: 0.6rem 0.8rem; 
  cursor: pointer;
  border-radius: 3px;
}
  
.itemCheckbox {
  width: 1.5em;
  min-width: 1.5em;
  height: 1.3em;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.1em;
}
    
.checkboxItemText {
  flex-grow: 1;
  line-height: 1.2;
}

.checkboxItemText p {
  margin-top: 0.1rem;
  margin-bottom: 1rem;
}

.submitContainer {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 3rem;
  left: 0;
  right: 0;
}
  
.submitButtonContainer {
  width: auto !important;
}
  
.submitButton {
  border: none;
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.submitButton:focus {
  border: solid 3px;
}

.skipLink {
  display: flex;
  justify-content: center;
  text-transform: none;
  font-size: .8rem;
  text-decoration: none;
  cursor: pointer;
  margin-top: 1.5rem;
}

.skipLink:hover {
  font-weight: bold;
}

.gatedErrorStrip {
  display: flex;
  justify-content: center;
  background-color: #FDC43E;
  font-weight: bold;
  font-size: 1.2rem;
  padding-top: .8rem;
  padding-bottom: .8rem;
  margin-top: 1.5rem;
}

.hiddenGateError {
  display: none;
}

.checkboxItemText li {
  display: list-item !important;
}

@keyframes buttonFadeInOut {
  0% { -webkit-transform: translateY(0); opacity: 0.4 }
  100% { -webkit-transform: translateY(0); opacity: 0.9 }
}
.buttonAnimation {
  animation: buttonFadeInOut 0.6s infinite alternate ease-in-out;
}