/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { get, omit } from 'lodash';
import { useTranslation } from 'react-i18next';
import { Box, Checkbox, styled, useTheme } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSquare } from '@fortawesome/free-solid-svg-icons';
import Button from '../../../../components/Button/Button';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import CheckmarkIcon from '../../../../images/checkedmark.svg';

import styles from './MultipleChoiceCard.module.css';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

const SquareUncheckedIcon = styled((props) => (<FontAwesomeIcon icon={faSquare} {...props} />
))(({ theme }) => ({
  border: theme.palette.border.slider,
  color: theme.palette.primary.contrastText,
  fontSize: '1.125rem',
  borderRadius: '0.125rem',
  width: '1em',
  transition: 'all 0.2s ease-out',
}));

const initialCheckboxState = { answer1: false, answer2: false, answer3: false, answer4: false, answer5: false, answer6: false, answer7: false, answer8: false, answer9: false, answer10: false };

export default function MultipleChoiceCard(props) {
  // eslint-disable-next-line no-unused-vars
  const { id, multipleChoices, title, description, fontColor, onSaveAnswer, saveAnswerLoading, onSkipCard, eventLoading } = props;
  const [checkboxAnswer, setCheckboxAnswer] = useState(initialCheckboxState);
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();
  const { palette } = useTheme();
  const [answerFocused, setAnswerFocused] = useState(null);

  function handleCheckboxChange({ key, value }) {
    const updatedCheckboxAnswer = {
      ...omit(checkboxAnswer, '__typename'),
      [key]: value,
    };
    setCheckboxAnswer(updatedCheckboxAnswer);
  }

  function handleSubmit() {
    onSaveAnswer({
      updateCacheKey: `MultipleChoiceCard:${id}`,
      answer: {
        type: 'MULTIPLE_CHOICE',
        multipleChoiceAnswer: checkboxAnswer,
      },
    });
  }

  const atLeastOneAnswerChecked = checkboxAnswer && Object.values(checkboxAnswer).some((answer) => answer === true);

  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`MultipleChoiceCard-${id}`}
    >
      <fieldset id={`fieldset-${id}`} aria-labelledby={`fieldset-${id}`} className={styles.fieldsetStyle}>
        <legend style={{ display: 'contents' }}>
          <CardTitleDescription
            title={title}
            description={description}
            fontColor={fontColor}
            hiddenText={t('general.question')}
            boxProps={{ id: `question-${id}` }}
          />
        </legend>
        <div className="srOnly">
          {t('lessonCards.results_description_multi', { numAnswers: multipleChoices.length })}
        </div>
        <Box mb={isSmallMobile ? 5 : 0}>
          {multipleChoices.map((item, i) => {
            const key = `answer${i + 1}`;
            return (
              <Box
                className={styles.listItem}
                  // eslint-disable-next-line react/no-array-index-key
                key={`NumberedListItem-${i}`}
                onClick={() => { handleCheckboxChange({ key, value: !checkboxAnswer[key] }); }}
                sx={{
                  color: checkboxAnswer[key] ? 'primary.contrastText' : 'text.primary',
                  backgroundColor: checkboxAnswer[key] ? 'primary.main' : 'primary.light',
                  '&:hover': {
                    backgroundColor: checkboxAnswer[key] ? 'primary.main' : 'background.highlighted',
                  },
                  outline: answerFocused === key ? `3px solid ${palette.card.backgroundColor}` : '',
                }}
              >
                <div className={styles.itemCheckbox} aria-labelledby={`question-${id}`}>
                  <Checkbox
                    disableRipple
                    sx={{ '& .MuiSvgIcon-root': { fontSize: 28 },
                      '&:hover': { backgroundColor: 'transparent' },
                    }}
                    checked={get(checkboxAnswer, key, false)}
                    // eslint-disable-next-line max-len
                    inputProps={{ 'aria-labelledby': `choice-${i + 1}`, 'data-cy': `Choice-${i}`, 'aria-checked': checkboxAnswer[key] }}
                    icon={<SquareUncheckedIcon aria-hidden />}
                    checkedIcon={<CheckmarkIcon aria-hidden />}
                    onFocus={() => setAnswerFocused(key)}
                    onBlur={() => setAnswerFocused(null)}
                  />
                </div>
                <div
                  id={`choice-${i + 1}`}
                  aria-hidden
                  className={styles.checkboxItemText}
                  style={{ fontWeight: get(checkboxAnswer, key, false) ? 600 : 'normal', color: fontColor }}
                  dangerouslySetInnerHTML={{ __html: item }}
                  data-cy={`multipleChoiceListItem-${i}`}
                />
              </Box>
            );
          })}
        </Box>
        {/* <div className={styles.skipLink} onClick={() => onSkipCard()}>
      <span>{t('lessonCards.dontKnowSkip')}</span>
    </div> */}
        <div className={styles.submitContainer}>
          <Button
            containerStyle={styles.submitButtonContainer}
            className={!eventLoading ? `${styles.submitButton} ${styles.buttonAnimation}` : styles.submitButton}
            style={{ borderColor: palette.card.backgroundColor }}
            loading={saveAnswerLoading}
            onClick={handleSubmit}
            disabled={!atLeastOneAnswerChecked || !eventLoading}
            variant="contained"
            data-cy="submit"
          >
            {t('platform.submitButton')}
          </Button>
        </div>
      </fieldset>
    </CardResponsiveText>
  );
}
