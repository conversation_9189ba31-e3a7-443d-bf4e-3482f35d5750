/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { useTheme, Box } from '@mui/material';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import styles from './BorderlessTextImageCard.module.css';

export default function BorderlessTextImageCard(props) {
  const { id, description, title, fontColor, imageUrl, imageAltText } = props;
  const [imageLoaded, setImageLoaded] = useState(false);
  const { isSmallMobile } = useResponsiveMode();
  const { palette } = useTheme();

  return (
    <CardResponsiveText
      key={`BorderlessTextImageCard-${id}`}
      containerStyle={isSmallMobile ? styles.cardContent : ''}
    >
      <div>
        <div className={styles.borderlessImageWrapper} style={{ margin: isSmallMobile ? '-1.6rem' : '-1.8rem' }}>
          <img
            className={styles.image}
            src={imageUrl}
            alt={imageAltText}
            onLoad={() => setImageLoaded(true)}
            style={{ display: imageLoaded ? '' : 'none' }}
            data-cy="image"
          />
        </div>
        <div style={{ marginTop: title ? '1rem' : '' }}>
          <CardTitleDescription title={title} fontColor={fontColor} />
        </div>
        <Box
          className={styles.paragraph}
          style={{ marginBottom: isSmallMobile ? '4.5rem' : 0, marginTop: title ? 0 : '-1.15rem', color: fontColor }}
          dangerouslySetInnerHTML={{ __html: description }}
          sx={{
            // eslint-disable-next-line max-len
            '& p > a, & h2 > a, & li > a': { color: `${palette.link.document} !important`, textDecoration: 'none', fontWeight: 'bold' },
          }}
          data-cy="imageDescription"
        />
      </div>
    </CardResponsiveText>
  );
}
