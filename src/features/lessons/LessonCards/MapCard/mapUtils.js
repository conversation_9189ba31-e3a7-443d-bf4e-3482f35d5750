import { mapDataUs } from '../../../../simplemaps/mapdata-us';
import { mapDataCa } from '../../../../simplemaps/mapdata-ca';
import { mapInfoUs, mapInfoCa } from '../../../../simplemaps/mapinfo-export';
import { stateNames } from '../../../../simplemaps/stateNames';

const US_MAP = 'US_MAP';

export function loadMap({ id, type, onMapLoad, handleMapClick }) {
  const { map, data, info, states } = getMapData({ type });
  map.mapdata = data;
  map.mapinfo = info;
  data.main_settings.div = `map-of-states-${id}`;
  map.create();
  map.load();
  map.hooks.click_state = handleMapClick;
  onMapLoad(states);
}

function getMapData({ type }) {
  const { simpleMapsUs, simpleMapsCa } = window;
  return {
    map: type === US_MAP ? simpleMapsUs : simpleMapsCa,
    data: type === US_MAP ? mapDataUs : mapDataCa,
    info: type === US_MAP ? mapInfoUs : mapInfoCa,
    states: type === US_MAP ? stateNames.US : stateNames.CA,
  };
}
