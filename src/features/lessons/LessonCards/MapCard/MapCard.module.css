.container {
  display: flex;
  align-items: center;
}

.usContainer {
  width: 100%;
  width: 100%;
}

.caContainer {
  width: 95%;
  width: 95%;
  margin: 0 auto;
}

.selectLabel {
  font-size: 0.9em;
  width: 100%;
}

.select {
  width: 65%;
  max-width: 20rem;
  margin-top: 0.5rem;
  font-size: 0.9em;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

.caSelect {
  width: 100%;
  max-width: 20rem;
  margin-top: 0.5rem;
  font-size: 0.9em;
  margin-bottom: 1rem;
}

.title {
  font-weight: bold;
  padding-bottom: 1rem;
  margin-block-start: 0;
  margin-block-end: 0;
  font-size: 1em;
  padding-right: 1.2rem;
}

.paragraph {
  line-height: 1.2;
  padding-right: 1.2rem;
}

.paragraph p {
  margin-bottom: 1.2rem;
  padding-right: 1.2rem;
}

.paragraph li {
  display: list-item;
  text-align: -webkit-match-parent;
}
