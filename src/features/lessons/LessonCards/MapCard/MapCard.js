/* eslint-disable react/no-danger */
import { get } from 'lodash';
import React, { useEffect, useState } from 'react';
import { Box, FormControl, InputLabel, MenuItem, Select, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../../../hooks/useUser';
import { useSpeechAudio } from '../../../../hooks/useSpeechAudio';
import { loadMap } from './mapUtils';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import '../../../../simplemaps/raphael';
import '../../../../simplemaps/usmap-eval';
import { stateNames } from '../../../../simplemaps/stateNames';
import styles from './MapCard.module.css';
import Dialog from '../../../../components/Modal/Dialog';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import AudioButton from '../../../../components/Button/AudioButton';
import SpeechAudio from '../../../../components/LessonCard/SpeechAudio/SpeechAudio';

export default function MapCard(props) {
  const { id, title, fontColor, type, mapKey, mapTraits } = props;

  const user = useUser();
  const { pausePopupAudio, setPopupOpen } = useSpeechAudio();

  const [locations, setLocations] = useState({});
  const [selectedLocation, setSelectedLocation] = useState('');
  const [modalData, setModalData] = useState(null);

  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isSmallMobile } = useResponsiveMode();

  useEffect(() => {
    const onMapLoad = (states) => {
      setLocations(states);
    };
    loadMap({ id, type, onMapLoad, handleMapClick });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function handleMapClick(locationId) {
    const mapTrait = mapTraits.find(({ locationAbbreviation }) => locationAbbreviation === locationId);
    setPopupOpen(true);
    setModalData(mapTrait);
  }

  const handleLocationChange = (event) => {
    const locationId = event.target.value;
    const mapTrait = mapTraits.find(({ locationAbbreviation }) => locationAbbreviation === locationId);
    setSelectedLocation(locationId);
    setModalData(mapTrait);
  };

  const isUsMap = type === 'US_MAP';
  const inputLabelText = isUsMap ? t('lessonCards.select_state') : t('lessonCards.select_province');
  const selectLabelText = isUsMap ? t('lessonCards.click_select_state') : t('lessonCards.click_select_province');
  const speechAudioURL = get(modalData, 'speechAudioURL');
  return (
    <>
      <CardResponsiveText key={`MapCard-${id}`}>
        <CardTitleDescription title={title} fontColor={fontColor} />
        <div aria-hidden="true">
          <div id={`map-of-states-${id}`} key={id} className={`${isUsMap ? styles.usContainer : styles.caContainer}`} />
        </div>
        <Box mb={isSmallMobile ? 8 : 0}>
          <p className={styles.selectLabel}>{selectLabelText}</p>
          <FormControl size="small" className={`${isUsMap ? styles.select : styles.caSelect}`}>
            <InputLabel id="map-select-label">{inputLabelText}</InputLabel>
            <Select
              labelId="map-select-label"
              id="map-select"
              value={selectedLocation}
              label={`${inputLabelText}`}
              onChange={handleLocationChange}
              inputProps={{ 'data-cy': 'selectState' }}
              data-cy="mapSelectLabel"
              sx={{
                '&.MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderWidth: '2px',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: palette.card.backgroundColor,
                    borderWidth: '3px',
                  },
                },
              }}
            >
              {Object.keys(locations).map((location) => (
                <MenuItem key={location} value={location} data-cy={location}>
                  {stateNames[mapKey][location]}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </CardResponsiveText>
      <Dialog
        ariaLabelledby={`${mapKey}-header-${id}`}
        open={!!modalData}
        onClose={() => { pausePopupAudio(); setModalData(null); setPopupOpen(false); }}
      >
        <CardResponsiveText>
          <h2 className={styles.title} data-cy="locationTitle" id={`${mapKey}-header-${id}`}>
            {/* eslint-disable-next-line max-len */}
            {`${stateNames[mapKey][get(modalData, 'locationAbbreviation')]} (${get(modalData, 'locationAbbreviation')})`}
          </h2>
          <Box
            className={styles.paragraph}
            dangerouslySetInnerHTML={{ __html: get(modalData, 'locationTraitText') }}
            sx={{
              // eslint-disable-next-line max-len
              '& p > a, & h2 > a, & li > a': { color: `${palette.link.document} !important`, textDecoration: 'none', fontWeight: 'bold' },
            }}
            data-cy="locationTraitText"
          />
        </CardResponsiveText>
        <AudioButton inPopup />
        {user.audio && speechAudioURL && (
          <SpeechAudio popupAudioURL={speechAudioURL} open={!!modalData} />
        )}
      </Dialog>
    </>
  );
}
