/* eslint-disable react/no-danger */
import React from 'react';
import { useTheme } from '@mui/material';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

import styles from './NumberedListCard.module.css';

export default function NumberedListCard(props) {
  const { id, listItems, title, fontColor } = props;
  const { palette } = useTheme();
  const { isSmallMobile } = useResponsiveMode();
  return (
    <CardResponsiveText key={`NumberedListCard-${id}`}>
      <CardTitleDescription title={title} fontColor={fontColor} />
      <ul className={styles.olListItem}>
        {listItems.map((item, i) => (
        // eslint-disable-next-line react/no-array-index-key
          <li className={styles.listItem} data-cy={`number_card${i}`} key={`NumberedListItem-${i}`}>
            <dd className={styles.numberedItemPrompt} style={{ background: palette.background.bullet }}>
              {i + 1}
            </dd>
            <dd
              data-cy={`numberedListItem-${i}`}
              style={{ color: fontColor }}
              className={styles.numberedItemText}
              dangerouslySetInnerHTML={{ __html: item }}
            />
          </li>
        ))}
      </ul>
      {isSmallMobile && <div style={{ marginBottom: '4.5rem' }} />}
    </CardResponsiveText>
  );
}
