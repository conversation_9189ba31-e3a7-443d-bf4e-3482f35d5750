/* eslint-disable max-len */
/* eslint-disable react/no-danger */

import React, { useState } from 'react';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { capitalize } from 'lodash';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import HalfDoughnutGraph from '../../../../components/HalfDoughnutGraph/HalfDoughnutGraph';
import Button from '../../../../components/Button/Button';
import styles from './WorkplaceColorSpectrumCard.module.css';

const angles = ['', '-67.5deg', '-22.5deg', '22.5deg', '67.5deg'];
let angle = '0deg';

export default function WorkplaceColorSpectrumCard(props) {
  const { id, description, title, fontColor, onSaveAnswer, saveAnswerLoading, regionAbbreviation, eventLoading } = props;
  const { palette } = useTheme();
  const { t } = useTranslation();

  const [selectedColor, setSelectedColor] = useState(null);

  const [hoverColor, setHoverColor] = useState(0);
  const needleClasses = ['centerNeedle', 'greenNeedle', 'yellowNeedle', 'orangeNeedle', 'redNeedle'];

  const { isSmallMobile, isMobile } = useResponsiveMode();
  let size;
  let bottom;
  if (isSmallMobile) {
    size = '36vw';
    bottom = '18vw';
  } else if (isMobile) {
    size = '31vw';
    bottom = '15.5vw';
  } else {
    size = '220px';
    bottom = '110px';
  }
  function getNeedleClass() {
    if (selectedColor) {
      const key = `${needleClasses[selectedColor]}`;
      return `${styles[key]}`;
    }
    if (hoverColor) {
      const key = `${needleClasses[hoverColor]}`;
      return `${styles[key]}`;
    }
    return 'centerNeedle';
  }
  function setAngle() {
    let newAngle = '0deg';
    if (selectedColor) {
      newAngle = angles[selectedColor];
    } else if (hoverColor) {
      newAngle = angles[hoverColor];
    }
    if (angle !== newAngle) {
      angle = newAngle;
    }
  }
  const centerNeedleClassName = 'centerNeedle';
  const needleClassName = getNeedleClass();
  setAngle();

  const colorDescriptions = {
    1: {
      key: 'GREEN',
      text: t(regionAbbreviation ? `colorSpectrum.green_description_region_${regionAbbreviation}` : 'colorSpectrum.green_description_region_US'),
      translatedKey: t('lessonCards.green') },
    2: {
      key: 'YELLOW',
      text: t(regionAbbreviation ? `colorSpectrum.yellow_description_region_${regionAbbreviation}` : 'colorSpectrum.yellow_description_region_US'),
      translatedKey: t('lessonCards.yellow') },
    3: {
      key: 'ORANGE',
      text: t(regionAbbreviation ? `colorSpectrum.orange_description_region_${regionAbbreviation}` : 'colorSpectrum.orange_description_region_US'),
      translatedKey: t('lessonCards.orange') },
    4: {
      key: 'RED',
      text: t(regionAbbreviation ? `colorSpectrum.red_description_region_${regionAbbreviation}` : 'colorSpectrum.red_description_region_US'),
      translatedKey: t('lessonCards.red') },
  };

  const translatedPieLabels = {
    'pie-labels-0': { name: capitalize(t('lessonCards.green')), label: capitalize(t('lessonCards.green_text')) },
    'pie-labels-1': { name: capitalize(t('lessonCards.yellow')), label: capitalize(t('lessonCards.yellow_text')) },
    'pie-labels-2': { name: capitalize(t('lessonCards.orange')), label: capitalize(t('lessonCards.orange_text')) },
    'pie-labels-3': { name: capitalize(t('lessonCards.red')), label: capitalize(t('lessonCards.red_text')) },
  };

  const FormattedDescription = ({ selectedText }) => {
    const originalText = colorDescriptions[selectedText].text;
    const normalizedText = originalText.replace(/\.([\s　]?)(?=[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}])/gu, '。');
    const sentenceRegex = /[^.!?。！？]+[.!?。！？]/g;
    const splitText = normalizedText.match(sentenceRegex) || [];
    const firstSentence = splitText[0] || '';
    const remainingSentence = splitText.slice(1).join('');
    return (
      <p className={styles.text} data-cy={firstSentence}>
        <span style={{ fontWeight: 600 }}>{firstSentence}</span>{' '}
        {remainingSentence}
      </p>
    );
  };

  const sliceData = [
    { x: 1, y: 25, ariaLabel: `${t('lessonCards.green')} - ${t('lessonCards.green_text')}` },
    { x: 2, y: 25, ariaLabel: `${t('lessonCards.yellow')} - ${t('lessonCards.yellow_text')}` },
    { x: 3, y: 25, ariaLabel: `${t('lessonCards.orange')} - ${t('lessonCards.orange_text')}` },
    { x: 4, y: 25, ariaLabel: `${t('lessonCards.red')} - ${t('lessonCards.red_text')}` }];

  function handleSubmit() {
    onSaveAnswer({
      updateCacheKey: `WorkplaceColorSpectrumCard:${id}`,
      answer: {
        type: 'WORKPLACE_COLOR_SPECTRUM',
        workplaceColorSpectrumAnswer: colorDescriptions[selectedColor].key,
      },
    });
  }

  const needleStyle = {
    pointerEvents: 'none',
    position: 'relative',
    bottom,
    overflow: 'visible',
    width: size,
    height: size,
    backgroundSize: `${size} ${size}`,
    backgroundPositionX: 'center',
    backgroundPositionY: 'center',
    '--deg': angle,
  };

  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`WorkplaceColorSpectrumCard-${id}`}
    >
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      <div>
        <HalfDoughnutGraph
          colorScale={palette.background.wcs}
          data={sliceData}
          selectedColor={selectedColor}
          setSelectedColor={setSelectedColor}
          hoverColor={hoverColor}
          setHoverColor={setHoverColor}
          colorDescriptions={colorDescriptions}
          translatedPieLabels={translatedPieLabels}
        />
      </div>
      <div className={styles.needleContainer} style={{ background: palette.doughnutGraph.strokeColor }}>
        <div
          className={`${styles[centerNeedleClassName]} ${needleClassName}`}
          style={needleStyle}
        />
      </div>
      <div
        aria-hidden
        className={styles.textContainer}
        style={{ marginBottom: isSmallMobile ? '2.5rem' : 0 }}
      >
        {
          hoverColor && !selectedColor
            ? <FormattedDescription selectedText={hoverColor} />
            : null
        }
        {
          selectedColor
            ? <FormattedDescription selectedText={selectedColor} />
            : null
        }
      </div>
      <div className={styles.submitContainer}>
        <Button
          containerStyle={styles.submitButtonContainer}
          className={!eventLoading ? `${styles.submitButton} ${styles.buttonAnimation}` : styles.submitButton}
          style={{ borderColor: palette.card.backgroundColor }}
          loading={saveAnswerLoading}
          onClick={handleSubmit}
          disabled={!selectedColor || !eventLoading}
          variant="contained"
          data-cy="submit"
          tabIndex={0}
          id="wcs_submit_button"
        >
          {t('platform.submitButton')}
        </Button>
      </div>
    </CardResponsiveText>
  );
}
