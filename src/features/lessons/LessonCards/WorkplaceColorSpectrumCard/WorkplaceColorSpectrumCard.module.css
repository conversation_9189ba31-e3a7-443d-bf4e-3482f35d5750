.cardContent {
  padding-bottom: 6rem;
  margin-top: 1.5rem;
}

.textContainer {
  display: flex;
  justify-content: center;
  min-height: 5.5rem;
}

.text {
  margin-top: 1.4rem;
  text-align: center;
  font-size: .6em;
  line-height: 1.3em;
  width: 75%;
  /* TODO - uncomment when re-render issue is fixed
  animation: fadeIn linear 1s;
  -webkit-animation: fadeIn linear 1s;
  -moz-animation: fadeIn linear 1s;
  -o-animation: fadeIn linear 1s;
  -ms-animation: fadeIn linear 1s; */
}

@keyframes fadeIn {
  0% {opacity:0;}
  100% {opacity:1;}
}

.submitContainer {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 3rem;
  left: 0;
  right: 0;
}

.submitButtonContainer {
  width: auto !important;
}

.submitButton {
  border: none;
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.submitButton:focus {
  border: solid 3px;
}

.needleContainer {
  display: flex;
  justify-content: center;
  height: 2px;
  width: 70%;
  overflow: visible;
  margin: -9px auto 0 auto;
}

:root {
  --deg: 0deg;
  --duration: 250ms;
}

.greenNeedle, .yellowNeedle, .orangeNeedle, .redNeedle, .centerNeedle {
  transform: rotate(var(--deg));
  transition: transform var(--duration);
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  will-change: transform;
}

.centerNeedle {
  background: url('../../../../images/needleNeutral.png') no-repeat;
}

.greenNeedle {
  background: url('../../../../images/needleSelected.png') no-repeat;
}

.yellowNeedle  {
  background: url('../../../../images/needleSelected.png') no-repeat;
}

.orangeNeedle {
  background: url('../../../../images/needleSelected.png') no-repeat;
}

.redNeedle {
  background: url('../../../../images/needleSelected.png') no-repeat;
}

@keyframes buttonFadeInOut {
  0% { -webkit-transform: translateY(0); opacity: 0.4 }
  100% { -webkit-transform: translateY(0); opacity: 0.9 }
}
.buttonAnimation {
  animation: buttonFadeInOut 0.6s infinite alternate ease-in-out;
}
