import React, { useRef, useEffect, useState } from 'react';
import { useRouteMatch } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import ProgramCongratulationsImage from '../../../../images/certificate-checkmark.png';
import Button from '../../../../components/Button/Button';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { downloadCompletionCertificate } from '../../../../services/api/completionCert';
import { useUser } from '../../../../hooks/useUser';
import styles from './CompletionCertificateCard.module.css';
import SaveExitCard from '../ScromCompletionCard/SaveExitCard';

export default function CompletionCertificateCard(props) {
  const { params: routeParams } = useRouteMatch();
  const {
    id, programId, programTitle, downloadInstructions,
    hasCertificate, completedMessage
  } = props;
  const assignmentId = routeParams.assignmentId;
  const { t } = useTranslation();
  const { palette } = useTheme();
  const user = useUser();
  const { isSmallMobile } = useResponsiveMode();
  const [showDownloadButton, setShowDownloadButton] = useState(true);
  const [downloadCertificate, setDownloadCertificate] = useState(false);
  let minHeightStyles = isSmallMobile ? styles.downloadTextSmallMobile : styles.downloadTextAll;

  const screenReaderFocusRef = useRef(null);
  useEffect(() => {
    if (screenReaderFocusRef && screenReaderFocusRef.current) {
      screenReaderFocusRef.current.focus();
    }
  }, []);

  const handleDownload = async (programId, assignmentId) => {
    setDownloadCertificate(true);
    const response = await downloadCompletionCertificate(programId, assignmentId);
    if (response) {
      setDownloadCertificate(false);
    }
    setShowDownloadButton(user.scorm ? false : true);
  };
  if (!showDownloadButton) {
    minHeightStyles = styles.downloadSaveExitText;
  }

  return (
    <div key={`CompletionCertificateCard-${id}`}>
      <div
        aria-hidden
        className={`${styles.centeredSection} ${styles.congratsImage}`}
      >
        <img src={ProgramCongratulationsImage} className={styles.image} alt="certificate-checkmark-img" />
      </div>
      {/* eslint-disable-next-line max-len, jsx-a11y/no-noninteractive-tabindex */}
      <div tabIndex="0" ref={screenReaderFocusRef} className={`${styles.centeredSection} ${styles.certificateCompletionTitle}`}>
        <h3 className={styles.title} style={{ color: palette.primary.contrastText }} data-cy="certificate_ready">
          {t('lessonCards.completion_certificate_ready')}
        </h3>
        <p style={{ color: palette.primary.contrastText }} data-cy="program_title">{programTitle}</p>
      </div>
      <div className={styles.contentContainer} style={{ marginBottom: isSmallMobile ? '4rem' : 0 }}>
        {!downloadInstructions && <div className={minHeightStyles} />}
        {downloadInstructions && (
          <div
            className={downloadInstructions.length < 240 ?
              `${styles.downloadText} ${minHeightStyles}` :
              `${styles.downloadTextSmall} ${minHeightStyles}`}
            style={{ color: palette.primary.contrastText }}
          >
            {downloadInstructions}
          </div>
        )}
        {showDownloadButton ? (
          <Button
            containerStyle={styles.submitButtonContainer}
            className={styles.submitButton}
            disabled={downloadCertificate}
            style={{ backgroundColor: palette.background.darkDarkPink, fontSize: '1.2rem' }}
            sx={{
              borderRadius: '1.5rem',
              padding: '0.313rem',
              '&:hover': {
                backgroundColor: `${palette.button.downloadCertificate.backgroundHoverColor}!important`,
              },
              '&:focus': {
                outline: `3px solid ${palette.card.backgroundColor}`,
              }
            }}
            variant="contained"
            data-cy="download_certificate"
            onClick={() => handleDownload(programId, assignmentId)}
          >
            {t('lessonCards.download_certificate')}
          </Button>
        ) :
          <SaveExitCard hasCertificate={hasCertificate} completedMessage={completedMessage} isCompletionCertificateCard={true} />
        }
      </div>
    </div>
  );
}
