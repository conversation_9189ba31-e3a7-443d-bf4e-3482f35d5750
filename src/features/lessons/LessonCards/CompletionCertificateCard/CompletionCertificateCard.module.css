.centeredSection {
  text-align: center;
  clear: both;
  width: 100%;
}

.paragraph {
  line-height: 1.4em;
}

.paragraph p {
  margin-bottom: 1.2rem;
  font-size: 1.1em;
}

.paragraphSmall {
  line-height: 1.5rem;
}

.paragraphSmall p {
  margin-bottom: 1rem;
}

.congratsImage {
  padding: 1rem 0 !important;
}

.title {
  margin-bottom: 1rem !important;
  font-weight: 600 !important;
  font-size: 2rem !important;
}

.certificateCompletionTitle {
  margin-bottom: 1.5rem !important;
  line-height: 1.2em;
}

.certificateCompletionTitle > h3 {
  margin-bottom: 0.5rem;
}
.certificateCompletionTitle > p {
  font-weight: 600;
  font-size: 1.6rem;
}

.downloadTextSmallMobile {
  min-height: 200px;
}

.downloadTextAll {
  min-height: 300px;
}

.downloadSaveExitText {
  min-height: 30px;
}

.downloadText {
  text-align: center;
  font-size: 1.2rem;
}

.downloadTextSmall {
  text-align: center;
  font-size: 1rem;
}

.submitButtonContainer {
  width: auto !important;
}

.submitButton {
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.image {
  border-radius: 3px;
  max-height: 6rem;
  max-width: 6rem;
}