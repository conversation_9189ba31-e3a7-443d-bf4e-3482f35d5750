import React from 'react';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

export default function TextCard(props) {
  const { id, description, title, fontColor } = props;
  const { isSmallMobile } = useResponsiveMode();

  return (
    <CardResponsiveText key={`TextCard-${id}`}>
      <CardTitleDescription
        title={title}
        description={description}
        fontColor={fontColor}
        boxProps={{ mb: isSmallMobile ? 9 : 0 }}
      />
    </CardResponsiveText>
  );
}
