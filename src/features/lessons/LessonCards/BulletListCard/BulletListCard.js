/* eslint-disable react/no-danger */
import React from 'react';
import BulletIcon from '../../../../images/bullet.svg';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';

import styles from './BulletListCard.module.css';

export default function BulletListCard(props) {
  const { id, listItems, title, fontColor } = props;
  return (
    <CardResponsiveText key={`NumberedListCard-${id}`}>
      <CardTitleDescription title={title} fontColor={fontColor} />
      <ul className={styles.ulListItem}>
        {listItems.map((item, i) => (
        // eslint-disable-next-line react/no-array-index-key
          <li
            className={styles.listItem}
            data-cy={`NumberedListItem-${i}`}
            style={{ color: fontColor }}
            key={`NumberedListItem-${item}`}
          >
            <dd className={styles.itemBullet}>
              <BulletIcon aria-hidden="true" focusable="false" />
            </dd>
            <dd className={styles.bulletItemText} dangerouslySetInnerHTML={{ __html: item }} />
          </li>
        ))}
      </ul>
    </CardResponsiveText>
  );
}
