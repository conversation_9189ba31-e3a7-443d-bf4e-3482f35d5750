/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import styles from './TextImageOverlayCard.module.css';

export default function TextImageOverlayCard(props) {
  const { id, description, title, imageUrl, imageAltText, fontColor, textOffset } = props;
  const [imageLoaded, setImageLoaded] = useState(false);
  // eslint-disable-next-line radix
  const marginTop = textOffset ? (`${(textOffset) * 10}%`) : 0;
  const alt = imageAltText || '';
  const { isSmallMobile } = useResponsiveMode();
  const paddingTop = isSmallMobile ? 0 : '8%';

  return (
    <>
      <div className={styles.imageContainer}>
        <img
          className={styles.image}
          src={imageUrl}
          alt={alt}
          onLoad={() => setImageLoaded(true)}
          style={{ display: imageLoaded ? '' : 'none' }}
        />
      </div>
      <CardResponsiveText
        key={`TextImageOverlayCard-${id}`}
        containerStyle={isSmallMobile ? styles.cardContent : ''}
      >
        <CardTitleDescription
          title={title}
          description={description}
          fontColor={fontColor}
          descriptionMarginTop={marginTop}
          descriptionPaddingTop={paddingTop}
        />
      </CardResponsiveText>
    </>
  );
}
