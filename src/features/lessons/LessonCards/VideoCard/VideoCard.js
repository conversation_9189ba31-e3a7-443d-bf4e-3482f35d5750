/* eslint-disable react/no-danger */
import React from 'react';
import { Box, useTheme } from '@mui/material';
import Video from '../../../../components/Video/Video';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useAccessibilityHelper } from '../../../../hooks/useAccessibilityHelper';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useUser } from '../../../../hooks/useUser';
import styles from './VideoCard.module.css';
import { get } from 'lodash';

export default function VideoCard(props) {
  const {
    id,
    description,
    title,
    fontColor,
    videoId,
    lessonId,
    lessonLifecycle,
    addViewedVideo,
    viewedFullVideos,
    setShowVideoTooltip,
    isTranscriptAdded,
    translationLanguage,
    programId,
  } = props;
  const { isSmallMobile } = useResponsiveMode();
  const { stripHTMLForReader } = useAccessibilityHelper();
  const { palette } = useTheme();
  const user = useUser();
  const userLanguage = user.language || 'en';
  const enforceFullVideoView = get(user, `accounts[0].enforceFullVideoView`, false);

  return (
    <CardResponsiveText key={`VideoCard-${id}`}>
      <CardTitleDescription title={title} fontColor={fontColor} />
      {/* description above video for screen reader */}
      <p className="srOnly">{stripHTMLForReader(description)}</p>
      <Video
        id={id}
        title={title}
        videoId={videoId}
        lessonId={lessonId}
        lessonLifecycle={lessonLifecycle}
        addViewedVideo={addViewedVideo}
        isTranscriptAdded={isTranscriptAdded}
        userLanguage={userLanguage}
        programId={programId}
        viewedFullVideos={viewedFullVideos}
        setShowVideoTooltip={setShowVideoTooltip}
        enforceFullVideoView={enforceFullVideoView}
      />
      <Box
        aria-hidden
        className={styles.paragraph}
        style={{ marginBottom: isSmallMobile ? '4.5rem' : 0, color: fontColor }}
        sx={{
          // eslint-disable-next-line max-len
          '& p > a, & h2 > a, & li > a': { color: `${palette.link.document} !important`, textDecoration: 'none', fontWeight: 'bold' },
        }}
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </CardResponsiveText>
  );
}
