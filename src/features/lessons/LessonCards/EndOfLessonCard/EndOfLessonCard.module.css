.centeredSection {
  text-align: center;
  clear: both;
  width: 100%;
}

.endOfLessonTitle {
  margin-bottom: 1.5rem !important;
  line-height: 1.2em;
}

.endOfLessonTitle > h3 {
  font-weight: 600;
  font-size: 2rem !important;
}

.paragraph {
  line-height: 1.4em;
}

.paragraph p {
  margin-bottom: 1.2rem;
  font-size: 1.1em;
}

.paragraphSmall {
  line-height: 1.5rem;
}

.paragraphSmall p {
  margin-bottom: 1rem;
}

.congratsImage {
  padding: 1rem 0 !important;
  line-height: 0;
}

.moreTimeImage {
  padding: 2rem 0 !important;
}

.title {
  margin-bottom: .5rem !important;
  line-height: 1.2em;
  font-family: 'Source Sans Pro';
}


.paragraph {
  font-size: 1rem;
}

.lessonTitle {
  font-style: italic;
}

.completedMessageTextSmallMobile {
  min-height: 12rem;
}

.completedMessageTextAll {
  min-height: 18.75rem;
}

.completedMessageText {
  font-size: 1.2rem;
}

.completedMessageTextSmall {
  font-size: 1rem;
}

.completionCertContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-bottom: 1rem;
}

.completionCertRowItem {
  display: inline-flex;
  align-items: center;
}

.completionCertText {
  font-weight: 600;
  font-size: 1.2rem;
  font-family: 'Source Sans Pro';
}

.completionCertText:hover {
  font-weight: bold;
}

.completionCertArrow {
  width: 1rem;
  height: 1rem;
}

.submitContainer {
  display: flex;
  justify-content: center;
}

.submitButtonContainer {
  width: auto !important;
}

.submitButton {
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}


.bottomBtns {
  margin-top: 0.5rem;
}

.courseTitle {
  font-weight: 600;
  font-size: 1.6rem;
}

.image {
  border-radius: 3px;
  max-height: 6rem;
  max-width: 6rem;
}

.gifSection {
  margin-top: 5rem;
}

.lessonCompleteGif {
  aspect-ratio: 1 / 1;
  width: 90%;
  margin-left: 2rem;
}

.sectionFadeout {
  opacity: .2;
  transition: opacity .4s linear;
}

.fadeIn {
  animation: fadeIn .4s ease-in;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.notice {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 4rem;
  width: 100%;
  font-size: 1.4rem;
  font-weight: bold;
}

.updateMessage {
  text-align: left;
  padding: 5rem 1rem 0 1rem;
  width: 100%;
  font-size: 1.4rem;
}

.continueButtonContainer {
  text-align: center;
  padding-top: 2rem;
}

.continueButton {
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}
