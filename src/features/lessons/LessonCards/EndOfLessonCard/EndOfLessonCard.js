/* eslint-disable react/no-danger */
import React, { useState, useEffect, useRef } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleExclamation, faClock } from '@fortawesome/free-solid-svg-icons';
import ArrowForwardIcon from '@mui/icons-material/ArrowForwardIos';
import CompletionCheckmarkImage from '../../../../images/end-of-completion-checkmark.png';
import LessonCompleteGif from '../../../../images/lesson-complete.gif';
import Button from '../../../../components/Button/Button';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import { useInterval } from '../../../../hooks/useInterval';

import styles from './EndOfLessonCard.module.css';
import CertificateIcon from '../../../../icons/Certificate';
import { useUser } from '../../../../hooks/useUser';
import SaveExitCard from '../ScromCompletionCard/SaveExitCard';

export default function EndOfLessonCard(props) {
  const { id, programTitle, completedMessage,
    lessonTitle, lessonStatus, onNextAssignment,
    checkMoreAssignmentsAvailable, isMicroLesson, setShowCompletionCertificate,
    hasCertificate, isTimedProgram, timeRequirementMet,
    programIsComplete, backNavigation, resetCurrentLesson, percentComplete } = props;
  const { t } = useTranslation();
  const { palette } = useTheme();
  const user = useUser();
  const { isSmallMobile } = useResponsiveMode();
  const [cardDisplayState, setCardDisplayState] = useState(backNavigation ? 'completedDisplay' : 'initialState');

  const displayTitle = programIsComplete ? programTitle : lessonTitle;
  const allContentCompleted = percentComplete === 100;
  const moreAssignmentsAvailable = checkMoreAssignmentsAvailable();
  const minHeightStyles = !user.scorm ? isSmallMobile ? styles.completedMessageTextSmallMobile : styles.completedMessageTextAll : '';

  // this interval handles the intra program transition fade out
  useInterval(() => {
    if (cardDisplayState === 'initialState') {
      setCardDisplayState('gifDisplay');
    } else if (cardDisplayState === 'gifDisplay') {
      setCardDisplayState('fadeOut');
    } else if (cardDisplayState === 'fadeOut') {
      setCardDisplayState('completedDisplay');
    }
  // eslint-disable-next-line no-nested-ternary, max-len
  }, (cardDisplayState === 'initialState' || cardDisplayState === 'gifDisplay') ? 600 : (cardDisplayState === 'fadeOut' ? 400 : null));

  const screenReaderFocusRef = useRef(null);
  useEffect(() => {
    if (cardDisplayState === 'completedDisplay' && screenReaderFocusRef && screenReaderFocusRef.current) {
      screenReaderFocusRef.current.focus();
    }
  }, [cardDisplayState]);

  return (
    <div key={`EndOfLessonCard-${id}`}>
      {lessonStatus !== 'completed' && (
        <>
          <div className={styles.notice} style={{ backgroundColor: palette.background.paused }}>
            {t('lessonCards.notice')}
          </div>
          <div className={styles.updateMessage}>
            <Trans i18nKey="lessonCards.lesson_update_message" />
          </div>
          <div className={styles.continueButtonContainer}>
            <Button
              containerStyle={styles.continueButtonContainer}
              className={styles.continueButton}
              onClick={() => { resetCurrentLesson(); }}
              variant="contained"
              data-cy="reset_assignment"
            >
              {t('lessonCards.continue')}
            </Button>
          </div>
        </>
      )}
      {lessonStatus === 'completed' && (isMicroLesson || programIsComplete) &&
        (!isTimedProgram || timeRequirementMet) && (
        <div>
          <div
            className={`${styles.gifSection} ${cardDisplayState === 'fadeOut' ? styles.sectionFadeout : ''}`}
            style={{
              display: `${cardDisplayState === 'completedDisplay' ? 'none' : ''}`,
              marginTop: `${isSmallMobile ? '3rem' : ''}`,
            }}
          >
            <img
              src={LessonCompleteGif}
              alt="lesson complete"
              className={styles.lessonCompleteGif}
              style={{
                marginLeft: `${isSmallMobile ? '1rem' : ''}`,
              }}
            />
          </div>
          <div
            style={{ display: `${cardDisplayState !== 'completedDisplay' ? 'none' : ''}` }}
            className={`${cardDisplayState === 'completedDisplay' ? styles.fadeIn : ''}`}
          >
            {programIsComplete && (
              <div
                aria-hidden
                className={`${styles.centeredSection} ${styles.congratsImage}`}
              >
                <img src={CompletionCheckmarkImage} className={styles.image} alt="program-img" />
              </div>
            )}
            {isMicroLesson && (
              <div aria-hidden className={`${styles.centeredSection} ${styles.congratsImage}`}>
                <img src={CompletionCheckmarkImage} className={styles.image} alt="lesson-img" />
              </div>
            )}

            {/* eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex, max-len */}
            <div tabIndex="0" ref={screenReaderFocusRef} className={`${styles.centeredSection} ${styles.endOfLessonTitle}`}>
              <h3 className={styles.title} data-cy="congratulation_msg">{t('lessonCards.congratulations')}</h3>
              <p data-cy="completed_msg">{t('lessonCards.youCompleted')}</p>
              <p data-cy="completed_msg" className={styles.courseTitle}>{displayTitle}</p>
            </div>
            <div className={styles.contentContainer}>
              {/* dummy container */}
              {
                (isMicroLesson || (programIsComplete && !completedMessage)) && (
                  <div className={minHeightStyles} />
                )
              }
              {programIsComplete && completedMessage && (
                <div
                  className={completedMessage.length < 240 ?
                    `${styles.completedMessageText} ${minHeightStyles}` :
                    `${styles.completedMessageTextSmall} ${minHeightStyles}`}
                  dangerouslySetInnerHTML={{ __html: completedMessage }}
                  data-cy="end_of_program"
                />
              )}

              {user.scorm && (<SaveExitCard hasCertificate={hasCertificate} completedMessage={completedMessage} />)}
              <div className={styles.bottomBtns} style={{ marginBottom: isSmallMobile ? '3rem' : 0 }}>
                {programIsComplete && hasCertificate && (
                  <div className={styles.completionCertContainer} onClick={() => setShowCompletionCertificate(true)}>
                    <CertificateIcon {...{ width: '19px', height: '19px' }} />
                    <span style={{ width: '.4rem' }} />
                    <div
                      className={`${styles.completionCertRowItem}
                  ${styles.completionCertText}`}
                      data-cy="completion_certificate"
                    >
                      {t('lessonCards.completion_certificate')}
                    </div>
                    <span style={{ width: '.2rem' }} />
                    <div className={styles.completionCertRowItem}>
                      <ArrowForwardIcon className={styles.completionCertArrow} />
                    </div>
                  </div>
                )}
                {moreAssignmentsAvailable && (
                  <div className={styles.submitContainer}>
                    <Button
                      containerStyle={styles.submitButtonContainer}
                      className={styles.submitButton}
                      onClick={() => { onNextAssignment(); }}
                      variant="contained"
                      data-cy="next_assignment"
                    >
                      {t('lessonCards.nextAssignment')}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Time requirement not met */}
      {lessonStatus === 'completed' &&
      (isTimedProgram && !timeRequirementMet) &&
      (allContentCompleted || isMicroLesson) && (
        <div>
          <div
            className={`${styles.centeredSection} ${styles.moreTimeImage}`}
          >
            <FontAwesomeIcon icon={faClock} size="4x" style={{ color: palette.background.paused }} />
          </div>
          <div className={styles.centeredSection}>
            <h3 className={styles.title}>{t('lessonCards.more_time_course')}</h3>
            <p className={styles.paragraph}>{t('lessonCards.review_more_lessons_requirement')}</p>
          </div>
        </div>
      )}
      {lessonStatus === 'completed' && !allContentCompleted && !isMicroLesson && (
        <div>
          <div
            className={`${styles.centeredSection} ${styles.moreTimeImage}`}
          >
            <FontAwesomeIcon icon={faCircleExclamation} size="4x" style={{ color: palette.background.paused }} />
          </div>
          <div className={styles.centeredSection}>
            <h3 className={styles.title}>{t('lessonCards.course_not_complete')}</h3>
            <p className={styles.paragraph}>{t('lessonCards.review_all_lessons_requirement')}</p>
          </div>
        </div>
      )}
    </div>
  );
}
