/* eslint-disable react/no-danger */
import React, { useEffect, useState, useRef } from 'react';
import { get } from 'lodash';
import { useTranslation } from 'react-i18next';
import Dialog from '@mui/material/Dialog';
import { useTheme, Checkbox, styled } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSquare, faCheckSquare } from '@fortawesome/free-regular-svg-icons';
import Button from '../../../../components/Button/Button';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';
import FileIcon from '../../../../icons/File';
import AllSetIcon from '../../../../images/all-set-icon.png';
import ContinueIcon from '../../../../images/continue-icon.png';
import styles from './PolicyAcknowledgementCard.module.css';

const SquareUncheckedIcon = styled((props) => (<FontAwesomeIcon icon={faSquare} {...props} />
))(({ theme }) => ({
  border: theme.palette.border.slider,
  color: theme.palette.primary.contrastText,
  fontSize: '1.125rem',
  borderRadius: '0.125rem',
  width: '1em',
  transition: 'all 0.2s ease-out',
}));

export default function PolicyAcknowledgementCard(props) {
  const { id, description, title, fontColor, policyType, url,
    answer, saveAnswerLoading, onSaveAnswer, onNextCard } = props;

  const { t } = useTranslation();
  const { palette } = useTheme();
  const { isMobile } = useResponsiveMode();
  const [modalOpen, setModalOpen] = useState(false);
  const [linkClicked, setLinkClicked] = useState(false);
  const [focusedAfterAnswerDone, setFocusedAfterAnswerDone] = useState(false);
  const newContentRef = useRef(null);

  useEffect(() => {
    if (modalOpen && isMobile) {
      setModalOpen(false);
      setLinkClicked(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMobile]);

  function handleSubmit() {
    setModalOpen(false);
    onSaveAnswer({
      updateCacheKey: `PolicyAcknowledgementCard:${id}`,
      answer: {
        type: 'POLICY_ACKNOWLEDGEMENT',
        policyAcknowledgementAnswer: true,
      },
    });
  }

  function handleOpenLink(linkURL) {
    window.open(linkURL, 'policyWindow', 'width=1000,height=800,top=200,left=600');
    setLinkClicked(true);
  }

  function handleMouseOver() {
    document.getElementById(`checkedIcon-${id}`).style.display = 'inline-block';
    document.getElementById(`uncheckedIcon-${id}`).style.display = 'none';
  }

  function handleMouseOut() {
    document.getElementById(`checkedIcon-${id}`).style.display = 'none';
    document.getElementById(`uncheckedIcon-${id}`).style.display = 'inline-block';
  }

  const answered = get(answer, 'policyAcknowledgementAnswer') || false;
  const isPDF = (policyType === 'FILE') && url && url.toUpperCase().includes('.PDF');
  const viewPDFInModal = isPDF && !isMobile;

  useEffect(() => {
    if (!focusedAfterAnswerDone && answered && newContentRef && newContentRef.current) {
      newContentRef.current.focus();
      setFocusedAfterAnswerDone(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [answered]);

  return (
    <CardResponsiveText containerStyle={styles.cardContent} key={`PolicyAcknowledgementCard-${id}`}>
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      {url && (
        <div className={styles.viewDocumentLink}>
          {viewPDFInModal && (
            <Button
              onClick={(e) => { e.preventDefault(); setModalOpen(true); }}
              startIcon={<FileIcon color={palette.primary.contrastText} height="1.2rem" />}
              containerStyle={styles.viewDocumentButtonContainer}
              className={styles.viewDocumentButton}
              variant="contained"
              data-cy="viewDocument"
              sx={{
                '&:focus': {
                  border: `3px solid ${palette.card.backgroundColor}`,
                } }}
            >
              {t('lessonCards.viewDocument')}
            </Button>
          )}
          {!viewPDFInModal && (
            <Button
              onClick={() => { handleOpenLink(url); }}
              label={t('lessonCards.viewDocument')}
              startIcon={<FileIcon color={palette.primary.contrastText} height="1.2rem" />}
              containerStyle={styles.viewDocumentButtonContainer}
              className={styles.viewDocumentButton}
              variant="contained"
              data-cy="urlLink"
              sx={{
                '&:focus': {
                  border: `3px solid ${palette.card.backgroundColor}`,
                } }}
            >
              {t('lessonCards.viewDocument')}
            </Button>
          )}
        </div>
      )}
      {!url && (
        <div
          className={styles.viewDocumentLink}
          style={{ color: palette.primary.errorText }}
          data-cy="noPolicyConfigured"
        >
          {t('lessonCards.noPolicyConfigured')}
        </div>
      )}
      {!linkClicked && !answered && url && (
        <div className={styles.mustReviewSection} style={{ marginBottom: isMobile ? '5rem' : '' }}>
          <p data-cy="mustReviewPolicyAck">{t('lessonCards.mustReviewPolicyAck')}</p>
        </div>
      )}
      {(answered || linkClicked) && !viewPDFInModal && url && (
        <div className={styles.acknowledgeCheck} onClick={handleSubmit}>
          <div className={styles.acknowledgeCheckboxContainer}>
            <Checkbox
              sx={{ '& .MuiSvgIcon-root': { fontSize: 28 } }}
              checked={answered}
              disabled={answered}
              inputProps={{ 'data-cy': 'reviewed', 'aria-checked': answered }}
              style={{ padding: 0, color: palette.primary.main }}
              icon={<SquareUncheckedIcon />}
              checkedIcon={<FontAwesomeIcon icon={faCheckSquare} style={{ width: '21px', height: '21px' }} />}
            />
          </div>
          <div data-cy="reviewedPolicyAck">
            {t('lessonCards.reviewedPolicyAck')}
          </div>
        </div>
      )}
      {linkClicked && !answered && url && (
        <div className={styles.mustReviewSection} style={{ marginBottom: isMobile ? '5rem' : 0 }}>
          <p data-cy="mustCheckPolicyAck">{t('lessonCards.mustCheckPolicyAck')}</p>
        </div>
      )}
      {answered && (
        <div className={styles.policyReturnSectionOuter} style={{ marginBottom: isMobile ? '5rem' : 0 }}>
          <div
            ref={newContentRef}
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex="0"
            style={{ clear: 'both' }}
            className={styles.allSetSection}
          >
            <img src={AllSetIcon} className={styles.allSetIcon} alt="" />
            <span className={styles.allSetText}>{t('lessonCards.allSet')}</span>
          </div>
          <div
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex="0"
            className={styles.policyReturnSection}
            data-cy="policyReturnText"
          >
            <p>{t('lessonCards.policyReturnText')}</p>
          </div>
        </div>
      )}
      {answered && (
        <div className={styles.continueLinkContainer}>
          <div
            tabIndex="0"
            role="button"
            style={{ clear: 'both' }}
            className={styles.continueSection}
            onClick={() => onNextCard()}
            onKeyDown={(e) => { if (e.code === 'Space' || e.code === 'Enter') { onNextCard(); } }}
          >
            <span className={styles.continueText}>{t('lessonCards.continue')}</span>
            <img src={ContinueIcon} className={styles.continueIcon} alt="placeholder-img" />
          </div>
        </div>
      )}
      <div>
        <Dialog
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          className={styles.modal}
          maxWidth="xl"
          fullWidth
        >
          <div className={styles.outerModal} style={{ backgroundColor: palette.background.default }}>
            <div className={styles.modalTitle} data-cy="policyAck">
              {t('lessonCards.policy_acknowledgement')}
            </div>
            <div className={styles.pdfContainer}>
              <object
                aria-label={t('lessonCards.pdf_viewer_instructions')}
                data={url}
                type="application/pdf"
                width="100%"
                height="100%"
                name={`pdf-policy-viewer-${id}`}
              />
            </div>
            <div className={styles.submitContainer} style={{ justifyContent: answered ? 'flex-end' : 'space-between' }}>
              {!answered && (
                <Button
                  onClick={() => setModalOpen(false)}
                  containerStyle={styles.cancelButtonContainer}
                  className={styles.submitButton}
                  // loading={saveAnswerLoading}
                  // onClick={handleSubmit}
                  // disabled={!choice}
                  variant="contained"
                  style={{
                    color: palette.primary.main,
                    backgroundColor: palette.background.default }}
                  data-cy="cancel"
                  sx={{
                    '&:focus': {
                      border: `3px solid ${palette.card.backgroundColor}`,
                    } }}
                >
                  {t('platform.cancel')}
                </Button>
              )}
              {!answered && (
                <Button
                  containerStyle={styles.submitButtonContainer}
                  className={styles.submitButton}
                  loading={saveAnswerLoading}
                  onClick={handleSubmit}
                  onMouseEnter={handleMouseOver}
                  onMouseLeave={handleMouseOut}
                  variant="contained"
                  data-cy="review"
                  sx={{
                    '&:focus': {
                      border: `3px solid ${palette.card.backgroundColor}`,
                    } }}
                >
                  <FontAwesomeIcon
                    id={`uncheckedIcon-${id}`}
                    icon={faSquare}
                    style={{
                      color: styles.submitButton.color,
                      fontSize: '1.2rem',
                      marginRight: '0.4rem',
                      pointerEvents: 'none',
                    }}
                  />
                  <FontAwesomeIcon
                    id={`checkedIcon-${id}`}
                    icon={faCheckSquare}
                    style={{
                      color: styles.submitButton.color,
                      fontSize: '1.2rem',
                      marginRight: '0.4rem',
                      pointerEvents: 'none',
                      display: 'none',
                    }}
                  />
                  <p data-cy="reviewedPolicyAck">{t('lessonCards.reviewedPolicyAck')}</p>
                </Button>
              )}
              {answered && (
                <Button
                  onClick={() => setModalOpen(false)}
                  containerStyle={styles.submitButtonContainer}
                  className={styles.submitButton}
                  variant="contained"
                  data-cy="close"
                  sx={{
                    '&:focus': {
                      border: `3px solid ${palette.card.backgroundColor}`,
                    } }}
                >
                  {t('lessonCards.close')}
                </Button>
              )}
            </div>
          </div>
        </Dialog>
      </div>
    </CardResponsiveText>
  );
}
