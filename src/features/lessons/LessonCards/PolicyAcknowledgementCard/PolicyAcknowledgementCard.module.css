.cardContent {
  margin-top: 1.5rem;
}

.viewDocumentLink {
  padding-top: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.viewDocumentLink a {
  padding-left: .4rem;
  text-decoration: none;
  font-weight: bold;
}

.viewDocumentButtonContainer {
  width: auto !important;
}

.viewDocumentButton {
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
  border-radius: 1.5rem;
}

.acknowledgeCheck {
  padding: 2rem 1rem 0 1rem;
  line-height: 1.35rem;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.acknowledgeCheckboxContainer {
  align-self: flex-start;
  margin-right: 0.5rem;
}

.mustReviewSection {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.4rem 0 1rem 0;
}

.mustReviewSection p {
  text-align: center;
  font-size: 1rem;
}

.allSetSection {
  display: flex;
  justify-content: center;
  align-items: center;
}

.allSetIcon {
  height: 2rem;
}

.allSetText {
  font-size: 1.2rem;
  font-weight: bold;
  padding-left: .4rem;
}

.policyReturnSectionOuter {
  padding: 2rem 0;
  display: block;
  text-align: center;
}

.policyReturnSection {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0 0 0;
}

.policyReturnSection p {
  text-align: center;
  font-size: 0.83em;
  line-height: 1.3em;
}

.modal {
  position: fixed;
  bottom: 0;
  max-height: 100%;
}

.modalTitle {
  width: 100%;
  height: 4rem;
  font-size: 1.8rem;
  font-weight: bold;
  text-align: center;
}

.outerModal {
  width: 100%;
  max-height: 90%;
  padding: 1rem 3rem 1rem 3rem;
}

.pdfContainer {
  display: inline-block;
  width: 100%;
  height: calc(90vh - 12rem);
  opacity: 1;
}

.submitContainer {
  padding-top: 1.4rem;
  display: flex;
  width: 100%;
  min-width: 30rem;
}

.submitButtonContainer {
  width: auto !important;
  align-self: flex-end;
}

.cancelButtonContainer {
  width: auto !important;
  align-self: flex-start;
}

.submitButton {
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
  align-items: center;
  border-radius: 1.5rem;
}

.continueLinkContainer {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 3rem;
  left: 0;
  right: 0;
}

.continueSection {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.continueText {
  font-size: 1.2rem;
  font-weight: bold;
  padding-right: .4rem;
}

.continueIcon {
  height: .7rem;
  margin-top: 0.2rem;
}

