/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { Box, InputLabel, TextareaAutosize, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Button from '../../../../components/Button/Button';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';

import styles from './FreeformTextCard.module.css';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

export default function FreeformTextCard(props) {
  const { id, description, title, fontColor, onSaveAnswer, saveAnswerLoading, eventLoading } = props;
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();
  const { palette } = useTheme();
  const [textFieldValue, setTextFieldValue] = useState('');

  const textAreaIdentifier = `freeFormTextArea-${id}`;

  const handleChange = (event) => {
    setTextFieldValue(event.target.value);
  };

  function handleSubmit() {
    const contentContainerElement = document.getElementById('card-scrolltop-target');
    onSaveAnswer({
      updateCacheKey: `FreeformTextCard:${id}`,
      answer: {
        type: 'FREEFORM_TEXT',
        freeFormTextAnswer: textFieldValue,
      },
    });
    if (contentContainerElement) {
      contentContainerElement.scrollIntoView({
        block: 'end',
        behavior: 'instant',
      });
    }
  }

  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`FreeformTextCard-${id}`}
    >
      <CardTitleDescription title={title} description={description} fontColor={fontColor} />
      <div className={styles.spacer} />
      <Box mb={isSmallMobile ? 6 : 0}>
        <InputLabel
          className={`${styles.freeformTextLabel}`}
          style={{ color: palette.primary.main }}
          htmlFor={textAreaIdentifier}
        >
          {t('lessonCards.freeformTextPrompt')}
        </InputLabel>
        <TextareaAutosize
          className={styles.freeformTextArea}
          style={{ color: palette.text.primary }}
          id={textAreaIdentifier}
          key={textAreaIdentifier}
          name={textAreaIdentifier}
          minRows={5}
          maxRows={5}
          onChange={handleChange}
          data-cy="freeformText"
        />
      </Box>
      <div className={styles.submitContainer}>
        <Button
          containerStyle={styles.submitButtonContainer}
          className={!eventLoading ? `${styles.submitButton} ${styles.buttonAnimation}` : styles.submitButton}
          style={{ borderColor: palette.card.backgroundColor }}
          loading={saveAnswerLoading}
          // eslint-disable-next-line react/jsx-no-bind
          onClick={handleSubmit}
          disabled={!textFieldValue || !eventLoading}
          variant="contained"
          data-cy="submit"
        >
          {t('platform.submitButton')}
        </Button>
      </div>
    </CardResponsiveText>
  );
}
