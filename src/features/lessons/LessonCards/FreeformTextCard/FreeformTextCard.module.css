.cardContent {
  padding-bottom: 6rem;
  margin-top: 1.5rem;
}


.freeformTextArea {
  font-family: 'Source Sans Pro';
  font-weight: 400;
  font-size: 1.1rem;
  line-height: 1.4375em;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  border-radius: 0.25rem;
  padding: 1.031rem 0.875rem;
}

.spacer {
  clear: both;
  height: 1rem;
}

.submitContainer {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 3rem;
  left: 0;
  right: 0;
}
  
.submitButtonContainer {
  width: auto !important;
}
  
.submitButton {
  border: none;
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.submitButton:focus {
  border: solid 3px;
}

.freeformTextLabel {
  margin-bottom: 0.3rem;
  font-size: 1.1rem;
}

@keyframes buttonFadeInOut {
  0% { -webkit-transform: translateY(0); opacity: 0.4 }
  100% { -webkit-transform: translateY(0); opacity: 0.9 }
}
.buttonAnimation {
  animation: buttonFadeInOut 0.6s infinite alternate ease-in-out;
}