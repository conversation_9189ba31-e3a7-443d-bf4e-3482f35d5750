import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, Typography, useTheme } from '@mui/material';
import { useAuth } from '../../../../hooks/useAuth';
import { concedeControl } from '../../../../services/api/scormRusticiAPI';
import { clearUserClosedRightPanel } from '../../../../services/layoutSettings';
import Button from '../../../../components/Button/Button';
import styles from './SaveExitCard.module.css';

export default function SaveExitCard({ hasCertificate, completedMessage, isCompletionCertificateCard = false }) {
    const { palette } = useTheme();
    const { clearTokens } = useAuth();
    const { t } = useTranslation();
    const completionCardMargin = !hasCertificate ? '60px 0px' : completedMessage ? '40px 0px' : '60px 0px 95px 0px';
    let buttonBackgroundColor = palette.button.login.backgroundColor;
    let hoverBackgroundColor = palette.button.login.hoverBackgroundColor;
    if (isCompletionCertificateCard) {
        buttonBackgroundColor = palette.background.darkDarkPink;
        hoverBackgroundColor = palette.button.downloadCertificate.backgroundHoverColor;
    }
    const saveExit = async () => {
        concedeControl();
        clearUserClosedRightPanel();
        await clearTokens();
        window.close();
    };
    return (
        <Card className={styles.saveExitCard}
            style={{ margin: completionCardMargin, backgroundColor: palette.background.lightGrayish }}>
            <CardContent sx={{ padding: 0 }}>
                <Typography variant="h6" gutterBottom style={{ fontWeight: 'bold' }}>
                    {t('scorm.save_exit_note_1')}...
                </Typography>
                <Typography variant="body1">
                    {t('scorm.save_exit_note_2')}
                </Typography>
                <Button
                    className={styles.saveExitButton}
                    onClick={() => saveExit()}
                    sx={{
                        color: palette.primary.white,
                        backgroundColor: buttonBackgroundColor,
                        '&:hover': {
                            backgroundColor: hoverBackgroundColor,
                        },
                        '&:focus': {
                            border: `solid ${palette.card.backgroundColor} 3px`,
                        },
                    }}
                >
                    {t('scorm.save_exit')}
                </Button>
            </CardContent>
        </Card>
    )
}