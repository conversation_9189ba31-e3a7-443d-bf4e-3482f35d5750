/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-danger */
/* eslint-disable react/no-array-index-key */
import React, { useState, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { RadioGroup, Radio, Tooltip, styled, useTheme, Box, Typography, Fade } from '@mui/material';
import { tooltipClasses } from '@mui/material/Tooltip';
import { Circle as RadioButtonChecked } from '@mui/icons-material';
import Button from '../../../../components/Button/Button';
import CardResponsiveText from '../../../../components/CardResponsiveText/CardResponsiveText';
import CardTitleDescription from '../../../../components/CardTitleDescription/CardTitleDescription';
import styles from './SliderCard.module.css';
import { useResponsiveMode } from '../../../../hooks/useResponsiveMode';

/**
 * Get Label at right index for radio buttons
 * @param {Integer} sliderMax Maximum number of slider radio options
 * @param {Array} labels Labels for the slider radio options
 * @param {Integer} index Index of Radio
 * @returns Label
 */
const formattedLabels = (sliderMax, labels, index, t, showChecked = false) => {
  const checked = showChecked ? `. ${t('general.selected')}. ` : '';
  if (sliderMax <= 7) {
    return `${labels[index]} ${checked}`;
  }

  if (sliderMax > 7 && index === 0) {
    return `${labels[0]} ${checked}`;
  }
  // Slider Cards with 8 & 10 Options
  if ((sliderMax === 8 && index === 7) || (sliderMax === 10 && index === 9)) {
    return `${labels[1]} ${checked}`;
  }

  // Slider Cards with 9 & 11 Options
  if ((sliderMax === 9 && index === 4) || (sliderMax === 11 && index === 5)) {
    return `${labels[1]} ${checked}`;
  }
  if ((sliderMax === 9 && index === 8) || (sliderMax === 11 && index === 10)) {
    return `${labels[2]} ${checked}`;
  }
  return '';
};

const RadioHoverText = styled(({ className, ...props }) => (
  // eslint-disable-next-line max-len
  <Tooltip {...props} tabIndex="0" describeChild placement="top" classes={{ popper: className }} TransitionComponent={Fade} />
))(({ theme, ...prop }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.primary.greyPurple,
    '&::before': {
      border: `1px solid ${theme.palette.primary.greyPurple}`,
    },
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: 'transparent !important',
    border: 'none !important',
    fontWeight: prop.checked ? 600 : 'normal',
    wordBreak: 'work-all',
    maxWidth: '8.2rem',
    textAlign: 'center',
    lineHeight: 1.1,
    wordWrap: 'normal',
  },
}));

const RadioButtonCheckedIcon = styled(
  ({ isMobile, isSmallMobile, ...props }) => <RadioButtonChecked {...props} />)(({ theme, ...prop }) => (
  {
    border: `1px solid ${theme.palette.primary.contrastText}`,
    borderRadius: '50%',
    fontSize: prop.isSmallMobile ? '1rem' : prop.isMobile ? '1.25rem' : '1.5rem',
    color: theme.palette.primary.dark,
  }));

const RadioButtonUncheckedIcon = styled(
  ({ isMobile, isSmallMobile, ...props }) => <RadioButtonChecked {...props} />)(({ theme, ...prop }) => (
  {
    border: theme.palette.border.slider,
    color: theme.palette.primary.contrastText,
    borderRadius: '50%',
    fontSize: prop.isSmallMobile ? '1rem' : prop.isMobile ? '1.25rem' : '1.5rem',
    'input:hover ~ &': {
      border: theme.palette.border.slider,
      color: theme.palette.primary.contrastText,
      borderRadius: '50%',
    },
  }));

const SliderCard = ({ id, description, title, fontColor, sliderMax, labelText, onSaveAnswer, saveAnswerLoading, eventLoading }) => {
  const { t } = useTranslation();
  const { isSmallMobile, isMobile } = useResponsiveMode();
  const { palette } = useTheme();
  const [groupFocused, setGroupFocused] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(null);

  const [answer, setAnswer] = useState(undefined);

  const scaleItemsArray = [...Array(sliderMax).keys()];

  const oddSliderCount = !!((labelText.length % 2) === 1);

  const listOfRadioButtons = () => {
    return scaleItemsArray.map((item, index) => (
      <RadioHoverText
        key={index}
        title={isSmallMobile ? '' : formattedLabels(sliderMax, labelText, index)}
        arrow
        checked={answer === (index + 1)}
      >
        <Radio
          id={`radio-${index}`}
          disableRipple
          checkedIcon={(
            <RadioButtonCheckedIcon
              isSmallMobile={isSmallMobile}
              isMobile={isMobile}
            />
          )}
          icon={(
            <RadioButtonUncheckedIcon
              isSmallMobile={isSmallMobile}
              isMobile={isMobile}
            />
          )}
          onClick={() => { setAnswer(index + 1); }}
          onFocus={() => setFocusedIndex(index)}
          onBlur={() => setFocusedIndex(null)}
          checked={answer === (index + 1)}
          inputProps={{
            'data-cy': `Choice-${index}`,
            'aria-checked': answer === (index + 1),
            'aria-label': formattedLabels(sliderMax, labelText, index),
          }}
          sx={{
            borderRight: '1px solid #E0E1EC',
            borderRadius: 0,
            padding: isSmallMobile ? '0.35rem' : '0.5rem',
            transition: 'font-size 0.2s ease-out',
            // eslint-disable-next-line max-len
            backgroundColor: answer === (index + 1) ? 'primary.main' : (focusedIndex === index ? 'background.highlighted' : 'primary.light'),
            '&:hover': {
              bgcolor: 'transparent',
              padding: isSmallMobile ? '0.35rem' : '0.5rem',
              backgroundColor: answer === (index + 1) ? 'primary.main' : 'background.highlighted',
            },
            ':first-of-type': {
              borderTopLeftRadius: '3rem',
              borderBottomLeftRadius: '3rem',
            },
            ':last-child': {
              borderRight: 0,
              borderTopRightRadius: '3rem',
              borderBottomRightRadius: '3rem',
            },
            '&:focus': { backgroundColor: 'pink' },
          }}
        />
      </RadioHoverText>

    ),
    );
  };

  function handleSubmit() {
    onSaveAnswer({
      updateCacheKey: `SliderCard:${id}`,
      answer: {
        type: 'SLIDER',
        sliderAnswer: answer,
      },
    });
  }

  return (
    <CardResponsiveText
      containerStyle={styles.cardContent}
      key={`SliderCard-${id}`}
    >
      <CardTitleDescription
        title={title}
        description={description}
        fontColor={fontColor}
        hiddenText={t('general.question')}
        boxProps={{ id: `question-${id}` }}
      />
      <RadioGroup
        aria-labelledby={`question-${id}`}
        row
        sx={{
          display: 'grid',
          gridTemplateColumns: `repeat(${sliderMax}, 1fr)`,
          borderRadius: '3rem',
          justifyContent: 'space-between',
          flexWrap: 'nowrap',
          mt: '4rem',
          mb: '0.25rem',
          outline: groupFocused ? `3px solid ${palette.card.backgroundColor}` : '',
        }}
        onFocus={() => { setGroupFocused(true); }}
        onBlur={() => setGroupFocused(false)}
      >
        {listOfRadioButtons()}
      </RadioGroup>

      <Box
        width="100%"
        display="grid"
        gridTemplateColumns={oddSliderCount ? '1fr 1fr 1fr' : ' 1fr 1fr'}
        sx={{ opacity: 0.8 }}
      >
        <Typography
          fontSize="0.6em"
          textAlign="left"
          sx={{ paddingLeft: '0.5rem' }}
          data-cy="labelTextStart"
          aria-hidden
        >{labelText[0]} {/* left-bottom label */}
        </Typography>
        {oddSliderCount && (
        <Typography
          fontSize="0.6em"
          textAlign="center"
          data-cy="labelTextCenter"
          aria-hidden
        >
          {labelText[((labelText.length + 1) / 2) - 1]} {/* center label */}
        </Typography>
        )}
        <Typography
          fontSize="0.6em"
          textAlign="right"
          sx={{ paddingRight: '0.5rem' }}
          data-cy="labelTextEnd"
          aria-hidden
        >
          {labelText[labelText.length - 1]} {/* right-bottom label */}
        </Typography>
      </Box>
      {answer && (
        <Typography
          fontWeight={600}
          textAlign="center"
          minHeight="1.5rem"
          lineHeight="3.5rem"
          mb={isSmallMobile ? 8 : 0}
          data-cy="sliderSelected"
        >
          <span aria-hidden>
            {formattedLabels(sliderMax, labelText, answer - 1)} {/* selected item label */}
          </span>
        </Typography>
      )}

      {/* <div className={styles.skipLink} onClick={() => onSkipCard()}>
        <span>{t('lessonCards.dontKnowSkip')}</span>
      </div> */}
      {answer && (
        <div className="srOnly">
          {`${t('general.you')} ${t('general.selected')} ${formattedLabels(sliderMax, labelText, answer - 1)}`}
        </div>
      )}
      <div className={styles.submitContainer}>
        <Button
          containerStyle={styles.submitButtonContainer}
          className={!eventLoading ? `${styles.submitButton} ${styles.buttonAnimation}` : styles.submitButton}
          style={{ borderColor: palette.card.backgroundColor }}
          loading={saveAnswerLoading}
          onClick={handleSubmit}
          disabled={!answer || answer === 0 || !eventLoading}
          variant="contained"
          data-cy="submit"
        >
          {t('platform.submitButton')}
        </Button>
      </div>
    </CardResponsiveText>
  );
};

export default memo(SliderCard);
