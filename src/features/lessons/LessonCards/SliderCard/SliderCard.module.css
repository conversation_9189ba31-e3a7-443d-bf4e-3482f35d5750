.cardContent {
  padding-bottom: 6rem;
  margin-top: 1.5rem;
}

.submitContainer {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 3rem;
  left: 0;
  right: 0;
}
    
.submitButtonContainer {
  width: auto !important;
}
    
.submitButton {
  border: none;
  padding: 0.5rem 1.75rem;
  text-transform: none;
  font-weight: bold;
  font-size: 1rem;
}

.submitButton:focus {
  border: solid 3px;
}
  
/* .skipLink {
  display: flex;
  justify-content: center;
  text-transform: none;
  font-size: .8rem;
  text-decoration: none;
  cursor: pointer;
  margin-top: 1.5rem;
}
  
.skipLink:hover {
  font-weight: bold;
} */

@keyframes buttonFadeInOut {
  0% { -webkit-transform: translateY(0); opacity: 0.4 }
  100% { -webkit-transform: translateY(0); opacity: 0.9 }
}
.buttonAnimation {
  animation: buttonFadeInOut 0.6s infinite alternate ease-in-out;
}