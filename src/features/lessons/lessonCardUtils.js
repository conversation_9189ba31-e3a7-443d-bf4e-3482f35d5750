// Lesson Card components
import BooleanCard from './LessonCards/BooleanCard/BooleanCard';
import ClickExpandCard from './LessonCards/ClickExpandCard/ClickExpandCard';
import EndOfLessonCard from './LessonCards/EndOfLessonCard/EndOfLessonCard';
import CompletionCertificateCard from './LessonCards/CompletionCertificateCard/CompletionCertificateCard';
import FreeformTextCard from './LessonCards/FreeformTextCard/FreeformTextCard';
import MapCard from './LessonCards/MapCard/MapCard';
import MultipleChoiceCard from './LessonCards/MultipleChoiceCard/MultipleChoiceCard';
import BulletListCard from './LessonCards/BulletListCard/BulletListCard';
import NumberedListCard from './LessonCards/NumberedListCard/NumberedListCard';
import CheckboxListCard from './LessonCards/CheckboxListCard/CheckboxListCard';
import PolicyAcknowledgementCard from './LessonCards/PolicyAcknowledgementCard/PolicyAcknowledgementCard';
import SingleChoiceCard from './LessonCards/SingleChoiceCard/SingleChoiceCard';
import SliderCard from './LessonCards/SliderCard/SliderCard';
import TextCard from './LessonCards/TextCard/TextCard';
import TextImageCard from './LessonCards/TextImageCard/TextImageCard';
import TextImageOverlayCard from './LessonCards/TextImageOverlayCard/TextImageOverlayCard';
import BorderlessTextImageCard from './LessonCards/BorderlessTextImageCard/BorderlessTextImageCard';
import VideoCard from './LessonCards/VideoCard/VideoCard';
import WorkplaceColorSpectrumCard from './LessonCards/WorkplaceColorSpectrumCard/WorkplaceColorSpectrumCard';

// Result card components
import BooleanResultCard from './ResultCards/BooleanResultCard/BooleanResultCard';
import WordcloudResultCard from './ResultCards/WordcloudResultCard/WordcloudResultCard';
import ChoiceResultCard from './ResultCards/ChoiceResultCard/ChoiceResultCard';
import GatedChoiceResultCard from './ResultCards/GatedChoiceResultCard/GatedChoiceResultCard';
import WorkplaceColorSpectrumResultCard
  from './ResultCards/WorkplaceColorSpectrumResultCard/WorkplaceColorSpectrumResultCard';
import SliderResultCard from './ResultCards/SliderResultCard/SliderResultCard';

// Card types
const TEXT_WITH_IMAGE = 'TEXT_WITH_IMAGE';
const BORDERLESS_TEXT_WITH_IMAGE = 'BORDERLESS_TEXT_WITH_IMAGE';
const VIDEO = 'VIDEO';
const BULLET_LIST = 'BULLET_LIST';
const NUMBERED_LIST = 'NUMBERED_LIST';
const CHECKBOX_LIST = 'CHECKBOX_LIST';
const TEXT_OVERLAY_WITH_IMAGE = 'TEXT_OVERLAY_WITH_IMAGE';
const CLICK_EXPAND = 'CLICK_EXPAND';
const US_MAP = 'US_MAP';
const CANADA_MAP = 'CANADA_MAP';
const BOOLEAN = 'BOOLEAN';
const SLIDER = 'SLIDER';
const SINGLE_CHOICE = 'SINGLE_CHOICE';
const MULTIPLE_CHOICE = 'MULTIPLE_CHOICE';
const FREEFORM_TEXT = 'FREEFORM_TEXT';
const POLICY_ACKNOWLEDGEMENT = 'POLICY_ACKNOWLEDGEMENT';
const WORKPLACE_COLOR_SPECTRUM = 'WORKPLACE_COLOR_SPECTRUM';
const END_OF_LESSON = 'END_OF_LESSON';

const quizCardTypes = [BOOLEAN, SINGLE_CHOICE, MULTIPLE_CHOICE, WORKPLACE_COLOR_SPECTRUM, SLIDER, FREEFORM_TEXT];

export function isQuizCardType(type) {
  return quizCardTypes.includes(type);
}

export function isPolicyCard(type) {
  return type === POLICY_ACKNOWLEDGEMENT;
}

export function cardRequiresUserResponse(type) {
  return (quizCardTypes.includes(type) || type === POLICY_ACKNOWLEDGEMENT);
}

export function isEndOfLessonCard(type) {
  return type === END_OF_LESSON;
}

export function getLessonCardComponent(lessonCardProps, showCompletionCertificate) {
  if (!lessonCardProps && !showCompletionCertificate) return null;

  if (showCompletionCertificate) {
    return CompletionCertificateCard;
  }
  const { type, answer, gated } = lessonCardProps;

  // If there the lesson card is a quiz card type and
  // It has already been answered, then return the result card
  return isQuizCardType(type) && !!answer
    ? getResultsCard(type, gated)
    : getLessonCard(type);
}

function getResultsCard(type, gated) {
  switch (type) {
    case BOOLEAN:
      return BooleanResultCard;
    case SLIDER:
      return SliderResultCard;
    case SINGLE_CHOICE:
      if (gated) {
        return GatedChoiceResultCard;
      }
      return ChoiceResultCard;
    case MULTIPLE_CHOICE:
      return ChoiceResultCard;
    case FREEFORM_TEXT:
      return WordcloudResultCard;
    case WORKPLACE_COLOR_SPECTRUM:
      return WorkplaceColorSpectrumResultCard;
    default:
      return null; // We should throw and error here
  }
}

function getLessonCard(type) {
  switch (type) {
    case TEXT_OVERLAY_WITH_IMAGE:
      return TextImageOverlayCard;
    case TEXT_WITH_IMAGE:
      return TextImageCard;
    case BORDERLESS_TEXT_WITH_IMAGE:
      return BorderlessTextImageCard;
    case VIDEO:
      return VideoCard;
    case BULLET_LIST:
      return BulletListCard;
    case CHECKBOX_LIST:
      return CheckboxListCard;
    case NUMBERED_LIST:
      return NumberedListCard;
    case CLICK_EXPAND:
      return ClickExpandCard;
    case US_MAP:
    case CANADA_MAP:
      return MapCard;
    case BOOLEAN:
      return BooleanCard;
    case SLIDER:
      return SliderCard;
    case SINGLE_CHOICE:
      return SingleChoiceCard;
    case MULTIPLE_CHOICE:
      return MultipleChoiceCard;
    case FREEFORM_TEXT:
      return FreeformTextCard;
    case WORKPLACE_COLOR_SPECTRUM:
      return WorkplaceColorSpectrumCard;
    case POLICY_ACKNOWLEDGEMENT:
      return PolicyAcknowledgementCard;
    case END_OF_LESSON:
      return EndOfLessonCard;
    default:
      return TextCard;
  }
}
