import React, { useEffect, useState } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { useMutation } from '@apollo/client';
import LaunchExternalAssignmentMutation from './LaunchExternalAssignmentMutation.graphql';
import { pushToContentFromExternal, pushToHome } from '../navigation/Routes/AppRoutes';
import { useUser } from '../../hooks/useUser';
import styles from '../shared-assignment/SharedAssignment.module.css';

function ExternalAssignment() {
  const { params: routeParams } = useRouteMatch();
  const history = useHistory();
  const user = useUser();
  const [ssoAdminError, setSsoAdminError] = useState('');
  const [contentError, setContentError] = useState('');

  // State to store the result of the mutation
  const [assignment, setAssignment] = useState(null);
  const [launchExternalAssignment, { loading, error }] = useMutation(LaunchExternalAssignmentMutation);
  useEffect(() => {
    async function ExternalContentLaunch() {
      if (user && user.adminAccountId) {
        setSsoAdminError('System admins cannot create External assignments on a client account.');
      } else {
        const programId = routeParams.programId;
        const lessonId = routeParams.lessonId;
        if (programId) {
          try {
            const response = await launchExternalAssignment({ variables: { programId } });
            if (response?.data?.launchExternalAssignment?.length > 0) {
              setAssignment(response.data.launchExternalAssignment[0]);
            }

            const assignmentId = response?.data?.launchExternalAssignment[0]?.id || null;
            const startingLessonId = response?.data?.launchExternalAssignment[0]?.lessonId || null;

            if (assignmentId && programId && startingLessonId) {
              history.push(pushToContentFromExternal({
                assignmentId,
                programId,
                lessonId: startingLessonId,
                contentType: 'program',
              }));
            } else {
              history.push(pushToHome());
            }
          } catch (err) {
            setContentError('Error creating externally launched program assignment:', err);
          }
        } else if (lessonId) {
          try {
            const response = await launchExternalAssignment({ variables: { lessonId } });
            setAssignment(response.data.launchExternalAssignment[0]);
            const assignmentId = response?.data?.launchExternalAssignment[0]?.id;

            if (assignmentId && lessonId) {
              history.push(pushToContentFromExternal({
                assignmentId,
                lessonId,
                contentType: 'microlesson',
              }));
            } else {
              history.push(pushToHome());
            }
          } catch (err) {
            setContentError('Error creating externally launched lesson assignment:', err);
          }
        } else {
          history.push(pushToHome());
        }
      }
    }
    ExternalContentLaunch();
  }, [routeParams, user, launchExternalAssignment, history]); // Include relevant dependencies

  if (loading || !assignment) {
    // While the mutation is loading or has failed, you can render a loading state
    return <div>Loading...</div>;
  }

  // Parsing the error response from Apollo
  let parsedErrorMessage = '';
  if (error) {
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      parsedErrorMessage = error.graphQLErrors[0].message || 'An error occurred';
    } else if (error.networkError) {
      if (error.networkError?.result?.errors?.length > 0) {
        parsedErrorMessage = error.networkError?.result?.errors[0]?.message;
      } else {
        parsedErrorMessage = 'Network error occurred';
      }
    } else {
      parsedErrorMessage = 'An unknown error occurred';
    }
  }

  return (
    <div className={ssoAdminError ? styles.adminWarn : ''}>
      {ssoAdminError && <div className={styles.error}>{ssoAdminError}</div>}
      {contentError && <div className={styles.error}>{contentError}</div>}
      {error && <div className={styles.error}>{parsedErrorMessage}</div>}
    </div>
  );
}

export default ExternalAssignment;
