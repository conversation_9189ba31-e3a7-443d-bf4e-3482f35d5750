.headingSpacer {
  width: 2rem;
}

.customLogoWrapper {
  display: flex;
  align-items: center;
}

.customLogo {
  object-fit: contain;
  max-height: 3.8rem;
  max-width: 11rem;
  padding: 0 !important;
  margin: 0 !important;
  margin-left: auto;
}

.emtrainLogo {
  height: 3rem;
  margin-bottom: -1rem;
  padding-right: 2em;
}

hr {
  margin-top: 0;
  margin-bottom: 0.25rem;
}

.formWrapper {
  align-self: center;
  height: auto;
  padding: 2em 2em 3em 2em;
  border-radius: 4px;
  overflow-y: auto;
}

.formWrapperMobile {
  align-self: center;
  height: 100%;
  border-radius: 4px;
  overflow-y: auto;
}

.form {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.label {
  font-weight: 600 !important;
  margin-bottom: 0.3rem;
  margin-top: 0.3rem;
}

.dropdownStyle {
  padding: 8.5px 14px;
  color: rgb(64, 64, 93);
  border-radius: 8px;
  font-family: Source Sans Pro, sans-serif;
  font-size: 16px;
  background-color: transparent;
}

.error {
  padding: 8.5px 14px;
  color: rgb(64, 64, 93);
  border-radius: 8px;
  font-family: Source Sans Pro, sans-serif;
  font-size: 16px;
  background-color: transparent;
  border: 1px solid rgb(211, 47, 47);
}

.fieldErrorMessage {
  color: rgb(211, 47, 47);
  margin: 4px 14px 0px 5px;
  font-size: 12px;
}

.signUpButton {
  font-size: 1rem !important;
  line-height: 1.2rem !important;
  margin-top: 1.1rem !important;
  padding: 0.6rem 1.2rem !important;
  font-weight: bold !important;
  text-transform: none !important;
  border-radius: 100px !important;
} 

.buttonContainer {
  display: grid;
  grid-template-columns: 2fr 1.3fr;
  justify-items: end;
  align-items: center;
}

.selfSignInLink {
  text-decoration: none;
  margin-top: 1.1rem;
  font-size: 0.9rem;
  font-weight: normal;
  cursor: pointer;
  text-align: center;
  line-height: 1.1rem;
}

.selfSignInLink:hover {
  font-weight: 600;
}

.signupError {
  margin-bottom: 1rem;
}
