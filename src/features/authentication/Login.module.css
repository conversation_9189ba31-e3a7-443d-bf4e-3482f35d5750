.container {
  display: flex;
  min-height: 100vh;
  max-width: 100vw;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.imageContainer {
  position: relative;
  display: flex;
  height: 100%;
  min-height: 100vh;
  width: auto;
  align-items: flex-start;
}
.loginImage {
  height: 100vh;
  width: auto;
  object-fit: cover;
  object-position: center;
  overflow: hidden;
  background-repeat: no-repeat;
}
.formContainer {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
}
.formColumn {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: center;
  height: 100%;
  overflow-y: hidden;
  /* border: 4px solid red; */
}
.formWrapper {
  position: relative;
  display: table;
  top: 50%;
  left: 50%;
  
  z-index: 2;
  transform: translate(-50%, -50%);
  padding: 0.8rem;
  border-radius: .4rem;

  width: 28rem;
  max-height: calc(100vh - 4rem);

 overflow-y: hidden;
 /* border: 4px solid blue; */
}
.formScroll {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 4rem);
  border-radius: .4rem;
  padding: 0rem 2rem 0rem 2rem;
  /* border: 2px solid green; */
}
.footerWrapper {
  width: 100%;
}
.customLogoContainer {
  display: flex;
  justify-content: space-between;
}
.employeeLoginHeading {
  font-size: 1.3rem;
  font-weight: bold;
  padding: 0 1rem 0 0 !important;
  margin: 0 !important;
}
.customLogo {
  object-fit: contain;
  max-height: 3.8rem;
  max-width: 11rem;
  padding: 0 !important;
  margin: 0 !important;
  margin-left: auto;
}
.emtrainLogo {
  height: 3rem;
  margin-left: -0.4rem;
  margin-bottom: -1rem;
  padding-right: 2em;
}
hr {
  margin-top: 1rem !important;
  margin-bottom: 0.25rem;
}
.loginInstructions {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.3rem;
  margin-top: 0.8rem;
  margin-bottom: 0.5rem;
}
.bottomMessage {
  display: flex;
  flex: 0 0 100%;
  justify-content: center;
  align-items: center;
  height: 2rem;
}
.poweredBySection {
  display: flex;
  flex: 0 0 100%;
  justify-content: center;
  align-items: center;
  height: 1.4rem;
  font-size: .9rem;
}
.logoSmall {
  height: 1.13rem;
  margin-left: -2.2rem;
  margin-bottom: 0.15rem;
}
.form {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.label {
  margin-top: 0.8rem;
  font-weight: 600 !important;
  margin-bottom: 0.3rem;
}
.link {
  margin-top: .6rem;
  text-decoration: none;
  font-size: 1rem;
  font-weight: normal;
  cursor: pointer;
}
.link:hover {
  font-weight: 600 !important;
  color: #166FDA !important; 
}
.loginButton {
  padding: 1rem 1.6rem !important;
  font-size: 1.2rem !important;
  line-height: 1.4rem !important;
  font-weight: bold !important;
  text-transform: none !important;
  margin-top: 1.1rem !important;
  border-radius: 100px !important;
}
.buttonContainer {
  display: grid;
  grid-template-columns: 2fr 1.3fr;
  justify-items: end;
  align-items: center;
}
.selfSignInLink {
  text-decoration: none;
  margin-top: 1.1rem;
  font-size: 0.9rem;
  font-weight: normal;
  cursor: pointer;
  text-align: center;
  line-height: 1.1rem;
}
.selfSignInLink:hover {
  font-weight: 600;
}
