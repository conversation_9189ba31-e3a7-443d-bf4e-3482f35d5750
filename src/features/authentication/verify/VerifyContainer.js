import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material';
import { useHistory } from 'react-router-dom';
import { verifyEmail, verifyEvent } from '../../../services/api/authentication';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import { useAuth } from '../../../hooks/useAuth';
import styles from './VerifyContainer.module.css';
import ResendEmailVerification from './ResendEmailVerification';
import VerifyOrCheckEmail from './VerifyOrCheckEmail';
import VerifyCompleteRegistration from './VerifyCompleteRegistration';
import Spinner from '../../../components/Spinner/Spinner';

function VerifyContainer({ token, action = null, errorMessage = null }) {
  const history = useHistory();
  const { logout } = useAuth();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const [content, setContent] = useState(null);
  const [status, setStatus] = useState({ loading: true });
  const { loading } = status;

  const [resetTokenVerified, setResetTokenVerified] = useState(false);

  useEffect(() => {
    async function verifyTokenExpire() {
      try {
        setStatus({ loading: true });
        await logout();
        const response = await verifyEmail(token);
        await verifyEvent(response.user.id);
        setStatus({ loading: false });
      } catch (e) {
        setStatus({ loading: false });
        switch (e.response.data) {
          case 'User not found.':
            setContent('checkEmail');
            break;
          case 'Verification token has expired.':
            setContent('expired');
            break;
          case 'User is already verified & not awaiting changes.':
            setContent('verifiedUser');
            break;
          default:
            setContent('checkEmail');
            break;
        }
      }
    }
    if (!resetTokenVerified && action === null) {
      verifyTokenExpire();
      setResetTokenVerified(true);
    } else {
      setStatus({ loading: false });
      setContent(action);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const verificationContainer = () => {
    switch (content) {
      case 'verify':
        return <VerifyOrCheckEmail content={content} />;
      case 'expired':
        return <ResendEmailVerification setContent={setContent} />;
      case 'checkEmail' || 'verifiedUser':
        return <VerifyOrCheckEmail content={content} />;
      case 'verifyAndReset':
      case 'reset':
        return <VerifyCompleteRegistration content={content} errorMessage={errorMessage} />;
      default:
        return <VerifyOrCheckEmail content={content} />;
    }
  };

  const VerifyEmail = () => {
    return (
      <>
        { verificationContainer()}
        {content !== 'expired' && (
          <div className={styles.backToLoginSuccess}>
            <a
              className={styles.link}
              onClick={() => history.push(appRoutes.HOME)}
              data-cy="backToLogin"
            >
              {t('login.backToLogin')}
            </a>
          </div>
        )}
        <hr style={{ border: palette.border.greyShade, marginBottom: '1.1rem' }} />
        <div style={{ fontWeight: 600 }}>{t('login.havingProblem?')}</div>
        <div style={{ fontSize: '1rem' }}>{t('login.email')}&nbsp;
          <a
            href={`mailto:${t('login.support')}`}
            style={{ color: palette.link.document }}
            className={styles.support}
            rel="noreferrer"
            target="_blank"
          >{t('login.support')}
          </a>
        </div>
      </>
    );
  };
  if (loading) {
    return <Spinner />;
  }
  return VerifyEmail();
}
export default VerifyContainer;
