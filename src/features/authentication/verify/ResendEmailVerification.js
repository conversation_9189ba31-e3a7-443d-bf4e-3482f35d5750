import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme, Alert } from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import styles from './VerifyContainer.module.css';
import Button from '../../../components/Button/Button';
import { resendVerifyEmail } from '../../../services/api/authentication';
import FormikTextField from '../../form/FormikTextField';

function ResendEmailVerification({ setContent }) {
  const { palette } = useTheme();
  const { t } = useTranslation();
  const [status, setStatus] = useState({ loading: false });
  const { loading } = status;

  const validationSchema = yup.object({
    email: yup
      .string()
      .email(t('platform.enter_valid_email'))
      .required(t('platform.email_required')),
  });

  async function onSubmit({ email }) {
    try {
      setStatus({ loading: true });
      await resendVerifyEmail(email);
      setContent('checkEmail');
      setStatus({ loading: false });
    } catch (e) {
      setContent('checkEmail');
      setStatus({ loading: false });
    }
  }

  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validationSchema,
    onSubmit,
  });

  const ResetSendEmailForm = () => {
    return (
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <>
          <FormikTextField
            required
            name="email"
            id="email"
            value={formik.values.email}
            formik={formik}
            labelText={t('login.workEmailAddress')}
            labelClassName={`${styles.label}`}
          />
          <div className={styles.buttonContainer}>
            <Button
              variant="contained"
              type="submit"
              loading={loading}
              disabled={loading || !formik.dirty || !formik.isValid}
              data-cy="submit"
              className={styles.resendButton}
              sx={{
                color: palette.button.login.color,
                backgroundColor: palette.button.login.backgroundColor,
                '&:hover': {
                  color: palette.button.login.hoverColor,
                  backgroundColor: palette.button.login.hoverBackgroundColor,
                },
                '&:disabled': {
                  color: palette.button.login.disabledColor,
                  backgroundColor: palette.button.login.disabledBackgroundColor,
                } }}
            >
              {t('verify.resendEmailVerification')}
            </Button>
          </div>
        </>
      </form>
    );
  };

  const OnResendVerification = () => {
    return (
      <div>
        <Alert
          icon={false}
          style={{ background: palette.background.lightRed, color: palette.primary.darkRed }}
        >
          <div className={styles.labelInfo}>{t('verify.oops')}</div>
          <div>{t('verify.resendEmailVerify')}</div>
        </Alert>
        {ResetSendEmailForm()}
      </div>
    );
  };

  return OnResendVerification();
}
export default ResendEmailVerification;
