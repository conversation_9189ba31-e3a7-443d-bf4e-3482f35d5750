import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme, Alert } from '@mui/material';
import ErrorCard from '../../../components/ErrorCard/ErrorCard';

function VerifyCompleteRegistration({ content, errorMessage }) {
  const { palette } = useTheme();
  const { t } = useTranslation();

  const onVerifyCompleteRegistration = () => {
    return (
      <div>
        <Alert
          icon={false}
          style={{ background: palette.background.lightBlue, color: palette.primary.darkBlue }}
        >
          <div>{t('verify.alreadyRegisteredMessage')}</div>
        </Alert>
      </div>
    );
  };

  const onVerifyResetPassword = () => {
    return (
      <div>
        <div style={{fontWeight: 600, fontSize: '1.3rem' }}>{t('password.resetPassword')}</div>
        {errorMessage && <ErrorCard>{errorMessage}</ErrorCard>}
      </div>
    );
  };
  if (content === 'verifyAndReset') {
    return onVerifyCompleteRegistration();
  }
  return onVerifyResetPassword();
}
export default VerifyCompleteRegistration;
