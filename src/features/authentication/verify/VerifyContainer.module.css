.formWrapper {
  align-self: center;
  width: 22rem;
}

.form {
  display: flex;
  flex-direction: column;
}

.label {
  margin-top: 0.8rem;
  font-weight: 600 !important;
  margin-bottom: 0.3rem;
}

.labelInfo {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.link {
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: normal;
  cursor: pointer;
}

.link:hover {
  font-weight: 600;
}

.support {
 text-decoration: none;
 cursor: pointer;
}

.support:visited {
  color: #166FDA !important; 
}

.support:hover {
  font-weight: bold;
  color: #166FDA !important; 
}

.textField {
  width: 20rem;
}

.buttonContainer {
  display: grid;
  grid-template-columns: 1.5fr;
  padding: 1.1rem 0rem;
}

.resendButton {
  padding: 0.35rem 1.4rem !important;
  font-size: 1.2rem !important;
  font-weight: bold !important;
  text-transform: none !important;
  border-radius: 100px !important;
  margin-top: unset !important;
}

.backToLoginSuccess {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 0rem;
}
