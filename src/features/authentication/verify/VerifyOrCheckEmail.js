import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme, Alert } from '@mui/material';
import styles from './VerifyContainer.module.css';

function VerifyOrCheckEmail({ content }) {
  const { palette } = useTheme();
  const { t } = useTranslation();

  const verifyCheck = (content === 'checkEmail' || content === 'verifiedUser');

  const onVerifyEmail = () => {
    return (
      <div>
        <Alert
          icon={false}
          style={{ background: palette.background.success, color: palette.primary.darkGreen }}
        >
          <div className={styles.labelInfo}>{t('verify.congratsTitle')}</div>
          <div>{t('verify.successMessage')}</div>
        </Alert>
      </div>
    );
  };

  const onCheckEmail = () => {
    return (
      <div>
        <Alert
          icon={false}
          style={{ background: palette.background.lightBlue, color: palette.primary.darkBlue }}
        >
          {content === 'checkEmail' && (<div className={styles.labelInfo}>{t('verify.checkEmail')}</div>)}
          <div>{content === 'checkEmail' ? t('verify.checkEmailMessage') : t('verify.verifiedMessage')}</div>
        </Alert>
      </div>
    );
  };
  if (verifyCheck) {
    return onCheckEmail();
  }
  return onVerifyEmail();
}
export default VerifyOrCheckEmail;
