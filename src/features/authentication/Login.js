import React, { useState, useEffect } from 'react';
import { useApolloClient, useMutation } from '@apollo/client';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useTheme } from '@mui/material';

import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import EmtrainLoginLogo from '../../images/emtrain-dark-logo.svg';
import LoginBackgrounImage1 from '../../images/login-background-image_1.png';
import LoginBackgrounImage2 from '../../images/login-background-image_2.png';
import LoginBackgrounImage3 from '../../images/login-background-image_3.png';
import LoginBackgrounImage4 from '../../images/login-background-image_4.png';
import LoginBackgrounImage5 from '../../images/login-background-image_5.png';

import { useAuth } from '../../hooks/useAuth';
import { appRoutes } from '../navigation/Routes/AppRoutes';

import { getCurrentAccount } from '../../services/api/currentAccount';
import styles from './Login.module.css';
import Button from '../../components/Button/Button';
import ErrorCard from '../../components/ErrorCard/ErrorCard';
import Spinner from '../../components/Spinner/Spinner';
import ResetPassword from './resetPassword/ResetPassword';
import VerifyResetPassword from './resetPassword/VerifyResetPassword';
import { getByPassSSOSetting, getChangePasswordToken } from '../../services/api/authentication';
import SetupResetPassword from './resetPassword/SetupResetPassword';
import SendSigninEventMutation from './SendSigninEventMutation.graphql';
import PrivacyText from '../privacy-notice/PrivacyText';
import SignUp from './SignUp';
import SocialLoginContainer from '../social-login/SocialLoginContainer';
import VerifyContainer from './verify/VerifyContainer';
import FormikTextField from '../form/FormikTextField';

import { EMTRAIN_WEBSITE_URL } from '../../config/constants';

function Login() {
  const history = useHistory();
  const { login } = useAuth();
  const client = useApolloClient();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { isLargeLoginView, isSmallMobile } = useResponsiveMode();
  const { url } = useRouteMatch();

  const [sendSigninEvent] = useMutation(SendSigninEventMutation, { ignoreResults: true });

  const [, path, token] = url.split('/');
  const [changePasswordToken, updateChangePasswordToken] = useState(null);
  const [status, setStatus] = useState({ loading: false, error: false, errorCode: 200, errorMessage: null });
  const [action, setAction] = useState('login');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentAccount, setCurrentAccount] = useState(null);
  const { loading, error, errorCode, errorMessage } = status;

  useEffect(() => {
    if (token && path) {
      updateChangePasswordToken(token);
      setAction(path);
    }
  }, [path, token]);

  const loginField = (currentAccount && currentAccount.authField) || 'email';
  let loginFieldError;
  let loginFieldLabel;
  if (loginField === 'email') {
    loginFieldLabel = t('login.workEmailAddress');
    loginFieldError = yup
      .string()
      .email(t('platform.enter_valid_email'))
      .required(t('platform.email_required'));
  } else if (loginField === 'employeeId') {
    loginFieldLabel = t('login.loginID');
    loginFieldError = yup.string()
      .required(t('platform.enter_valid_LoginID'))
      .min(1, t('platform.loginID_required'));
  } else {
    loginFieldLabel = t('login.workEmailOrLoginID');
    loginFieldError = yup.string()
      .required(t('platform.enter_valid_workEmailOrLoginID'))
      .min(1, t('platform.workEmailOrLoginID_required')) || yup
      .string()
      .email(t('platform.enter_valid_workEmailOrLoginID'))
      .required(t('platform.workEmailOrLoginID_required'));
  }

  const validationSchema = yup.object({
    email: loginFieldError,
    password: yup
      .string()
      .min(8, t('platform.password_min_chars'))
      .required(t('platform.password_required')),
  });

  // May reintroduce this styling if we figure out the bug. If not, delete this section.
  // const StyledTextField = styled(TextField)({
  //   '& .MuiOutlinedInput-root': {
  //     '& fieldset': {
  //       border: `solid 2px ${palette.background.secondary}`,
  //       borderRadius: '10px',
  //     },
  //   },
  // });

  const backgroundImages = [
    LoginBackgrounImage1,
    LoginBackgrounImage2,
    LoginBackgrounImage3,
    LoginBackgrounImage4,
    LoginBackgrounImage5,
  ];

  useEffect(() => {
    async function fetchCurrentAccount() {
      sessionStorage.clear(); // remove session storage for unauthenticated user
      const account = await getCurrentAccount();
      // if the account is not found redirect to  emtrain.com
      if (account && account.accountNotFoundError) {
        window.location.assign(EMTRAIN_WEBSITE_URL);
        return;
      }
      if (token && path) {
        setAction(path);
      } else if (account) {
        if (account.selfSignup && account.selfSignupEntry === 'signup') {
          setAction('signUp');
        } else {
          setAction('login');
          const ssoSetting = JSON.parse(getByPassSSOSetting());
          if (account.sso && account.ssoEntryPoint && !ssoSetting) {
            return window.location.assign(account && account.ssoEntryPoint);
          }
        }
      }
      setCurrentAccount(account);
    }
    const randomNumber = Math.floor(Math.random() * backgroundImages.length);
    setCurrentImageIndex(randomNumber);
    fetchCurrentAccount();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  async function onSubmit({ email, password }) {
    try {
      setStatus({ loading: true, error: false });
      const forwardTo = localStorage.getItem('aiForwardTo') || null;
      localStorage.removeItem('aiForwardTo');
      const validJettLogin = await login({ email, password, forwardTo });
      if (getChangePasswordToken()) {
        updateChangePasswordToken(getChangePasswordToken());
        setAction('changeDefaultPassword');
      } else {
        sendSigninEvent();
        await client.clearStore();
        if (!validJettLogin) {
          return;
        }
        const forwardToPath = localStorage.getItem('answersForwardTo') || localStorage.getItem('forwardToRoute');
        if (forwardToPath) {
          // localStorage.removeItem('answersForwardTo');
          history.push(forwardToPath);
        } else {
          history.push(appRoutes.HOME);
        }
      }
    } catch (e) {
      setStatus({ loading: false, error: true, errorCode: e.response.status, errorMessage: e.response.data });
    }
  }

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema,
    onSubmit,
  });
  const loginForm = () => {
    let customLogoUrl;
    if (currentAccount) {
      const { customLogoSrc, customDarkLogoSrc } = currentAccount;
      customLogoUrl = customLogoSrc && !customDarkLogoSrc ? customLogoSrc : customDarkLogoSrc || null;
    }

    return (
      <div>
        <div>
          {/* eslint-disable-next-line max-len */}
          {(!currentAccount || (currentAccount && !customLogoUrl)) && (
            <>
              <EmtrainLoginLogo
                tabIndex="0"
                role="img"
                aria-label={t('platform.emtrain_logo')}
                className={styles.emtrainLogo}
              />
              <h1 className="srOnly">{t('login.login')}</h1>
            </>
          )}
          {customLogoUrl && (
            <div
              className={styles.customLogoContainer}
              style={{ flexDirection: `${isSmallMobile ? 'column-reverse' : ''}` }}
            >
              <h1 className={styles.employeeLoginHeading}>{t('login.employee_login')}</h1>
              <img
                src={customLogoUrl}
                alt="LogoImage"
                className={styles.customLogo}
                style={{ alignSelf: `${isSmallMobile ? 'flex-start' : 'flex-end'}` }}
              />
            </div>
          )}
        </div>
        <hr aria-hidden style={{ border: palette.border.greyShade }} />
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          {error && errorCode === 409 && <ErrorCard>{errorMessage}</ErrorCard>}
          {/* eslint-disable-next-line max-len, no-nested-ternary */}
          {error && errorCode === 401 && <ErrorCard>{errorMessage.message === 'accountLockout' ? t('login.accountLockout') : errorMessage.message === 'remaningLastAttampt' ? t('login.remaningLastAttampt') : t('login.loginError')}</ErrorCard>}
          {currentAccount && currentAccount.loginInstructions && (
            <div style={{ color: palette.primary.darkLightPurple }} className={styles.loginInstructions}>
              {currentAccount.loginInstructions}
            </div>
          )}
          <FormikTextField
            autoComplete="email"
            required
            name="email"
            id="email"
            value={formik.values.email}
            formik={formik}
            labelText={loginFieldLabel}
            labelClassName={`${styles.label}`}
          />
          <FormikTextField
            autoComplete="password"
            required
            name="password"
            id="password"
            value={formik.values.password}
            type="password"
            formik={formik}
            labelText={t('login.password')}
            labelClassName={`${styles.label}`}
          />
          <a
            href="#" // for tab focus (note: lastpass will generate a console error)
            className={styles.link}
            onClick={() => setAction('forgetPassword')}
            onKeyDown={
              (e) => {
                if (e.code === 'Enter' || e.code === 'Space') {
                  e.preventDefault();
                  setAction('forgetPassword');
                }
              }
            }
            style={{ color: palette.link.linkColor }}
            data-cy="forgotPassword"
          >
            {t('login.forgot_your_password')}
          </a>
          <div className={styles.buttonContainer}>
            <Button
              variant="contained"
              type="submit"
              disabled={loading || !(formik.isValid && formik.dirty)}
              data-cy="submit"
              className={styles.loginButton}
              sx={{
                color: palette.button.login.color,
                backgroundColor: palette.button.login.backgroundColor,
                '&:hover': {
                  color: palette.button.login.hoverColor,
                  backgroundColor: palette.button.login.hoverBackgroundColor,
                },
                '&:disabled': {
                  color: palette.button.login.disabledColor,
                  backgroundColor: palette.button.login.disabledBackgroundColor,
                },
                '&:focus': {
                  border: `solid ${palette.card.backgroundColor} 3px`,
                },
              }}
            >
              {t('login.login')}
            </Button>
            {currentAccount && currentAccount.selfSignup && loginField !== 'employeeId' && (
              <a
                href="#" // for tab focus (note: lastpass will generate a console error)
                className={styles.selfSignInLink}
                onClick={() => setAction('signUp')}
                data-cy="signUp"
                onKeyDown={
                  (e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      setAction('signUp');
                    }
                  }
                }
              >
                <span>{t('signUp.noAccount')}&nbsp;</span>
                <span style={{ fontWeight: 600, color: palette.link.document }}>{t('signUp.signUp')}</span>
              </a>
            )}
          </div>
          {loading && (
            <div style={{
              marginTop: '-42px',
              marginLeft: `${isSmallMobile &&
                (currentAccount && currentAccount.selfSignup && loginField !== 'employeeId' ? '23px' : '40px')}`,
            }}
            >
              <Spinner customWidth="38" />
            </div>
          )}
          {currentAccount && (
            <SocialLoginContainer currentAccount={currentAccount} setStatus={setStatus} type="login" />
          )}
          <PrivacyText currentAccount={currentAccount} />
        </form>
      </div>
    );
  };

  const footer = () => {
    return (
      <div className={styles.footerWrapper}>
        <div
          className={styles.bottomMessage}
          style={{
            color: palette.primary.contrastText,
            backgroundColor: palette.primary.darkLightPurple,
            fontSize: `${isSmallMobile ? '.9rem' : ''}`,
          }}
        >
          {t('login.buildingSkills')}
        </div>
        {currentAccount && (currentAccount.customDarkLogoSrc || currentAccount.customLogoSrc) && (
          <div className={styles.poweredBySection} style={{ backgroundColor: palette.primary.lightGreyShade }}>
            {t('sideDrawer.powered_by')}
            <EmtrainLoginLogo className={styles.logoSmall} />
          </div>
        )}
      </div>
    );
  };

  const authenticationContainer = () => {
    switch (action) {
      case 'login':
        return loginForm();
      case 'forgetPassword':
        return <ResetPassword loginField={loginField} />;
      case 'reset': // user click email to reset their password
        return <VerifyResetPassword token={changePasswordToken} currentAccount={currentAccount} action={action} />;
      case 'verifyAndReset':// user clicked email to verify account created from /manage/users
        return <VerifyResetPassword token={changePasswordToken} currentAccount={currentAccount} action={action} />;
      case 'setupuser':// campaign welcome email to user that hasn't been set up, similar to reset
        return <SetupResetPassword token={changePasswordToken} currentAccount={currentAccount} action={action} />;
      case 'changeDefaultPassword':// campaign welcome email to user that hasn't been set up, similar to reset
        return <VerifyResetPassword token={changePasswordToken} currentAccount={currentAccount} action={action} />;
      case 'verify':// verify email while Ask Expert enable to submit questions
        return <VerifyContainer token={changePasswordToken} />;
      case 'signUp': // register new user for self signup
        return <SignUp currentAccount={currentAccount} setAction={setAction} />;
      default:
        break;
    }
  };

  if (!currentAccount || (currentAccount && currentAccount.sso)) {
    const ssoSetting = JSON.parse(getByPassSSOSetting());
    if (!ssoSetting) {
      return null;
    }
  }
  if (isLargeLoginView) {
    return (
      <div className={styles.container}>
        <div className={styles.imageContainer}>
          <img src={backgroundImages[currentImageIndex]} alt="" className={styles.loginImage} />
        </div>
        <div
          className={styles.formContainer}
          style={{ backgroundColor: palette.background.default }}
        >
          <div className={styles.formColumn}>
            <div
              className={styles.formWrapper}
              style={{ backgroundColor: palette.background.default }}
            >
              <div className={styles.formScroll}>
                {authenticationContainer()}
              </div>
            </div>
          </div>
          {footer()}
        </div>
      </div>
    );
  }
  return (
    <div
      className={styles.formContainer}
      style={{
        backgroundColor: palette.background.default,
        backgroundImage: `url(${backgroundImages[currentImageIndex]})`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }}
    >
      <div className={styles.formColumn}>
        <div
          className={styles.formWrapper}
          style={{ backgroundColor: palette.background.default, width: `${isSmallMobile ? '100%' : ''}` }}
        >
          <div className={styles.formScroll}>
            {authenticationContainer()}
          </div>
        </div>
      </div>
      {footer()}
    </div>
  );
}

export default Login;
