import React, { useState, useRef } from 'react';
import { useApolloClient, useMutation } from '@apollo/client';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useFormik, Field, FormikProvider } from 'formik';
import * as yup from 'yup';
import { useTheme } from '@mui/material';
import { CountryDropdown, RegionDropdown } from 'react-country-region-selector';
import { orderBy } from 'lodash';
import EmtrainLoginLogo from '../../images/emtrain-dark-logo.svg';
import { useAuth } from '../../hooks/useAuth';
import { appRoutes } from '../navigation/Routes/AppRoutes';
import styles from './SignUp.module.css';
import Button from '../../components/Button/Button';
import ErrorCardWithRef from '../../components/ErrorCard/ErrorCardWithRef';
import { useResponsiveMode } from '../../hooks/useResponsiveMode';
import { registerUsers } from '../../services/api/authentication';
import SendSignupEventMutation from './SendSignupEventMutation.graphql';
import Spinner from '../../components/Spinner/Spinner';
import PrivacyText from '../privacy-notice/PrivacyText';
import SocialLoginContainer from '../social-login/SocialLoginContainer';
import FormikTextField from '../form/FormikTextField';
import { PASSWORD_DEFAULT_MIN_LENGTH, PASSWORD_DEFAULT_MAX_LENGTH } from '../../config/constants';

const groupFields = (arr, first, second) => {
  const secondIndex = arr.findIndex((item) => item.fieldName === second);
  const elementToInsert = arr[secondIndex];

  if (secondIndex !== -1) {
    arr.splice(secondIndex, 1);
  }

  const firstIndex = arr.findIndex((item) => item.fieldName === first);
  if (firstIndex !== -1 && secondIndex !== -1) {
    return [
      arr.slice(0, firstIndex + 1),
      elementToInsert,
      arr.slice(firstIndex + 1),
    ].flat();
  }
};

function SignUp({ currentAccount, setAction }) {
  const history = useHistory();
  const { login } = useAuth();
  const client = useApolloClient();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();

  const [showError, setShowErrors] = useState(false);
  const [status, setStatus] = useState({ loading: false, error: false, errorMessage: '' });
  const { loading, error, errorMessage } = status;
  const useCode = currentAccount && currentAccount.useCode;

  const { minPasswordLength, maxPasswordLength,
    upperCaseRequiredInPassword, numberRequiredInPassword, specialCharRequiredInPassword, accountSignupFields } = currentAccount;
  const [sendSignupEvent] = useMutation(SendSignupEventMutation, { ignoreResults: true });
  const errorCardRef = useRef(null);

  const pwMinLen = minPasswordLength || PASSWORD_DEFAULT_MIN_LENGTH;
  const pwMaxLen = maxPasswordLength || PASSWORD_DEFAULT_MAX_LENGTH;

  const numRegx = numberRequiredInPassword ? '(?=.*[0-9].*)' : '(?=.{0,})';
  const upperRegx = upperCaseRequiredInPassword ? '(?=.*[A-Z].*)' : '(?=.{0,})';
  const specialCharRegx = specialCharRequiredInPassword ? '(?=.*[*@!#%&()^~{}`!$=_+-].*)' : '';

  const customSignupAccountFields = accountSignupFields?.filter((field) => !field.isStandard);
  const standardSignupAccountFields = accountSignupFields?.filter((field) => field.isStandard);
  const stateField = standardSignupAccountFields?.find((field) => field.fieldName === 'stateCode');
  const countryField = standardSignupAccountFields?.find((field) => field.fieldName === 'countryCode');

  let sorted = orderBy(accountSignupFields, 'sortOrder', 'asc');
  if (stateField && countryField) {
    sorted = groupFields(sorted, 'countryCode', 'stateCode');
  }

  const validationSchema = yup.object({
    firstName: yup
      .string()
      .required(t('platform.firstName_required')),
    lastName: yup
      .string()
      .required(t('platform.lastName_required')),
    email: yup
      .string()
      .email(t('platform.enter_valid_email'))
      .required(t('platform.email_required')),
    password: yup
      .string()
      .min(pwMinLen, t('platform.password_min_chars', { num: pwMinLen }))
      .matches(new RegExp(numRegx), t('platform.password_number'))
      .matches(new RegExp(upperRegx), t('platform.password_upper_case'))
      .matches(new RegExp(specialCharRegx), t('platform.password_special_char'))
      .max(pwMaxLen, t('platform.password_max_chars', { num: pwMaxLen }))
      .required(t('platform.password_required')),
    retypePassword: yup
      .string()
      .required(t('platform.passwords_must_match'))
      .oneOf([yup.ref('password')], t('platform.passwords_must_match')),
    signupCode: useCode && yup
      .string()
      .required(t('platform.signUpCode_required')),
    stateRequired: yup.boolean(),
    countryRequired: yup.boolean(),
    countryCode: yup
      .string()
      .when('countryRequired', {
        is: () => countryField?.required,
        then: yup.string().required(t('platform.countryCode_required')),
      }),
    stateCode: yup
      .string()
      .when('stateRequired', {
        is: () => (stateField?.required && formik.values.countryCode === 'United States') || (stateField?.required && !countryField),
        then: yup.string().required(t('platform.stateCode_required')),
      }),
  });

  async function onSubmit(props) {
    const variables = {
      ...props,
      skipVerification: true,
      customSignupAccountFieldIds: customSignupAccountFields?.map((field) => field.id),
      standardSignupAccountFieldIds: standardSignupAccountFields?.map((field) => field.id),
    };
    delete variables.retypePassword;
    delete variables.countryRequired;
    delete variables.stateRequired;

    const { email, password } = props;
    try {
      setStatus({ loading: true, error: false, errorMessage: '' });
      await registerUsers(variables);
      await login({ email, password });
      sendSignupEvent();
      await client.clearStore();
      history.push(appRoutes.HOME);
    } catch (e) {
      setStatus({ loading: false, error: true, errorMessage: e.response.data });
      if (errorCardRef && errorCardRef.current) {
        errorCardRef.current.scrollIntoView();
      }
    }
  }

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      retypePassword: '',
      signupCode: '',
      countryRequired: false,
      stateRequired: false,
    },
    validationSchema,
    onSubmit,
  });

  async function handleBlur(e, hasErrors) {
    if (hasErrors) {
      e.target.classList.add(`${styles.error}`);
      setShowErrors(true);
    } else {
      e.target.classList.remove(`${styles.error}`);
      setShowErrors(false);
    }
  }

  async function handleChange(e) {
    await formik.handleChange(e);
    if (e.target.value !== 'United States') {
      await formik.setFieldValue('stateCode', undefined);
    }
    await formik.validateForm();
  }

  function validateCustomField(value, name, isRequired) {
    let error;
    if (!value && isRequired) {
      error = `Error: ${name} is required`;
    }
    return error;
  }

  const signUpForm = () => {
    return (
      <>
        {/* eslint-disable-next-line max-len */}
        {(!currentAccount || (currentAccount && !currentAccount.customLogoSrc && !currentAccount.customDarkLogoSrc)) && (
          <EmtrainLoginLogo
            tabIndex="0"
            role="img"
            aria-label={t('platform.emtrain_logo')}
            className={styles.emtrainLogo}
          />
        )}
        {currentAccount && currentAccount.customLogoSrc && !currentAccount.customDarkLogoSrc && (
          <div
            style={{ width: '100%', border: '2px sold blue', flexDirection: `${isSmallMobile ? 'column-reverse' : 'row-reverse'}` }}
          >
            <img src={currentAccount.customLogoSrc} alt="LogoImage" className={styles.customLogo} />
          </div>
        )}
        {currentAccount && currentAccount.customDarkLogoSrc && (
          <div
            style={{ width: '100%', border: '2px sold blue', flexDirection: `${isSmallMobile ? 'column-reverse' : 'row-reverse'}` }}
          >
            <img src={currentAccount.customDarkLogoSrc} alt="LogoImage" className={styles.customLogo} />
          </div>
        )}
        <hr style={{ border: palette.border.greyShade, width: '100%' }} />
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{
            fontWeight: 600,
            color: palette.primary.darkLightPurple,
            margin: isSmallMobile
              ? '0.5rem 0'
              : '1rem 0 0.5rem 0',
          }}
          >{t('signUp.signUpWelcomeMsg')}
          </div>
          {currentAccount && (
            <SocialLoginContainer currentAccount={currentAccount} type="signup" setStatus={setStatus} />
          )}
          {error && <ErrorCardWithRef ref={errorCardRef} errorMessage={errorMessage} className={styles.signupError} />}
          <div style={{ display: 'flex' }}>
            <div style={{ paddingRight: '0.5rem' }}>
              <FormikTextField
                required
                name="firstName"
                id="firstName"
                value={formik.values.firstName}
                formik={formik}
                labelText={t('profile.firstName')}
                labelClassName={`${styles.label}`}
                labelStyle={{ color: palette.primary.darkLightPurple }}
                fontSize={isSmallMobile ? '0.7rem' : '1rem'}
              />
            </div>
            <div style={{ paddingLeft: '0.5rem' }}>
              <FormikTextField
                required
                name="lastName"
                id="lastName"
                value={formik.values.lastName}
                formik={formik}
                labelText={t('profile.lastName')}
                labelClassName={`${styles.label}`}
                labelStyle={{ color: palette.primary.darkLightPurple }}
                fontSize={isSmallMobile ? '0.7rem' : '1rem'}
              />
            </div>
          </div>
          {sorted?.map((field) => {
            return (
              <>
                {field?.fieldName === 'countryCode' && (
                <>
                  <span key={`${field.id}-label`} className={`${styles.label}`}>{t('profile.country')}</span>
                  <CountryDropdown
                    name={field.fieldName}
                    value={formik.values.countryCode}
                    classes={`${styles.dropdownStyle}`}
                    onChange={(_, e) => handleChange(e)}
                    key={field.id}
                    onBlur={(_, e) => handleBlur(e, formik?.errors?.countryCode)}
                  />
                  {showError && formik?.errors?.countryCode && (
                  <span key={`${field.id}-errorMessage`} className={`${styles.fieldErrorMessage}`}>{formik?.errors?.countryCode}</span>
                  )}
                </>
                )}
                {field?.fieldName === 'stateCode' && (!countryField || formik.values.countryCode === 'United States') && (
                <>
                  <span key={`${field.id}-label`} className={`${styles.label}`}>{t('profile.state')}</span>
                  <RegionDropdown
                    country={formik.values.countryCode ? formik.values.countryCode : 'United States'}
                    name={field.fieldName}
                    classes={`${styles.dropdownStyle}`}
                    value={formik.values.stateCode}
                    key={field.id}
                    defaultOptionLabel="Select State"
                    onChange={(_, e) => formik.handleChange(e)}
                    onBlur={(_, e) => handleBlur(e, formik?.errors?.stateCode)}
                  />
                  {showError && formik?.errors?.stateCode && (
                  <span key={`${field.id}-errorMessage`} className={`${styles.fieldErrorMessage}`}>{formik?.errors?.stateCode}</span>
                  )}
                </>
                )}
                {field?.fieldName !== 'stateCode' && field?.fieldName !== 'countryCode' && (
                <FormikProvider key={`${field.id}-formProvider`} value={formik}>
                  <Field
                    name={field.id?.toString()}
                    key={field.id}
                    component={FormikTextField}
                    labelClassName={`${styles.label}`}
                    labelText={field.fieldName}
                    formik={formik}
                    required={field.required}
                    labelStyle={{ color: palette.primary.darkLightPurple }}
                    fontSize={isSmallMobile ? '0.7rem' : '1rem'}
                    validate={(value) => validateCustomField(value, field.fieldName, field.required)}
                  />
                </FormikProvider>
                )}
              </>
            );
          })}
          <FormikTextField
            required
            name="email"
            id="email"
            value={formik.values.email}
            formik={formik}
            labelText={t('login.workEmailAddress')}
            labelClassName={`${styles.label}`}
            labelStyle={{ color: palette.primary.darkLightPurple }}
            fontSize={isSmallMobile ? '0.7rem' : '1rem'}
          />
          <FormikTextField
            required
            name="password"
            id="password"
            type="password"
            value={formik.values.password}
            formik={formik}
            labelText={t('login.password')}
            labelClassName={`${styles.label}`}
            labelStyle={{ color: palette.primary.darkLightPurple }}
            fontSize={isSmallMobile ? '0.7rem' : '1rem'}
          />
          <FormikTextField
            required
            name="retypePassword"
            id="retypePassword"
            type="password"
            value={formik.values.retypePassword}
            formik={formik}
            labelText={t('login.retypePassword')}
            labelClassName={`${styles.label}`}
            labelStyle={{ color: palette.primary.darkLightPurple }}
            fontSize={isSmallMobile ? '0.7rem' : '1rem'}
          />
          {useCode && (
            <FormikTextField
              required
              name="signupCode"
              id="signupCode"
              value={formik.values.signupCode}
              formik={formik}
              labelText={t('signUp.useCode')}
              labelClassName={`${styles.label}`}
              labelStyle={{ color: palette.primary.darkLightPurple }}
              fontSize={isSmallMobile ? '0.7rem' : '1rem'}
            />
          )}
          <div className={styles.buttonContainer}>
            <Button
              variant="contained"
              type="submit"
              // disabled={loading || !(formik.isValid && formik.dirty) || !valuesSet()}
              disabled={loading || !formik.dirty || !formik.isValid}
              data-cy="signUpLogIn"
              className={styles.signUpButton}
              sx={{
                color: palette.button.login.color,
                fontSize: `${isSmallMobile ? '0.9rem !important' : '1.2rem !important'}`,
                backgroundColor: palette.button.login.backgroundColor,
                '&:hover': {
                  color: palette.button.login.hoverColor,
                  backgroundColor: palette.button.login.hoverBackgroundColor,
                },
                '&:disabled': {
                  color: palette.button.login.disabledColor,
                  backgroundColor: palette.button.login.disabledBackgroundColor,
                } }}
            >
              {t('signUp.signUpLogIn')}
            </Button>
            {currentAccount && currentAccount.selfSignup && (
            <a
              href="#" // for tab focus (note: lastpass will generate a console error)
              className={styles.selfSignInLink}
              onClick={() => setAction('login')}
              data-cy="signUp"
              onKeyDown={
                (e) => {
                  if (e.code === 'Enter' || e.code === 'Space') {
                    setAction('login');
                  }
                }
              }
            >
              <span>{t('signUp.havingAccount')}&nbsp;</span>
              <span style={{ fontWeight: 600, color: palette.link.document }}>{t('login.login')}</span>
            </a>
            )}
          </div>
          <PrivacyText currentAccount={currentAccount} />
        </form>
        {loading && (<Spinner customWidth="40" />)}
      </>
    );
  };

  return signUpForm();
}

export default SignUp;
