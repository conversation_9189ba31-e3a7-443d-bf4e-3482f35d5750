.formWrapper {
    align-self: center;
    width: 26rem;
    padding: 1rem 2rem;
    border-radius: .4rem;
}

.errorCard {
    font-weight: 400;
}

.form {
    display: flex;
    flex-direction: column;
}

.label {
    margin-top: 0.8rem;
    font-weight: 600 !important;
    margin-bottom: 0.3rem;
}

.resetPasswordH2 {
    margin-block-start: 0;
    margin-block-end: 0;
    font-size: 1.3rem;
    font-weight: 600;
    /* margin: 0 !important; */
}

.link {
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: normal;
    cursor: pointer;
}

.link:hover {
    font-weight: 600;
}

.support {
   text-decoration: none;
   cursor: pointer;
}

.support:visited {
    color: #166FDA !important; 
}

.support:hover {
    font-weight: bold;
    color: #166FDA !important; 
}

.textField {
    width: 20rem;
}

.buttonContainer {
    display: grid;
    grid-template-columns: 1.5fr 1.2fr;
    padding: 1.1rem 0rem;
    justify-items: end;
    align-items: center;
    justify-content: center;
}

.resetButton {
    padding: 0.45rem 1.4rem !important;
    font-size: 1.2rem !important;
    font-weight: bold !important;
    text-transform: none !important;
    border-radius: 100px !important;
    margin-top: unset !important;
    width: 95% !important;
}

.backToLoginSuccess {
    display: flex;
    justify-content: flex-end;
    padding: 1rem 0rem;
}
