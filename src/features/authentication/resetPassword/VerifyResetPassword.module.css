.container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  align-items: stretch;
}

.column {
  position: relative;
  display: flex;
  height: 100%;
  min-height: 32rem;;
  width: auto;
  align-items: flex-start;
}

.customLogoContainer {
  display: flex;
  justify-content: space-between;
}

.employeeLoginHeading {
  display: flex;
  align-items: center;
  font-size: 1.4rem;
  font-weight: bold;
}

.headingSpacer {
  width: 2rem;
}

.customLogoWrapper {
  display: flex;
  align-items: center;
}

.customLogo {
  max-width: 10rem;
  height: auto;
  margin-left: -0.2rem;
}

.emtrainLogo {
  height: 3rem;
  margin-left: -0.4rem;
  margin-top: -1rem;
}

.loginImage {
  height: 100%;
  max-width: 100%;
  width: auto;
}

hr {
  margin-top: 0;
  margin-bottom: 0.25rem;
}

.bottomSection {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.bottomMessage {
  display: flex;
  flex: 0 0 100%;
  justify-content: center;
  align-items: center;
  height: 2rem;
}

.poweredBySection {
  display: flex;
  flex: 0 0 100%;
  justify-content: center;
  align-items: center;
  height: 1.4rem;
  font-size: .9rem;
}

.logoSmall {
  width: 4.2rem;
  margin: .2rem 0 0 .2rem;
}

.loginSection {
  justify-content: center;
  flex-grow: 2;
}

.formWrapper {
  align-self: center;
  width: 22rem;
  padding: 5rem 0;
}


.form {
  display: flex;
  flex-direction: column;
}

.label {
  margin-top: 0.8rem;
  font-weight: 600 !important;
  margin-bottom: 0.3rem;
}


.link {
  margin-top: .6rem;
  text-decoration: none;
  font-size: 1rem;
  font-weight: normal;
  cursor: pointer;
}

.link:hover {
  font-weight: 600; 
}

.resetButton {
  margin-top: 1.1rem !important;
  padding: 0.45rem 2.4rem !important;
  font-size: 1.2rem !important;
  font-weight: bold !important;
  text-transform: none !important;
  border-radius: 100px !important;
}

.mobileContainer {
  position: relative;
  padding: 0 !important;
  background-position: center;
}

.mobileLoginForm {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: .4rem;
  height: 50%;
  padding: .8rem;
}
