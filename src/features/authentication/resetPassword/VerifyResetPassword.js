import React, { useEffect, useState } from 'react';
import { useApolloClient } from '@apollo/client';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useTheme } from '@mui/material';
import EmtrainLoginLogo from '../../../images/emtrain-dark-logo.svg';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import styles from './VerifyResetPassword.module.css';
import Button from '../../../components/Button/Button';
import ErrorCard from '../../../components/ErrorCard/ErrorCard';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import { checkResetTokenExpired, removeChangePasswordToken,
  verifyAndReset } from '../../../services/api/authentication';
import { useAuth } from '../../../hooks/useAuth';
import VerifyContainer from '../verify/VerifyContainer';
import FormikTextField from '../../form/FormikTextField';

import { PASSWORD_DEFAULT_MIN_LENGTH } from '../../../config/constants';

function VerifyResetPassword({ token, currentAccount, action }) {
  const history = useHistory();
  const { logout, verifyAndResetPassword } = useAuth();
  const client = useApolloClient();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();

  const [status, setStatus] = useState({ loading: false, error: false, errorMessage: '' });
  const [resetTokenVerified, setResetTokenVerified] = useState(false);
  const { loading, error, errorMessage } = status;
  const [accountUserVerified, setAccountUserVerified] = useState(false);

  const { minPasswordLength } = currentAccount;

  const pwMinLen = minPasswordLength || PASSWORD_DEFAULT_MIN_LENGTH;

  useEffect(() => {
    async function checkTokenExpire() {
      try {
        await logout();
        const tokenUser = await checkResetTokenExpired({ token, action });
        if (action === 'verifyAndReset' && tokenUser && tokenUser.user && tokenUser.user.isVerified) {
          setAccountUserVerified(true);
        }
      } catch (err) {
        setStatus({ loading: false, error: true, errorMessage: err.response.data });
        if (action === 'reset') {
          await client.clearStore();
          removeChangePasswordToken();
          history.push(appRoutes.HOME);
        }
      }
    }
    if (!resetTokenVerified) {
      checkTokenExpire();
      setResetTokenVerified(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const validationSchema = yup.object({
    password: yup
      .string()
      .min(pwMinLen, t('platform.password_min_chars'))
      .required(t('platform.password_required')),
    retypePassword: yup.string().oneOf([yup.ref('password'), null], 'Passwords must match.'),
  });

  async function onSubmit({ password }) {
    try {
      setStatus({ loading: true, error: false, errorMessage: '' });
      if (action === 'changeDefaultPassword' || action === 'reset') {
        // In this case the user will be automatically logged in right after the change their password.
        // This is why we call a function in the useAuth hook to go through the user verification path.
        await verifyAndResetPassword(token, password);
        await client.clearStore();
        removeChangePasswordToken();
      }
      if (action === 'verifyAndReset') {
        await verifyAndReset(token, password);
        await client.clearStore();
        history.push(appRoutes.HOME);
      }
    } catch (e) {
      if (action === 'reset') {
        setAccountUserVerified(false);
      }
      setStatus({ loading: false, error: true, errorMessage: e.response.data });
    }
  }

  const formik = useFormik({
    initialValues: {
      password: '',
      retypePassword: '',
    },
    validationSchema,
    onSubmit,
  });

  const valuesSet = () => {
    if (formik.values.password === '' || formik.values.retypePassword === '') {
      return false;
    }
    return true;
  };

  if (accountUserVerified) {
    return <VerifyContainer action={action} errorMessage={errorMessage} />;
  }

  const verifyResetPasswordForm = () => {
    return (
      <>
        {/* eslint-disable-next-line max-len */}
        {(!currentAccount || (currentAccount && !currentAccount.customLogoSrc && !currentAccount.customDarkLogoSrc)) && (
          <EmtrainLoginLogo
            tabIndex="0"
            role="img"
            aria-label={t('platform.emtrain_logo')}
            className={styles.emtrainLogo}
          />
        )}
        {currentAccount && currentAccount.customLogoSrc && !currentAccount.customDarkLogoSrc && (
          <div
            style={{ flexDirection: `${isSmallMobile ? 'column-reverse' : ''}` }}
          >
            <div className={styles.headingSpacer} />
            <div className={styles.customLogoWrapper}>
              <img src={currentAccount.customLogoSrc} alt="LogoImage" className={styles.customLogo} />
            </div>
          </div>
        )}
        {currentAccount && currentAccount.customDarkLogoSrc && (
          <div
            style={{ flexDirection: `${isSmallMobile ? 'column' : ''}` }}
          >
            <div className={styles.headingSpacer} />
            <div className={styles.customLogoWrapper}>
              <img src={currentAccount.customDarkLogoSrc} alt="LogoImage" className={styles.customLogo} />
            </div>
          </div>
        )}
        <hr style={{ border: palette.border.greyShade }} />
        {/* <div style={{
          fontWeight: 600,
          color: palette.primary.darkLightPurple,
          margin: isSmallMobile
            ? '0.5rem 0'
            : '1rem 0',
        }}
        >{t('password.welcome_set_password_msg')}
        </div> */}
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          {error && <ErrorCard>{errorMessage}</ErrorCard>}
          {currentAccount && (
          <div style={{ fontSize: '1rem',
            fontWeight: 600,
            color: palette.primary.darkLightPurple }}
          // eslint-disable-next-line max-len
          >{action === 'changeDefaultPassword' ? t('password.change_password_msg') : t('password.welcome_set_password_msg')}
          </div>
          )}
          <FormikTextField
            required
            name="password"
            id="password"
            type="password"
            value={formik.values.password}
            formik={formik}
            labelText={t('login.password')}
            labelClassName={`${styles.label}`}
          />
          <FormikTextField
            required
            name="retypePassword"
            id="retypePassword"
            type="password"
            value={formik.values.retypePassword}
            formik={formik}
            labelText={t('login.retypePassword')}
            labelClassName={`${styles.label}`}
          />
          <Button
            variant="contained"
            type="submit"
            // loading={loading}
            disabled={loading || !(formik.isValid && formik.dirty) || !valuesSet()}
            data-cy="signUpLogIn"
            className={styles.resetButton}
            sx={{
              color: palette.button.login.color,
              backgroundColor: palette.button.login.backgroundColor,
              '&:hover': {
                color: palette.button.login.hoverColor,
                backgroundColor: palette.button.login.hoverBackgroundColor,
              },
              '&:disabled': {
                color: palette.button.login.disabledColor,
                backgroundColor: palette.button.login.disabledBackgroundColor,
              } }}
          >
            {/* eslint-disable-next-line max-len */}
            {`${action === 'changeDefaultPassword' || action === 'reset' ? t('platform.saveLogInButton') : t('platform.save')}`}
          </Button>
        </form>
      </>
    );
  };
  return verifyResetPasswordForm();
}
export default VerifyResetPassword;
