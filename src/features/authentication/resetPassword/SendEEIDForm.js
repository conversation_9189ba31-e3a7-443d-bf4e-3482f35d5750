import React, { useState, useEffect, useRef } from 'react';
import { useApolloClient } from '@apollo/client';
import { Trans, useTranslation } from 'react-i18next';
import { useFormik } from 'formik';
import { useTheme, Alert } from '@mui/material';
import { useHistory } from 'react-router-dom';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import styles from './ResetPassword.module.css';
import Button from '../../../components/Button/Button';
import ErrorCard from '../../../components/ErrorCard/ErrorCard';
import { resetEmployeeIdPwd, restEmailPwd } from '../../../services/api/authentication';
import FormikTextField from '../../form/FormikTextField';

function SendEEIDForm() {
  const client = useApolloClient();
  const history = useHistory();
  const { palette } = useTheme();
  const { t } = useTranslation();

  const initialState = {
    loading: false,
    error: false,
    success: false,
    displayDefaultPwdSuccess: false,
    displayResetEmailSuccess: false,
  };

  const [status, setStatus] = useState(initialState);
  const { loading, error, success, displayDefaultPwdSuccess, displayResetEmailSuccess } = status;

  const initialFocusRef = useRef(null);
  const emailSuccessFocusRef = useRef(null);

  const validate = ({ email, employeeId }) => {
    const errors = {};

    errors.email = undefined;
    errors.employeeId = undefined;
    const isValidEmailAddress = (mail) => {
      return (/^[A-Z0-9!$&*-=`|~#%‘+/?_{}']+@[A-Z0-9.-]+\.[A-Z]{2,63}$/i.test(mail));
    };
    if (!!email && !isValidEmailAddress(email)) {
      errors.email = t('platform.enter_valid_email');
      return errors;
    }

    if (!!email && !!employeeId) {
      errors.email = t('platform.oneFieldRequired');
      errors.employeeId = t('platform.oneFieldRequired');
      return errors;
    }
    return true;
  };

  async function onSubmit({ email, employeeId }) {
    try {
      setStatus({ loading: true,
        error: false,
        displayDefaultPwdSuccess: false,
        displayResetEmailSuccess: false,
        success: false });
      if (email) {
        await restEmailPwd({ email });
        setStatus({ ...status, loading: false, error: false, displayResetEmailSuccess: true, success: true });
      } else {
        await resetEmployeeIdPwd({ employeeId });
        setStatus({ ...status, loading: false, error: false, displayDefaultPwdSuccess: true, success: true });
      }
      await client.clearStore();
    } catch (e) {
      setStatus({ ...status, loading: false, error: true });
    }
  }

  const formik = useFormik({
    initialValues: {
      email: '',
      employeeId: '',
    },
    validate,
    onSubmit,
  });

  useEffect(() => {
    if (initialFocusRef && initialFocusRef.current) {
      initialFocusRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (emailSuccessFocusRef && emailSuccessFocusRef.current) {
      emailSuccessFocusRef.current.focus();
    }
  }, [success]);

  const ResetPasswordForm = () => {
    return (
      <>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <h2
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex="0"
            ref={initialFocusRef}
            className={styles.resetPasswordH2}
            style={{ color: palette.primary.darkLightPurple, marginTop: '0.5rem', marginBottom: '1rem' }}
          >
            {t('password.resetPassword')}
          </h2>
          {error && <ErrorCard className={styles.errorCard}>{t('password.resetPasswordError')}</ErrorCard>}
          {success && (
          <Alert
            ref={emailSuccessFocusRef}
            icon={false}
            style={{ background: palette.background.success }}
          >
            {displayResetEmailSuccess && (
            <span>{t('password.linkEmailed')}&nbsp;
              <b>{`${formik.values.email}`}</b>&nbsp;{t('password.foundEmail')}
            </span>
            )}
            {displayDefaultPwdSuccess && (
            <span>{t('password.defaultPasswordReset')}&nbsp;
              <b>{`${formik.values.employeeId}`}</b>&nbsp;{t('password.system')}
            </span>
            )}
          </Alert>
          )}
          {!success && (
          <>
            <div style={{ color: palette.primary.darkLightPurple, marginTop: '0.5rem', lineHeight: '1.3rem' }}>
              <Trans i18nKey={t('password.resetPasswordText')} />
            </div>
            <FormikTextField
              // required
              name="email"
              id="email"
              value={formik.values.email}
              formik={formik}
              labelText={t('login.workEmailAddress')}
              labelClassName={`${styles.label}`}
            />
            <div style={{ color: palette.primary.darkLightPurple, marginTop: '2rem', lineHeight: '1.3rem' }}>
              <Trans i18nKey={t('password.resetDefaultPasswordText')} />
            </div>
            <FormikTextField
              // required
              name="employeeId"
              id="employeeId"
              value={formik.values.employeeId}
              formik={formik}
              labelText={t('login.loginID')}
              labelClassName={`${styles.label}`}
              labelStyle={{ color: palette.primary.darkLightPurple }}
            />
            <div className={styles.buttonContainer}>
              <Button
                variant="contained"
                type="submit"
                // loading={loading}
                disabled={loading || !formik.dirty || !formik.isValid}
                data-cy="submit"
                className={styles.resetButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: palette.button.login.backgroundColor,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: palette.button.login.hoverBackgroundColor,
                  },
                  '&:disabled': {
                    color: palette.button.login.disabledColor,
                    backgroundColor: palette.button.login.disabledBackgroundColor,
                  },
                  '&:focus': {
                    border: `solid ${palette.card.backgroundColor} 3px`,
                  } }}
              >
                {t('platform.submitButton')}
              </Button>
              <a
                href="#" // for tab focus (note: lastpass will generate a console error)
                className={styles.link}
                onClick={() => history.push(appRoutes.HOME)}
                onKeyDown={
                  (e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      history.push(appRoutes.HOME);
                    }
                  }
                }
                data-cy="backToLogin"
              >
                {t('login.backToLogin')}
              </a>
            </div>
          </>
          )}
          {success && (
            <div className={styles.backToLoginSuccess}>
              <a
                className={styles.link}
                onClick={() => history.push(appRoutes.HOME)}
                data-cy="forgotPassword"
              >
                {t('login.backToLogin')}
              </a>
            </div>
          )}
        </form>
        <hr style={{ border: palette.border.greyShade, marginBottom: '1.1rem' }} />
        <div style={{ fontWeight: 600 }}>{t('login.havingProblem?')}</div>
        <div style={{ fontSize: '1rem' }}>{t('login.email')}&nbsp;
          <a
            href={`mailto:${t('login.support')}`}
            style={{ color: palette.link.document }}
            className={styles.support}
            rel="noreferrer"
            target="_blank"
          >{t('login.support')}
          </a>
        </div>
      </>
    );
  };

  return ResetPasswordForm();
}
export default SendEEIDForm;
