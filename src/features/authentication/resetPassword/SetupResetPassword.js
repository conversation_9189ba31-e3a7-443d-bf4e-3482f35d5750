import React, { useEffect, useState } from 'react';
import { useApolloClient } from '@apollo/client';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useTheme } from '@mui/material';

import EmtrainLoginLogo from '../../../images/emtrain-dark-logo.svg';

import { useAuth } from '../../../hooks/useAuth';
import { appRoutes } from '../../navigation/Routes/AppRoutes';

import styles from './VerifyResetPassword.module.css';
import Button from '../../../components/Button/Button';
import ErrorCard from '../../../components/ErrorCard/ErrorCard';
import { useResponsiveMode } from '../../../hooks/useResponsiveMode';
import { checkResetTokenExpired } from '../../../services/api/authentication';
import FormikTextField from '../../form/FormikTextField';

function SetupResetPassword({ token, currentAccount, action }) {
  const history = useHistory();
  const { logout, verifyAndResetPassword } = useAuth();
  const client = useApolloClient();
  const { palette } = useTheme();
  const { t } = useTranslation();
  const { isSmallMobile } = useResponsiveMode();

  const [status, setStatus] = useState({ loading: false, error: false, errorMessage: '' });
  const { loading, error, errorMessage } = status;

  useEffect(() => {
    async function checkTokenExpire() {
      try {
        await logout();
        await checkResetTokenExpired({ token, action });
      } catch (err) {
        setStatus({ loading: false, error: true, errorMessage: err.response.data });
        await client.clearStore();
        history.push(appRoutes.HOME);
      }
    }
    checkTokenExpire();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, token]);

  const loginField = (currentAccount && currentAccount.authField) || 'email';
  let loginFieldError;
  let loginFieldLabel;
  if (loginField === 'email') {
    loginFieldLabel = t('login.workEmailAddress');
    loginFieldError = yup
      .string()
      .email(t('platform.enter_valid_email'))
      .required(t('platform.email_required'));
  } else if (loginField === 'employeeId') {
    loginFieldLabel = t('login.loginID');
    loginFieldError = yup.string()
      .required(t('platform.enter_valid_LoginID'))
      .min(4, t('platform.loginID_required'));
  } else {
    loginFieldLabel = t('login.workEmailOrLoginID');
    loginFieldError = yup.string()
      .required(t('platform.enter_valid_workEmailOrLoginID'))
      .min(4, t('platform.workEmailOrLoginID_required')) || yup
      .string()
      .email(t('platform.enter_valid_workEmailOrLoginID'))
      .required(t('platform.workEmailOrLoginID_required'));
  }

  const validationSchema = yup.object({
    emailOrId: loginFieldError,
    password: yup
      .string()
      .min(8, t('platform.password_min_chars'))
      .required(t('platform.password_required')),
    retypePassword: yup.string().oneOf([yup.ref('password'), null], 'Passwords must match.'),
  });

  async function onSubmit({ emailOrId, password }) {
    try {
      setStatus({ loading: true, error: false, errorMessage: '' });
      // In this case the user will be automatically logged in right after the change their password.
      // This is why we call a function in the useAuth hook to go through the user verification path.
      await verifyAndResetPassword(token, password, emailOrId, true);
      await client.clearStore();
      history.push(appRoutes.HOME);
    } catch (e) {
      setStatus({ loading: false, error: true, errorMessage: e.response.data });
    }
  }

  const formik = useFormik({
    initialValues: {
      emailOrId: '',
      password: '',
      retypePassword: '',
    },
    validationSchema,
    onSubmit,
  });

  const valuesSet = () => {
    if (formik.values.password === '' || formik.values.retypePassword === '') {
      return false;
    }
    return true;
  };

  const setupResetPasswordForm = () => {
    return (
      <>
        {/* eslint-disable-next-line max-len */}
        {(!currentAccount || (currentAccount && !currentAccount.customLogoSrc && !currentAccount.customDarkLogoSrc)) && (
          <EmtrainLoginLogo
            tabIndex="0"
            role="img"
            aria-label={t('platform.emtrain_logo')}
            className={styles.emtrainLogo}
          />
        )}
        {currentAccount && currentAccount.customLogoSrc && !currentAccount.customDarkLogoSrc && (
          <div
            style={{ flexDirection: `${isSmallMobile ? 'column-reverse' : ''}` }}
          >
            <div className={styles.headingSpacer} />
            <div className={styles.customLogoWrapper}>
              <img src={currentAccount.customLogoSrc} alt="LogoImage" className={styles.customLogo} />
            </div>
          </div>
        )}
        {currentAccount && currentAccount.customDarkLogoSrc && (
          <div
            style={{ flexDirection: `${isSmallMobile ? 'column' : ''}` }}
          >
            <div className={styles.headingSpacer} />
            <div className={styles.customLogoWrapper}>
              <img src={currentAccount.customDarkLogoSrc} alt="LogoImage" className={styles.customLogo} />
            </div>
          </div>
        )}
        <hr style={{ border: palette.border.greyShade }} />
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          {error && <ErrorCard>{errorMessage}</ErrorCard>}
          {currentAccount && loginField === 'email' && (
          <div style={{ fontSize: '1rem',
            fontWeight: 600,
            color: palette.primary.darkLightPurple }}
          >{t('password.welcome_set_password_msg')}
          </div>
          )}
          {/* {(action !== 'changeDefaultPassword' && action !== 'verifyAndReset') && ( */}
          <FormikTextField
            required
            name="emailOrId"
            id="emailOrId"
            value={formik.values.emailOrId}
            formik={formik}
            labelText={loginFieldLabel}
            labelClassName={`${styles.label}`}
          />
          {/* )} */}
          <FormikTextField
            required
            name="password"
            id="password"
            type="password"
            value={formik.values.password}
            formik={formik}
            labelText={t('login.password')}
            labelClassName={`${styles.label}`}
          />
          <FormikTextField
            required
            name="retypePassword"
            id="retypePassword"
            type="password"
            value={formik.values.retypePassword}
            formik={formik}
            labelText={t('login.retypePassword')}
            labelClassName={`${styles.label}`}
          />
          <Button
            variant="contained"
            type="submit"
            loading={loading}
            disabled={loading || !(formik.isValid && formik.dirty) || !valuesSet()}
            data-cy="signUpLogIn"
            className={styles.resetButton}
            sx={{
              color: palette.button.login.color,
              backgroundColor: palette.button.login.backgroundColor,
              '&:hover': {
                color: palette.button.login.hoverColor,
                backgroundColor: palette.button.login.hoverBackgroundColor,
              },
              '&:disabled': {
                color: palette.button.login.disabledColor,
                backgroundColor: palette.button.login.disabledBackgroundColor,
              } }}
          >
            {t('platform.saveLogInButton')}
          </Button>
        </form>
      </>
    );
  };
  return setupResetPasswordForm();
}
export default SetupResetPassword;
