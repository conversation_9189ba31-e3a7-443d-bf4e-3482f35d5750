import React, { useState, useEffect, useRef } from 'react';
import { useApolloClient } from '@apollo/client';
import { Trans, useTranslation } from 'react-i18next';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useTheme, Alert } from '@mui/material';
import { useHistory } from 'react-router-dom';
import styles from './ResetPassword.module.css';
import Button from '../../../components/Button/Button';
import ErrorCard from '../../../components/ErrorCard/ErrorCard';
import { restEmailPwd } from '../../../services/api/authentication';
import { appRoutes } from '../../navigation/Routes/AppRoutes';
import FormikTextField from '../../form/FormikTextField';

function SendEmailForm() {
  const client = useApolloClient();
  const { palette } = useTheme();
  const history = useHistory();
  const { t } = useTranslation();

  const initialFocusRef = useRef(null);
  const successFocusRef = useRef(null);

  const initialState = {
    loading: false,
    error: false,
    success: false,
  };
  const [status, setStatus] = useState(initialState);
  const { loading, error, success } = status;

  const validationSchema = yup.object({
    email: yup
      .string()
      .email(t('platform.enter_valid_email'))
      .required(t('platform.email_required')),
  });

  async function onSubmit({ email }) {
    try {
      setStatus({ loading: true,
        error: false,
        success: false });
      await restEmailPwd({ email });
      setStatus({ ...status, loading: false, error: false, success: true });
      await client.clearStore();
      // history.push(appRoutes.HOME);
    } catch (e) {
      setStatus({ ...status, loading: false, error: true });
    }
  }

  useEffect(() => {
    if (initialFocusRef && initialFocusRef.current) {
      initialFocusRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (successFocusRef && successFocusRef.current) {
      successFocusRef.current.focus();
    }
  }, [success]);

  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validationSchema,
    onSubmit,
  });

  const ResetSendEmailForm = () => {
    return (
      <>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <h2
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex="0"
            ref={initialFocusRef}
            className={styles.resetPasswordH2}
            style={{ color: palette.primary.darkLightPurple }}
          >
            {t('password.resetPassword')}
          </h2>
          {error && <ErrorCard className={styles.errorCard}>{t('password.resetPasswordError')}</ErrorCard>}
          {success && (
          <Alert
            ref={successFocusRef}
            icon={false}
            style={{ background: palette.background.success }}
          >
            <span>{t('password.linkEmailed')}&nbsp;
              <b>{`${formik.values.email}`}</b>&nbsp;{t('password.foundEmail')}
            </span>
          </Alert>
          )}
          {!success && (
          <>
            <div style={{ color: palette.primary.darkLightPurple, marginTop: '0.5rem', lineHeight: '1.3rem' }}>
              <Trans i18nKey={t('password.workEmailMessage')} />
            </div>
            <FormikTextField
              required
              name="email"
              id="email"
              value={formik.values.email}
              formik={formik}
              labelText={t('login.workEmailAddress')}
              labelClassName={`${styles.label}`}
              labelStyle={{ color: palette.primary.darkLightPurple }}
            />
            <div className={styles.buttonContainer}>
              <Button
                variant="contained"
                type="submit"
                loading={loading}
                disabled={loading || !formik.dirty || !formik.isValid}
                data-cy="submit"
                className={styles.resetButton}
                sx={{
                  color: palette.button.login.color,
                  backgroundColor: palette.button.login.backgroundColor,
                  '&:hover': {
                    color: palette.button.login.hoverColor,
                    backgroundColor: palette.button.login.hoverBackgroundColor,
                  },
                  '&:disabled': {
                    color: palette.button.login.disabledColor,
                    backgroundColor: palette.button.login.disabledBackgroundColor,
                  },
                  '&:focus': {
                    border: `solid ${palette.card.backgroundColor} 3px`,
                  } }}
              >
                {t('platform.sendLink')}
              </Button>
              <a
                href="#" // for tab focus (note: lastpass will generate a console error)
                className={styles.link}
                onClick={() => history.push(appRoutes.HOME)}
                data-cy="backToLogin"
                onKeyDown={
                  (e) => {
                    if (e.code === 'Enter' || e.code === 'Space') {
                      history.push(appRoutes.HOME);
                    }
                  }
                }
              >
                {t('login.backToLogin')}
              </a>
            </div>
          </>
          )}
          {success && (
            <div className={styles.backToLoginSuccess}>
              <a
                className={styles.link}
                onClick={() => history.push(appRoutes.HOME)}
                data-cy="backToLogin"
              >
                {t('login.backToLogin')}
              </a>
            </div>
          )}
        </form>
        <hr style={{ border: palette.border.greyShade, marginBottom: '1.1rem' }} />
        <div style={{ fontWeight: 600 }}>{t('login.havingProblem?')}</div>
        <div style={{ fontSize: '1rem' }}>{t('login.email')}&nbsp;
          <a
            href={`mailto:${t('login.support')}`}
            style={{ color: palette.link.document }}
            className={styles.support}
            rel="noreferrer"
            target="_blank"
          >{t('login.support')}
          </a>
        </div>
      </>
    );
  };

  return ResetSendEmailForm();
}
export default SendEmailForm;
