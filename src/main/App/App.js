import React, { useMemo } from 'react';
import { ThemeProvider } from '@mui/material';

import { useAuth } from '../../hooks/useAuth';
import { useUser } from '../../hooks/useUser';
import { useColorMode } from '../../hooks/useColorMode';
import { getTheme } from '../../theme';

import Spinner from '../../components/Spinner/Spinner';
import Routes from '../../features/navigation/Routes/Routes';

import './App.css';
import './AccessibleStyles.css';

function App() {
  const { validatingJwt } = useAuth();
  const user = useUser();
  const { mode } = useColorMode();

  const theme = useMemo(() => getTheme(mode), [mode]);

  if (validatingJwt) {
    return <Spinner customContainerHeight="100vh" />;
  }

  return (
    <ThemeProvider theme={theme}>
      <Routes isAuthed={!!user} />
    </ThemeProvider>
  );
}

export default App;
