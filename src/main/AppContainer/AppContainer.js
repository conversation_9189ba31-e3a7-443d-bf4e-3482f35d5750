import React from 'react';
import Button from '../../components/Button/Button';
// eslint-disable-next-line no-unused-vars
import PrivacyNotice from '../../features/privacy-notice/PrivacyNotice';
import styles from './AppContainer.module.css';

export default function AppContainer({ children }) {
  return (
    <>
      <div className={styles.container} id="app-container">
        {children}
      </div>
      <Button className="srOnly" style={{ display: 'none' }} />
    </>
  );
}
