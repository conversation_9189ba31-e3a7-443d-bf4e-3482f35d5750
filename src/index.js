// Bypass SSL requirements in development for SCORM testing
if (process.env.NODE_ENV === 'development') {
  process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
}

import React from 'react';
import ReactDOM from 'react-dom';
import { ApolloProvider } from '@apollo/client';

import App from './main/App/App';
import { apolloClient } from './config/apollo';
import { AuthProvider } from './hooks/useAuth';
import { UserProvider } from './hooks/useUser';
import { ColorModeProvider } from './hooks/useColorMode';
import { PreviewModeProvider } from './hooks/usePreviewMode';
import { SpeechAudioProvider } from './hooks/useSpeechAudio';
import { ViewedVideosProvider } from './hooks/useViewedVideos';
import { ClickExpandProvider } from './hooks/useViewedClickExpand';
import { MenuDrawerProvider, AssignmentsDrawerProvider } from './hooks/useDrawer';
import { ActiveAssignmentProvider } from './hooks/useActiveAssignment';

import './i18next';

ReactDOM.render(
  <ApolloProvider client={apolloClient}>
    <AuthProvider>
      <UserProvider>
        <ColorModeProvider>
          <PreviewModeProvider>
            <MenuDrawerProvider>
              <AssignmentsDrawerProvider>
                <SpeechAudioProvider>
                  <ViewedVideosProvider>
                    <ClickExpandProvider>
                      <ActiveAssignmentProvider>
                        <App />
                      </ActiveAssignmentProvider>
                    </ClickExpandProvider>
                  </ViewedVideosProvider>
                </SpeechAudioProvider>
              </AssignmentsDrawerProvider>
            </MenuDrawerProvider>
          </PreviewModeProvider>
        </ColorModeProvider>
      </UserProvider>
    </AuthProvider>
  </ApolloProvider>,
  document.getElementById('root'),
);
