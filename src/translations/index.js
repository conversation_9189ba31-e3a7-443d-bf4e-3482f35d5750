import en from './en.app.json';
import enCa from './en-ca.app.json';
import enGb from './en-gb.app.json';
import de from './de.app.json';
import es from './es.app.json';
import esMx from './es-mx.app.json';
import fr from './fr.app.json';
import frCa from './fr-ca.app.json';
import ptBr from './pt-br.app.json';
import ar from './ar.app.json';
import cs from './cs.app.json';
import da from './da.app.json';
import zhCn from './zh-cn.app.json';
import zhTw from './zh-tw.app.json';
import bg from './bg.app.json';
import id from './id.app.json';
import ja from './ja.app.json';
import nl from './nl.app.json';
import pl from './pl.app.json';
import he from './he.app.json';
import hi from './hi.app.json';
import hu from './hu.app.json';
import ko from './ko.app.json';
import mr from './mr.app.json';
import ms from './ms.app.json';
import it from './it.app.json';
import ru from './ru.app.json';
import sq from './sq.app.json';
import th from './th.app.json';
import vi from './vi.app.json';
import sk from './sk.app.json';
import sv from './sv.app.json';
import tr from './tr.app.json';
import bs from './bs.app.json';
import tl from './tl.app.json';
import so from './so.app.json';
import sw from './sw.app.json';
import ht from './ht.app.json';
import am from './am.app.json';
import pa from './pa.app.json';
import bn from './bn.app.json';
import el from './el.app.json';
import ur from './ur.app.json';

export const translations = {
  en,
  'en-ca': enCa,
  'en-gb': enGb,
  es, // spanish
  'es-mx': esMx, // mexican spanish
  fr, // french
  'fr-ca': frCa, // canadian french
  'pt-br': ptBr, // brasilian portugese
  ar, // arabic
  bg, // bulgarian
  cs, // czech
  da, // danish
  'zh-cn': zhCn, // simplified chinese
  'zh-tw': zhTw, // tranditional chinese (taiwan)
  de, // german
  he, // hebrew
  hi, // hindi
  hu, // hungarian
  it, // italian
  ja, // japanese
  ko, // korean
  ms, // malay
  ru, // russian
  sk, // slovak
  sv, // swedish
  sq, // albanian
  nl, // dutch
  id, // indonesian
  pl, // polish
  th, // thai
  tr, // turkish
  vi, // vietnamese
  bs, // bosnian
  so, // somali
  sw, // swahili
  tl, // tagalog
  mr, // Marathi
  ht, // Haitian Creole
  am, // Amharic
  pa, // punjabi
  bn, // bengali
  el, // greek
  ur, // urdu
};

export const languageNames = {
  en: 'English',
  'en-ca': 'English (Canada)',
  'en-gb': 'English (UK)',
  es: 'Spanish - Español',
  'es-mx': 'Spanish (Mexico)',
  // af: 'Afrikaans - Afrikaans',
  sq: 'Albanian - Shqiptare',
  am: 'Amharic - አማርኛ', // no locale
  ar: 'Arabic - عربى',
  // az: 'Azerbaijani - Azərbaycan dili',
  bn: 'Bengali - বাংলা',
  bs: 'Bosnian - Bosanski',
  bg: 'Bulgarian - български',
  'zh-cn': 'Chinese (Simplified) - 简体中文',
  'zh-tw': 'Chinese (Traditional) - 繁體中文',
  de: 'German - Deutsch',
  // hr: 'Croatian - Hrvatski',
  cs: 'Czech - Český',
  da: 'Danish - Dansk',
  // 'fa-af': 'Dari - دری', // no locale
  nl: 'Dutch - Nederlands',
  // et: 'Estonian - Eestlane',
  // fi: 'Finnish - Suomalainen',
  fr: 'French - Français',
  'fr-ca': 'French - Français (Canada)',
  // ka: 'Georgian - ქართველი',
  el: 'Greek - Ελληνικά',
  ht: 'Haitian Creole - Kreyòl ayisyen',
  //  ha: 'Hausa - Hausa', // no locale
  he: 'Hebrew - עִברִית',
  hi: 'Hindi - हिंदी',
  hu: 'Hungarian - Magyar',
  id: 'Indonesian - bahasa Indonesia',
  it: 'Italian - Italiano',
  ja: 'Japanese - 日本語',
  ko: 'Korean  - 한국어',
  // lv: 'Latvian - Latvietis',
  ms: 'Malay - Bahasa Melayu',
  mr: 'Marathi - मराठी',
  // no: 'Norwegian - Norsk',
  // fa: 'Persian - فارسی',
  // ps: 'Pashto - پښتو',
  pl: 'Polish - Polskie',
  'pt-br': 'Portuguese - Português (Brasil)',
  pa: 'Punjabi - ਪੰਜਾਬੀ',
  // ro: 'Romanian - Română',
  ru: 'Russian - Pусский',
  // sr: 'Serbian - Српски',
  sk: 'Slovak - Slovák',
  // sl: 'Slovenian - Slovenščina',
  so: 'Somali - Soomaali',
  sw: 'Swahili - Kiswahili',
  sv: 'Swedish - Svenska',
  tl: 'Tagalog - Tagalog',
  // ta: 'Tamil - தமிழ்',
  th: 'Thai - ไทย',
  tr: 'Turkish - Türk',
  // uk: 'Ukrainian - Український',
  ur: 'Urdu - اردو',
  vi: 'Vietnamese - Tiếng Việt',
};

// These are all supported languages by jwPlayer
export const jwPlayerSupportedLanguages = {
  en: 'English',
  'en-ca': 'English Canadian',
  'en-gb': 'British English',
  es: 'Español',
  'es-mx': 'Español',
  fr: 'Français',
  'fr-ca': 'Français du Canada',
  'pt-br': 'Português (Brasil)',
  ar: 'عربى',
  bg: 'български', // Bulgarian
  cs: 'Český', // czech
  da: 'Dansk', // danish
  'zh-cn': '汉语',
  'zh-tw': '繁體中文',
  de: 'Deutsch',
  he: 'עִברִית',
  hi: 'हिन्दी, हिंदी',
  hu: 'Magyar',
  it: 'Italiano',
  ja: '日本語',
  ko: '한국어',
  ms: 'Bahasa Melayu',
  ru: 'Русский язык',
  sk: 'Slovák', // slovak
  sv: 'Svenska', // swedish
  sq: 'Shqiptare', // albanian
  nl: 'Nederlands',
  id: 'Bahasa Indonesia',
  pl: 'Polskie',
  th: 'ภาษาไทย',
  tr: 'Türk', // turkish
  vi: 'Tiếng Việt',
  bs: 'Bosanski', // bosnian
  so: 'Soomaali', // somali
  sw: 'Kiswahili', // swahili
  tl: 'Wikang Tagalog',
  mr: 'मराठी', // Marathi
  ht: 'kreyòl ayisyen', // Haitian Creole
  am: 'አማርኛ', // Amharic
  bn: 'বাংলা', // Bengali
  el: 'Ελληνικά', // Greek
  ur: 'اردو', // Urdu
};

export default translations;

// run manually when updating languages
// eslint-disable-next-line no-unused-vars
const integrityCheck = () => {
  Object.keys(languageNames).forEach((v) => {
    if (!translations[v]) {
      // eslint-disable-next-line no-console
      console.error('NO TRANSLATIONS FOR', v, languageNames[v]);
    }
  });
  Object.keys(translations).forEach((v) => {
    if (!languageNames[v]) {
      // eslint-disable-next-line no-console
      console.error(v, 'IS NOT IN LANGUAGE NAMES');
    }
  });
};
// integrityCheck();
