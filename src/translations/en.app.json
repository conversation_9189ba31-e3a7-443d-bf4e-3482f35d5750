{"translations": {"general": {"you": "You", "selected": "Selected", "checked": "Checked", "pleaseSelect": "Please select", "and": "and", "or": "or", "question": "Question", "option": "Option"}, "login": {"login": "Log In", "email": "Email", "workEmailAddress": "Work Email Address", "loginID": "Employee ID", "workEmailOrLoginID": "Work Email Address or Employee ID", "enter_new_password": "Please enter a new password for your account", "password": "Password", "retypePassword": "Retype Password", "forgot_your_password": "Need help logging in?", "loginError": "Incorrect user name or password", "buildingSkills": "Building skills to strengthen our workplace culture", "employee_login": "Employee Login", "backToLogin": "« Back to Log In", "havingProblem?": "Still having trouble?", "support": "<EMAIL>", "accountLockout": "Your account has been locked for security reasons. <br /><br />Use the &ldquo;Need help logging in?&rdquo; link below to unlock your account.", "remaningLastAttampt": "Incorrect user name or password. <br /><br />For security reasons, your account will be locked after one more unsuccessful attempt."}, "password": {"resetPassword": "Reset Password", "workEmailMessage": "Enter your work email address and we’ll send you a link to reset your password.", "resetPasswordText": "<strong>If you have an email address,</strong> enter it here and we’ll send you a link to reset your password.", "resetDefaultPasswordText": "<strong>If you don’t have an email address,</strong> enter your Employee ID and we’ll reset your password to the default. Ask your supervisor if you don’t know the default password.", "resetPasswordError": "As an account admin, you must provide an email address to reset your password. If you don’t have an email address, please contact us for assistance", "defaultPasswordReset": "We’ve reset your password to your company’s default password if we found employee ID", "system": "in our system.", "linkEmailed": "We’ve sent a link to", "foundEmail": "if we found that email in our system.", "welcome_set_password_msg": "Welcome! Let’s set a password for your account.", "oldPassword": "Current password", "newPassword": "New password", "confirmNew": "Confirm new password", "change_password_msg": "Please create a new password for your account."}, "signUp": {"signUpWelcomeMsg": "Welcome! Let’s sign up for an account.", "noAccount": "Don’t have an account?", "havingAccount": "Already have an account?", "signUp": "Sign Up", "signUpLogIn": "Sign Up and Log In", "useCode": "Sign Up Code", "oAuthSelfSignupError": "This Google account is not recognized. Contact your training administrator for assistance.", "loginWithGoogle": "Sign in with Google", "googleSignInError": "You are registered with your Google account. Please use that method to sign in.", "linkedInSelfSignupError": "This LinkedIn account is not recognized. Contact your training administrator for assistance.", "loginWithLinkedIn": "Sign In with LinkedIn", "linkedinSignInError": "You are registered with your LinkedIn account. Please use that method to sign in.", "loginWithMicrosoft": "Sign In with Microsoft", "microsoftSelfSignupError": "This Microsoft account is not recognized. Contact your training administrator for assistance.", "microsoftSignInError": "You are registered with your Microsoft account. Please use that method to sign in."}, "verify": {"congratsTitle": "Congrats!", "successMessage": "You have been verified! Click below to login and start using Emtrain.", "checkEmail": "Check Mail", "checkEmailMessage": "Please check your email to verify your account and begin using Emtrain.", "verifiedMessage": "User is already verified! Click below to login and start using Emtrain.", "oops": "Oops!", "resendEmailVerify": "Sorry, this verification link is no longer valid. Please enter your email to be sent a new verification link.", "resendEmailVerification": "Resend Email Verification", "alreadyRegisteredMessage": "You are already registered! Click below to log in and start using Emtrain."}, "footer": {"privacyPolicy": "Privacy", "serviceTerms": "Terms of Service", "accessibility": "Accessibility"}, "assignmentList": {"todo": "To Do", "completed": "Completed", "review": "Review", "due_date": "Due", "in_progress": "In Progress", "microlesson": "Microlesson", "lessons": "Lessons", "lessons_completed": "Lessons Completed", "current": "current", "assignments": "assignments", "assignment": "assignment", "assignment_capitalized": "Assignment", "required_time": "Required time", "estimated_time": "Estimated time", "incomplete_lesson": "Incomplete Lesson", "completed_lesson": "Completed Lesson", "in_progress_lesson": "In Progress Lesson", "shared_with_me": "Shared With Me", "closeAssignmentList": "Close Assignment List", "close_shared_assignment": "Are you sure you want to remove this? Your progress will be lost.", "skip_to_manin_content": "Skip to Main Content", "enforce_sequence_message": "You must complete your assignments in order."}, "sideDrawer": {"language": "Language", "expertTitle": "Ask a Topic Expert", "supportTitle": "Help & Support", "askExpert": {"askPrompt": "Ask a topic question", "askAnotherPrompt": "Ask another?", "missingQuestionError": "Please enter your question", "missingEmailError": "Please enter your email", "sendQuestionHeading": "Send Question", "questionSentHeading": "Question Sent!", "sendPrompt": "Send a confidential question about any training topic to our team of subject matter experts.", "confirmPrompt": "Almost there! Please review the following before sending.", "responsePrompt": "We'll get back to you within 48 hours.", "question": "Question", "enter_email": "Enter your email address to ensure that you are notified when your question is answered.", "email": "Email", "notify_me": "Notify me by email when this question is answered", "confirmationText": "Our experts provide general best practices. We cannot provide legal advice or advice regarding specific workplace or business situations. Emtrain does not disclose the identities of the questioners to employers but does provide a summary of top questions asked on particular topics. By submitting a question, you are agreeing to our website terms of service and privacy policy.", "question_sent": "Your question has been sent.", "estimated_time_answer": "Estimated time for an answer is two business days.", "response_notification": "When your question has been answered, you'll see a notification in your profile area. You'll also receive an email if you opted in.", "answer_helpful": "Was this answer helpful?", "disclaimer": "Experts provide general guidance, not legal advice. Emtrain does not disclose identities of questioners but will provide employers a summary of the topics of the questions from their workforce.", "enter_your_reply": "Enter your reply here", "reply_to": "Reply to", "headerText": "Send a confidential question about any training topic to Emtrain’s team of subject matter experts.", "answeredByEmtrain": "Your question will be answered by Emtrain topic experts, not your employer."}, "cancel": "Cancel", "save": "Save", "audio": "Audio", "audio_tooltip": "Listen to audio narration of the cards", "on": "On", "off": "Off", "submit": "Submit", "send": "Send", "got_it": "Got It!", "powered_by": "Powered by", "switch_manage": "Switch to Manage", "switch_admin": "Switch to Admin", "audio_on": "Audio narration on, controls added to lesson cards.", "audio_off": "Audio narration off, controls removed from lesson cards", "ok": "OK"}, "welcome": {"noTodoAssignments": "You have no current assignments.", "topWelcomeTitle": "Welcome!", "topWelcomeBackTitle": "Welcome Back!", "topAssignments": "You'll find your assignments in the <strong>To Do</strong> list.", "topAssignmentsVideo": "Before you get started, watch this short video about our learning environment.", "topNoAssignments": "You don't have any assignments at this time. You'll be notified by email when you do. If you're looking for completed content or a completion certificate, check the <1><strong>Completed</strong></1> list.", "topShortVideo": "While you're here, watch this short video about our learning environment.", "bottomHurry": "In a hurry?", "bottomNoProblem": "No problem! You can always access this video from <strong>Help & Support</strong>.", "noAssignmentsNow": "You don't have any assignments at this time. If you're looking for completed content or a completion certificate, check the <1><strong>Completed</strong></1> list.", "assignmentsCompleted": "You've completed all of your assignments. If you're looking for completed content or a completion certificate, check the <1><strong>Completed</strong></1> list.", "gettingStarted": "Learn how to navigate our learning environment in our <1><strong>Getting Started Guide</strong></1>", "noItemsToReview": "No items to review."}, "completedItems": {"noCompletedAssignments": "You have no completed assignments.", "completeTodoPrompt": "Once you complete an assignment from your <1><strong>To Do</strong></1> list you'll be able to review it here.", "selectCompletedList": "Select an item from the <strong>Completed</strong> list to review content or access a completion certificate."}, "lessons": {"askExpertTitle": "Ask an expert about this topic", "languageWarningLabel": "This lesson is not available in your selected language.", "breadcrumb_accessible_name": "Assignment title", "viewVideoTootip": "Please watch the video before proceeding.", "downloadVideoTranscript": "Download this video's text transcript and description.", "viewClickExpandTootip": "Please click and review the content for each box before proceeding."}, "lessonCards": {"lessonCard": "Lesson Card", "playVideo": "Play video", "freeformTextPrompt": "Add your response here.", "viewDocument": "View Document", "mustReviewPolicyAck": "You must review the document before continuing.", "mustCheckPolicyAck": "You must check the box before continuing.", "reviewedPolicyAck": "I have read and reviewed the information.", "policyReturnText": "You can return to this card at any time to review or download the document.", "noPolicyConfigured": "There is no policy configured for this lesson.", "allSet": "All Set!", "acknowledgement": "Acknowledgement", "continue": "Continue", "dontKnowSkip": "I don't know, skip", "gatedError": "Try again.", "myOrg": "My Org", "global": "Global", "topFive": "The top five words in the chart presented in order are", "showMore": "View Details", "showLess": "Hide Details", "pdf_viewer_instructions": "You are on a PDF viewer. Use the Tab key to navigate the PDF viewer controls. After reviewing the document, click the button to indicate that you have read and reviewed the information.", "close": "Close", "green": "Green", "yellow": "Yellow", "orange": "Orange", "red": "Red", "green_text": "Positive", "yellow_text": "Stressful", "orange_text": "Disturbing", "org_responses": "Responses from the people in your company are as follows:", "global_responses": "Responses from all Emtrain customers are as follows:", "org_customers": "of people in my company selected", "global_customers": "of all Emtrain Customers selected", "slider_possible_answers": "Possible answers are between 1, {{notAtAllText}} and {{sliderMax}}, {{veryText}}. ", "slider_you_selected": "You selected {{answer}}, {{answerText}}.", "wcs_possible_answers": "Possible answers are Green, Yellow, Orange and Red", "wcs_user_answer": "You selected {{userAnswer}}.", "wcs_correct_answer": "The Correct Answer is {{correct<PERSON><PERSON>wer}}.", "myorg_answers": "My Org: Includes all answers to this question from people in your company.", "global_answers": "Global: Includes all answers to  this question from all Emtrain customers.", "red_text": "Illegal", "response": "response", "responses": "responses", "my": "My", "answer": "Answer", "correct_answer": "Correct Answer", "question": "Question", "results": "Results", "congratulations": "Congratulations!", "youCompleted": "You've completed", "completion_certificate_ready": "Your certificate is ready!", "download_certificate": "Download Certificate", "nextAssignment": "Next Assignment", "nextCard": "View next card", "prevCard": "View previous card", "completion_certificate": "Completion Certificate", "view_certificate": "View Certificate", "certificate": "Certificate", "myOrg_tooltip": "MyOrg: Includes all answers to this question from people in your company.", "global_tooltip": "Global: Includes all answers to this question from Emtrain's learner community.", "lesson_complete": "Lesson Complete!", "notEnoughData": "We haven’t received enough responses to show you meaningful results.", "more_time_course": "You need to spend more time on this course.", "course_not_complete": "This course is not complete.", "review_more_lessons_requirement": "Please review the lessons of your choice until you've met the time requirement noted above.", "review_all_lessons_requirement": "You must review all of the lessons in the course to receive credit for completing it.", "policy_acknowledgement": "Policy Acknowledgement", "correct": "Correct!", "select_state": "Select state", "click_select_state": "Click your state or select it below.", "select_province": "Select province or territory", "click_select_province": "Click your province or territory or select it below.", "no_correct_answer": "There is no correct answer to this question.", "results_reflect_input": "The results reflect learner input, which may evolve as social expectations change.", "results_description_single": "There are {{numAnswers}} possible answers for this question. You may select 1.", "results_description_multi": "There are {{numAnswers}} possible answers for this question. You may select all that apply.", "the_options_are": "The options are:", "notice": "Notice", "lesson_update_message": "There has been an update to this lesson. Please click <strong>Continue</strong> to return to the beginning of the lesson. You’ll need to review it again to receive credit for completing it.", "lesson_notice_message": "We've noticed that you moved quickly through this lesson. Click <strong>Continue</strong> to proceed, and please remember to take your time!", "graph": "Graph", "table": "Table", "boolean_please_select": "Please select {{trueText}} or {{falseText}}.", "my_answer": "My answer", "view_as_list": "List", "view_as_wordcloud": "Word Cloud", "top": "Top"}, "colorSpectrum": {"graph_description": "Select an option from this interactive graphic using the arrow keys. Then use the tab key to continue.", "green_description": "Green is Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "yellow_description": "Yellow is Stressful. It means being unconscious and reactive to people and situations. You’re not your best self when yellow.", "orange_description": "Orange is Disturbing. It means consciously engaging in risky behaviour by referencing legally protected characteristics regardless of co-workers’ comfort. You’re engaging in risky behaviour when you’re orange.", "red_description": "Red is Illegal. It means the orange behaviour happens frequently, negatively affecting and making the workplace toxic. Red conduct is illegal.", "green_description_CA": "Green is Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "yellow_description_CA": "Yellow is Stressful. It means being unconscious and reactive to people and situations. You’re not your best self when yellow.", "orange_description_CA": "Orange is Disturbing. It means consciously engaging in risky behaviour by referencing legally protected characteristics regardless of co-workers’ comfort. You’re engaging in risky behaviour when you’re orange.", "red_description_CA": "Red is Illegal. It means the orange behaviour happens frequently, negatively affecting and making the workplace toxic. Red conduct is illegal.", "green_description_NY": "Green is Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "yellow_descriptiont_NY": "Yellow is Stressful. It means being unconscious and reactive to people and situations. You’re not your best self when yellow.", "orange_description_NY": "Orange is Disturbing. It means deliberately referencing legally protected characteristics regardless of co-workers’ comfort. Orange behavior is risky and can be unlawful in New York.", "red_description_NY": "Red is Illegal. It means conduct that is more frequent or more serious than orange. Conduct in the red zone is well over the line. It’s almost always unlawful and it makes a workplace toxic.", "green_description_CN": "Green is Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "yellow_description_CN": "Yellow is Stressful. It means being unconscious and reactive to people and situations. You’re not your best self when yellow.", "orange_description_CN": "Orange is Disturbing. It means words or conduct that are offensive or that single out or stereotype a person or group because of legally protected characteristics. You’re engaging in risky behaviour when you go orange and your conduct could be unlawful.", "red_description_CN": "Red is Illegal. It means the orange behaviour is so serious or so frequent that it negatively affects the workplace - making it toxic.  Red conduct is probably unlawful.", "green_description_region_US": "Green is intentionally Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "green_description_region_CN": "Green is intentionally Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "green_description_region_GL": "Green is intentionally Positive. It means consciously shifting your perspective, being socially aware and respectful of others. You bring your best self to work when you’re green.", "yellow_description_region_US": "Yellow is Reactive. It means being unconscious and thoughtless of people and situations. You’re not your best self when yellow.", "yellow_description_region_CN": "Yellow is Reactive. It means being unconscious and reactive to people and situations. You’re not your best self when yellow.", "yellow_description_region_GL": "Yellow is Reactive. It means being unconscious and thoughtless of people and situations. You’re not your best self when yellow.", "orange_description_region_US": "Orange is Disturbing. It means consciously engaging in risky behavior by referencing legally protected characteristics regardless of co-workers’ comfort. You’re engaging in risky behavior when you’re orange.", "orange_description_region_CN": "Orange is Disturbing. It means words or conduct that are offensive and that single out a person or group because of legally protected grounds and/or because of an intent to be malicious and bullying. You’re engaging in risky behaviour when you go orange and your conduct could be unlawful.", "orange_description_region_GL": "Orange is Disturbing. It means consciously engaging in risky behaviour by referencing legally protected grounds regardless of co-workers’ comfort. You’re engaging in risky behaviour when you’re orange.", "red_description_region_US": "Red is Illegal. It means the orange behavior is not trivial, and is negatively affecting and making the workplace toxic. Red conduct is illegal.", "red_description_region_CN": "Red is Illegal. It means the orange behaviour is so serious and non-trivial that it negatively affects the workplace — making it toxic. Red conduct is probably unlawful.", "red_description_region_GL": "Red is Illegal. It means the orange behaviour is not trivial and is negatively affecting and making the workplace toxic. Red conduct is illegal."}, "slider": {"useArrowKeys_change_selection": "Use the arrow keys to change your selection.", "useArrowKeys_make_selection": "Use the arrow keys to make a selection.", "sliderResultsDescription": "Answers are between 1", "sliderCurrentSelection": "Current selection is:", "sliderNeutralLabel": "Neutral"}, "boolean": {"true": "True", "false": "False", "yes": "Yes", "no": "No"}, "timer": {"time_requirement": "Time Requirement", "met": "Met", "not_met": "Not Met", "of": "of", "minutes_abbrev": "min", "mins_remaining": "mins remaining", "min_remaining": "min remaining", "minutes_remaining": "minutes remaining", "minute_remaining": "minute remaining", "paused": "Paused", "timer_paused": "Timer <PERSON>", "in_addition_spend": "In addition to completing the lessons, you must spend a <strong>minimum</strong> of", "in_addition_spend_extended": "minutes actively interacting with this course. Please continue to review the lessons of your choice until you have reached the minimum time requirement.", "minutes_on_course": "minutes on this course.", "not_time_limit": "This is NOT a time limit! Please take as much time as you need to absorb the material.", "required_minimum_spend": "You are required to spend a minimum of", "interacting_with_course": "minutes interacting with the material on this course.", "taking_break": "It looks like you're taking a break, so we've paused the timer that keeps track of the time you've spent in the course.", "continue": "Continue", "course_time_requirement": "Course Time Requirement", "welcome": "Welcome to", "must_spend": "You must spend a <strong>minimum of</strong>", "must_spend_extended": "<strong>minutes</strong> on this course", "take_your_time": "so please <strong>take your time</strong> and pace yourself accordingly!", "welcome_back": "Welcome back to", "returning_reminder": "As a reminder, you must spend a <strong>minimum of</strong>", "have_spent": "You've spent ", "so_far": "minutes so far, and <strong>have another</strong>", "to_go": "<strong>to go</strong>"}, "platform": {"requestError": "There was a problem with your request", "sendButton": "Send", "submitButton": "Submit", "submitting": "Submitting", "saveChanges": "Save Changes", "save": "Save", "saveLogInButton": "Save and Log In", "sendLink": "Send Link", "cancel": "Cancel", "enter_valid_email": "Error: Enter a valid email", "email_required": "Error: Email is required", "enter_valid_LoginID": "Error: Enter a valid employee id", "loginID_required": "Error: Employee id is required", "enter_valid_workEmailOrLoginID": "Error: Enter a valid work email or employee id", "workEmailOrLoginID_required": "Error: Work email or employee id is required", "password_min_chars": "Error: Password should be of minimum {{num}} characters length", "password_max_chars": "Error: Password should be of maximum {{num}} characters length", "password_number": "Error: Password should have at least 1 number", "password_upper_case": "Error: Password should have at least 1 upper case letter", "password_special_char": "Error: Password should have at least 1 special character", "passwords_must_match": "Error: Passwords must match", "password_required": "Error: Password is required", "field_required": "Error: {{field}} is required", "oneFieldRequired": "Error: Please only use one field", "firstName_required": "Error: First Name is required", "lastName_required": "Error: Last Name is required", "signUpCode_required": "Error: Signup Code is required", "countryCode_required": "Error: Country is required", "stateCode_required": "Error: US State is required", "of": "of", "welcome_video_description": "Learning Environment Overview Video", "close": "Close", "emtrain_logo": "Emtrain Logo", "emtrain_navigation_panel": "Emtrain Navigation Panel"}, "charts": {"start": "start", "end": "end"}, "supportMenu": {"overviewTitle": "Overview", "need_help_assignment": "Need help to complete an assignment?", "check_faqs": "Check our FAQs", "cant_find_answer": "Can't find the answer you're looking for?", "contact_support_team": "Contact our Support Team", "contact_support": "Contact Support", "indication_text": "* Indicates a required field", "your_name": "Your Name", "email": "* Email", "subject": "* Subject", "how_can_help": "How can we help you?", "optional": "Optional", "add_5_files": "Add up to 5 files", "files": "Files", "message_sent": "Message Sent!", "thanks_reaching_out": "Thanks for reaching out.", "team_will_review": "Our team will review your message and get back to you soon.", "sorry_message_error": "We're sorry, but there was a problem sending your message.", "contact_via_email": "Please contact us via <NAME_EMAIL> if this problem persists.", "message_min_chars": "Error: Your message should be of minimum 10 characters in length", "subject_required": "Error: Subject is a required field", "message_required": "Error: Message is a required field", "message": "* Describe your issue in detail", "attachments": "Attach a screenshot or video of your issue", "browser_window": "include entire browser window"}, "userMenu": {"userMenu": "User <PERSON>u", "language": "Language", "my_profile": "My Profile", "sign_out": "Sign Out"}, "profile": {"about_me": "About Me", "about_you": "About You", "password": "Password", "my_questions": "My Questions", "personal_information": "Personal Info", "personal_info_success": "Your personal info has been successfully updated.", "employee_info": "Employee Info", "employee_info_message": "This information is provided by your employer.", "employee": "Employee", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "employeeId": "Employee Id", "title": "Title", "role": "Role", "hireDate": "Hire Date", "supervisor": "Supervisor", "country": "Country", "city": "City", "state": "US State", "scormId": "SCORM ID", "location": "Location", "change_password": "Change Password", "password_success": "Your password has been successfully updated.", "password_failure": "Incorrect password,please try again", "light": "Light", "dark": "Dark", "mode": "Mode", "fields_optional": "All fields are optional.", "select": "Select...", "date_of_birth": "Date of Birth", "date_helper": "mm/dd/yyyy", "date_format_error": "Please use the format mm/dd/yyyy for your date of birth.", "gender": "Gender", "identify_transgender": "Do you identify as transgender?", "identify_heterosexual": "Do you identify as heterosexual?", "race_ethnicity": "Race/Ethnicity", "disability_prompt": "Do you have a documented physical, mental, or emotional disability?", "veteran_prompt": "Are you a military veteran?", "male": "Male", "female": "Female", "non_binary": "Non Binary", "prefer_not_say": "Prefer not to say", "yes": "Yes", "no": "No", "american_indian_alaskan": "American Indian or Alaskan Native", "asian": "Asian", "hispanic": "Hispanic or Latino or Spanish Origin of any race", "hawaiian_pacific": "Native Hawaiian or Other Pacific Islander", "african": "Black or African American", "white": "White", "multiple": "Two or more races", "protecting_privacy_header": "Protecting Your Privacy", "protecting_privacy_message": "Providing your personal demographic information is voluntary. This information will be treated as confidential and used only for research purposes. For more information, review our <1>Privacy Policy</1>.", "save_continue": "Save and Continue", "dont_share": "I don’t want to share any of this information", "demographics_header_message": "Emtrain is learning how traits such as gender, race, ethnicity, and disability status affect the workplace experience. Be part of this real-world impact by sharing your demographic information.", "no_consent": "Tracking", "no_consent_message": "Tracking has been", "no_consent_enable": "enabled", "no_consent_disable": "disabled", "no_consent_info": "Disabling tracking prevents Emtrain from tracking your IP address, browser information or any information specific to how you access our service. Disabling tracking does not prevent saving learner results specific to the service we provide our customers. Emtrain does not use any third party tracking mechanisms to track user activity on non-Emtrain websites or applications.", "back_to_my_questions": "Back to My Questions"}, "qa": {"questions_empty_state": "Questions you ask our subject matter experts will appear here. We'll notify you when they reply.", "ask_question": "Ask a question", "ask_another_question": "Ask another question", "not_answered_response": "Your question hasn’t been answered yet. We’ll notify you once it has!", "support_response": "You’ll receive a response via email. If you’d like to follow up on this question <NAME_EMAIL>.", "answered": "Answered", "awaiting_response": "Awaiting Response", "support_questions": "Forwarded to Emtrain Support", "related_question": "Related Questions", "new_response": "New Response!", "question_label": "Q", "answer_label": "A", "question": "Question", "answer": "Answer", "expertsQA": {"expert_answers_Header": "Expert Answers", "expert_answer_title": "Anonymous learner questions answered by subject matter experts."}}, "privacy": {"privacy_heading": "Data Privacy Notice", "privacy_text": "By using our site you acknowledge you have accepted our <1>Privacy Policy</1> and understand our use of user data. We respect the rights and privacy of all of our users and will comply with all data and privacy requests in accordance with the GDPR initiative and in compliance with all applicable laws and regulations.", "close": "Accept", "privacy_note": "By using our site, you agree to our <link1>Terms of Service</link1> and <link2>Privacy Policy</link2>.", "privacy_header": "Privacy Notice", "privacy_note_1": "This training and the surveys it contains are being conducted on behalf of your employer for the purposes of providing training and gathering insights regarding its culture. Your responses will be treated as confidential at all times. To provide clients with a better understanding of the survey results, your responses will be aggregated and benchmarked across those within the Emtrain database.  The results of the survey and benchmarking analyses will be presented to your employer in aggregate form and will not include individual responses.", "privacy_note_2": "Surveys are embedded within the training experience. Your employer determines your training assignments and it also decides which are elective and which are mandatory. You will be expressly notified if a training assignment is mandatory. Your participation and completion of training assignments are tracked and reported to your employer. Survey data and training completion data are maintained separately.", "privacy_note_3": "Emtrain employs data aggregation practices that ensure individual identities remain protected. This approach allows us to derive meaningful insights that support our business and compliance objectives, while upholding the confidentiality and privacy of all data entrusted to us.", "privacy_note_4": "This training is confidential and proprietary. You may not share it or any access credentials (e.g., login credentials and/or personalized URL) you have been provided with any other person.  If you decide to leave the training before completing it, your responses will be collected and retained as described above."}, "preview": {"preview_heading": "Preview Mode", "preview_text": "You are previewing content. Your answers to questions will not be saved and the “My Org” responses you see are sample data, not representative of your organization.", "close_preview": "Close Preview", "preview": "Preview"}, "scorm": {"scorm_label_text": "<strong>Don’t close your browser window</strong> when you are finished! Instead, click the <strong>Save & Exit</strong> button on the right side of your screen.", "save_exit": "Save & Exit", "save_exit_note_1": "One last thing", "save_exit_note_2": "Click this button to record your completion and close the assignment."}, "speech_audio": {"playback_rate": "Playback Rate", "audio_narration": "Audio narration", "audio_on": "Audio narration on.", "audio_off": "Audio narration off.", "play_pause": "Play/Pause", "audio_status": "Audio status", "volume": "Volume", "volume_slider": "Volume slider", "playing": "Playing", "not_playing": "Not playing"}, "404-error": {"error_message": "Oops! The page you were looking for doesn't exist.", "home": "Back to Home"}, "logged-out": {"error_message": "You’ve been logged out.", "home": "Log Back In"}, "scorm-exit": {"account_deactivate": "This assignment cannot be launched. Your account has been deactivated.", "scorm_deactivate": "This assignment cannot be launched. Your account’s SCORM integration has been deactivated.", "assignment_msg": "This assignment cannot be launched. We cannot find an assignment of {{ contentType }} {{ contentID }} for {{ scormId }} (suspend_data: {{ resourceId }}).", "bad_request": "This assignment cannot be launched. {{ code }} Bad Request.", "bad_gateway": "This assignment cannot be launched. {{ code }} Bad Gateway.", "exit": "Exit"}}}