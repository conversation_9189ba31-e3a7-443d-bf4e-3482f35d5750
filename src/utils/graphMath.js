export const radian = Math.PI / 180;
export const getXOffsetMultiplierByAngle = (angle) => Math.cos(angle - 90 * radian);
export const getYOffsetMultiplierByAngle = (angle) => Math.sin(angle - 90 * radian);
export const getXOffset = (offset, angle) => offset * getXOffsetMultiplierByAngle(angle);
export const getYOffset = (offset, angle) => offset * getYOffsetMultiplierByAngle(angle);
export const getAverage = (array) => array.reduce((acc, cur) => acc + cur, 0) / array.length;
