/**
 * Google Tag Manager utility functions
 * Based on the pattern from the admin frontend
 */

/**
 * Check if account has SCORM integration
 * @param {Object} integration - Integration object
 * @returns {boolean}
 */
const isScormIntegrationType = (integration) => {
  return integration && integration.type === 'scorm';
};

/**
 * Get user role based on permissions and account structure
 * @param {Object} user - User object from authentication
 * @returns {string} - User role string
 */
const getRoleFromUser = (user) => {
  if (!user) {
    return null;
  }
  if (user?.isScormAccount && user?.emtrainUserRole === 'User') {
    return 'user';
  }
  if (!user.permissions || !user.accounts || !user.accounts[0]) {
    return 'unknown';
  }
  if (user.permissions.accounts && user.permissions.accounts.includes('create')) {
    return 'system admin';
  }
  if (user.accounts[0].accountOwnerId === user.id) {
    return 'emtrain admin';
  }
  if (user.accounts[0].clientAccountOwnerId === user.id) {
    return 'client account owner';
  }
  if (user.permissions.users && user.permissions.users.includes('createAccount')) {
    return 'account admin';
  }
  if (user.permissions.questionAnswers && user.permissions.questionAnswers.includes('updateAssigned')) {
    return 'expert';
  }
  if (user.permissions.lessons && user.permissions.lessons.includes('update')) {
    return 'content developer';
  }
  if (user.permissions.lessons && user.permissions.lessons.includes('review')) {
    return 'content reviewer';
  }
  if (user.permissions.accounts && user.permissions.accounts.includes('read')) {
    return 'account executive';
  }
  return 'user';
};

/**
 * Get account role and site type information
 * @param {Object} user - User object from authentication
 * @returns {Object} - Account role and site type data
 */
const getAccountRoleAndSiteType = (user) => {
  if (!user) {
    return null;
  }
  const data = { aiSiteType: '', aiAdminSubRole: '' };
  const account = user.accounts && user.accounts[0];
  if (account) {
    data.aiAdminSubRole = user.accountRole && user.accountRole.accountId === account.id ? user.accountRole.name : '';
    if (account.integrations) {
      data.aiSiteType = account.integrations.find(isScormIntegrationType)
        ? `scorm-${account.accountType}`
        : `hosted-${account.accountType}`;
    }
    data.selfSignup = account.selfSignup;
    data.subdomain = account.subdomain;
  }
  return data;
};

/**
 * Set GTM user role and push user data to dataLayer
 * Main function to call when user authenticates
 * @param {Object} user - User object from authentication
 */
export const setGtmUserRole = (user) => {
  // dataLayer is an array created by Google Tag Manager
  if (!user || typeof window === 'undefined' || !window.dataLayer) {
    return;
  }

  try {
    const data = getAccountRoleAndSiteType(user);
    // Push user identification data
    window.dataLayer.push({
      event: 'user_identified',
      aiUserRole: getRoleFromUser(user),
      userId: user?.id,
      aiSiteType: data && data.aiSiteType,
      aiAdminSubRole: data && data.aiAdminSubRole,
      userEmail: user?.email,
      userName: `${user?.firstName} ${user?.lastName}`,
      accountId: user?.accounts?.[0]?.id,
      accountName: user?.accounts?.[0]?.name,
      accountType: user?.accounts?.[0]?.accountType,
      licenseLevel: user?.accounts?.[0]?.licenseLevel,
      isScormAccount: user?.isScormAccount || false,
    });

    // Push signup event if this is a self-signup account
    if (data && data.selfSignup) {
      window.dataLayer.push({
        event: 'sign_up',
        userId: user.id,
        siteName: data.subdomain,
      });
    }
  } catch (error) {
    console.error('Error setting GTM user role:', error);
  }
};
