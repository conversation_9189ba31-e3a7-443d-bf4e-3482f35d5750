/**
 * Utility functions for managing Google Tag Manager dataLayer
 * and Userpilot integration
 */

/**
 * Push user data to dataLayer for GTM and Userpilot
 * @param {Object} user - User object from authentication
 */
export const pushUserDataToGTM = (user) => {
  if (!window.dataLayer) {
    console.warn('dataLayer not initialized');
    return;
  }

  try {
    const userData = {
      event: 'user_identified',
      user_id: user.id,
      user_email: user.email,
      user_name: `${user.firstName} ${user.lastName}`,
      account_id: user.accounts?.[0]?.id,
      account_name: user.accounts?.[0]?.name,
      account_type: user.accounts?.[0]?.accountType,
      user_role: user.accounts?.[0]?.accountUsers?.roleId,
      is_scorm_account: user.isScormAccount || false,
      license_level: user.accounts?.[0]?.licenseLevel,
      // Add any other user properties that <PERSON>rp<PERSON><PERSON> needs
    };

    // Push to dataLayer
    window.dataLayer.push(userData);

    console.log('User data pushed to GTM dataLayer:', userData);
  } catch (error) {
    console.error('Error pushing user data to GTM:', error);
  }
};

/**
 * Push custom events to dataLayer
 * @param {string} eventName - Name of the event
 * @param {Object} eventData - Additional event data
 */
export const pushEventToGTM = (eventName, eventData = {}) => {
  if (!window.dataLayer) {
    console.warn('dataLayer not initialized');
    return;
  }

  try {
    window.dataLayer.push({
      event: eventName,
      ...eventData,
    });

    console.log(`Event '${eventName}' pushed to GTM dataLayer:`, eventData);
  } catch (error) {
    console.error(`Error pushing event '${eventName}' to GTM:`, error);
  }
};

/**
 * Initialize Userpilot with user data
 * This should be called after user authentication
 * @param {Object} user - User object from authentication
 */
export const initializeUserpilot = (user) => {
  // First push user data to dataLayer
  pushUserDataToGTM(user);

  // Then trigger userpilot initialization event
  pushEventToGTM('userpilot_init', {
    user_id: user.id,
    user_email: user.email,
    account_id: user.accounts?.[0]?.id,
  });
};

/**
 * Track page views for analytics
 * @param {string} pageName - Name of the page
 * @param {string} pageUrl - URL of the page
 */
export const trackPageView = (pageName, pageUrl) => {
  pushEventToGTM('page_view', {
    page_name: pageName,
    page_url: pageUrl,
    timestamp: new Date().toISOString(),
  });
};

/**
 * Track user interactions for analytics
 * @param {string} action - Action performed
 * @param {string} category - Category of the action
 * @param {string} label - Label for the action
 */
export const trackUserInteraction = (action, category, label = '') => {
  pushEventToGTM('user_interaction', {
    action,
    category,
    label,
    timestamp: new Date().toISOString(),
  });
};
