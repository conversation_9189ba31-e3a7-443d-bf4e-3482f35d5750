import { ApolloClient, createHttpLink, InMemoryCache, from, ApolloLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { appRoutes } from '../features/navigation/Routes/AppRoutes';
import { apiURL } from './constants';
import { getLoginToken, removeLoginToken, checkTokenValid } from '../services/api/authentication';

const invalidTokenMessage = 'Invalid token';
const invalidTokenErrorCode = '1000';

const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`,
      );
    });
  }
  if (networkError && networkError.result &&
    ((networkError.result.message && networkError.result.message.startsWith(invalidTokenMessage)) ||
    (networkError.result.errCode && networkError.result.errCode === invalidTokenErrorCode))) {
    removeLoginToken();
    window.location.assign(appRoutes.LOGIN);
  }
});

const roundTripLink = new ApolloLink((operation, forward) => {
  const validToken = checkTokenValid();
  if (!validToken) {
    removeLoginToken();
    window.location.assign(appRoutes.LOGIN);
  }
  // Called before operation is sent to server
  // operation.setContext({ start: new Date() });

  return forward(operation).map((data) => {
    // Called after server responds
    // const time = new Date() - operation.getContext().start;
    // console.log(`Operation ${operation.operationName} took ${time} to complete`);
    return data;
  });
});

const httpLink = createHttpLink({
  uri: `${apiURL()}/graphql`,
});

const authLink = setContext((_, { headers }) => {
  // return the headers to the context so httpLink can read them
  return {
    headers: {
      ...headers,
      authorization: getLoginToken() || undefined,
    },
  };
});

export const apolloClient = new ApolloClient({
  link: from([authLink, roundTripLink, errorLink, httpLink]),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          getAssignmentList: {
            // eslint-disable-next-line no-unused-vars
            merge(existing = [], incoming = []) {
              return [...incoming];
            },
          },
        },
      },
      Query: {
        fields: {
          getResourceAssets: {
            keyArgs: false, // Ignoring arguments, assuming your args might differ per request
            merge(existing = {}, incoming = {}) {
              return {
                ...incoming,
                data: existing.data ? [...existing.data, ...incoming.data] : incoming.data,
              };
            },
          },
        },
      },
      BooleanCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
      SliderCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
      SingleChoiceCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
      MultipleChoiceCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
      PolicyAcknowledgementCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
      WorkplaceColorSpectrumCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
      FreeformTextCard: {
        fields: {
          answer: {
            merge: false,
          },
        },
      },
    },
    // onError: errorLink,
  }),
});
