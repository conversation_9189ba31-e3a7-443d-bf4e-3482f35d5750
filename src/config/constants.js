import { get } from 'lodash';

// return the api subdomains (ci-api, uat-api, ai-api)
export const apiURL = () => {
  const subdomains = window.location.hostname.split('.');
  const count = subdomains.length;
  let apiHostName = '';

  const domainMap = {
    ci2: 'ci',
    'ci-admin': 'ci',
    'ci-app': 'ci',
    'stage-admin': 'stage',
    'stage-app': 'stage',
    jettuat: 'uat',
    'uat-admin': 'uat',
    'uat-app': 'uat',
    ai2: 'ai',
    app: 'ai',
    admin: 'ai',
  };

  if (subdomains.includes('localhost')) {
    // Developers use the port for the server API instead of a separate URL
    apiHostName = `${window.location.hostname}:3030`;
  } else {
    const apiPosition = count - 3; // Location to add the api extemnsion

    // URLs have the format account.environment.emtrain.com.  The server API is located
    // at account.environment-api.emtrain.com.  For the public site, the URL is
    // em.emtrain.com, which is why we need the apiPosition.
    subdomains.forEach((subdomain, index) => {
      const addDot = index < count - 1 ? '.' : '';
      const updatedSubdomain = get(domainMap, subdomain, subdomain);
      const addSubdomain = index === apiPosition ? `${updatedSubdomain}-api` : subdomain;
      apiHostName += `${addSubdomain}${addDot}`;
    });
  }
  return `${window.location.protocol}//${apiHostName}`;
};

// Return the manage subdomains (ci-admin, uat-admin, admin)
export const aiAppURL = () => {
  const subdomains = window.location.hostname.split('.');
  const count = subdomains.length;
  let aiAppHostName = '';

  const domainMap = {
    ci: 'ci-admin',
    ci2: 'ci-admin',
    'ci-app': 'ci-admin',
    'stage-app': 'stage-admin',
    jettuat: 'uat-admin',
    uat: 'uat-admin',
    'uat-app': 'uat-admin',
    ai: 'admin',
    ai2: 'admin',
    app: 'admin',
  };

  if (subdomains.includes('localhost')) {
    // Developers use the 8080 port for AI app
    aiAppHostName = `${window.location.hostname}:8080`;
  } else {
    const aiDomainPosition = count - 3; // Location to add the api extemnsion

    // URLs have the format account.environment.emtrain.com.
    subdomains.forEach((subdomain, index) => {
      const addDot = index < count - 1 ? '.' : '';
      const updatedSubdomain = get(domainMap, subdomain, subdomain);
      const addSubdomain = index === aiDomainPosition ? `${updatedSubdomain}` : subdomain;
      aiAppHostName += `${addSubdomain}${addDot}`;
    });
  }
  return `${window.location.protocol}//${aiAppHostName}`;
};

export const JW_LICENSE = 'Hq6I/vn2cBnC57VaaUST07bWFgPNG6K2ZjZRCesGgxq+pwFU';

export const LESSON_CARD_ASPECT_RATIO = 1.228;

export const RESPONSE_THRESHOLD_DEFAULT = 5;

export const GOOGLE_BASE_URL = 'https://www.googleapis.com/oauth2/v1/userinfo';

export const PASSWORD_DEFAULT_MIN_LENGTH = 8;

export const PASSWORD_DEFAULT_MAX_LENGTH = 32;

export const LOGIN_SESSION_TIME_OUT = 48;

export const SAML_SESSION_TIME_OUT = 24;

export const EMTRAIN_WEBSITE_URL = 'https://emtrain.com';

export const TABLEAU_URL = 'https://10ay.online.tableau.com/#/site/emtrain/views';
