export const setLocalStorageVolume = (val) => {
  window.localStorage.setItem('volume', val);
};
export const getLocalStorageVolume = () => {
  const val = window.localStorage.getItem('volume') || '50';
  return Number(val);
};

export const setLocalStorageRate = (val) => {
  window.localStorage.setItem('rate', val);
};
export const getLocalStorageRate = () => {
  const val = window.localStorage.getItem('rate') || '100';
  return Number(val);
};

export const setLocalStorageAudioNarration = (val) => {
  window.localStorage.setItem('audioPause', val);
};
