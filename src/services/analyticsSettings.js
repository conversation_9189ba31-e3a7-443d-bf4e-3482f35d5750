export function updateSessionRiskFilters(riskPerson, riskObject) {
  try {
    const existing = sessionStorage.getItem(riskPerson);
    const currentData = existing ? JSON.parse(existing) : {};

    const updatedData = { ...currentData, ...riskObject };
    sessionStorage.setItem(riskPerson, JSON.stringify(updatedData));
  } catch (error) {
    console.error(`Failed to update sessionStorage for riskPerson: ${riskPerson}`, error);
  }
}

export const getSelectedSessionFilters = (riskPerson) => {
  return JSON.parse(sessionStorage.getItem(riskPerson));
};
