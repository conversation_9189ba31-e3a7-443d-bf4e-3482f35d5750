import { get } from 'lodash';
import { axiosClient } from '../../config/axios';
import {
  getStudentID,
  getSuspendData,
  getStudentName,
} from './scormRusticiAPI';
import { refreshCookie } from '../../hooks/useCookies';

const JWT_STORAGE_KEY = 'ai-jwt';
const CHANGE_PASSWORD = 'change_password';
const BY_PASS_SSO_SETTING = 'byPassSSOSetting';
const IS_PREVIEW_ASSIGNMENT = 'isPreviewAssignment';
const IS_COMPLETED_ITEM_VIEW = 'isCompletedItemView';

export const loginById = async ({ loginId, password }) => {
  const result = await axiosClient.post('/authentication', {
    email: loginId,
    password,
    strategy: 'local',
  });
  return result.data;
};

export const validateJwt = async ({ accessToken }) => {
  const result = await axiosClient.post('/authentication', {
    accessToken,
    strategy: 'jwt',
  });
  return result.data;
};

export const setLoginToken = (token) => {
  localStorage.setItem(JWT_STORAGE_KEY, token);
};

export const getLoginToken = () => {
  return localStorage.getItem(JWT_STORAGE_KEY) || null;
};

export const setByPassSSOSetting = (byPassSSO) => {
  localStorage.setItem(BY_PASS_SSO_SETTING, byPassSSO);
};

export const getByPassSSOSetting = () => {
  return localStorage.getItem(BY_PASS_SSO_SETTING) || false;
};

export const removeByPassSSOSetting = () => {
  localStorage.removeItem(BY_PASS_SSO_SETTING);
};

export const removeLoginToken = () => {
  localStorage.removeItem(JWT_STORAGE_KEY);
};

export const setChangePasswordToken = (token) => {
  localStorage.setItem(CHANGE_PASSWORD, token);
};

export const getChangePasswordToken = () => {
  return localStorage.getItem(CHANGE_PASSWORD) || null;
};

export const removeChangePasswordToken = () => {
  localStorage.removeItem(CHANGE_PASSWORD);
};

export const setIsPreviewModeEnable = (assignmentData) => {
  localStorage.setItem(IS_PREVIEW_ASSIGNMENT, assignmentData);
};

export const getIsPreviewModeEnable = () => {
  return localStorage.getItem(IS_PREVIEW_ASSIGNMENT) || null;
};

export const removeIsPreviewModeEnable = () => {
  localStorage.removeItem(IS_PREVIEW_ASSIGNMENT);
};

export const setIsCompletedItemView = (completeView) => {
  localStorage.setItem(IS_COMPLETED_ITEM_VIEW, completeView);
};

export const getIsCompletedItemView = () => {
  return localStorage.getItem(IS_COMPLETED_ITEM_VIEW) === 'true' || false;
};

export const removeIsCompletedItemView = () => {
  localStorage.removeItem(IS_COMPLETED_ITEM_VIEW);
};

export const checkTokenValid = () => {
  try {
    const token = getLoginToken();
    const parsedToken = JSON.parse(atob(token.split('.')[1]));
    if (parsedToken.exp * 1000 < Date.now()) {
      return false;
    }
    return true;
  } catch (exp) {
    return false;
  }
};

export const authenticateScorm = async ({ apiKey, integrationKey, programId, lessonId, enforceSequence }) => {
  const scormId = getStudentID();
  const suspendData = getSuspendData();
  const fullName = getStudentName(); // last name, first name
  const [lastName, firstName] = fullName.split(',').map((item) => item.trim());

  const userPayload = {
    userId: scormId,
    apiKey,
    integrationKey,
    programId: programId || 0,
    lessonId: lessonId || 0,
    suspendData,
    enforceSequence,
  };
  if (firstName) {
    userPayload.firstName = firstName;
  }
  if (lastName) {
    userPayload.lastName = lastName;
  }
  const result = await axiosClient.post('/authentication/verifyscorm', userPayload);
  return result.data;
};

export const authenticateSaml = async (token) => {
  const result = await axiosClient.post('/authentication/verifysaml', { token });
  return result.data;
};

export const authenticateJettSSO = async (token, email, employeeId) => {
  const result = await axiosClient.post('/authentication/jett-sso', { token, email, employeeId });
  return result.data;
};

// authenticate for content preview from emtrain.com
export const verifyAuth = async (authKey, authToken) => {
  const result = await axiosClient.post('/authentication/verifyauth', { authKey, authToken });
  return result.data;
};

export const restEmailPwd = async ({ email }) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'sendResetPwd',
    value: { email },
  });
  return result.data;
};

export const verifyEmail = async (token) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'verifySignupLong',
    value: token,
  });
  return result.data;
};

export const resendVerifyEmail = async (email) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'resendVerifySignup',
    value: { email },
  });
  return result.data;
};

export const verifyEvent = async (userId) => {
  const result = await axiosClient.post('/events', {
    type: 'verify',
    trackableId: userId,
    userId,
    trackableType: 'users',
  });
  return result.data;
};

export const resetEmployeeIdPwd = async ({ employeeId }) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'resetEmployeeIdPwd',
    value: { employeeId },
  });
  return result.data;
};

export const checkResetTokenExpired = async ({ token, action }) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'checkResetTokenExpired',
    value: { token, action },
  });
  return result.data;
};

export const resetPassword = async (token, password, emailOrId = null, setup = false) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'resetPwdLong',
    value: {
      token,
      password,
      emailOrId,
      setup,
    },
  });
  return result.data;
};

export const verifyAndReset = async (token, password) => {
  const result = await axiosClient.post('/auth-management', {
    action: 'verifyAndResetPwdLong',
    value: {
      token,
      password,
    },
  });
  return result.data;
};

export const registerUsers = async (variables) => {
  const result = await axiosClient.post('/users', variables);
  return result.data;
};

export const getAuthenticationHeader = () => {
  return {
    headers: {
      Authorization: getLoginToken(),
    },
  };
};

export const getAuthenticationMultipartHeader = () => {
  return {
    headers: {
      Authorization: getLoginToken(),
      'Content-Type': 'multipart/form-data',
    },
  };
};

export const getJettSSOToken = async () => {
  const result = await axiosClient.get('/authentication/jettSSOToken', getAuthenticationHeader());
  const token = get(result, 'data.token');
  return token;
};

export const tableauToken = async () => {
  // Check if we're in development mode (localhost)
  const isDevelopment = window.location.hostname === 'localhost';

  if (isDevelopment) {
    console.log('Development mode: Returning mock Tableau token');
    return 'mock-tableau-token-for-development';
  }

  try {
    const result = await axiosClient.get('/authentication/tableauJwtToken', getAuthenticationHeader());
    const token = get(result, 'data.tableauJwtToken');
    return token;
  } catch (error) {
    console.error('Failed to fetch Tableau JWT token:', error);
    // Return a mock token as fallback to prevent errors
    return 'mock-tableau-token-fallback';
  }
};

export const googleOauthEvent = async (variables) => {
  const result = await axiosClient.post('/users/googleOauth', variables);
  return result.data;
};

export const linkedinAuthorizationCode = async (code, redirectUrl) => {
  const result = await axiosClient.post('/users/linkedinOauth', {
    code,
    redirectUrl,
    oAuthProvider: 'linkedin',
  });
  return result.data;
};

export const microsoftOauthEvent = async (msToken) => {
  const result = await axiosClient.post('/users/microsoftOauth', { accessToken: msToken });
  return result.data;
};

export const getUserById = async (id) => {
  const result = await axiosClient.get(`/users/${id}`, getAuthenticationHeader());
  return result.data;
};

export const refreshToken = async () => {
  const result = await axiosClient.post('/authentication/refreshToken', {}, getAuthenticationHeader());
  setLoginToken(result.data.accessToken);
  refreshCookie();
  return result.data;
};
