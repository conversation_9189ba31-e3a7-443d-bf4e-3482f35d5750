const { RXD } = window.parent;

export const setPassed = () => {
  RXD.SetPassed();
};

export const setSuspendData = (suspendData) => {
  RXD.SetSuspendData(suspendData);
};

export const concedeControl = () => {
  RXD.ConcedeControl();
};

export const getStudentID = () => {
  return RXD.GetStudentID();
};

export const getSuspendData = () => {
  return RXD.GetSuspendData();
};

export const getStudentName = () => {
  return RXD.GetStudentName();
};
