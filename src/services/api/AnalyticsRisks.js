import { axiosClient } from '../../config/axios';
import { getAuthenticationHeader } from './authentication';

export const getRiskAreas = async (analyticsId) => {
  try {
    const responseData = {};
    const result = await axiosClient.get(
      `/analytics/riskAreas?analyticsId=${analyticsId}`,
      getAuthenticationHeader(),
    );
    if (result && result?.data?.data) {
      responseData.riskAreas = result.data.data.riskAreas;
      responseData.flagHasTenureData = result.data.data.flagHasTenureData;
      return responseData;
    }
  } catch (e) {
    console.error(e);
  }
};

export const getRisksQuestions = async (params) => {
  const { minResponseRate, analyticsId, riskId, includeNewHires, periodCategory,
    period, sortCriteria, isDefaultPeriod } = params;
  try {
    const result = await axiosClient.post('/analytics/riskDetails', {
      minResponseRate,
      analyticsId,
      riskId,
      includeNewHires,
      periodCategory,
      periodId: period,
      sortCriteria,
      isDefaultPeriod,
    }, getAuthenticationHeader());
    return result.data;
  } catch (e) {
    console.error(e);
  }
};

export const getOverViewList = async (analyticsId) => {
  try {
    const result = await axiosClient.get(
      `/analytics/overview/${analyticsId}`,
      getAuthenticationHeader(),
    );
    return result.data;
  } catch (e) {
    console.error(e);
  }
};

export const getRisksSummary = async (params) => {
  const { minResponseRate, analyticsId, includeNewHires,
    periodCategory, period, riskPersona, isDefaultPeriod } = params;
  try {
    const result = await axiosClient.post('/analytics/riskSummary', {
      minResponseRate,
      analyticsId,
      includeNewHires,
      periodCategory,
      periodId: period,
      riskPersona,
      isDefaultPeriod,
    }, getAuthenticationHeader());
    return result.data;
  } catch (e) {
    console.error(e);
  }
};
