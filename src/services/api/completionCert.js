import { get } from 'lodash';
import { axiosClient } from '../../config/axios';
import { getAuthenticationHeader } from './authentication';

export const downloadCompletionCertificate = async (programId, assignmentId) => {
  const result = await axiosClient.get('/authentication/downloadToken', getAuthenticationHeader());
  const token = get(result, 'data.token');
  try {
    const data = { userLessonId: assignmentId, token };
    const response = await axiosClient.post(`/programs/${programId}/certDownload`, data,
      { responseType: 'blob', ...getAuthenticationHeader() });
    if (response) {
      const contentDisposition = response.headers['content-disposition'];
      const match = contentDisposition.match(/filename="(.+)"/);
      const filename = match && match[1] ? match[1] : 'program_completion_certificate.pdf';
      const url = window.URL.createObjectURL(response.data);
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `${filename}`;
      downloadLink.click();

      window.URL.revokeObjectURL(url);
      return true;
    }
  } catch { return true; } // eslint-disable-line no-empty
};

//Download resource files with the file path/urls(s3 url).
export const downloadResourceFile = async (filePath) => {
  const result = await axiosClient.get('/authentication/downloadToken', getAuthenticationHeader());
  const token = get(result, 'data.token');
  function filePathExtract(filePath) {
    const parts = filePath.split('/');
    const filenameWithUUID = parts[parts.length - 1];
    // Split the filename by '_' and get the last part of the exact filename
    const filenameParts = filenameWithUUID.split('_');
    const filename = filenameParts.slice(1).join('_');
    return filename;
  }

  const filename = filePathExtract(filePath);

  try {
    const data = { imageUrl: filePath, token };
    const response = await axiosClient.post(`/resources/downloadResourcefile`, data,
      { responseType: 'blob', ...getAuthenticationHeader() });
    if (response) {
      const url = window.URL.createObjectURL(response.data);
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `${filename}`;
      downloadLink.click();

      window.URL.revokeObjectURL(url);
    }
  } catch {} // eslint-disable-line no-empty
};

export const downloadVideoTranscript = async (lessonCardId, lessonId, programId) => {
  const result = await axiosClient.get('/authentication/downloadToken', getAuthenticationHeader());
  const token = get(result, 'data.token');
  try {
    const data = { token, lessonId, programId };
    const response = await axiosClient.post(`/lessons/${lessonCardId}/downloadVideoTranscript`, data,
      { responseType: 'blob', ...getAuthenticationHeader() });
    if (response) {
      // eslint-disable-next-line max-len
      const filename = response.headers?.['content-disposition']?.match(/filename="(.+)"/)?.[1] || 'program_completion_certificate.pdf';
      const url = window.URL.createObjectURL(response.data);
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `${filename}`;
      downloadLink.click();

      window.URL.revokeObjectURL(url);
      return { res: 'ok' };
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error downloading video transcript:', error);
    return { res: 'error' };
  }
};
