import { axiosClient } from '../../config/axios';
import { getAuthenticationMultipartHeader } from './authentication';

export const policyCardUpdate = async (values) => {
  const data = new FormData();
  if (values?.lessonCardId) {
    data.append('lessonCardId', values.lessonCardId);
  }

  data.append('link', values?.link || '');
  data.append('fileId', values?.fileId || null);
  data.append('title', values.title?.trim() || '');
  data.append('description', values.description?.trim() || '');

  if (values?.file) {
    data.append('file', values.file);
  }

  if (values?.policyType) {
    data.append('policyType', values.policyType);
  }

  const result = await axiosClient.post(
    '/account-lesson-cards',
    data,
    getAuthenticationMultipartHeader(),
  );

  return result.data;
};
