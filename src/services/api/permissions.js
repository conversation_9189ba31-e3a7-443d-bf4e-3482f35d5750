import { aiAppURL } from '../../config/constants';

export const canViewAdminMenu = (permissions, accountType) => {
  return permissions && permissions.admin && permissions.admin.includes('read') && accountType === 'admin';
};

export const canViewGlobalDashboard = (permissions) => {
  return permissions && permissions.dashboard && permissions.dashboard.includes('read');
};

export const canViewAccountDashboard = (permissions) => {
  return permissions && permissions.dashboard &&
    (permissions.dashboard.includes('read') || permissions.dashboard.includes('readAccount'));
};

export const canCreateUsers = (permissions) => {
  return permissions && permissions.users &&
    (permissions.users.includes('create') || permissions.users.includes('createAccount'));
};

export const canImportUsers = (permissions) => {
  return permissions && permissions.users &&
    (permissions.users.includes('create') || permissions.users.includes('createAccount'));
};

export const canReadOwnUsers = (permissions) => {
  return permissions && permissions.users &&
    (permissions.users.includes('read') || permissions.users.includes('readAccount'));
};

export const canCreateLessons = (permissions) => {
  return permissions && permissions.lessons && permissions.lessons.includes('create');
};

export const canCreateResources = (permissions) => {
  return permissions && permissions.resources && permissions.resources.includes('create');
};

export const canReadResources = (permissions) => {
  return permissions && permissions.resources && permissions.resources.includes('read');
};

export const canAccessLessons = (permissions) => {
  return permissions && permissions.lessons && permissions.lessons.includes('review');
};

export const canAccessQuestions = (permissions) => {
  return permissions && permissions.questionAnswers &&
    (permissions.questionAnswers.includes('update') || permissions.questionAnswers.includes('updateAssigned'));
};

export const canAssignQuestions = (permissions) => {
  return permissions && permissions.questionAnswers && permissions.questionAnswers.includes('update');
};

export const canDeleteQuestions = (permissions) => {
  return permissions && permissions.questionAnswers && permissions.questionAnswers.includes('delete');
};

export const canReadCompanies = (permissions) => {
  return permissions && permissions.companies && permissions.companies.includes('read');
};

export const canReadAccounts = (permissions) => {
  return permissions && permissions.accounts && permissions.accounts.includes('read');
};

export const canEditAccounts = (permissions) => {
  return permissions && permissions.accounts && permissions.accounts.includes('update');
};

export const canEditOwnAccount = (permissions) => {
  return permissions && permissions.accounts &&
    (permissions.accounts.includes('update') || permissions.accounts.includes('updateAccount'));
};

export const canCreateAccounts = (permissions) => {
  return permissions && permissions.accounts && permissions.accounts.includes('create');
};

export const canAccessPrograms = (permissions) => {
  return permissions && permissions.programs && permissions.programs.includes('read');
};

export const canAccessContentPackages = (permissions) => {
  return permissions && permissions.contentPackages && permissions.contentPackages.includes('read');
};

export const canReadOwnGroups = (permissions) => {
  return permissions && permissions.groups &&
    (permissions.groups.includes('read') || permissions.groups.includes('readAccount'));
};

export const canCreateOwnGroups = (permissions) => {
  return permissions && permissions.groups &&
    (permissions.groups.includes('create') || permissions.groups.includes('createAccount'));
};

export const canAccessUploads = (permissions) => {
  return permissions && permissions.users &&
    (permissions.users.includes('read'));
};

// TODO: Change to customer admin-specific permissions (once they exist)
// TODO: should check to see if the requested account belongs to the user
export const canViewManageMenu = (permissions) => {
  return (permissions && permissions.admin && permissions.admin.includes('read'))
    || (permissions && permissions.accounts && permissions.accounts.includes('updateAccount'));
};

export const canEditCampaigns = (permissions) => {
  return permissions && permissions.campaigns
    && (permissions.campaigns.includes('update') || permissions.campaigns.includes('updateAccount'));
};

export const canAccessCustomerAdmin = (isAuthed, accountSubdomain, permissions) => {
  const matches = aiAppURL.match(/\/\/(.+?)\./);
  const urlSubdomain = matches && matches[1];

  return isAuthed && accountSubdomain && accountSubdomain === urlSubdomain && canViewManageMenu(permissions);
};

export const canAccessContentConfiguration = (permissions) => {
  return permissions && permissions.contentConfiguration && permissions.contentConfiguration.includes('read');
};

export const canAccessSiteConfiguration = (permissions) => {
  return permissions && permissions.siteConfiguration &&
  (permissions.siteConfiguration.includes('read') || permissions.siteConfiguration.includes('readAccount'));
};

export const canAccessOwnReports = (permissions) => {
  return permissions && permissions.reports && permissions.reports.includes('readAccount');
};

export const canAccessCatalogItems = (permissions) => {
  return permissions && permissions.catalogItems && permissions.catalogItems.includes('read');
};

export const canAccessAssessmentItems = (permissions) => {
  return permissions && permissions.assessmentItems && permissions.assessmentItems.includes('read');
};

export const canAccessQuizCard = (permissions) => {
  return permissions && permissions.lessonCards && permissions.lessonCards.includes('read');
};

export const canAccessAnalytics = (permissions) => {
  return permissions && permissions.analytics && permissions.analytics.includes('readAccount');
};

export const canAccessResources = (permissions) => {
  return permissions && permissions.resourceAssets && permissions.resourceAssets.includes('read');
};
