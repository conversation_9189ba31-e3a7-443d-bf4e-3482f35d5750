import { get } from 'lodash';
import { axiosClient } from '../../config/axios';

/*
* returns the current account information or accountNotFoundError
*/
export const getCurrentAccount = async () => {
  const result = await axiosClient.get('/accounts/current');
  if (result?.data?.notFoundError) {
    return { accountNotFoundError: true };
  }
  const name = get(result, 'data.name');
  const customLogoSrc = get(result, 'data.customLogoSrc');
  const customDarkLogoSrc = get(result, 'data.customDarkLogoSrc');
  const authField = get(result, 'data.authField');
  const loginInstructions = get(result, 'data.loginInstructions');
  const selfSignup = get(result, 'data.selfSignup');
  const selfSignupEntry = get(result, 'data.selfSignupEntry');
  const useCode = get(result, 'data.useCode');
  const selectedLanguages = get(result, 'data.selectedLanguages');
  const isJettClient = get(result, 'data.isJettClient');
  const googleClientId = get(result, 'data.googleClientId');
  const linkedinClientId = get(result, 'data.linkedinClientId');
  const microsoftClientId = get(result, 'data.microsoftClientId');
  const microsoftTenantId = get(result, 'data.microsoftTenantId');
  const sso = get(result, 'data.sso');
  const ssoEntryPoint = get(result, 'data.ssoEntryPoint');
  const ssoLogoutUrl = get(result, 'data.ssoLogoutUrl');
  const gdprPolicyUrl = get(result, 'data.gdprPolicyUrl');
  const minPasswordLength = get(result, 'data.minPasswordLength');
  const maxPasswordLength = get(result, 'data.maxPasswordLength');
  const upperCaseRequiredInPassword = get(result, 'data.upperCaseRequiredInPassword');
  const numberRequiredInPassword = get(result, 'data.numberRequiredInPassword');
  const specialCharRequiredInPassword = get(result, 'data.specialCharRequiredInPassword');
  const accountSignupFields = get(result, 'data.signinAccountFields');
  const launchDarklyKey = get(result, 'data.launchDarklyKey');
  const analyticsIdHash = get(result, 'data.analyticsIdHash');
  const welcomeMessageEnabled = get(result, 'data.welcomeMessageEnabled');
  return {
    name,
    customLogoSrc,
    customDarkLogoSrc,
    authField,
    loginInstructions,
    selfSignup,
    selfSignupEntry,
    useCode,
    isJettClient,
    selectedLanguages,
    googleClientId,
    linkedinClientId,
    microsoftClientId,
    microsoftTenantId,
    sso,
    ssoEntryPoint,
    ssoLogoutUrl,
    minPasswordLength,
    maxPasswordLength,
    upperCaseRequiredInPassword,
    numberRequiredInPassword,
    specialCharRequiredInPassword,
    gdprPolicyUrl,
    accountSignupFields,
    launchDarklyKey,
    analyticsIdHash,
    welcomeMessageEnabled,
  };
};
