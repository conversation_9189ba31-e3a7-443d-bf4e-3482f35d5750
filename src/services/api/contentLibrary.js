import { get } from 'lodash';
import { axiosClient } from '../../config/axios';
import { getAuthenticationHeader } from './authentication';

export const trainingTiles = async (params) => {
  const {
    limit = 15,
    skip = 0,
    pillarId,
    contentId,
    title,
    sortBy,
    trainingType,
    location,
    audience,
    concepts,
    indicators,
    isCustomized,
  } = params;

  const data = {
    $sort: sortBy,
    $limit: limit,
    $skip: skip,
    ...(title && { title }),
    ...(contentId && { contentId }),
    ...(pillarId && { pillar: pillarId }),
    ...(trainingType?.length > 0 && { trainingType }),
    ...(location?.length > 0 && { location }),
    ...(audience?.length > 0 && { audience }),
    ...(concepts?.length > 0 && { concepts }),
    ...(indicators?.length > 0 && { indicators }),
    ...(isCustomized && { isCustomized }),
  };

  const result = await axiosClient.post('/content-library', data, getAuthenticationHeader());
  return result.data;
};

export const getSocialIndicators = async () => {
  try {
    const result = await axiosClient.get('/content-library/filter/list', getAuthenticationHeader());
    const socialIndicators = result?.data?.data.reduce((acc, item) => {
      if (!acc[item.competency]) {
        acc[item.competency] = [];
      }
      acc[item.competency].push({ key: item.id, value: item.name });
      return acc;
    }, {});
    return socialIndicators;
  } catch (e) {
    console.error(e);
  }
};

export const getConcepts = async () => {
  try {
    const result = await axiosClient.get('/content-library/filter/list?type=concept', getAuthenticationHeader());
    const concepts = result?.data?.data.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push({ key: item.id, value: item.concept });
      return acc;
    }, {});
    return concepts;
  } catch (e) {
    console.error(e);
  }
};

export const getContentDetails = async (mclType, mclID) => {
  try {
    const result = await axiosClient.get(`/content-library/details/${mclType}/${mclID}`, getAuthenticationHeader());
    return result.data;
  } catch (e) {
    console.error(e);
  }
};

export const patchContentConfiguration = async (contentId, contentType, data) => {
  try {
    // eslint-disable-next-line max-len
    const result = await axiosClient.post(`content-library/${contentId}/${contentType}/manageContentConfiguration`, data, getAuthenticationHeader());
    return result.data;
  } catch (e) {
    console.error(e);
  }
};

export const getContentPolicies = async (contentId, contentType) => {
  try {
    // eslint-disable-next-line max-len
    const result = await axiosClient.get(`content-library/${contentId}/${contentType}/contentConfiguration`, getAuthenticationHeader());
    return result.data;
  } catch (e) {
    console.error(e);
  }
};

export const handleScormPIFDownload = async (path, accountId, file) => {
  const result = await axiosClient.get('/authentication/downloadToken', getAuthenticationHeader());
  const token = get(result, 'data.token');
  const data = { token, accountId };
  try {
    const response = await axiosClient.post(`${path}`, data, { responseType: 'blob', ...getAuthenticationHeader() });
    if (response) {
      const contentDisposition = response.headers['content-disposition'];
      const match = contentDisposition.match(/filename="(.*)"/) || contentDisposition.match(/filename=(.*)/);
      const filename = match && match[1] ? match[1] : file;
      const url = window.URL.createObjectURL(response.data);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}`;
      link.click();

      window.URL.revokeObjectURL(url);
    }
  // eslint-disable-next-line no-empty
  } catch {}
};
