import { axiosClient } from '../../config/axios';
import { getAuthenticationHeader, getAuthenticationMultipartHeader } from './authentication';

export const createZendeskTicket = async ({ name, email, subject, message, files }) => {
  const data = new FormData();
  data.append('name', name);
  data.append('email', email);
  data.append('subject', subject);
  data.append('message', message);
  data.append('url', window.location.href);
  if (files) {
    files.map((nextFile) => {
      data.append('files', nextFile, nextFile.name);
      return { name: nextFile.name, size: nextFile.size };
    });
  }

  if (files) {
    const result = await axiosClient.post('/zendesk/createTicket', data, getAuthenticationMultipartHeader());
    return result.data;
  }

  const result = await axiosClient.post('/zendesk/createTicket', data, getAuthenticationHeader());
  return result.data;
};
