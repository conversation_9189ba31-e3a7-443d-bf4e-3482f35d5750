import { jwPlayerSupportedLanguages } from '../translations';

const JW_PLAYER_QUALITY_LABEL_NAME = 'jwplayer.qualityLabel';
const JW_PLAYER_QUALITY_LABEL_VALUE = '576p';
const JW_PLAYER_BANDWIDTH_ESTIMATE_NAME = 'jwplayer.defaultBandwidthEstimate';
const JW_PLAYER_CAPTION_LABEL = 'jwplayer.captionLabel';
export const JW_HIGH_BANDWIDTH_ESTIMATE = '1000000000';

// This function forces the settings for JW Player to assume high bandwidth on startup.
// see also: Video.js customProps
export const setJWPlayerBandwidth = () => {
  localStorage.removeItem('jwplayerLocalId');
  localStorage.removeItem('jwplayer.bitrateSelection');
  localStorage.setItem(JW_PLAYER_BANDWIDTH_ESTIMATE_NAME, JW_HIGH_BANDWIDTH_ESTIMATE);
  localStorage.setItem(JW_PLAYER_QUALITY_LABEL_NAME, JW_PLAYER_QUALITY_LABEL_VALUE);
};

export const setJWPlayerLanguage = (language, videoClosedCaption) => {
  const currentSetting = localStorage.getItem(JW_PLAYER_CAPTION_LABEL);
  // if the user explicity turned off captions, don't see them on again.
  if (currentSetting === 'Off') {
    return;
  }
  // if the account has captions turned off, and the user didn't have a setting and it's an english language user
  if (!videoClosedCaption && !currentSetting && (language === 'en' || language.startsWith('en-'))) {
    return;
  }
  const closedCaptionLanguage =
      Object.prototype.hasOwnProperty.call(jwPlayerSupportedLanguages, language) ?
        jwPlayerSupportedLanguages[language] : 'English';
  if (closedCaptionLanguage) {
    localStorage.setItem(JW_PLAYER_CAPTION_LABEL, closedCaptionLanguage);
  }
};
