import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import resources from './translations';

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    ns: ['translations'],
    lowerCaseLng: true,
    defaultNS: 'translations',
    keySeparator: '.',
    interpolation: { escapeValue: false },
  });
export const setLanguage = (lang) => {
  i18n.changeLanguage(lang.toLowerCase());
};

export default i18n;
