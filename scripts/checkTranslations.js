/** NOTE - this script is not up to date and is not very useful */

// const colors = require('colors');
const _ = require('lodash');
const fs = require('fs');
const path = require('path');
const glob = require('glob');

/* colors.setTheme({
  key: 'cyan',
  text: 'grey',
  info: 'green',
  warn: 'yellow',
  error: 'red',
}); */
const filePattern = path.join(__dirname, '../src/translations/*.json');
const allFiles = glob.sync(filePattern);

const error = (msg) => {
  process.stderr.write(`${msg}\n`);
};

const output = (msg) => {
  process.stdout.write(`${msg}\n`);
};

const getBaseFilename = (fullPath) => {
  return fullPath.replace(/^.*[\\\/]/, ''); // eslint-disable-line no-useless-escape
};

/**
 * Creates an array of flattened object key paths.
 *
 * Example:
 * ```
 * var object = { a: { a2: 'test' }, b: 'test' };
 * objectPaths(object); // Returns [ 'a.a2', 'b' ]
 * ```
 */
function objectPaths(object) {
  const result = [];
  _.forOwn(object, (value, key) => {
    if (_.isPlainObject(value)) {
      // Recursive step
      const keys = objectPaths(value);
      keys.forEach((subKey) => {
        result.push(`${key}.${subKey}`);
      });
    } else {
      result.push(key);
    }
  });
  return result;
}

/**
 * Returns a collection of arrays where each array contains all elements missing
 * from the corresponding object.
 */
const compareObjects = (objects, files) => {
  const paths = objects.map(objectPaths);

  // Use English as a baseline
  const idx = files.findIndex((file) => getBaseFilename(file) === 'en.app.json');
  const allPaths = _.uniq(paths[idx]);

  // Combine object, path and file arrays into a single array with objects
  return _.zipWith(objects, paths, files, (object, aPath, file) => {
    return {
      object,
      missingPaths: _.difference(allPaths, aPath),
      file,
    };
  });
};

/**
 * Compares a collection of json files with one-another and returns
 */
const compareFiles = (files, done) => {
  if (files.length === 0) {
    return done(new Error('Require at least one file'));
  }
  if (files.length === 1) {
    return done(null, [{ file: files[0] }]); // Quick exit
  }

  // Load json files and parse them as regular objects
  const objects = files.map((filepath) => {
    try {
      return JSON.parse(fs.readFileSync(filepath, { encoding: 'utf-8' }));
    } catch (e) {
      return done(new Error(`Error occurred while parsing file '${filepath}'`));
    }
  });

  // Compare all objects
  const diff = compareObjects(objects, files);

  return done(null, diff);
};

const generateOutput = (err, results) => {
  if (err) {
    throw err;
  }

  const outputFolder = path.join(__dirname, '../src/ToTranslate/');

  const englishFile = allFiles.find((file) => getBaseFilename(file) === 'en.app.json');
  const englishObject = results.find((result) => result.file === englishFile).object;

  results.forEach((result) => {
    const outFile = path.join(outputFolder, getBaseFilename(result.file));
    if (result.missingPaths && result.missingPaths.length > 0) {
      error(`${result.file} is missing the following keys:`);
      result.missingPaths.forEach((missing) => {
        const objectPath = _.toPath(missing);
        if (!result.object[objectPath[0]]) {
          result.object[objectPath[0]] = {}; // eslint-disable-line no-param-reassign
        }
        const mainSection = result.object[objectPath[0]];
        const engText = englishObject[objectPath[0]][objectPath[1]];
        // error(`Not translated: ${missing} - ${engText}`);
        // error(colors.key(`"${colors.key(missing)}": "${engText}"`));
        mainSection[objectPath[1]] = `TRANSLATE ${engText}`;
      });
      // Write the new file.
      if (!fs.existsSync(outputFolder)) {
        fs.mkdirSync(outputFolder);
      }
      fs.writeFileSync(outFile, JSON.stringify(result.object, null, 2));
      error('');
    } else if (getBaseFilename(outFile) !== 'en.app.json') {
      if (fs.existsSync(outFile)) {
        fs.unlinkSync(outFile);
      }
      output(`${result.file} is complete\n`);
    }
  });
};

compareFiles(allFiles, generateOutput);
