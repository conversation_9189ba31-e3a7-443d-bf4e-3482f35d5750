const _ = require('lodash');
const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { exec } = require('child_process');

/**
 * Translates ui language files in app/translations
 *
 * The keys that will be translated
 *   all keys in the en translation file which have not been translated
 *   all keys listed in changedTextFilter (retranslate)
 *
 * The translation languages
 *   see translationLanguages below
 */

// changedTextFilter
const changedTextFilter = [];
// example:
// const changedTextFilter = ['translations.privacy.privacy_note_1', 'translations.privacy.privacy_note_3'];

// see src/translations/index
const translationLanguages = [
  // 'en', // english
  'en-ca', // english canadian
  'en-gb', // british english
  'es', // spanish
  'es-mx', // spanish mexico
  'fr', // french
  'fr-ca', // french canadian
  'pt-br', // portuguese brazil
  'ar', // arabic
  'bg', // bulgarian
  'cs', // czech
  'da', // danish
  'zh-cn', // chinese simplified
  'zh-tw', // chinese traditional
  'de', // german
  'he', // hebrew
  'hi', // hindi
  'hu', // hungarian
  'it', // italian
  'ja', // japanese
  'ko', // korean
  'ms', // malay
  'ru', // russian
  'sk', // slovak
  'sv', // swedish
  'sq', // albanian
  'nl', // dutch
  'id', // indonesian
  'pl', // polish
  'th', // thai
  'tr', // turkish
  'vi', // vietnamese
  'bs', // bosnian
  'so', // somali
  'sw', // swahili
  'tl', // tagalog
  'mr', // Marathi
  'ht', // Haitian Creole
  'am', // Amharic
  'pa', // punjabi
  'bn', // bengali
  'el', // greek
  'ur', // urdu
];

// not supported by aws - en text will be subsituted
const nonAWSEnglishVariants = ['en-gb', 'en-ca'];

const filesFromLangs = translationLanguages.map((abbr) => {
  return `${abbr}.app.json`;
});
const filePath = path.join(__dirname, '../src/translations/');

filesFromLangs.forEach((fileName) => {
  const fullPath = `${filePath}${fileName}`;
  // add new file (with empty obj) if it's not there yet
  if (!fs.existsSync(fullPath)) {
    fs.writeFileSync(fullPath, JSON.stringify({}, null, 2));
    filesFromLangs.push(fullPath);
  }
});

const filePattern = path.join(__dirname, '../src/translations/*.json');
const allFiles = glob.sync(filePattern);

const error = (msg) => {
  process.stderr.write(`${msg}\n`);
};

const output = (msg) => {
  process.stdout.write(`${msg}\n`);
};

const getBaseFilename = (fullPath) => {
  return fullPath.replace(/^.*[\\\/]/, ''); // eslint-disable-line no-useless-escape
};

const getAWScode = (abbr) => {
  const map = { // aws code != abbr
    'en-gb': 'en-gb',
    'en-ca': 'en-ca',
    'pt-br': 'pt',
    'zh-cn': 'zh',
  };
  return map[abbr] || abbr;
};

/**
 * Executes a shell command and returns it as a Promise.
 * @param cmd {string}
 * @return {Promise<string>}
 */
const execShellCommand = async (cmd) => {
  return new Promise((resolve, reject) => { // eslint-disable-line
    exec(cmd, (e, stdout, stderr) => {
      if (e) {
        error(`1 ERROR: execShellCommand Error: ${e}`);
      }
      resolve(stdout || stderr);
    });
  });
};

const awsTranslate = async (langCode, engText, key) => {
  const command = `aws translate translate-text --source-language-code "en" --target-language-code "${langCode}" --text "${engText}"`; // eslint-disable-line
  try {
    const translatedText = await execShellCommand(command);
    return JSON.parse(translatedText);
  } catch (e) {
    error(`2 ERROR: Unable to translate '${key}': '${engText}' in ${langCode}. Error: ${e}`);
  }
  return null;
};

/**
 * Creates an array of flattened object key paths.
 *
 * Example:
 * ```
 * var object = { a: { a2: 'test' }, b: 'test' };
 * objectPaths(object); // Returns [ 'a.a2', 'b' ]
 * ```
 */
function objectPaths(object) {
  const result = [];
  _.forOwn(object, (value, key) => {
    if (_.isPlainObject(value)) {
      // Recursive step
      const keys = objectPaths(value);
      keys.forEach((subKey) => {
        result.push(`${key}.${subKey}`);
      });
    } else {
      result.push(key);
    }
  });
  return result;
}

/**
 * Returns a collection of arrays where each array contains all elements missing
 * from the corresponding object.
 */
const compareObjects = (objects, files) => {
  const paths = objects.map(objectPaths);

  // Use English as a baseline
  const idx = files.findIndex((file) => getBaseFilename(file) === 'en.app.json');
  const allPaths = _.uniq(paths[idx]);

  // Combine object, path and file arrays into a single array with objects
  const data = _.zipWith(objects, paths, files, (object, aPath, file) => {
    return {
      object,
      missingPaths: _.difference(allPaths, aPath),
      file,
    };
  });
  // add keys where the text has changed for retranslation
  if (changedTextFilter && changedTextFilter.length > 0) {
    data.forEach((datum) => {
      changedTextFilter.forEach((key) => {
        if (!datum.missingPaths.includes(key)) {
          datum.missingPaths.push(key);
        }
      });
    });
  }
  return data;
};

/**
 * Compares a collection of json files with one-another and returns
 */
const compareFiles = (files, done) => {
  if (files.length === 0) {
    return done(new Error('Require at least one file'));
  }
  if (files.length === 1) {
    return done(null, [{ file: files[0] }]); // Quick exit
  }

  // Load json files and parse them as regular objects
  const objects = files.map((filepath) => {
    try {
      const data = fs.readFileSync(filepath, { encoding: 'utf-8' });
      return data ? JSON.parse(data) : {};
    } catch (e) {
      return done(new Error(`Error occurred while parsing file '${filepath}'`));
    }
  });

  // Compare all objects
  const diff = compareObjects(objects, files);

  return done(null, diff);
};

const generateOutput = async (err, results) => {
  if (err) {
    throw err;
  }

  const outputFolder = path.join(__dirname, '../src/translations/');

  const englishFile = allFiles.find((file) => getBaseFilename(file) === 'en.app.json');
  const englishObject = results.find((result) => result.file === englishFile).object;

  results.forEach(async (result) => {
    const outFile = path.join(outputFolder, getBaseFilename(result.file));
    const fileName = getBaseFilename(result.file);
    const abbr = fileName.split('.')[0];
    const awsAbbr = abbr && getAWScode(abbr);

    if (!abbr) {
      error(`3 ERROR: Missing abbr for fileName ${fileName})`);
      return;
    }
    if (abbr === 'en') {
      return;
    }
    if (!translationLanguages.includes(abbr)) {
      return;
    }
    if (result.missingPaths && result.missingPaths.length > 0) {
      for (let index = 0; index < result.missingPaths.length; index += 1) {
        const missing = result.missingPaths[index];
        const { object } = result;
        let engText = _.get(englishObject, missing);
        engText = engText ? engText.trim() : '';
        try {
          // translate or pass engText through for en variants which are not supported by aws
          let newText = '';
          if (!engText) {
            newText = '';
          } else if (nonAWSEnglishVariants.includes(awsAbbr)) {
            newText = engText;
          } else {
            const regExpSpan = new RegExp('{{', 'g');
            let modText = engText.replace(regExpSpan, '<span translate=no>{{');

            const regExpCloseSpan = new RegExp('}}', 'g');
            modText = modText.replace(regExpCloseSpan, '}}</span>');

            // ***** Translate the item ***** //
            const translated = await awsTranslate(awsAbbr, modText); // eslint-disable-line no-await-in-loop

            // eslint-disable-next-line no-console
            console.log(`${awsAbbr} - ${missing} "${modText}": ${translated.TranslatedText}`);

            newText = translated.TranslatedText;

            const regExpSpanReverse = new RegExp('<span translate=no>{{', 'g');
            newText = newText.replace(regExpSpanReverse, ' {{');

            const regExpCloseSpanReverse = new RegExp('}}</span>', 'g');
            newText = newText.replace(regExpCloseSpanReverse, '}}');
          }

          // ***** Assign translation to object ***** //
          _.set(object, missing, newText);
        } catch (e) {
          error(`5 ERROR: translateMissingStrings Error: ${e}`);
        }
      }

      // ***** Create new file if it does not exist ***** //
      if (!fs.existsSync(outputFolder)) {
        fs.mkdirSync(outputFolder);
      }
      // ***** Write the file ***** //
      fs.writeFileSync(outFile, JSON.stringify(result.object, null, 2));
      error('');
    } else if (getBaseFilename(outFile) !== 'en.app.json') {
      // output(colors.info(`${colors.bold(result.file)} is complete\n`));
      output(`${result.file} is complete\n`);
    }
  });
};

compareFiles(allFiles, generateOutput);
