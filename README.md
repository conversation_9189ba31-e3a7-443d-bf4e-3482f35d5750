# Jett project

This project is the new learner client for the Emtrain Ai platform.

## Coding guidelines

The following guidelines apply to both the new client and any server changes that need to be made.  
If a guideline is specific to client or server, it will include a designation (CLIENT ONLY) or
(SERVER ONLY).

### index.js files (CLIENT ONLY)

Index.js files should only be used to aggregate information in a folder (i.e. getTheme).  Even if 
there is a clear default export in the folder, the file should be named based on the function or
area of the folder and not named index.js.

### Data access layer (SERVER ONLY)

Files in the *data-access* folder should be named according to the area or function and should have 
the word *data* at the end.  For example:

> data-access
>
>> AskExpertData

Functions in the data access folder should start with *save* for storing data and *fetch* for
retrieving data.

### Context/req paramaters

The context should only be passed to data access or util functions if those functions rely on a supporting
legacy functions that already require a *req* object.  Req and context are the same object.  You should 
also use the lowestest required level in the JSON hierarchy where possible (i.e. don't pass a user object 
as a paramater if you just need the user.id).  The developer can consider re-writing the legacy function 
for efficiency or performance reasons, in which case the new function should not require the context or 
req object.  

## Translations 
1. To re-generate language files in `src/translations`, add or update the text in the english version and then run  `node scripts/translateUi.js`

### Optional (suggested):
1. Chrome extension for Apollo devtools
https://chromewebstore.google.com/detail/apollo-client-devtools/jdkknkkbebbapilgoeccciglkfbmbnfm?hl=en-US