const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

// npm run-script production
const isProduction = process.env.npm_lifecycle_event === 'production';
const CopyPlugin = require('copy-webpack-plugin');

const CSSModuleLoader = {
  loader: 'css-loader',
  options: {
    modules: {
      localIdentName: '[name]__[local]__[hash:base64:5]',
    },
  },
};

const baseConfig = {
  entry: {
    index: './src/index.js',
  },
  output: {
    publicPath: '/',
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        loader: 'babel-loader',
        options: {
          presets: [
            '@babel/preset-env',
            '@babel/preset-react',
          ],
          plugins: [
            '@babel/transform-runtime',
          ],
        },
      },
      {
        test: /\.css$/i,
        exclude: [/node_modules/, /\.module\.css$/i],
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.module\.css$/i,
        exclude: /node_modules/,
        use: ['style-loader', CSSModuleLoader],
      },
      {
        test: /\.scss$/i,
        exclude: [/node_modules/, /\.module\.scss$/],
        use: ['style-loader', 'css-loader', 'sass-loader'], // translates CSS into CommonJS
      },
      {
        test: /\.module\.scss$/,
        exclude: /node_modules/,
        use: ['style-loader', CSSModuleLoader, 'sass-loader'],
      },
      {
        test: /\.html$/,
        use: {
          loader: 'html-loader',
        },
      },
      {
        test: /\.(jpg|png|gif)$/,
        exclude: [/\.svg$/],
        use: {
          loader: 'url-loader',
        },
      },
      {
        test: /\.svg$/,
        use: ['@svgr/webpack'],
      },
      {
        test: /\.(graphql|gql)$/,
        exclude: /node_modules/,
        loader: 'graphql-tag/loader',
      },
      {
        test: /\.css$/i,
        include: /node_modules/,
        use: ['style-loader', 'css-loader'],
      },
    ],
  },
};

const HTMLWebpackPluginConfig = new HtmlWebpackPlugin({
  template: './src/index.html',
});

const MiniCssExtractPluginConfig = new MiniCssExtractPlugin({
  filename: '[name].[hash].css',
  chunkFilename: '[id].[hash].css',
});

const developmentConfig = {
  mode: 'development',
  devtool: 'inline-source-map',
  devServer: {
    historyApiFallback: true,
    port: 3000,
    disableHostCheck: true,
  },
  plugins: [
    HTMLWebpackPluginConfig,
    new CopyPlugin({
      patterns: [
        { from: 'scorm-rustici', to: 'scorm-rustici' },
        { from: 'jwplayer_8_27', to: 'jwplayer_8_27' },
      ],
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
    },
  },
};

const productionConfig = {
  mode: 'production',
  output: {
    path: path.join(__dirname, 'dist'),
    filename: './js/[name].[hash].bundle.js',
    publicPath: '/',
  },
  plugins: [
    HTMLWebpackPluginConfig,
    MiniCssExtractPluginConfig,
    new CopyPlugin({
      patterns: [
        { from: 'scorm-rustici', to: 'scorm-rustici' },
        { from: 'jwplayer_8_27', to: 'jwplayer_8_27' },
      ],
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
    },
    minimizer: [
      new CssMinimizerPlugin(),
    ],
  },
};

module.exports = Object.assign({}, baseConfig, isProduction ? productionConfig : developmentConfig);
