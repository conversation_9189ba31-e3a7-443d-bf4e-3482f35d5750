!function(){"use strict";var u="0.0.0",e=u;const t={trigger(u,...e){if(this._listeners[u]){const t=this._listeners[u];for(let u=0;u<t.length;u++)t[u].call(this,...e)}if(this._onceListeners[u]){const t=this._onceListeners[u];for(let u=0;u<t.length;u++)t[u].call(this,...e);this._onceListeners[u]=[]}},on(u,e){this._listeners[u]=this._listeners[u]||[],this._listeners[u].push(e)},once(u,e){this._onceListeners[u]=this._onceListeners[u]||[],this._onceListeners[u].push(e)},off(u,e){if(this._listeners[u]){const t=this._listeners[u].indexOf(e);-1!==t&&(this._listeners[u]=this._listeners[u].filter(((u,e)=>e!==t)))}if(this._onceListeners[u]){const t=this._onceListeners[u].indexOf(e);-1!==t&&(this._onceListeners[u]=this._onceListeners[u].filter(((u,e)=>e!==t)))}},destroy(){this._listeners={}}},i=(u=>{const e=function(...e){return u&&u.call(this,...e),this._listeners={},this._onceListeners={},this.addEventListener=this.on,this.removeEventListener=this.off,this};return e.prototype=u?Object.create(u.prototype):Object.create(Object.prototype),Object.assign(e.prototype,t),e})();class n{constructor(u){this._ref=u}deref(){return this._ref}}class s{constructor(){this._weakContructor=window.WeakRef||n,this._listeners={},this.addEventListener=this.on,this.removeEventListener=this.off}trigger(u,...e){if(!function(u,e){if(null==u)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(u),e)}(this._listeners,u))return;const t=this._listeners[u];let i=0;t.forEach((u=>{const t=u.deref();"function"==typeof t?t(...e):i++})),(i>20||i>=t.length/2)&&(this._listeners[u]=t.filter((u=>u.deref())))}on(u,e){this._listeners[u]=this._listeners[u]||[],this._listeners[u].push(new this._weakContructor(e))}off(u,e){if(!this._listeners[u])return;const t=this._listeners[u];this._listeners[u]=t.filter((u=>{const t=u.deref();return!(!t||t===e)}))}destroy(){this._listeners={}}}const r=["id","uniqueId","plugins","_qoe","version","Events","utils","_"],D=[{name:"setup",chains:!1},{name:"remove",chains:!0},{name:"qoe",chains:!1},{name:"addCues",chains:!0},{name:"getAudioTracks",chains:!1},{name:"getBuffer",chains:!1},{name:"getCaptions",chains:!1},{name:"getCaptionsList",chains:!1},{name:"getConfig",chains:!1},{name:"getContainer",chains:!1},{name:"getControls",chains:!1},{name:"getCues",chains:!1},{name:"getCurrentAudioTrack",chains:!1},{name:"getCurrentCaptions",chains:!1},{name:"getCurrentQuality",chains:!1},{name:"getCurrentTime",chains:!1},{name:"getDuration",chains:!1},{name:"getEnvironment",chains:!1},{name:"getFullscreen",chains:!1},{name:"getHeight",chains:!1},{name:"getItemMeta",chains:!1},{name:"getMute",chains:!1},{name:"getPercentViewable",chains:!1},{name:"getPip",chains:!1},{name:"getPlaybackRate",chains:!1},{name:"getPlaylist",chains:!1},{name:"getPlaylistIndex",chains:!1},{name:"getPlaylistItem",chains:!1},{name:"getPosition",chains:!1},{name:"getProvider",chains:!1},{name:"getQualityLevels",chains:!1},{name:"getSafeRegion",chains:!1},{name:"getState",chains:!1},{name:"getStretching",chains:!1},{name:"getViewable",chains:!1},{name:"getVisualQuality",chains:!1},{name:"getVolume",chains:!1},{name:"getWidth",chains:!1},{name:"setCaptions",chains:!0},{name:"setConfig",chains:!0},{name:"setControls",chains:!0},{name:"setCurrentAudioTrack",chains:!0},{name:"setCurrentCaptions",chains:!0},{name:"setCurrentQuality",chains:!0},{name:"setFullscreen",chains:!0},{name:"setAllowFullscreen",chains:!0},{name:"setMute",chains:!0},{name:"setPip",chains:!0},{name:"setPlaybackRate",chains:!0},{name:"setPlaylistItem",chains:!0},{name:"setCues",chains:!0},{name:"setVolume",chains:!0},{name:"load",chains:!0},{name:"play",chains:!0},{name:"pause",chains:!0},{name:"playToggle",chains:!1},{name:"seek",chains:!0},{name:"playlistItem",chains:!0},{name:"playlistNext",chains:!0},{name:"playlistPrev",chains:!0},{name:"next",chains:!0},{name:"requestPip",chains:!0},{name:"requestCast",chains:!0},{name:"castToggle",chains:!0},{name:"stopCasting",chains:!0},{name:"createInstream",chains:!1},{name:"stop",chains:!0},{name:"resize",chains:!0},{name:"addButton",chains:!0},{name:"addTextTrack",chains:!0},{name:"getTextTrackList",chains:!1},{name:"removeButton",chains:!0},{name:"attachMedia",chains:!0},{name:"detachMedia",chains:!0},{name:"isBeforeComplete",chains:!1},{name:"isBeforePlay",chains:!1},{name:"setPlaylistItemCallback",chains:!0},{name:"removePlaylistItemCallback",chains:!0},{name:"getPlaylistItemPromise",chains:!1},{name:"getFloating",chains:!1},{name:"setFloating",chains:!0},{name:"getChapters",chains:!1},{name:"getCurrentChapter",chains:!1},{name:"setChapter",chains:!1},{name:"getPlugin",chains:!1},{name:"addPlugin",chains:!0},{name:"registerPlugin",chains:!0},{name:"getAdBlock",chains:!1},{name:"playAd",chains:!0},{name:"pauseAd",chains:!0},{name:"skipAd",chains:!0}];class a extends s{constructor(u){super(),this.player=u,u.on("all",((...u)=>this._all(...u))),D.forEach((({name:u,chains:e})=>{this[u]=e?(...e)=>{this.player[u](...e)}:(...e)=>this.player[u](...e)})),r.forEach((e=>{Object.defineProperty(this,e,{enumerable:!0,get:()=>u[e]})}))}_all(u,e){this.trigger(u,e)}}const o=/^\s*@/;class c{static generateId(u="unknown"){return`jw-i9e-${u}-${Math.floor(Math.random()*2**52).toString(36)}`}static makeCssSelectorSet(u,e){const t=Object.keys(e),i={};return t.forEach((t=>{t[0];const n=e[t];if(o.test(t))return void(i[t]=e[t]);if("string"==typeof n)return i[u]=i[u]||{},void(i[u][t]=e[t]);const s=t.replace(/&/g,u);i[s]=e[t]})),i}}class h{static getTargetAndEvent(u){let{element:e,eventNameSelector:t}=u;if(t=t.trim(),-1===t.indexOf("{")){if(-1===t.indexOf("."))return{target:e,eventName:t};const[i,n]=t.split(".");return function(u,e){if(null==u)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(u),e)}(u,i)?{target:u[i],eventName:n}:{target:null,eventName:n}}const i=t.split("}."),n=i[0].replace("{",""),s=i[1];return{target:e.querySelector(n)||null,eventName:s}}static addEventHandler(u){const{eventNameSelector:e,handlerFn:t}=u,{target:i,eventName:n}=h.getTargetAndEvent(u);return i&&n?(i.addEventListener(n,t),[i,n]):[null,null]}}const l=(u,e)=>(Array.isArray(e)?e:e.split(".")).reduce(((u,e)=>null!=u&&u[e]?"function"==typeof u[e]?u[e].bind(u):u[e]:null),u),C=/^#argv\[(\d+)\]/,F=(u,e,t,i)=>(...e)=>{i.forEach((i=>{try{const r=i.namespace;if(u[r]){const D=l(u[r],i.name);"function"==typeof D?(i.args=i.args||[],D(...(n=i.args,s=e,n.map((u=>{if("string"==typeof u){const[e,...t]=u.split("."),i=e.match(C);if(i&&2===i.length&&!isNaN(i[1])){const u=s[Number(i[1])];return"object"==typeof u&&t.length?l(u,t):u}}return u}))))):t.logger.warn(`Unknown '${r}' action '${i.name}':`,i)}else t.logger.warn(`Unknown namespace '${r}' found:`,i)}catch(u){t.logger.error(u),t.logger.error("Exception occurred while processing handler:",i)}var n,s}))};class E{constructor(u,e,t,i){this.player=u,this.interactive=e,this.context=t,this.options=i,this.name=i.name,this.type=i.type,this.logger=t.logger.child(`card(${this.name})`),this._handlerFunctions=[],this._cssSelectors=[],this._boundKeyDownHandler=(...u)=>this._keyDownHandler(...u),this._setup()}_setup(){if(this.logger.debug("Creating card type:",this.options.type),this.options.css.pointerEvents="auto",this.element=this._createElement(),this._cssSelectors=this._createElementStyles(),this.options.handlers){const u=this.options.handlers;Object.keys(u).forEach((e=>{const t=u[e],i=F({player:this.player,interactive:this.interactive,card:this,element:this.element,context:this.context},this.context,this,t),[n,s]=h.addEventHandler({player:this.player,interactive:this.interactive,context:this.context,element:this.element,eventNameSelector:e,handlerFn:i});n&&s?this._handlerFunctions.push({target:n,eventName:s,handler:i}):this.logger.warn(`Did not find an element with ${e}`)}))}}remove(u){this.context.removeCard(this,u)}_createElement(){const u=document.createElement(this.type);return u.name=this.options.name,this.id=c.generateId(this.type),u.id=this.id,this.options.attributes&&"object"==typeof this.options.attributes&&Object.keys(this.options.attributes).forEach((e=>{const t=this.options.attributes[e];u.setAttribute(e,t)})),this.options.properties&&"object"==typeof this.options.properties&&Object.keys(this.options.properties).forEach((e=>{const t=this.options.properties[e];u[e]=t})),u.classList.add("jw-reset"),u.addEventListener("keydown",this._boundKeyDownHandler),u}_keyDownHandler(u){"Enter"!==u.key&&"NumpadEnter"!==u.key&&13!==u.keyCode||(u.stopPropagation(),u.target.dispatchEvent(new MouseEvent("click")))}_createElementStyles(){let u=[];if(this.options.css){const e=c.makeCssSelectorSet(`#${this.id}`,this.options.css);u=Object.keys(e),u.forEach((u=>{this.player.utils.css(u,e[u],this.context.id)}))}return u}_clearSelectors(){this._cssSelectors.forEach((u=>{this.player.utils.clearCss(this.context.id,u)}))}destroy(u){this._clearSelectors(),this._handlerFunctions&&this._handlerFunctions.forEach((({target:u,eventName:e,handler:t})=>{u.removeEventListener(e,t)})),this.element.removeEventListener("keydown",this._boundKeyDownHandler),this.context=null,this.player=null,this.element=null,this._handlerFunctions=null,this._cssSelectors=null}_trySelectorFromElement(u){let e=this.element;return u&&(e=this.element.querySelector(u)),e}setStyle(u,e,t){const i=this._trySelectorFromElement(t);i&&(i.style[u]=e)}addClass(u,e){const t=this._trySelectorFromElement(e);t&&t.classList.add(u)}removeClass(u,e){const t=this._trySelectorFromElement(e);t&&t.classList.remove(u)}toggleClass(u,e){const t=this._trySelectorFromElement(e);t&&t.classList.toggle(u)}}class A extends s{constructor(u){super(),this.plugin=u,this.player=this.plugin.player,this.player.on("time",(u=>this._time(u)))}hideProject(u){const e=this.plugin.getProject(u);e&&e.hide()}showProject(u){const e=this.plugin.getProject(u);e&&e.show()}prevChapter(u="dumb"){const[e,t]=this._getChapterData();if(!e.length||-1===t)return this.player.seek(0);if("smart"===u){const u=this.player.getPosition(),i=e[t];if(u-i.time>4)return this.player.seek(i.time)}const i=e[Math.max(0,t-1)];this.player.seek(i.time)}nextChapter(){const[u,e]=this._getChapterData(),t=u[Math.min(e+1,u.length-1)];if(!u.length||-1===e)return this.player.seek(this.getDuration());this.player.seek(t.time)}_time(u){const{duration:e,position:t}=u,i=t/e*100,[n,s]=this._getChapterData();let r=0,D=e;n.length&&s>-1&&(r=n[s].time,s<n.length-1&&(D=n[s+1].time));const a=(t-r)/(D-r)*100,o={progress:`${i}%`,remaining:100-i+"%",halfProgress:i/2+"%",halfRemaining:(100-i)/2+"%",chapterProgress:`${a}%`,chapterRemaining:100-a+"%",halfChapterProgress:a/2+"%",halfChapterRemaining:(100-a)/2+"%"};this.trigger("timeAsPercent",o)}_getChapterData(){this.player.getPosition();const u=this.player.getChapters(),e=this.player.getCurrentChapter(),t=u.sort(((u,e)=>u.time-e.time)),i=t.findIndex((u=>u===e));return[t,i]}}var d={Space_Separator:/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,ID_Start:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,ID_Continue:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},m={isSpaceSeparator:u=>"string"==typeof u&&d.Space_Separator.test(u),isIdStartChar:u=>"string"==typeof u&&(u>="a"&&u<="z"||u>="A"&&u<="Z"||"$"===u||"_"===u||d.ID_Start.test(u)),isIdContinueChar:u=>"string"==typeof u&&(u>="a"&&u<="z"||u>="A"&&u<="Z"||u>="0"&&u<="9"||"$"===u||"_"===u||"‌"===u||"‍"===u||d.ID_Continue.test(u)),isDigit:u=>"string"==typeof u&&/[0-9]/.test(u),isHexDigit:u=>"string"==typeof u&&/[0-9A-Fa-f]/.test(u)};let p,B,g,f,y,v,w,_,b;function x(u,e,t){const i=u[e];if(null!=i&&"object"==typeof i)if(Array.isArray(i))for(let u=0;u<i.length;u++){const e=String(u),n=x(i,e,t);void 0===n?delete i[e]:Object.defineProperty(i,e,{value:n,writable:!0,enumerable:!0,configurable:!0})}else for(const u in i){const e=x(i,u,t);void 0===e?delete i[u]:Object.defineProperty(i,u,{value:e,writable:!0,enumerable:!0,configurable:!0})}return t.call(u,e,i)}let P,j,S,k,L;function O(){for(P="default",j="",S=!1,k=1;;){L=T();const u=I[P]();if(u)return u}}function T(){if(p[f])return String.fromCodePoint(p.codePointAt(f))}function N(){const u=T();return"\n"===u?(y++,v=0):u?v+=u.length:v++,u&&(f+=u.length),u}const I={default(){switch(L){case"\t":case"\v":case"\f":case" ":case" ":case"\ufeff":case"\n":case"\r":case"\u2028":case"\u2029":return void N();case"/":return N(),void(P="comment");case void 0:return N(),$("eof")}if(!m.isSpaceSeparator(L))return I[B]();N()},comment(){switch(L){case"*":return N(),void(P="multiLineComment");case"/":return N(),void(P="singleLineComment")}throw U(N())},multiLineComment(){switch(L){case"*":return N(),void(P="multiLineCommentAsterisk");case void 0:throw U(N())}N()},multiLineCommentAsterisk(){switch(L){case"*":return void N();case"/":return N(),void(P="default");case void 0:throw U(N())}N(),P="multiLineComment"},singleLineComment(){switch(L){case"\n":case"\r":case"\u2028":case"\u2029":return N(),void(P="default");case void 0:return N(),$("eof")}N()},value(){switch(L){case"{":case"[":return $("punctuator",N());case"n":return N(),M("ull"),$("null",null);case"t":return N(),M("rue"),$("boolean",!0);case"f":return N(),M("alse"),$("boolean",!1);case"-":case"+":return"-"===N()&&(k=-1),void(P="sign");case".":return j=N(),void(P="decimalPointLeading");case"0":return j=N(),void(P="zero");case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return j=N(),void(P="decimalInteger");case"I":return N(),M("nfinity"),$("numeric",1/0);case"N":return N(),M("aN"),$("numeric",NaN);case'"':case"'":return S='"'===N(),j="",void(P="string")}throw U(N())},identifierNameStartEscape(){if("u"!==L)throw U(N());N();const u=H();switch(u){case"$":case"_":break;default:if(!m.isIdStartChar(u))throw q()}j+=u,P="identifierName"},identifierName(){switch(L){case"$":case"_":case"‌":case"‍":return void(j+=N());case"\\":return N(),void(P="identifierNameEscape")}if(!m.isIdContinueChar(L))return $("identifier",j);j+=N()},identifierNameEscape(){if("u"!==L)throw U(N());N();const u=H();switch(u){case"$":case"_":case"‌":case"‍":break;default:if(!m.isIdContinueChar(u))throw q()}j+=u,P="identifierName"},sign(){switch(L){case".":return j=N(),void(P="decimalPointLeading");case"0":return j=N(),void(P="zero");case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return j=N(),void(P="decimalInteger");case"I":return N(),M("nfinity"),$("numeric",k*(1/0));case"N":return N(),M("aN"),$("numeric",NaN)}throw U(N())},zero(){switch(L){case".":return j+=N(),void(P="decimalPoint");case"e":case"E":return j+=N(),void(P="decimalExponent");case"x":case"X":return j+=N(),void(P="hexadecimal")}return $("numeric",0*k)},decimalInteger(){switch(L){case".":return j+=N(),void(P="decimalPoint");case"e":case"E":return j+=N(),void(P="decimalExponent")}if(!m.isDigit(L))return $("numeric",k*Number(j));j+=N()},decimalPointLeading(){if(m.isDigit(L))return j+=N(),void(P="decimalFraction");throw U(N())},decimalPoint(){switch(L){case"e":case"E":return j+=N(),void(P="decimalExponent")}return m.isDigit(L)?(j+=N(),void(P="decimalFraction")):$("numeric",k*Number(j))},decimalFraction(){switch(L){case"e":case"E":return j+=N(),void(P="decimalExponent")}if(!m.isDigit(L))return $("numeric",k*Number(j));j+=N()},decimalExponent(){switch(L){case"+":case"-":return j+=N(),void(P="decimalExponentSign")}if(m.isDigit(L))return j+=N(),void(P="decimalExponentInteger");throw U(N())},decimalExponentSign(){if(m.isDigit(L))return j+=N(),void(P="decimalExponentInteger");throw U(N())},decimalExponentInteger(){if(!m.isDigit(L))return $("numeric",k*Number(j));j+=N()},hexadecimal(){if(m.isHexDigit(L))return j+=N(),void(P="hexadecimalInteger");throw U(N())},hexadecimalInteger(){if(!m.isHexDigit(L))return $("numeric",k*Number(j));j+=N()},string(){switch(L){case"\\":return N(),void(j+=function(){switch(T()){case"b":return N(),"\b";case"f":return N(),"\f";case"n":return N(),"\n";case"r":return N(),"\r";case"t":return N(),"\t";case"v":return N(),"\v";case"0":if(N(),m.isDigit(T()))throw U(N());return"\0";case"x":return N(),function(){let u="",e=T();if(!m.isHexDigit(e))throw U(N());if(u+=N(),e=T(),!m.isHexDigit(e))throw U(N());return u+=N(),String.fromCodePoint(parseInt(u,16))}();case"u":return N(),H();case"\n":case"\u2028":case"\u2029":return N(),"";case"\r":return N(),"\n"===T()&&N(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case void 0:throw U(N())}return N()}());case'"':return S?(N(),$("string",j)):void(j+=N());case"'":return S?void(j+=N()):(N(),$("string",j));case"\n":case"\r":throw U(N());case"\u2028":case"\u2029":!function(u){console.warn(`JSON5: '${W(u)}' in strings is not valid ECMAScript; consider escaping`)}(L);break;case void 0:throw U(N())}j+=N()},start(){switch(L){case"{":case"[":return $("punctuator",N())}P="value"},beforePropertyName(){switch(L){case"$":case"_":return j=N(),void(P="identifierName");case"\\":return N(),void(P="identifierNameStartEscape");case"}":return $("punctuator",N());case'"':case"'":return S='"'===N(),void(P="string")}if(m.isIdStartChar(L))return j+=N(),void(P="identifierName");throw U(N())},afterPropertyName(){if(":"===L)return $("punctuator",N());throw U(N())},beforePropertyValue(){P="value"},afterPropertyValue(){switch(L){case",":case"}":return $("punctuator",N())}throw U(N())},beforeArrayValue(){if("]"===L)return $("punctuator",N());P="value"},afterArrayValue(){switch(L){case",":case"]":return $("punctuator",N())}throw U(N())},end(){throw U(N())}};function $(u,e){return{type:u,value:e,line:y,column:v}}function M(u){for(const e of u){if(T()!==e)throw U(N());N()}}function H(){let u="",e=4;for(;e-- >0;){const e=T();if(!m.isHexDigit(e))throw U(N());u+=N()}return String.fromCodePoint(parseInt(u,16))}const R={start(){if("eof"===w.type)throw z();V()},beforePropertyName(){switch(w.type){case"identifier":case"string":return _=w.value,void(B="afterPropertyName");case"punctuator":return void J();case"eof":throw z()}},afterPropertyName(){if("eof"===w.type)throw z();B="beforePropertyValue"},beforePropertyValue(){if("eof"===w.type)throw z();V()},beforeArrayValue(){if("eof"===w.type)throw z();"punctuator"!==w.type||"]"!==w.value?V():J()},afterPropertyValue(){if("eof"===w.type)throw z();switch(w.value){case",":return void(B="beforePropertyName");case"}":J()}},afterArrayValue(){if("eof"===w.type)throw z();switch(w.value){case",":return void(B="beforeArrayValue");case"]":J()}},end(){}};function V(){let u;switch(w.type){case"punctuator":switch(w.value){case"{":u={};break;case"[":u=[]}break;case"null":case"boolean":case"numeric":case"string":u=w.value}if(void 0===b)b=u;else{const e=g[g.length-1];Array.isArray(e)?e.push(u):Object.defineProperty(e,_,{value:u,writable:!0,enumerable:!0,configurable:!0})}if(null!==u&&"object"==typeof u)g.push(u),B=Array.isArray(u)?"beforeArrayValue":"beforePropertyName";else{const u=g[g.length-1];B=null==u?"end":Array.isArray(u)?"afterArrayValue":"afterPropertyValue"}}function J(){g.pop();const u=g[g.length-1];B=null==u?"end":Array.isArray(u)?"afterArrayValue":"afterPropertyValue"}function U(u){return G(void 0===u?`JSON5: invalid end of input at ${y}:${v}`:`JSON5: invalid character '${W(u)}' at ${y}:${v}`)}function z(){return G(`JSON5: invalid end of input at ${y}:${v}`)}function q(){return v-=5,G(`JSON5: invalid identifier character at ${y}:${v}`)}function W(u){const e={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(e[u])return e[u];if(u<" "){const e=u.charCodeAt(0).toString(16);return"\\x"+("00"+e).substring(e.length)}return u}function G(u){const e=new SyntaxError(u);return e.lineNumber=y,e.columnNumber=v,e}const Y={parse:function(u,e){p=String(u),B="start",g=[],f=0,y=1,v=0,w=void 0,_=void 0,b=void 0;do{w=O(),R[B]()}while("eof"!==w.type);return"function"==typeof e?x({"":b},"",e):b},stringify:function(u,e,t){const i=[];let n,s,r,D="",a="";if(null==e||"object"!=typeof e||Array.isArray(e)||(t=e.space,r=e.quote,e=e.replacer),"function"==typeof e)s=e;else if(Array.isArray(e)){n=[];for(const u of e){let e;"string"==typeof u?e=u:("number"==typeof u||u instanceof String||u instanceof Number)&&(e=String(u)),void 0!==e&&n.indexOf(e)<0&&n.push(e)}}return t instanceof Number?t=Number(t):t instanceof String&&(t=String(t)),"number"==typeof t?t>0&&(t=Math.min(10,Math.floor(t)),a="          ".substr(0,t)):"string"==typeof t&&(a=t.substr(0,10)),o("",{"":u});function o(u,e){let t=e[u];switch(null!=t&&("function"==typeof t.toJSON5?t=t.toJSON5(u):"function"==typeof t.toJSON&&(t=t.toJSON(u))),s&&(t=s.call(e,u,t)),t instanceof Number?t=Number(t):t instanceof String?t=String(t):t instanceof Boolean&&(t=t.valueOf()),t){case null:return"null";case!0:return"true";case!1:return"false"}return"string"==typeof t?c(t):"number"==typeof t?String(t):"object"==typeof t?Array.isArray(t)?function(u){if(i.indexOf(u)>=0)throw TypeError("Converting circular structure to JSON5");i.push(u);let e=D;D+=a;let t,n=[];for(let e=0;e<u.length;e++){const t=o(String(e),u);n.push(void 0!==t?t:"null")}if(0===n.length)t="[]";else if(""===a){t="["+n.join(",")+"]"}else{let u=",\n"+D,i=n.join(u);t="[\n"+D+i+",\n"+e+"]"}return i.pop(),D=e,t}(t):function(u){if(i.indexOf(u)>=0)throw TypeError("Converting circular structure to JSON5");i.push(u);let e=D;D+=a;let t,s=n||Object.keys(u),r=[];for(const e of s){const t=o(e,u);if(void 0!==t){let u=h(e)+":";""!==a&&(u+=" "),u+=t,r.push(u)}}if(0===r.length)t="{}";else{let u;if(""===a)u=r.join(","),t="{"+u+"}";else{let i=",\n"+D;u=r.join(i),t="{\n"+D+u+",\n"+e+"}"}}return i.pop(),D=e,t}(t):void 0}function c(u){const e={"'":.1,'"':.2},t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};let i="";for(let n=0;n<u.length;n++){const s=u[n];switch(s){case"'":case'"':e[s]++,i+=s;continue;case"\0":if(m.isDigit(u[n+1])){i+="\\x00";continue}}if(t[s])i+=t[s];else if(s<" "){let u=s.charCodeAt(0).toString(16);i+="\\x"+("00"+u).substring(u.length)}else i+=s}const n=r||Object.keys(e).reduce(((u,t)=>e[u]<e[t]?u:t));return i=i.replace(new RegExp(n,"g"),t[n]),n+i+n}function h(u){if(0===u.length)return c(u);const e=String.fromCodePoint(u.codePointAt(0));if(!m.isIdStartChar(e))return c(u);for(let t=e.length;t<u.length;t++)if(!m.isIdContinueChar(String.fromCodePoint(u.codePointAt(t))))return c(u);return u}}};var Q=Y;const K={debug:0,info:100,warn:200,error:400},X=u=>{if("object"==typeof u){if(null===u)return"null";let e="";try{e+=Q.stringify(u,null,"  ")}catch(u){e+="{error stringifying value}"}return e}return u.toString()};class Z{constructor(u,e=K){this._namespace=u,this._levels=e}child(u){return new Z(`${this._namespace}/${u}`,this._levels)}_log(u,e,...t){u>=Z.LOG_LEVEL&&console[e](`[${this._namespace}]:`,...t),Z.LOG_HISTORY.length>=Z.MAX_LOG_HISTORY&&Z.LOG_HISTORY.shift(),Z.LOG_HISTORY.push(`${(new Date).toISOString()} - ${e.toUpperCase()} [${this._namespace}]: ${t.map(X).join("\n")}`)}debug(...u){this._log(this._levels.debug,"debug",...u)}info(...u){this._log(this._levels.info,"info",...u)}warn(...u){this._log(this._levels.warn,"warn",...u)}error(...u){this._log(this._levels.error,"error",...u)}}Z.LOG_LEVEL=0,Z.MAX_LOG_HISTORY=200,Z.LOG_HISTORY=[];class uu extends s{constructor(u,e){super(),this.id=c.generateId("base-context"),this._plugin=u,this._player=u.playerApiShim,this._interactiveApi=u.interactiveApi,this.logger=u.logger.child(`base-context(${this.id})`),this.container=this._makeChildContainer(e||u.container),this.cards=[],this.deleted=!1}_makeChildContainer(u){const e=document.createElement("div");return e.id=this.id,e.classList.add("jw-reset"),e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.width="100%",e.style.height="100%",e.style.pointerEvents="none",u.appendChild(e),e}addCard(u){const e=new E(this._player,this._plugin.interactiveApi,this,u);this.cards.push(e),this.container.appendChild(e.element),e.element.dispatchEvent(new Event("add")),e.element.dispatchEvent(new Event("show"))}removeCard(u,e){u.element.parentElement&&u.element.parentElement.removeChild(u.element);const t=this.cards.indexOf(u);-1!==t&&this.cards.splice(t,1),e?(u.element.dispatchEvent(new Event(`hide:${e}`)),u.element.dispatchEvent(new Event(`remove:${e}`))):(u.element.dispatchEvent(new Event("hide")),u.element.dispatchEvent(new Event("remove"))),u.destroy(e)}hide(){this.container.style.display="none"}show(){this.container.style.display=""}destroy(u){const e=this.container;e.parentElement&&(this.deleted=!0,e.parentElement.removeChild(e),this.cards.forEach((e=>{u?(e.element.dispatchEvent(new Event(`hide:${u}`)),e.element.dispatchEvent(new Event(`remove:${u}`))):(e.element.dispatchEvent(new Event("hide")),e.element.dispatchEvent(new Event("remove"))),e.destroy(u)}))),this._player.utils.clearCss(this.id);const t=document.querySelector(`style[data-jwplayer-id="${this.id}"]`);t&&t.parentElement.removeChild(t)}}class eu extends uu{constructor(u,e,t,i){super(u,i),this.id=c.generateId("cue-context"),this.cue=e,this.track=t,this.logger=u.logger.child(`cue-context(${this.id})`),this._boundTime=(...u)=>this._time(...u),this._player.on("time",this._boundTime)}_time({position:u}){const e=Math.max(0,u-this.cue.startTime),t=Math.max(0,this.cue.endTime-this.cue.startTime),i=100*Math.min(Math.max(0,e/t),1),n={progress:`${i}%`,remaining:100-i+"%",halfProgress:i/2+"%",halfRemaining:(100-i)/2+"%"};this.trigger("timeAsPercent",n)}destroyCue(u){this.destroy(u),this.track._removeCue(this.cue)}destroy(u){this._player.off("time",this._boundTime),super.destroy(u)}}class tu{constructor(u,e){var t;this.name=e.name,this.viewport=Object.assign({},e.viewport),this.sourceUrl=null==(t=e.manifest)?void 0:t.uri,this.plugin=u,this.player=u.player,this.id=c.generateId("base-project"),this._contexts=new Map,this.mode="display",this._logger=this.plugin.logger.child(`project-${this.name}`),this._initPromise=Promise.resolve(),this.container=this._makeChildContainer(u.container)}_makeChildContainer(u){const e=document.createElement("div");return e.id=this.id,e.classList.add("jw-reset"),e.style.position="absolute",this.viewport.width,this.viewport.height,e.style.top="0px",e.style.left="0px",e.style.width="100%",e.style.height="100%",e.style.pointerEvents="none",u.appendChild(e),e}addContext(u){const e=new uu(this.plugin,this.container);this._contexts.set(u,e)}getContext(u){return this._contexts.get(u,context)}removeContext(u){const e=this._contexts.get(u);e&&(e.destroy(),this._contexts.delete(u))}readyPromise(){return this._initPromise}_adjustToPlayerLayout(u){this.container.style.top=u.top,this.container.style.left=u.left,this.container.style.width=u.width,this.container.style.height=u.height}hide(){this.mode="hidden",this.container.style.display="none"}show(){this.mode="display",this.container.style.display=""}destroy(){this._contexts.forEach(((u,e)=>{this._contexts.delete(e),u.deleted||u.destroy()})),this._contexts=null,this.container.parentElement&&this.container.parentElement.removeChild(this.container),this.container=null}}class iu extends tu{constructor(u,e){var t;super(u,e),this.id=c.generateId("cue-project"),this._cueToCueContextMap=new Map,this.sourceUrl=null==(t=e.manifest)?void 0:t.uri,this._boundAddTrackListener=(...u)=>this._addTrackListener(...u),this._boundCueChangeListener=(...u)=>this._cueChangeListener(...u),this.trackElement=document.createElement("track"),this.trackElement.kind="metadata",this.trackElement.label=this.id,this.trackElement.id=c.generateId("interactive-track"),this.sourceUrl&&(this.trackElement.src=this.sourceUrl);const i=this.player.getMediaElement();i.textTracks.addEventListener("addtrack",this._boundAddTrackListener),this._initAccept=null,this._initPromise=new Promise((u=>{this._initAccept=u})),i.appendChild(this.trackElement)}_addTrackListener(u){const e=u.track;e.label===this.id&&(this.player.getMediaElement().textTracks.removeEventListener("addtrack",this._boundAddTrackListener),this._boundAddTrackListener=null,this.track=e,this.track.mode="hidden",this.track._removeCue=this.track.removeCue,this.track.removeCue=()=>{},this.track.addEventListener("cuechange",this._boundCueChangeListener),this._initAccept())}_cueChangeListener(u){const e=u.target,t=e.activeCues,i=[];for(let u=0;u<t.length;u++){const n=t[u];if(i.push(n),this._cueToCueContextMap.has(n))continue;const s=new eu(this.plugin,n,e,this.container);let r;try{r=Q.parse(n.text)}catch(u){this._logger.error(u),this._logger.error("Error parsing cue:",n.text);continue}Array.isArray(r)||(r=[r]),r.forEach((u=>{s.addCard(u)})),this._contexts.set(s.id,s),this._cueToCueContextMap.set(n,s)}this._cueToCueContextMap.forEach(((u,e)=>{-1===i.indexOf(e)&&(this._cueToCueContextMap.delete(e),this._contexts.delete(u.id),u.deleted||u.destroy())}))}addCue(u,e,t){let i=t;"string"!=typeof i&&(i=Q.stringify(t)),this.track.addCue(new VTTCue(u,e,i))}removeCue(u){this.track._removeCue(u)}destroy(){super.destroy(),this._cueToCueContextMap.forEach(((u,e)=>{this._cueToCueContextMap.delete(e),u.deleted||u.destroy()})),this._cueToCueContextMap=null,this.track.removeEventListener("cuechange",this._boundCueChangeListener),this.track=null,this.trackElement.parentElement&&this.trackElement.parentElement.removeChild(this.trackElement),this.trackElement=null}}class nu{static fromArray(u){return new nu(...u)}constructor(u,e,t,i){this.x=u,this.y=e,this.w=t,this.h=i}centerOn(u){const e=(u.x+(u.w-this.w))/2,t=(u.y+(u.h-this.h))/2;return new nu(e,t,this.w,this.h)}fitInside(u){const e=Math.max(this.w/u.w,this.h/u.h),t=this.w/e,i=this.h/e;return new nu(this.x,this.y,t,i)}fitTouching(u){const e=Math.min(this.w/u.w,this.h/u.h),t=this.w/e,i=this.h/e;return new nu(this.x,this.y,t,i)}scale(u){return new nu(this.x,this.y,this.w*u,this.h*u)}touchLeft(u){return new nu(u.x,this.y,this.w,this.h)}touchRight(u){return new nu(u.x+u.w-this.w,this.y,this.w,this.h)}touchTop(u){return new nu(this.x,u.y,this.w,this.h)}touchBottom(u){return new nu(this.x,u.y+u.h-this.h,this.w,this.h)}matchTransform(u,e){const t=e.w/u.w,i=(this.x-u.x)*t,n=(this.y-u.y)*t;return new nu(e.x+i,e.y+n,this.w*t,this.h*t)}max(u){const e=Math.min(u.x,this.x),t=Math.min(u.y,this.y),i=Math.max(u.w+u.x,this.w+this.x),n=Math.max(u.h+u.y,this.h+this.y);return new nu(e,t,i-e,n-t)}toRelative(u){return{left:(this.x-u.x)/u.w,top:(this.y-u.y)/u.h,width:this.w/u.w,height:this.h/u.h}}toRelativeCss(u){const e=this.toRelative(u);return{left:100*e.left+"%",top:100*e.top+"%",width:100*e.width+"%",height:100*e.height+"%"}}}const su=new Z("JWPlayer");Z.LOG_LEVEL=200;const ru={width:"match",height:"match"};(window.jwplayerPluginJsonp||window.jwplayer().registerPlugin)("interactive",u,class extends i{constructor(u,t,i){super(),this.player=u,this.config=t,this.container=i,this.version=e,this._projectUrl=t.projectUrl||t.project,this.L4=Z,this.logger=su.child("interactive"),this._boundWindowMessageHandler=(...u)=>this._windowMessageHandler(...u),this._boundPlaylistItemHandler=(...u)=>this._playlistItemHandler(...u),this._boundPlayHandler=(...u)=>this._playHandler(...u),this._boundAdjustToPlayerLayout=(...u)=>this._adjustToPlayerLayout(...u),this.player.on("fullscreen",this._boundAdjustToPlayerLayout),this.player.on("resize",this._boundAdjustToPlayerLayout),window.addEventListener("message",this._boundWindowMessageHandler),this.player.on("playlistItem",this._boundPlaylistItemHandler),this._namedProjects=new Map,this.playerApiShim=new a(this.player),this.interactiveApi=new A(this),this._setContainerStyle(),this._reset()}_setContainerStyle(){this.container.style.bottom="0",this.container.style.position="relative",this.container.style.height="100%",this.container.style.marginLeft="auto",this.container.style.marginRight="auto",this.container.style.paddingTop="0px",this.container.style.pointerEvents="none"}_reset(){var u;this.logger.debug(`Destroying ${this._namedProjects.size} projects...`),this._namedProjects.forEach((u=>{u.destroy()})),this._namedProjects.clear(),this._currentItem=null,null!=(u=this._currentManifestFetch)&&u.abort&&this._currentManifestFetch.abort(),this._currentManifestFetch=null,this.logger.debug(`Deleting all CSS entries in the "interactive-${this.player.id}" style element...`),this.player.utils.clearCss(`interactive-${this.player.id}`)}_playlistItemHandler(u){this._currentItem!==u.item&&(this._reset(),this.logger.debug("Hiding the video element while we initialize"),this.player.utils.css(`#${this.player.id}:not(.jw-flag-ads) .jw-wrapper>.jw-media`,{display:"none"},`interactive-${this.player.id}`),this._currentItem=u.item,this.player.once("play",this._boundPlayHandler),this._projectUrl?(this.logger.debug(`Fetching interactive manifest from "${this._projectUrl}"...`),this._currentManifestFetch=function(u,e){let t;window.AbortController&&(t=new AbortController,Object.assign({},e,{signal:t.signal}));const i=fetch(u,e);return t&&(i.abort=()=>t.abort()),i}(this._projectUrl)):this._currentManifestFetch=Promise.resolve())}_playHandler(){this._currentManifestFetch.then((u=>{if(u)return u.text()})).then((u=>{if(this._currentManifestFetch=null,!u||!u.length)return this.trigger("projects-loaded"),this.trigger("ready");let e;try{e=Q.parse(u)}catch(e){this.logger.error(e),this.logger.error("Error parsing manifest:",u)}if(!e||!e.length)return this.trigger("projects-loaded"),this.trigger("ready");if(e.forEach((u=>{const e=new iu(this,u);this._namedProjects.set(u.name,e)})),this.trigger("projects-loaded"),!this._namedProjects.size)return this.trigger("ready");const t=Array.from(this._namedProjects.values()).map((u=>u.readyPromise()));Promise.all(t).then((()=>{this._adjustToPlayerLayout(),this.trigger("ready")}))}))}_windowMessageHandler(u){var e,t;const i=null==u?void 0:u.data;if(null==i||null==(e=i.actions)||!e.length||0!==(null==i||null==(t=i.type)?void 0:t.indexOf("jwp-interactive-iframe")))return;const n=((u,e)=>{for(let t=0;t<u.length;t++){const i=u[t];if(i.contentWindow===e)return i}})(this.container.querySelectorAll("iframe"),u.target[0]);n&&i.actions.forEach((u=>{if("event"===u.namespace)n.dispatchEvent(new Event(u.name));else this.logger.info(`Unknown jwplayer-interactive-iframe action namespace ${u.namespace}`,u)}))}_adjustToPlayerLayout(){if(!this._namedProjects.size||!this.player.getMediaElement())return void this.player.utils.css(`#${this.player.id}:not(.jw-flag-ads) .jw-wrapper>.jw-media`,"",`interactive-${this.player.id}`,!0);const u=this.player.getMediaElement(),e=nu.fromArray([0,0,this.player.getWidth(),this.player.getHeight()]),t=nu.fromArray([0,0,u.videoWidth,u.videoHeight]),i=Array.from(this._namedProjects.values()).map((e=>{var i,n,s,r;e.viewport||(e.viewport={}),"number"!=typeof(null==(i=e.viewport)?void 0:i.width)&&(e.viewport.width=u.videoWidth),"number"!=typeof(null==(n=e.viewport)?void 0:n.height)&&(e.viewport.height=u.videoHeight);const D=(null==(s=e.viewport)?void 0:s.mediaScale)||1;let a=nu.fromArray([0,0,e.viewport.width,e.viewport.height]).fitTouching(t).scale(1/D).centerOn(t);const o=((null==e||null==(r=e.viewport)?void 0:r.mediaPin)||"center, middle").trim().split(/\s*,\s*/);let c="center",h="middle";return-1!==["top","bottom","middle"].indexOf(o[0])?h=o[0]:-1!==["top","bottom","middle"].indexOf(o[1])&&(h=o[1]),-1!==["left","right","center"].indexOf(o[0])?c=o[0]:-1!==["left","right","center"].indexOf(o[1])&&(c=o[1]),"left"===c?a=a.touchLeft(t):"right"===c&&(a=a.touchRight(t)),"top"===h?a=a.touchTop(t):"bottom"===h&&(a=a.touchBottom(t)),a})),n=i.reduce(((u,e)=>u.max(e))),s=n.fitInside(e).centerOn(e),r=i.map((u=>u.matchTransform(n,s).toRelativeCss(e))),D=t.matchTransform(n,s).toRelativeCss(e);D.display="",this.player.utils.css(`#${this.player.id}:not(.jw-flag-ads) .jw-wrapper>.jw-media`,D,`interactive-${this.player.id}`,!0);const a={objectFit:"contain",aspectRatio:`${n.w} / ${n.h}`};this.player.utils.css(`#${this.player.id}:not(.jw-flag-ads) .jw-wrapper>.jw-media>video`,a,`interactive-${this.player.id}`,!0),Array.from(this._namedProjects.values()).forEach(((u,e)=>{u._adjustToPlayerLayout(r[e])}))}createTrackProject(u,e,t=ru){if(this._namedProjects.has(u))return void this.logger.error(`Error creating project: A project with the id '${u}' already exists!`);const i=new iu(this,u,{name:u,viewport:t,manifest:{uri:e}});return this._namedProjects.set(u,i),this._adjustToPlayerLayout(),i}createBaseProject(u,e=ru){if(this._namedProjects.has(u))return void this.logger.error(`Error creating project: A project with the id '${u}' already exists!`);const t=new tu(this,u,{name:u,viewport:e});return this._namedProjects.set(u,t),this._adjustToPlayerLayout(),t}getProject(u){return this._namedProjects.get(u)}removeProject(u){const e=this._namedProjects.get(u);e&&(this._namedProjects.delete(u),e.destroy())}destroy(){this._destroyed||(this._destroyed=!0,super.destroy(),this.player.off("playlistItem",this._boundPlaylistItemHandler),this.player.off("play",this._boundPlayHandler),this.player.off("fullscreen",this._boundAdjustToPlayerLayout),this.player.off("resize",this._boundAdjustToPlayerLayout),this._reset(),this.container=null,window.removeEventListener("message",this._boundWindowMessageHandler))}})}();
